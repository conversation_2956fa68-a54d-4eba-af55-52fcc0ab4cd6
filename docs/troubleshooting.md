# Contractly - Troubleshooting Guide

## Common Issues and Solutions

This guide covers the most common issues encountered with Contractly and their solutions, organized by category for quick reference.

## 🚨 Application Issues

### Issue: Application Not Loading

#### Symptoms
- White screen or 500 error
- "Application in maintenance mode" message
- Connection timeout errors

#### Diagnosis
```bash
# Check application status
php artisan down --render="errors::503"

# Check if application is in maintenance mode
php artisan up

# View recent logs
tail -f storage/logs/laravel.log

# Check web server logs
sudo tail -f /var/log/nginx/error.log

# Verify file permissions
ls -la storage/
ls -la bootstrap/cache/
```

#### Solutions
```bash
# Clear all caches
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Regenerate autoloader
composer dump-autoload

# Fix file permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache

# Check environment configuration
php artisan config:show

# Restart services
sudo systemctl restart php8.3-fpm
sudo systemctl restart nginx
```

### Issue: Database Connection Failed

#### Symptoms
- "SQLSTATE[HY000] [2002] Connection refused"
- "Access denied for user"
- "Unknown database" errors

#### Diagnosis
```bash
# Test database connection
mysql -u contractly -p contractly_central

# Check database service status
sudo systemctl status mysql

# Verify database credentials
php artisan tinker
>>> DB::connection()->getPdo();

# Check database configuration
php artisan config:show database
```

#### Solutions
```bash
# Restart MySQL service
sudo systemctl restart mysql

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log

# Verify environment variables
grep DB_ .env

# Test connection with artisan
php artisan migrate:status

# For connection issues
sudo mysql
GRANT ALL PRIVILEGES ON *.* TO 'contractly'@'localhost';
FLUSH PRIVILEGES;
```

### Issue: Multi-Tenancy Not Working

#### Symptoms
- Tenant not found errors
- Wrong database context
- Domain resolution failures

#### Diagnosis
```bash
# List all tenants
php artisan tenants:list

# Check domain mappings
php artisan tinker
>>> \App\Models\Tenant::with('domains')->get();

# Test tenant initialization
php artisan tenants:run --tenants=uuid 'DB::table("users")->count()'

# Check tenant middleware
php artisan route:list | grep tenant
```

#### Solutions
```bash
# Create missing tenant
php artisan tenants:create --domain=example.com

# Fix domain mapping
php artisan tinker
>>> $tenant = \App\Models\Tenant::find('tenant-uuid');
>>> $tenant->domains()->create(['domain' => 'correct-domain.com']);

# Clear tenant cache
php artisan cache:clear
php artisan config:clear

# Run tenant migrations
php artisan tenants:migrate

# Initialize tenant context manually
php artisan tinker
>>> tenancy()->initialize(\App\Models\Tenant::first());
>>> tenant('id');
```

## 🔐 Authentication Issues

### Issue: OTP Not Working

#### Symptoms
- OTP not received
- Invalid OTP errors
- OTP expired messages

#### Diagnosis
```bash
# Check OTP records
php artisan tinker
>>> \App\Models\UserOTP::latest()->take(5)->get();

# Test SMS provider connection
php artisan tinker
>>> $channel = new \App\Broadcasting\MsegatChannel();
>>> $channel->test();

# Check queue status
php artisan queue:work --once
php artisan queue:failed

# View notification logs
tail -f storage/logs/laravel.log | grep -i notification
```

#### Solutions
```bash
# Clear expired OTPs
php artisan tinker
>>> \App\Models\UserOTP::where('expires_at', '<', now())->delete();

# Test SMS configuration
# Check .env values for SMS providers
grep -E "MSEGAT|TAQNYAT|WAHA" .env

# Restart queue workers
sudo supervisorctl restart contractly-worker:*

# Debug notification sending
php artisan tinker
>>> $user = \App\Models\User::first();
>>> $user->notify(new \App\Notifications\OTPNotification('123456'));
```

### Issue: Login Session Issues

#### Symptoms
- Frequent logouts
- "Session expired" errors
- CSRF token mismatch

#### Diagnosis
```bash
# Check session configuration
php artisan config:show session

# Test Redis connection
redis-cli ping

# Check session storage
php artisan tinker
>>> Session::all();

# Verify CSRF protection
curl -I https://your-domain.com
```

#### Solutions
```bash
# Clear sessions
php artisan session:table
redis-cli FLUSHDB

# Fix session permissions
sudo chown -R www-data:www-data storage/framework/sessions

# Update session configuration in .env
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=lax

# Restart Redis
sudo systemctl restart redis-server
```

## 📄 Document Generation Issues

### Issue: PDF Generation Failing

#### Symptoms
- Document generation timeout
- Empty PDF files
- DocKing API errors

#### Diagnosis
```bash
# Test DocKing connection
php artisan tinker
>>> app(\App\Services\DocKingService::class)->testConnection();

# Check document templates
php artisan tinker
>>> \App\Models\DocumentTemplate::all();

# Monitor document generation
tail -f storage/logs/laravel.log | grep -i document

# Check file permissions
ls -la storage/app/documents/
```

#### Solutions
```bash
# Increase PHP memory and timeout
# In /etc/php/8.3/fpm/pool.d/contractly.conf
php_admin_value[memory_limit] = 1G
php_admin_value[max_execution_time] = 600

# Create documents directory
mkdir -p storage/app/documents
sudo chown -R www-data:www-data storage/app/documents

# Test template rendering
php artisan tinker
>>> $template = \App\Models\DocumentTemplate::first();
>>> $service = app(\App\Services\DocumentTemplateService::class);
>>> $result = $service->render($template, ['test' => 'data']);

# Restart PHP-FPM
sudo systemctl restart php8.3-fpm
```

### Issue: File Upload Problems

#### Symptoms
- File upload failures
- "File too large" errors
- Permission denied errors

#### Diagnosis
```bash
# Check upload limits
php -i | grep -E "upload_max_filesize|post_max_size|max_file_uploads"

# Test file permissions
ls -la storage/app/public/
ls -la public/storage/

# Check Nginx configuration
grep -E "client_max_body_size" /etc/nginx/sites-available/contractly

# Monitor upload process
tail -f /var/log/nginx/error.log
```

#### Solutions
```bash
# Increase upload limits in PHP
# /etc/php/8.3/fpm/pool.d/contractly.conf
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M

# Update Nginx configuration
# /etc/nginx/sites-available/contractly
client_max_body_size 100M;

# Create storage link
php artisan storage:link

# Fix storage permissions
sudo chown -R www-data:www-data storage/app/public/
sudo chmod -R 755 storage/app/public/

# Restart services
sudo systemctl restart php8.3-fpm nginx
```

## 📡 Communication Issues

### Issue: SMS/WhatsApp Not Sending

#### Symptoms
- Messages not delivered
- API authentication errors
- Provider timeout errors

#### Diagnosis
```bash
# Test provider APIs
php artisan tinker
>>> $msegat = new \App\Broadcasting\MsegatChannel();
>>> $msegat->send($user, new \App\Notifications\OTPNotification('123'));

# Check provider credentials
grep -E "MSEGAT|TAQNYAT|WAHA" .env

# Monitor queue processing
php artisan queue:work --once -vvv

# Check failed jobs
php artisan queue:failed
```

#### Solutions
```bash
# Verify API credentials
# Test with provider directly using curl
curl -X POST "https://api.msegat.com/gw/sendsms.php" \
  -d "username=your-username" \
  -d "password=your-password" \
  -d "numbers=966501234567" \
  -d "msg=Test message"

# Update provider configuration
php artisan config:clear
php artisan cache:clear

# Retry failed jobs
php artisan queue:retry all

# Check broadcast channel configuration
php artisan tinker
>>> \App\Models\BroadcastChannel::active()->get();
```

### Issue: Email Delivery Problems

#### Symptoms
- Emails not delivered
- SMTP authentication failure
- Emails in spam folder

#### Diagnosis
```bash
# Test email configuration
php artisan tinker
>>> Mail::raw('Test email', function($msg) {
    $msg->to('<EMAIL>')->subject('Test');
});

# Check mail configuration
php artisan config:show mail

# Monitor mail logs
tail -f storage/logs/laravel.log | grep -i mail

# Test SMTP connection
telnet smtp.gmail.com 587
```

#### Solutions
```bash
# Verify SMTP credentials
# Update .env with correct settings
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

# Test with different mail driver
MAIL_MAILER=log

# Check for rate limiting
# Add delays between emails
# Queue email sending

# Configure SPF and DKIM records
# Add to DNS:
# TXT record: "v=spf1 include:_spf.google.com ~all"
```

## ⚡ Performance Issues

### Issue: Slow Page Loading

#### Symptoms
- High response times
- Database query timeouts
- Memory exhaustion

#### Diagnosis
```bash
# Enable query logging
# In .env
DB_LOG_QUERIES=true

# Monitor slow queries
tail -f storage/logs/laravel.log | grep -i "slow query"

# Check memory usage
php artisan tinker
>>> memory_get_usage(true);
>>> memory_get_peak_usage(true);

# Analyze with Telescope
# Visit /telescope/requests

# Check database performance
mysql -u root -p
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Slow_queries';
```

#### Solutions
```bash
# Enable caching
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Optimize database queries
# Add eager loading in models
php artisan tinker
>>> $contracts = \App\Models\Contract::with(['client', 'type'])->get();

# Increase PHP memory
# /etc/php/8.3/fpm/pool.d/contractly.conf
php_admin_value[memory_limit] = 512M

# Enable OPcache
# /etc/php/8.3/fpm/conf.d/10-opcache.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000

# Restart PHP-FPM
sudo systemctl restart php8.3-fpm
```

### Issue: High Database Load

#### Symptoms
- Slow database queries
- Connection pool exhaustion
- High CPU usage on database server

#### Diagnosis
```bash
# Check database connections
mysql -u root -p
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

# Analyze slow queries
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

# Check database size
SELECT table_schema AS "Database",
ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS "Size (MB)"
FROM information_schema.TABLES
GROUP BY table_schema;
```

#### Solutions
```bash
# Add database indexes
php artisan make:migration add_indexes_to_contracts_table

# Optimize database configuration
# /etc/mysql/mysql.conf.d/mysqld.cnf
innodb_buffer_pool_size = 2G
query_cache_size = 256M
query_cache_type = 1

# Use database connection pooling
# .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_POOL_SIZE=10

# Restart MySQL
sudo systemctl restart mysql
```

## 🔧 System Issues

### Issue: Queue Workers Not Processing

#### Symptoms
- Jobs stuck in queue
- Queue workers crashing
- High memory usage in workers

#### Diagnosis
```bash
# Check queue status
php artisan queue:work --once
redis-cli LLEN "queues:default"

# Monitor workers
sudo supervisorctl status contractly-worker:*

# Check worker logs
sudo tail -f /var/log/supervisor/contractly-worker.log

# Test job processing
php artisan tinker
>>> dispatch(new \App\Jobs\CreateFrameworkDirectoriesForTenant($tenant));
```

#### Solutions
```bash
# Restart queue workers
sudo supervisorctl restart contractly-worker:*

# Clear failed jobs
php artisan queue:flush
php artisan queue:restart

# Update supervisor configuration
# /etc/supervisor/conf.d/contractly.conf
numprocs=4
stopwaitsecs=3600
autorestart=true

# Monitor memory usage
# Add memory limits to jobs
class ProcessDocument implements ShouldQueue
{
    public $timeout = 3600;
    public $memory = 512;
}

# Update supervisor
sudo supervisorctl reread
sudo supervisorctl update
```

### Issue: Redis Connection Problems

#### Symptoms
- Redis connection refused
- Cache not working
- Session storage failures

#### Diagnosis
```bash
# Test Redis connection
redis-cli ping

# Check Redis status
sudo systemctl status redis-server

# Monitor Redis logs
sudo tail -f /var/log/redis/redis-server.log

# Test from application
php artisan tinker
>>> Cache::store('redis')->put('test', 'value');
>>> Cache::store('redis')->get('test');
```

#### Solutions
```bash
# Restart Redis
sudo systemctl restart redis-server

# Check Redis configuration
# /etc/redis/redis.conf
bind 127.0.0.1
port 6379
requirepass your_password

# Clear Redis data
redis-cli FLUSHALL

# Test connection with password
redis-cli -a your_password ping

# Update Laravel configuration
# .env
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_password
REDIS_PORT=6379
```

## 🛠️ Maintenance Commands

### Routine Maintenance

```bash
# Daily maintenance script
#!/bin/bash
# /var/www/contractly/scripts/daily-maintenance.sh

echo "Starting daily maintenance..."

# Clear expired sessions
php artisan session:gc

# Clear expired OTPs
php artisan tinker --execute="
\App\Models\UserOTP::where('expires_at', '<', now())->delete();
echo 'Expired OTPs cleared';
"

# Optimize database
php artisan model:prune

# Clear old logs
find storage/logs -name "*.log" -mtime +30 -delete

# Restart queue workers
sudo supervisorctl restart contractly-worker:*

echo "Daily maintenance completed"
```

### Emergency Recovery

```bash
# Emergency recovery script
#!/bin/bash
# /var/www/contractly/scripts/emergency-recovery.sh

echo "Starting emergency recovery..."

# Put application in maintenance mode
php artisan down --secret="emergency-access-token"

# Clear all caches
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Restart all services
sudo systemctl restart mysql
sudo systemctl restart redis-server
sudo systemctl restart php8.3-fpm
sudo systemctl restart nginx
sudo supervisorctl restart all

# Run health checks
php artisan migrate:status
redis-cli ping
curl -I http://localhost

# Bring application back online
php artisan up

echo "Emergency recovery completed"
```

### Database Recovery

```bash
# Database recovery commands
#!/bin/bash

# Backup current state
mysqldump -u contractly -p contractly_central > emergency_backup.sql

# Check database integrity
mysqlcheck -u contractly -p --all-databases

# Repair corrupted tables
mysqlcheck -u contractly -p --auto-repair contractly_central

# Rebuild indexes
php artisan tinker --execute="
DB::statement('ANALYZE TABLE contracts, clients, maintenance_requests');
echo 'Database analysis completed';
"

# Re-run migrations if needed
php artisan migrate:status
php artisan migrate --force
```

## 📞 Getting Help

### Log Locations
- **Application**: `storage/logs/laravel.log`
- **Nginx**: `/var/log/nginx/contractly_*.log`
- **PHP-FPM**: `/var/log/php/contractly-error.log`
- **MySQL**: `/var/log/mysql/error.log`
- **Redis**: `/var/log/redis/redis-server.log`
- **Supervisor**: `/var/log/supervisor/contractly-*.log`

### Debug Commands
```bash
# Application debugging
php artisan about
php artisan config:show
php artisan route:list
php artisan queue:monitor

# System debugging
systemctl status nginx php8.3-fpm mysql redis-server
supervisorctl status
free -h
df -h
```

### Support Information
When seeking support, please provide:
- Error messages from logs
- Steps to reproduce the issue
- Environment information (`php artisan about`)
- Recent changes or deployments
- System resource usage
- Tenant ID (if tenant-specific issue)

---

*This troubleshooting guide covers the most common issues with Contractly. For complex issues, please check the logs and system status before applying solutions.*