# Contractly - Database Schema Documentation

## Database Architecture Overview

Contractly uses a **multi-tenant database-per-tenant architecture** with a central landlord database managing tenant information and separate tenant databases containing business data.

## 🏗️ Database Structure

### Central (Landlord) Database

#### Tenants Management
```sql
-- Central database tables
tenants
├── id (uuid, primary)
├── data (json) - tenant configuration
├── created_at
└── updated_at

domains
├── id (auto-increment, primary)
├── domain (varchar, unique)
├── tenant_id (uuid, foreign → tenants.id)
├── created_at
└── updated_at

telescope_entries
├── [Laravel Telescope monitoring tables]
```

### Tenant Database Schema

Each tenant has their own database with the following structure:

## 👥 User Management

### Users Table
```sql
users
├── id (auto-increment, primary)
├── name (varchar)
├── email (varchar, unique)
├── email_verified_at (timestamp, nullable)
├── password (varchar)
├── remember_token (varchar, nullable)
├── created_at
├── updated_at
└── deleted_at (soft deletes)
```

### User Authentication
```sql
user_otps
├── id (auto-increment, primary)
├── user_id (foreign → users.id)
├── otp (varchar) - encrypted OTP code
├── expires_at (timestamp)
├── verified_at (timestamp, nullable)
├── attempts (integer, default: 0)
├── created_at
└── updated_at
```

## 👨‍💼 Client Management

### Clients Table
```sql
clients
├── id (auto-increment, primary)
├── name (varchar) - client full name
├── email (varchar, unique, nullable)
├── phone (varchar, unique)
├── address (text, nullable)
├── national_id (varchar, unique, nullable)
├── gender (enum: male, female, nullable)
├── birth_date (date, nullable)
├── notes (text, nullable)
├── is_active (boolean, default: true)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_clients_phone (phone)
INDEX idx_clients_email (email)
INDEX idx_clients_active (is_active)
```

## 📋 Contract Management

### Contract Types
```sql
contract_types
├── id (auto-increment, primary)
├── name (varchar) - contract type name
├── description (text, nullable)
├── default_duration_months (integer, nullable)
├── default_price (decimal(10,2), nullable)
├── is_active (boolean, default: true)
├── created_at
├── updated_at
└── deleted_at (soft deletes)
```

### Contracts
```sql
contracts
├── id (auto-increment, primary)
├── contract_number (varchar, unique) - auto-generated
├── client_id (foreign → clients.id)
├── contract_type_id (foreign → contract_types.id)
├── title (varchar)
├── description (text, nullable)
├── start_date (date)
├── end_date (date)
├── total_amount (decimal(10,2))
├── status (enum: draft, active, completed, cancelled)
├── terms_and_conditions (text, nullable)
├── notes (text, nullable)
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_contracts_client (client_id)
INDEX idx_contracts_type (contract_type_id)
INDEX idx_contracts_status (status)
INDEX idx_contracts_dates (start_date, end_date)
UNIQUE idx_contracts_number (contract_number)
```

## 🔧 Maintenance Management

### Maintenance Requests
```sql
maintenance_requests
├── id (auto-increment, primary)
├── request_number (varchar, unique) - auto-generated
├── client_id (foreign → clients.id)
├── contract_id (foreign → contracts.id, nullable)
├── title (varchar)
├── description (text)
├── priority (enum: low, medium, high, urgent)
├── status (enum: pending, in_progress, completed, cancelled)
├── requested_date (date)
├── scheduled_date (date, nullable)
├── completed_date (date, nullable)
├── estimated_cost (decimal(10,2), nullable)
├── actual_cost (decimal(10,2), nullable)
├── technician_notes (text, nullable)
├── client_notes (text, nullable)
├── assigned_to (foreign → users.id, nullable)
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_maintenance_client (client_id)
INDEX idx_maintenance_contract (contract_id)
INDEX idx_maintenance_status (status)
INDEX idx_maintenance_priority (priority)
INDEX idx_maintenance_assigned (assigned_to)
INDEX idx_maintenance_dates (requested_date, scheduled_date)
UNIQUE idx_maintenance_number (request_number)
```

## 📅 Visit Management

### Visits
```sql
visits
├── id (auto-increment, primary)
├── visit_number (varchar, unique) - auto-generated
├── client_id (foreign → clients.id)
├── contract_id (foreign → contracts.id, nullable)
├── maintenance_request_id (foreign → maintenance_requests.id, nullable)
├── title (varchar)
├── description (text, nullable)
├── visit_type (enum: maintenance, inspection, installation, consultation)
├── status (enum: scheduled, in_progress, completed, cancelled, rescheduled)
├── scheduled_date (datetime)
├── actual_start_time (datetime, nullable)
├── actual_end_time (datetime, nullable)
├── technician_id (foreign → users.id, nullable)
├── client_notes (text, nullable)
├── technician_notes (text, nullable)
├── completion_notes (text, nullable)
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_visits_client (client_id)
INDEX idx_visits_contract (contract_id)
INDEX idx_visits_maintenance (maintenance_request_id)
INDEX idx_visits_technician (technician_id)
INDEX idx_visits_status (status)
INDEX idx_visits_scheduled (scheduled_date)
UNIQUE idx_visits_number (visit_number)
```

### Visit Schedules
```sql
visit_schedules
├── id (auto-increment, primary)
├── visit_id (foreign → visits.id)
├── original_date (datetime) - original scheduled time
├── new_date (datetime) - rescheduled time
├── reason (text) - reason for rescheduling
├── rescheduled_by (foreign → users.id)
├── created_at
└── updated_at

-- Indexes
INDEX idx_visit_schedules_visit (visit_id)
INDEX idx_visit_schedules_dates (original_date, new_date)
```

## 💰 Payment Management

### Payments
```sql
payments
├── id (auto-increment, primary)
├── payment_number (varchar, unique) - auto-generated
├── client_id (foreign → clients.id)
├── contract_id (foreign → contracts.id, nullable)
├── maintenance_request_id (foreign → maintenance_requests.id, nullable)
├── amount (decimal(10,2))
├── payment_method (enum: cash, bank_transfer, credit_card, check)
├── status (enum: pending, completed, failed, refunded)
├── payment_date (date, nullable)
├── due_date (date, nullable)
├── reference_number (varchar, nullable) - external payment ref
├── notes (text, nullable)
├── processed_by (foreign → users.id, nullable)
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_payments_client (client_id)
INDEX idx_payments_contract (contract_id)
INDEX idx_payments_maintenance (maintenance_request_id)
INDEX idx_payments_status (status)
INDEX idx_payments_dates (payment_date, due_date)
UNIQUE idx_payments_number (payment_number)
```

## 📄 Document Management

### Document Templates
```sql
document_templates
├── id (auto-increment, primary)
├── name (varchar) - template name
├── type (enum: contract, invoice, receipt, report, certificate)
├── template_content (longtext) - HTML template
├── variables (json) - available template variables
├── is_active (boolean, default: true)
├── version (varchar, default: '1.0')
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_document_templates_type (type)
INDEX idx_document_templates_active (is_active)
```

### Documents
```sql
documents
├── id (auto-increment, primary)
├── document_number (varchar, unique) - auto-generated
├── documentable_type (varchar) - polymorphic type
├── documentable_id (integer) - polymorphic ID
├── template_id (foreign → document_templates.id, nullable)
├── title (varchar)
├── type (enum: contract, invoice, receipt, report, certificate)
├── file_path (varchar, nullable) - stored file path
├── file_name (varchar, nullable) - original file name
├── file_size (integer, nullable) - file size in bytes
├── mime_type (varchar, nullable)
├── status (enum: draft, generated, sent, signed)
├── generated_at (timestamp, nullable)
├── sent_at (timestamp, nullable)
├── metadata (json, nullable) - additional document data
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_documents_polymorphic (documentable_type, documentable_id)
INDEX idx_documents_template (template_id)
INDEX idx_documents_type (type)
INDEX idx_documents_status (status)
UNIQUE idx_documents_number (document_number)
```

## 🏆 Certificate Management

### Certificate Requests
```sql
certificate_requests
├── id (auto-increment, primary)
├── request_number (varchar, unique) - auto-generated
├── client_id (foreign → clients.id)
├── contract_id (foreign → contracts.id, nullable)
├── certificate_type (varchar) - type of certificate
├── purpose (text) - purpose of certificate
├── status (enum: pending, approved, rejected, issued)
├── requested_date (date)
├── issue_date (date, nullable)
├── expiry_date (date, nullable)
├── certificate_data (json, nullable) - certificate details
├── rejection_reason (text, nullable)
├── processed_by (foreign → users.id, nullable)
├── created_by (foreign → users.id)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_certificate_requests_client (client_id)
INDEX idx_certificate_requests_contract (contract_id)
INDEX idx_certificate_requests_status (status)
INDEX idx_certificate_requests_dates (requested_date, issue_date)
UNIQUE idx_certificate_requests_number (request_number)
```

## 📢 Communication Management

### Broadcast Channels
```sql
broadcast_channels
├── id (auto-increment, primary)
├── name (varchar) - channel name
├── type (enum: sms, whatsapp, email)
├── provider (enum: msegat, taqnyat, waha) - service provider
├── configuration (json) - provider-specific config
├── is_active (boolean, default: true)
├── is_default (boolean, default: false)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_broadcast_channels_type (type)
INDEX idx_broadcast_channels_active (is_active)
INDEX idx_broadcast_channels_default (is_default)
```

### Broadcast Channel Senders
```sql
broadcast_channel_senders
├── id (auto-increment, primary)
├── channel_id (foreign → broadcast_channels.id)
├── sender_name (varchar) - sender identifier
├── sender_number (varchar, nullable) - phone number for SMS
├── api_credentials (json) - encrypted API credentials
├── is_active (boolean, default: true)
├── created_at
├── updated_at
└── deleted_at (soft deletes)

-- Indexes
INDEX idx_broadcast_senders_channel (channel_id)
INDEX idx_broadcast_senders_active (is_active)
```

### Notifications
```sql
notifications
├── id (uuid, primary)
├── type (varchar) - notification class name
├── notifiable_type (varchar) - recipient model type
├── notifiable_id (integer) - recipient model ID
├── data (json) - notification payload
├── read_at (timestamp, nullable)
├── created_at
└── updated_at

-- Indexes
INDEX idx_notifications_notifiable (notifiable_type, notifiable_id)
INDEX idx_notifications_read (read_at)
INDEX idx_notifications_created (created_at)
```

## 🔄 Relationships Overview

### Primary Relationships

#### Client-Centric Relationships
```
Client (1:N) → Contract
Client (1:N) → MaintenanceRequest
Client (1:N) → Visit
Client (1:N) → Payment
Client (1:N) → CertificateRequest
Client (1:N) → Document (polymorphic)
```

#### Contract-Centric Relationships
```
Contract (1:1) → MaintenanceRequest (nullable)
Contract (1:N) → Visit
Contract (1:N) → Payment
Contract (1:N) → Document (polymorphic)
Contract (1:N) → CertificateRequest
```

#### Maintenance-Centric Relationships
```
MaintenanceRequest (1:N) → Visit
MaintenanceRequest (1:N) → Payment
MaintenanceRequest (1:N) → Document (polymorphic)
```

#### Polymorphic Relationships
```
Document → morphsTo(documentable)
├── Contract
├── MaintenanceRequest
├── Payment
└── CertificateRequest
```

### User Relationships
```
User (1:N) → Contract (created_by)
User (1:N) → MaintenanceRequest (created_by, assigned_to)
User (1:N) → Visit (created_by, technician_id)
User (1:N) → Payment (created_by, processed_by)
User (1:N) → Document (created_by)
User (1:N) → CertificateRequest (created_by, processed_by)
```

## 📊 Data Integrity & Constraints

### Foreign Key Constraints
- All relationships enforced with foreign key constraints
- Cascade deletes where appropriate
- Restrict deletes for audit trail preservation

### Unique Constraints
- Auto-generated numbers for all major entities
- Email uniqueness per tenant
- Phone number uniqueness per client

### Check Constraints
```sql
-- Date validations
CHECK (end_date >= start_date) ON contracts
CHECK (actual_end_time >= actual_start_time) ON visits
CHECK (due_date >= created_at) ON payments

-- Status validations
CHECK (status IN ('draft', 'active', 'completed', 'cancelled')) ON contracts
CHECK (priority IN ('low', 'medium', 'high', 'urgent')) ON maintenance_requests

-- Amount validations
CHECK (amount >= 0) ON payments
CHECK (total_amount >= 0) ON contracts
```

### Indexes for Performance

#### Query Optimization Indexes
```sql
-- Search indexes
INDEX idx_clients_search (name, email, phone)
INDEX idx_contracts_search (contract_number, title)
INDEX idx_maintenance_search (request_number, title)

-- Date range indexes
INDEX idx_contracts_period (start_date, end_date)
INDEX idx_visits_schedule (scheduled_date)
INDEX idx_payments_due (due_date, status)

-- Status filtering indexes
INDEX idx_active_contracts (status, end_date)
INDEX idx_pending_maintenance (status, priority)
INDEX idx_scheduled_visits (status, scheduled_date)
```

## 🔒 Security Considerations

### Data Protection
- Sensitive data encryption at application level
- OTP codes encrypted in database
- API credentials stored encrypted
- Personal information access logging

### Audit Trail
- Soft deletes on all major entities
- created_at/updated_at timestamps
- User tracking on modifications
- Document version history

### Access Control
- Row-level security via application logic
- Tenant isolation enforced
- User permission checks via policies
- API rate limiting on sensitive operations

---

*This database documentation provides a comprehensive overview of Contractly's data structure. The schema is designed for scalability, data integrity, and multi-tenant isolation while supporting complex business workflows.*