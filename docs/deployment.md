# Contractly - Deployment Guide

## Production Deployment Overview

This guide covers the complete deployment process for Contractly, including server setup, configuration, security hardening, and maintenance procedures for a multi-tenant SaaS application.

## 🏗️ Infrastructure Requirements

### Server Specifications

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 50GB SSD
- **Network**: 100 Mbps
- **OS**: Ubuntu 22.04 LTS or CentOS 8+

#### Recommended Production
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 100GB+ SSD with backup
- **Network**: 1 Gbps
- **Load Balancer**: For horizontal scaling
- **CDN**: For static asset delivery

### Software Stack

#### Core Components
- **Web Server**: Nginx 1.22+
- **PHP**: 8.3+ with PHP-FPM
- **Database**: MySQL 8.0+ or PostgreSQL 15+
- **Cache**: Redis 7.0+
- **Queue**: Redis with Supervisor
- **SSL**: Let's Encrypt or commercial certificate

#### PHP Extensions Required
```bash
# Required extensions
php8.3-fpm
php8.3-mysql      # or php8.3-pgsql
php8.3-redis
php8.3-mbstring
php8.3-xml
php8.3-curl
php8.3-zip
php8.3-gd
php8.3-intl
php8.3-bcmath
php8.3-dom
php8.3-json
```

## 🚀 Server Setup

### 1. Server Preparation

#### Update System
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo dnf update -y
```

#### Install Dependencies
```bash
# Ubuntu/Debian
sudo apt install -y \
    nginx \
    mysql-server \
    redis-server \
    supervisor \
    certbot \
    python3-certbot-nginx \
    git \
    curl \
    wget \
    unzip

# Install PHP 8.3
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update
sudo apt install -y php8.3-fpm php8.3-mysql php8.3-redis \
    php8.3-mbstring php8.3-xml php8.3-curl php8.3-zip \
    php8.3-gd php8.3-intl php8.3-bcmath php8.3-dom
```

#### Install Composer
```bash
cd /tmp
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
composer --version
```

#### Install Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
node --version
npm --version
```

### 2. Database Setup

#### MySQL Configuration
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create databases
sudo mysql -u root -p
```

```sql
-- Central database
CREATE DATABASE contractly_central CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create application user
CREATE USER 'contractly'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON contractly_central.* TO 'contractly'@'localhost';

-- Grant privileges for tenant databases
GRANT ALL PRIVILEGES ON `contractly_tenant_%`.* TO 'contractly'@'localhost';
FLUSH PRIVILEGES;
```

#### MySQL Optimization
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# Performance settings
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_type = 1
query_cache_size = 256M

# Connection settings
max_connections = 200
thread_cache_size = 8

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
```

### 3. Redis Configuration

```ini
# /etc/redis/redis.conf
# Basic settings
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 60

# Memory management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
requirepass your_redis_password_here
```

```bash
# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

## 📁 Application Deployment

### 1. Code Deployment

#### Create Application Directory
```bash
sudo mkdir -p /var/www/contractly
sudo chown -R www-data:www-data /var/www/contractly
```

#### Deploy Code
```bash
# Clone repository
cd /var/www/contractly
sudo -u www-data git clone https://github.com/your-repo/contractly.git .

# Install dependencies
sudo -u www-data composer install --optimize-autoloader --no-dev
sudo -u www-data npm ci --production
sudo -u www-data npm run build
```

### 2. Environment Configuration

#### Production Environment File
```bash
# Copy and configure environment
sudo -u www-data cp .env.example .env
sudo -u www-data nano .env
```

```env
# Production environment configuration
APP_NAME="Contractly"
APP_ENV=production
APP_KEY=base64:your-generated-production-key
APP_DEBUG=false
APP_TIMEZONE=Asia/Riyadh
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=contractly_central
DB_USERNAME=contractly
DB_PASSWORD=secure_password_here

# Cache and Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password_here
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Contractly"

# Broadcasting Services
MSEGAT_USERNAME=your-msegat-username
MSEGAT_PASSWORD=your-msegat-password
MSEGAT_SENDER=your-sender-name

TAQNYAT_API_KEY=your-taqnyat-api-key
TAQNYAT_SENDER=your-sender-name

WAHA_API_URL=https://api.waha.com
WAHA_API_KEY=your-waha-api-key
WAHA_INSTANCE_ID=your-instance-id

# DocKing Integration
DOCKING_API_URL=https://api.docking.com
DOCKING_API_KEY=your-docking-api-key
DOCKING_SECRET_KEY=your-docking-secret

# Security
SESSION_LIFETIME=120
SANCTUM_STATEFUL_DOMAINS=your-domain.com

# Logging
LOG_CHANNEL=daily
LOG_LEVEL=warning

# Telescope (disable in production)
TELESCOPE_ENABLED=false
```

#### Generate Application Key
```bash
sudo -u www-data php artisan key:generate
```

### 3. Database Migration

```bash
# Run central database migrations
sudo -u www-data php artisan migrate --force

# Seed initial data
sudo -u www-data php artisan db:seed --force

# Create sample tenant (optional)
sudo -u www-data php artisan tenants:create \
    --domain=demo.your-domain.com \
    --name="Demo Tenant"
```

### 4. File Permissions

```bash
# Set proper ownership
sudo chown -R www-data:www-data /var/www/contractly

# Set directory permissions
sudo find /var/www/contractly -type d -exec chmod 755 {} \;
sudo find /var/www/contractly -type f -exec chmod 644 {} \;

# Set writable directories
sudo chmod -R 775 /var/www/contractly/storage
sudo chmod -R 775 /var/www/contractly/bootstrap/cache
sudo chmod 600 /var/www/contractly/.env
```

## 🌐 Web Server Configuration

### Nginx Configuration

#### Main Site Configuration
```nginx
# /etc/nginx/sites-available/contractly
server {
    listen 80;
    server_name your-domain.com *.your-domain.com;
    root /var/www/contractly/public;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'none'; frame-src 'none'; base-uri 'self'; form-action 'self';" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Main location block
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP handling
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Increase timeouts for document generation
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
    }

    # Static file caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
        access_log off;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /\.env {
        deny all;
    }

    location ~ /composer\.(json|lock) {
        deny all;
    }

    location ~ /package(-lock)?\.json {
        deny all;
    }

    # Increase body size for file uploads
    client_max_body_size 100M;

    # Rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Error and access logs
    error_log /var/log/nginx/contractly_error.log;
    access_log /var/log/nginx/contractly_access.log;
}
```

#### Rate Limiting Configuration
```nginx
# /etc/nginx/nginx.conf - Add to http block
http {
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=60r/m;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=120r/m;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    limit_conn addr 10;
}
```

#### Enable Site
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/contractly /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### PHP-FPM Configuration

```ini
# /etc/php/8.3/fpm/pool.d/contractly.conf
[contractly]
user = www-data
group = www-data
listen = /var/run/php/php8.3-fpm-contractly.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 20
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 10
pm.max_requests = 500

php_admin_value[memory_limit] = 512M
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M
php_admin_value[max_execution_time] = 300
php_admin_value[max_input_time] = 300

# Logging
php_admin_value[error_log] = /var/log/php/contractly-error.log
php_admin_flag[log_errors] = on
```

```bash
# Create log directory
sudo mkdir -p /var/log/php
sudo chown www-data:www-data /var/log/php

# Restart PHP-FPM
sudo systemctl restart php8.3-fpm
sudo systemctl enable php8.3-fpm
```

## 🔒 SSL/TLS Configuration

### Let's Encrypt SSL

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d *.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### SSL Configuration
```nginx
# SSL configuration will be added by Certbot
server {
    listen 443 ssl http2;
    server_name your-domain.com *.your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # Rest of configuration...
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name your-domain.com *.your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## ⚡ Queue and Task Management

### Supervisor Configuration

```ini
# /etc/supervisor/conf.d/contractly.conf
[program:contractly-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/contractly/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
directory=/var/www/contractly
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/log/supervisor/contractly-worker.log
stopwaitsecs=3600

[program:contractly-scheduler]
process_name=%(program_name)s
command=php /var/www/contractly/artisan schedule:work
directory=/var/www/contractly
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/log/supervisor/contractly-scheduler.log
```

```bash
# Update supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start contractly-worker:*
sudo supervisorctl start contractly-scheduler:*

# Check status
sudo supervisorctl status
```

### Cron Jobs

```bash
# Add to www-data crontab
sudo -u www-data crontab -e

# Add Laravel scheduler
* * * * * cd /var/www/contractly && php artisan schedule:run >> /dev/null 2>&1

# Backup cron (daily at 2 AM)
0 2 * * * /var/www/contractly/scripts/backup.sh >> /var/log/backup.log 2>&1
```

## 📊 Monitoring and Logging

### Application Monitoring

#### Log Configuration
```bash
# Create log directories
sudo mkdir -p /var/log/contractly
sudo chown www-data:www-data /var/log/contractly

# Configure log rotation
sudo nano /etc/logrotate.d/contractly
```

```
# /etc/logrotate.d/contractly
/var/log/contractly/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        /bin/systemctl reload php8.3-fpm > /dev/null 2>&1 || true
    endscript
}
```

#### Health Check Script
```bash
#!/bin/bash
# /var/www/contractly/scripts/health-check.sh

# Check application health
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/health)

if [ $response -eq 200 ]; then
    echo "Application is healthy"
else
    echo "Application health check failed (HTTP $response)"
    # Send alert notification
    systemctl restart php8.3-fpm
    systemctl restart nginx
fi

# Check database connection
php /var/www/contractly/artisan migrate:status > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "Database connection OK"
else
    echo "Database connection failed"
fi

# Check Redis connection
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "Redis connection OK"
else
    echo "Redis connection failed"
    systemctl restart redis-server
fi
```

### System Monitoring

#### Nginx Status
```nginx
# Add to nginx configuration
location /nginx_status {
    stub_status on;
    allow 127.0.0.1;
    deny all;
}
```

#### PHP-FPM Status
```ini
# Add to PHP-FPM pool configuration
pm.status_path = /fpm-status
ping.path = /fpm-ping
```

## 🔐 Security Hardening

### Firewall Configuration

```bash
# Install and configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow necessary services
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow mysql
sudo ufw allow redis

# Check status
sudo ufw status verbose
```

### Fail2Ban Configuration

```bash
# Install Fail2Ban
sudo apt install fail2ban

# Configure Nginx protection
sudo nano /etc/fail2ban/jail.local
```

```ini
# /etc/fail2ban/jail.local
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true

[nginx-req-limit]
enabled = true

[php-url-fopen]
enabled = true
```

### File System Security

```bash
# Secure sensitive files
sudo chmod 600 /var/www/contractly/.env
sudo chmod 600 /etc/nginx/sites-available/contractly
sudo chmod 600 /etc/ssl/private/*

# Remove unnecessary packages
sudo apt autoremove -y

# Update system regularly
sudo apt update && sudo apt upgrade -y
```

## 📦 Backup Strategy

### Database Backup Script

```bash
#!/bin/bash
# /var/www/contractly/scripts/backup.sh

# Configuration
BACKUP_DIR="/var/backups/contractly"
DATE=$(date +%Y%m%d_%H%M%S)
MYSQL_USER="contractly"
MYSQL_PASSWORD="secure_password_here"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup central database
mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD contractly_central > \
    $BACKUP_DIR/central_$DATE.sql

# Backup tenant databases
mysql -u$MYSQL_USER -p$MYSQL_PASSWORD -e "SHOW DATABASES" | \
grep "contractly_tenant_" | \
while read database; do
    mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD $database > \
        $BACKUP_DIR/${database}_$DATE.sql
done

# Backup application files
tar -czf $BACKUP_DIR/app_$DATE.tar.gz \
    /var/www/contractly \
    --exclude=/var/www/contractly/storage/logs \
    --exclude=/var/www/contractly/node_modules

# Backup uploads and documents
tar -czf $BACKUP_DIR/storage_$DATE.tar.gz \
    /var/www/contractly/storage/app/public

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Automated Backup

```bash
# Make script executable
sudo chmod +x /var/www/contractly/scripts/backup.sh

# Add to crontab
0 2 * * * /var/www/contractly/scripts/backup.sh >> /var/log/backup.log 2>&1
```

## 🚀 Deployment Automation

### Deployment Script

```bash
#!/bin/bash
# /var/www/contractly/scripts/deploy.sh

# Configuration
APP_DIR="/var/www/contractly"
BRANCH="main"

echo "Starting deployment..."

# Switch to application directory
cd $APP_DIR

# Put application in maintenance mode
sudo -u www-data php artisan down

# Backup current version
sudo -u www-data cp .env .env.backup

# Pull latest code
sudo -u www-data git fetch origin
sudo -u www-data git reset --hard origin/$BRANCH

# Install/update dependencies
sudo -u www-data composer install --optimize-autoloader --no-dev
sudo -u www-data npm ci --production
sudo -u www-data npm run build

# Run migrations
sudo -u www-data php artisan migrate --force

# Clear caches
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

# Restart services
sudo systemctl reload php8.3-fpm
sudo systemctl reload nginx
sudo supervisorctl restart contractly-worker:*

# Bring application back online
sudo -u www-data php artisan up

echo "Deployment completed successfully!"
```

### Zero-Downtime Deployment

```bash
#!/bin/bash
# /var/www/contractly/scripts/zero-downtime-deploy.sh

# Blue-Green deployment strategy
BLUE_DIR="/var/www/contractly-blue"
GREEN_DIR="/var/www/contractly-green"
CURRENT_LINK="/var/www/contractly"

# Determine which environment is currently active
if [ -L $CURRENT_LINK ]; then
    CURRENT_TARGET=$(readlink $CURRENT_LINK)
    if [[ $CURRENT_TARGET == *"blue"* ]]; then
        DEPLOY_TO=$GREEN_DIR
        ACTIVE_ENV="blue"
    else
        DEPLOY_TO=$BLUE_DIR
        ACTIVE_ENV="green"
    fi
else
    DEPLOY_TO=$BLUE_DIR
    ACTIVE_ENV=""
fi

echo "Deploying to: $DEPLOY_TO"

# Clone/update code in deployment directory
sudo -u www-data git clone https://github.com/your-repo/contractly.git $DEPLOY_TO

# Install dependencies and build
cd $DEPLOY_TO
sudo -u www-data composer install --optimize-autoloader --no-dev
sudo -u www-data npm ci --production
sudo -u www-data npm run build

# Copy environment configuration
sudo -u www-data cp /var/www/contractly/.env $DEPLOY_TO/.env

# Run migrations
sudo -u www-data php artisan migrate --force

# Update symlink to new version
sudo ln -sfn $DEPLOY_TO $CURRENT_LINK

# Restart services
sudo systemctl reload php8.3-fpm
sudo systemctl reload nginx

echo "Deployment completed. Active environment: $(basename $DEPLOY_TO)"
```

## 📋 Post-Deployment Checklist

### Immediate Verification
- [ ] Application loads correctly
- [ ] Database connections working
- [ ] Redis cache operational
- [ ] Queue workers running
- [ ] SSL certificate valid
- [ ] All scheduled tasks running

### Performance Verification
- [ ] Page load times acceptable
- [ ] Database query performance
- [ ] Memory usage within limits
- [ ] PHP-FPM pool status healthy
- [ ] Nginx worker processes stable

### Security Verification
- [ ] Firewall rules active
- [ ] File permissions correct
- [ ] Environment variables secure
- [ ] SSL/TLS configuration optimal
- [ ] Security headers present

### Monitoring Setup
- [ ] Log files rotating properly
- [ ] Health check endpoints responding
- [ ] Backup scripts scheduled
- [ ] Monitoring alerts configured
- [ ] Performance metrics collecting

---

*This deployment guide provides comprehensive coverage for production deployment of Contractly. Following these procedures ensures a secure, scalable, and maintainable production environment.*