# Contractly - Architecture Documentation

## System Architecture Overview

Contractly is built as a **multi-tenant SaaS application** using Lara<PERSON> with a database-per-tenant architecture pattern. This document details the architectural decisions, patterns, and implementation strategies used throughout the system.

## 🏛️ High-Level Architecture

### Multi-Tenancy Strategy

#### Database-per-Tenant Pattern
```
Central Database (landlord)
├── tenants table
├── domains table
└── telescope_entries table

Tenant Databases (per tenant)
├── users table
├── contracts table
├── clients table
├── maintenance_requests table
└── [all business logic tables]
```

**Benefits:**
- Complete data isolation between tenants
- Scalable performance (each tenant has dedicated database)
- Easy backup and restore per tenant
- Regulatory compliance (data sovereignty)
- Independent schema evolution per tenant

#### Tenant Resolution Flow
1. **Domain-based Resolution**: Incoming request domain mapped to tenant
2. **Database Context Switch**: Application switches to tenant-specific database
3. **Request Processing**: All queries execute against tenant database
4. **Response**: Data returned from tenant-specific context

### Panel Architecture

#### Admin Panel (Filament)
```
app/Filament/
├── Pages/                    # Custom admin pages
│   ├── Dashboard.php
│   ├── ManageTenantSettings.php
│   └── Auth/CustomLogin.php
└── Resources/               # CRUD resources
    ├── ClientResource.php
    ├── ContractResource.php
    ├── MaintenanceRequestResource.php
    └── [other resources]
```

#### Client Panel (Separate Filament Instance)
```
app/Filament/Client/
├── Pages/
│   ├── Dashboard.php
│   └── LandingPage.php
└── Resources/
    └── MaintenanceRequestResource.php
```

#### Web Portal (Livewire/Volt)
```
resources/views/client/
├── auth/
├── dashboard.blade.php
├── contracts/
├── maintenance/
├── payments/
└── visits/
```

## 🔄 Service Layer Architecture

### Service Pattern Implementation

#### Core Services
- **DocKingService**: Document generation and PDF processing
- **DocumentTemplateService**: Template management and rendering
- **PaymentService**: Payment processing and tracking
- **VisitSchedulerService**: Appointment scheduling logic

#### Service Structure
```php
class DocKingService
{
    private DocKingConnector $connector;
    
    public function generateDocument(array $data): Document;
    public function processTemplate(string $template, array $variables): string;
    public function convertToPdf(string $html): string;
}
```

### Integration Layer

#### External API Connectors
```
app/Http/Integrations/
└── DocKing/
    └── DocKingConnector.php    # Saloon-based API connector
```

#### Broadcasting Channels
```
app/Broadcasting/
├── MsegatChannel.php         # SMS provider
├── TaqnyatChannel.php        # SMS provider
└── WahaChannel.php           # WhatsApp provider
```

## 🗄️ Data Architecture

### Entity Relationship Overview

#### Core Business Entities
```
Client 1:N Contract 1:N Visit
              │
              └── 1:1 MaintenanceRequest 1:N Payment
                           │
                           └── N:N Document (polymorphic)
```

#### Authentication & Authorization
```
User
├── hasMany(Contract) [via Client relationship]
├── hasMany(Visit)
├── hasMany(MaintenanceRequest)
└── belongsToMany(Role) [implied via policies]
```

#### Document System
```
Document (Polymorphic)
├── morphsTo(documentable) → Contract|MaintenanceRequest|Payment
├── belongsTo(DocumentTemplate)
└── hasMany(versions) [implied for revision tracking]
```

### Database Design Patterns

#### Polymorphic Relationships
- **Documents**: Attachable to multiple entity types
- **Notifications**: Sent to various user types
- **Audit Trails**: Track changes across entities

#### Soft Deletes
- All core business entities use soft deletes
- Maintains data integrity for historical records
- Supports compliance requirements

#### UUID Primary Keys
- Tenant identification uses UUIDs
- Prevents tenant enumeration attacks
- Enables easy data migration between environments

## 🔐 Security Architecture

### Authentication Strategy

#### Multi-Context Authentication
- **Admin Panel**: Laravel's default authentication
- **Client Panel**: Separate guard configuration
- **API Access**: Sanctum token-based (if applicable)

#### OTP-Based Authentication
```php
UserOTP Model:
├── user_id (relationship)
├── otp_code (encrypted)
├── expires_at (timestamp)
├── verified_at (nullable)
└── attempts (rate limiting)
```

### Authorization Patterns

#### Policy-Based Authorization
```
app/Policies/
├── ClientPolicy.php
├── ContractPolicy.php
├── MaintenanceRequestPolicy.php
└── [entity-specific policies]
```

#### Multi-Tenant Authorization
- All policies check tenant context
- Users can only access their tenant's data
- Admin users have elevated permissions within tenant

### Data Protection

#### Tenant Isolation
- Database-level isolation
- Application-level guards
- File storage separation
- Cache namespace isolation

#### Input Validation & Sanitization
```php
app/Rules/
├── Phone.php                # International phone validation
└── [custom validation rules]

app/Helpers/functions/
├── validators.php           # Custom validation helpers
├── formatters.php          # Data formatting
└── utilities.php           # General utilities
```

## 🚀 Performance Architecture

### Caching Strategy

#### Multi-Level Caching
- **Application Cache**: Laravel cache for computed data
- **Database Query Cache**: ORM-level caching
- **View Cache**: Compiled Blade templates
- **Asset Cache**: Vite-compiled frontend assets

#### Tenant-Aware Caching
```php
// Cache keys include tenant context
Cache::put("tenant.{$tenantId}.contracts.count", $count);
```

### Queue Architecture

#### Job Processing
```
app/Jobs/
└── CreateFrameworkDirectoriesForTenant.php
```

#### Notification Queuing
- OTP delivery queued for reliability
- Email notifications batched
- SMS/WhatsApp sent via queue workers

### Database Optimization

#### Indexing Strategy
- Foreign key indexes
- Tenant-aware compound indexes
- Search optimization indexes
- Performance-critical query indexes

#### Query Optimization
- Eager loading relationships
- Pagination for large datasets
- Database query monitoring via Telescope

## 🔧 Development Architecture

### Code Organization Patterns

#### Helper System
```
app/Helpers/
├── functions/
│   ├── formatters.php      # Data formatting utilities
│   ├── utilities.php       # General helper functions
│   └── validators.php      # Validation helpers
└── loader.php              # Auto-loader for helpers
```

#### Provider Pattern
```
app/Providers/
├── AppServiceProvider.php
├── TenancyServiceProvider.php
├── DocKingServiceProvider.php
├── CustomPhoneRuleServiceProvider.php
└── Filament/
    ├── AdminPanelProvider.php
    └── ClientPanelProvider.php
```

### Dependency Injection

#### Service Container Configuration
- Services registered in appropriate providers
- Interface binding for testability
- Singleton services for performance
- Factory patterns for complex object creation

### Testing Architecture

#### Test Structure
```
tests/
├── Feature/                 # Integration tests
│   ├── Auth/               # Authentication testing
│   ├── Settings/           # Settings functionality
│   └── DashboardTest.php   # Core feature testing
└── Unit/                   # Unit tests
    └── ExampleTest.php
```

#### Testing Patterns
- **Feature Tests**: End-to-end workflow testing
- **Unit Tests**: Individual component testing
- **Policy Tests**: Authorization testing
- **Integration Tests**: External service testing

## 📊 Monitoring Architecture

### Application Monitoring

#### Laravel Telescope Integration
- Request/response monitoring
- Database query analysis
- Job queue monitoring
- Exception tracking
- Performance profiling

#### Logging Strategy
```php
// Tenant-aware logging
Log::channel('tenant')->info('Action performed', [
    'tenant_id' => tenant('id'),
    'user_id' => auth()->id(),
    'action' => 'contract.created'
]);
```

### Performance Monitoring

#### Metrics Collection
- Response time tracking
- Database query performance
- Memory usage monitoring
- Cache hit/miss ratios
- Queue processing times

#### Error Tracking
- Exception logging with context
- User action replay capability
- Stack trace analysis
- Error rate monitoring

## 🔄 Deployment Architecture

### Environment Configuration

#### Multi-Environment Support
- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized for performance and reliability

#### Configuration Management
- Environment-specific configuration files
- Secret management for API keys
- Database connection pooling
- Redis clustering for scale

### Scalability Considerations

#### Horizontal Scaling
- Load balancer configuration
- Session storage in Redis
- File storage on shared systems
- Database read replicas

#### Vertical Scaling
- PHP-FPM optimization
- Database query optimization
- Memory usage optimization
- Cache warming strategies

## 🔮 Future Architecture Considerations

### Planned Enhancements
- **API Layer**: RESTful API for mobile applications
- **Microservices**: Breaking out document processing
- **Event Sourcing**: Audit trail implementation
- **CQRS**: Read/write separation for performance

### Technology Evolution
- **Laravel Upgrades**: Staying current with framework
- **PHP Version**: Leveraging latest language features
- **Database**: Considering NoSQL for specific use cases
- **Frontend**: Potential Vue.js/React integration

---

*This architecture documentation provides a comprehensive overview of Contractly's system design. The architecture prioritizes security, scalability, and maintainability while supporting the complex requirements of a multi-tenant business application.*