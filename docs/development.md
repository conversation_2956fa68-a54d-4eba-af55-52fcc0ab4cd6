# Contractly - Development Guide

## Development Environment Setup

This guide covers the complete development setup process for Contractly, including local environment configuration, development workflows, and best practices.

## 🚀 Quick Start

### Prerequisites
- **PHP 8.3+** with required extensions
- **Composer** (latest version)
- **Node.js 18+** and npm
- **MySQL 8.0+** or **PostgreSQL 13+**
- **Redis 6.0+** (recommended for caching and sessions)
- **Git** for version control

### Installation Steps

#### 1. Clone Repository
```bash
git clone [repository-url] contractly
cd contractly
```

#### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install JavaScript dependencies
npm install
```

#### 3. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure database and other services
nano .env
```

#### 4. Database Setup
```bash
# Run migrations for central database
php artisan migrate

# Seed initial data
php artisan db:seed

# Create a test tenant (optional)
php artisan tenants:create
```

#### 5. Build Assets
```bash
# Build for development
npm run dev

# Or build for production
npm run build
```

#### 6. Start Development Server
```bash
# Using composer script (recommended)
composer run dev

# Or manually
php artisan serve &
php artisan queue:work &
npm run dev
```

## ⚙️ Environment Configuration

### Core Environment Variables

#### Application Settings
```env
APP_NAME="Contractly"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_TIMEZONE=Asia/Riyadh
APP_URL=http://localhost:8000
APP_LOCALE=ar
APP_FALLBACK_LOCALE=en
```

#### Database Configuration
```env
# Central Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=contractly_central
DB_USERNAME=root
DB_PASSWORD=

# Tenant Database Template
TENANCY_DB_CONNECTION=mysql
TENANCY_DB_HOST=127.0.0.1
TENANCY_DB_PORT=3306
TENANCY_DB_USERNAME=root
TENANCY_DB_PASSWORD=
```

#### Cache and Sessions
```env
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

#### Mail Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Contractly"
```

#### Broadcasting Services
```env
# Msegat SMS
MSEGAT_USERNAME=your-username
MSEGAT_PASSWORD=your-password
MSEGAT_SENDER=your-sender

# Taqnyat SMS
TAQNYAT_API_KEY=your-api-key
TAQNYAT_SENDER=your-sender

# Waha WhatsApp
WAHA_API_URL=https://api.waha.com
WAHA_API_KEY=your-api-key
WAHA_INSTANCE_ID=your-instance
```

#### External Services
```env
# DocKing Integration
DOCKING_API_URL=https://api.docking.com
DOCKING_API_KEY=your-api-key
DOCKING_SECRET_KEY=your-secret

# Telescope (Development only)
TELESCOPE_ENABLED=true
TELESCOPE_PATH=telescope
```

## 🏗️ Development Workflow

### Git Workflow

#### Branch Strategy
```bash
# Main branches
main            # Production-ready code
develop         # Integration branch
feature/*       # Feature branches
hotfix/*        # Emergency fixes
release/*       # Release preparation
```

#### Feature Development
```bash
# Create feature branch
git checkout -b feature/maintenance-scheduling develop

# Work on feature
git add .
git commit -m "Add maintenance scheduling functionality"

# Push to remote
git push origin feature/maintenance-scheduling

# Create pull request to develop
```

#### Commit Message Convention
```
type(scope): description

feat(contracts): add contract renewal functionality
fix(payments): resolve payment validation issue
docs(api): update API documentation
refactor(auth): improve OTP validation logic
test(visits): add visit scheduling tests
```

### Development Commands

#### Laravel Artisan Commands
```bash
# Application
php artisan serve                    # Start development server
php artisan config:clear            # Clear configuration cache
php artisan route:clear              # Clear route cache
php artisan view:clear               # Clear view cache

# Database
php artisan migrate                  # Run migrations
php artisan migrate:fresh --seed     # Fresh migrations with seeding
php artisan db:seed                  # Run seeders

# Multi-tenancy
php artisan tenants:list             # List all tenants
php artisan tenants:create           # Create new tenant
php artisan tenants:migrate          # Run tenant migrations
php artisan tenants:seed             # Seed tenant data

# Queue Management
php artisan queue:work               # Start queue worker
php artisan queue:restart            # Restart queue workers
php artisan queue:failed             # List failed jobs

# Cache Management
php artisan cache:clear              # Clear application cache
php artisan config:cache             # Cache configuration
php artisan route:cache              # Cache routes
```

#### Custom Artisan Commands
```bash
# Generate default document templates
php artisan generate:default-templates

# Create tenant with sample data
php artisan tenant:setup --with-demo-data

# Generate tenant report
php artisan tenant:report {tenant-id}
```

#### Composer Scripts
```bash
# Development workflow
composer run dev                     # Start all dev services
composer run test                    # Run test suite
composer run lint                    # Run code linting
composer run format                  # Format code style

# Quality assurance
composer run phpstan                 # Static analysis
composer run phpcs                   # Code style check
composer run security               # Security audit
```

#### NPM Scripts
```bash
# Development
npm run dev                          # Start Vite dev server
npm run build                        # Build for production
npm run watch                        # Watch for changes

# Quality
npm run lint                         # Lint JavaScript/CSS
npm run format                       # Format code
npm run test                         # Run frontend tests
```

## 🧪 Testing

### Test Structure
```
tests/
├── Feature/                         # Integration tests
│   ├── Auth/                       # Authentication tests
│   ├── Admin/                      # Admin panel tests
│   ├── Client/                     # Client portal tests
│   └── Api/                        # API endpoint tests
├── Unit/                           # Unit tests
│   ├── Models/                     # Model tests
│   ├── Services/                   # Service tests
│   └── Helpers/                    # Helper function tests
└── Browser/                        # Browser tests (Dusk)
    ├── Admin/                      # Admin UI tests
    └── Client/                     # Client UI tests
```

### Running Tests

#### PHPUnit/Pest Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/Auth/LoginTest.php

# Run with filter
php artisan test --filter=login
```

#### Database Testing
```bash
# Use testing database
php artisan test --env=testing

# Refresh database for each test
php artisan test --migrate-refresh
```

### Writing Tests

#### Feature Test Example
```php
<?php

use function Pest\Laravel\{actingAs, get, post, assertDatabaseHas};

test('admin can create maintenance request', function () {
    $admin = User::factory()->admin()->create();
    $client = Client::factory()->create();
    
    actingAs($admin)
        ->post('/admin/maintenance-requests', [
            'client_id' => $client->id,
            'title' => 'Test Maintenance',
            'description' => 'Test description',
            'priority' => 'medium'
        ])
        ->assertRedirect()
        ->assertSessionHas('success');
    
    assertDatabaseHas('maintenance_requests', [
        'client_id' => $client->id,
        'title' => 'Test Maintenance'
    ]);
});
```

#### Unit Test Example
```php
<?php

use App\Services\DocKingService;

test('document service generates PDF correctly', function () {
    $service = app(DocKingService::class);
    
    $result = $service->generateDocument([
        'template' => 'contract',
        'data' => ['client_name' => 'Ahmed Mohammed']
    ]);
    
    expect($result)
        ->toHaveKey('pdf_content')
        ->and($result['pdf_content'])
        ->not->toBeEmpty();
});
```

## 🔧 Development Tools

### Laravel Telescope

#### Installation and Setup
```bash
# Already included in composer.json
# Configure in .env
TELESCOPE_ENABLED=true
```

#### Usage
- **URL**: `http://localhost:8000/telescope`
- **Features**:
  - Request monitoring
  - Database query analysis
  - Job queue monitoring
  - Exception tracking
  - Performance profiling

### Code Quality Tools

#### Laravel Pint (Code Formatting)
```bash
# Format all files
./vendor/bin/pint

# Check formatting without changes
./vendor/bin/pint --test

# Format specific directory
./vendor/bin/pint app/Services
```

#### PHPStan (Static Analysis)
```bash
# Analyze code
./vendor/bin/phpstan analyse

# Generate baseline
./vendor/bin/phpstan analyse --generate-baseline
```

### Debugging Tools

#### Debug Configuration
```env
# Enable debug mode
APP_DEBUG=true

# Database query logging
DB_LOG_QUERIES=true

# Log level
LOG_LEVEL=debug
```

#### Useful Debug Commands
```bash
# View logs in real-time
php artisan pail

# Clear and view logs
php artisan log:clear
tail -f storage/logs/laravel.log

# Debug routes
php artisan route:list

# Debug events
php artisan event:list
```

## 🎨 Frontend Development

### Asset Building

#### Vite Configuration
```javascript
// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/landing.css',
                'resources/js/app.js',
                'resources/js/landing.js'
            ],
            refresh: true,
        }),
    ],
});
```

#### Development Workflow
```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Watch mode for continuous building
npm run watch
```

### Livewire/Volt Development

#### Creating Volt Components
```bash
# Create Volt page
php artisan make:volt client/dashboard

# Create Volt component
php artisan make:volt components/maintenance-form
```

#### Volt Component Example
```php
<?php
// resources/views/livewire/maintenance/create.blade.php

use function Livewire\Volt\{state, rules, save};

state([
    'title' => '',
    'description' => '',
    'priority' => 'medium'
]);

rules([
    'title' => 'required|string|max:255',
    'description' => 'required|string',
    'priority' => 'required|in:low,medium,high,urgent'
]);

$save = function () {
    $this->validate();
    
    MaintenanceRequest::create([
        'client_id' => auth()->id(),
        'title' => $this->title,
        'description' => $this->description,
        'priority' => $this->priority
    ]);
    
    session()->flash('success', 'تم إنشاء طلب الصيانة بنجاح');
    
    return redirect()->route('client.maintenance.index');
};

?>

<div>
    <form wire:submit="save">
        <input wire:model="title" type="text" placeholder="عنوان الطلب">
        <textarea wire:model="description" placeholder="وصف المشكلة"></textarea>
        <select wire:model="priority">
            <option value="low">منخفض</option>
            <option value="medium">متوسط</option>
            <option value="high">عالي</option>
            <option value="urgent">عاجل</option>
        </select>
        <button type="submit">إرسال الطلب</button>
    </form>
</div>
```

### Styling with TailwindCSS

#### Configuration
```javascript
// tailwind.config.js
module.exports = {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./app/Filament/**/*.php",
    ],
    theme: {
        extend: {
            fontFamily: {
                'arabic': ['TheYearofHandicrafts', 'sans-serif'],
                'camel': ['TheYearOfTheCamel', 'sans-serif'],
            },
            colors: {
                primary: {
                    50: '#eff6ff',
                    500: '#3b82f6',
                    900: '#1e3a8a',
                }
            }
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
    ],
}
```

## 🐛 Debugging Multi-Tenancy

### Tenant Context Debugging
```php
// Check current tenant
if (tenancy()->initialized) {
    $tenant = tenant();
    logger('Current tenant: ' . $tenant->id);
} else {
    logger('No tenant context');
}

// Debug tenant database
$connection = app('db.tenancy')->connection();
logger('Tenant DB: ' . $connection->getDatabaseName());
```

### Common Multi-Tenancy Issues

#### Issue: Tenant not found
```bash
# Check tenant exists
php artisan tenants:list

# Verify domain mapping
SELECT * FROM domains WHERE domain = 'your-domain.com';
```

#### Issue: Database not switching
```php
// Force tenant context
tenancy()->initialize($tenant);

// Check connection
DB::connection()->getDatabaseName();
```

## 📦 Package Development

### Creating Custom Packages
```bash
# Create package structure
mkdir packages/contractly/document-generator
cd packages/contractly/document-generator

# Initialize composer
composer init
```

### Package Integration
```json
// composer.json
{
    "repositories": [
        {
            "type": "path",
            "url": "./packages/contractly/document-generator"
        }
    ],
    "require": {
        "contractly/document-generator": "@dev"
    }
}
```

## 🚀 Performance Optimization

### Development Performance Tips

#### Database Optimization
```php
// Use eager loading
$contracts = Contract::with(['client', 'type', 'visits'])->get();

// Use select to limit columns
$clients = Client::select('id', 'name', 'phone')->active()->get();

// Use pagination for large datasets
$maintenance = MaintenanceRequest::paginate(20);
```

#### Cache Optimization
```php
// Cache expensive queries
$stats = Cache::remember('dashboard.stats', 3600, function () {
    return [
        'total_clients' => Client::count(),
        'active_contracts' => Contract::active()->count(),
        'pending_maintenance' => MaintenanceRequest::pending()->count(),
    ];
});
```

#### Asset Optimization
```bash
# Optimize images
npm install imagemin-cli -g
imagemin public/images/* --out-dir=public/images/optimized

# Minify CSS/JS in production
npm run build
```

## 🔐 Security in Development

### Environment Security
```bash
# Never commit .env files
echo ".env*" >> .gitignore

# Use different keys per environment
php artisan key:generate --show

# Secure file permissions
chmod 600 .env
chmod 755 storage bootstrap/cache
```

### Code Security Practices
```php
// Always validate input
$request->validate([
    'amount' => 'required|numeric|min:0',
    'phone' => 'required|phone:SA'
]);

// Use authorization
$this->authorize('update', $contract);

// Sanitize output
echo e($user->name);
```

## 📋 Development Checklist

### Before Committing
- [ ] Run tests: `php artisan test`
- [ ] Check code style: `./vendor/bin/pint --test`
- [ ] Run static analysis: `./vendor/bin/phpstan analyse`
- [ ] Update documentation if needed
- [ ] Verify migrations are reversible
- [ ] Check for security issues

### Before Deployment
- [ ] Test in staging environment
- [ ] Run performance tests
- [ ] Verify backup procedures
- [ ] Check environment configuration
- [ ] Test multi-tenant functionality
- [ ] Validate external integrations

---

*This development guide provides comprehensive coverage of the development workflow for Contractly. Following these practices ensures code quality, security, and maintainability.*