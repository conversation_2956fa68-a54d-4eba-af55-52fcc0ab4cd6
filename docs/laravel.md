TITLE: Add Notifiable Trait to Laravel Model
DESCRIPTION: This snippet demonstrates how to include the `Notifiable` trait in a Laravel model, such as `App\Models\User`, to enable the model to send and receive notifications. The trait provides the `notify` method for dispatching notification instances.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;
}
```

----------------------------------------

TITLE: Repopulating Form Fields with Blade's Global old() Helper
DESCRIPTION: For convenience in Blade templates, <PERSON><PERSON> provides a global `old` helper function to repopulate form fields with previously flashed input. If no old input exists for the given field, `null` will be returned. This snippet demonstrates its use within an HTML input tag.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_11

LANGUAGE: blade
CODE:
```
<input type="text" name="title" value="{{ old('title') }}">
```

----------------------------------------

TITLE: Handling User File Uploads in Laravel Controller
DESCRIPTION: This controller method demonstrates how to store an uploaded file using the `store` method on an `UploadedFile` instance. It automatically generates a unique filename and determines the extension from the MIME type, returning the stored file's path.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_42

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class UserAvatarController extends Controller
{
    /**
     * Update the avatar for the user.
     */
    public function update(Request $request): string
    {
        $path = $request->file('avatar')->store('avatars');

        return $path;
    }
}
```

----------------------------------------

TITLE: Defining a Base Layout with Blade
DESCRIPTION: This snippet defines a base Blade layout file (`app.blade.php`) using standard HTML markup combined with Blade's `@section` and `@yield` directives. It establishes placeholders for dynamic content like page titles, sidebars, and main content, allowing child views to inject their specific content.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_133

LANGUAGE: blade
CODE:
```
<!-- resources/views/layouts/app.blade.php -->

<html>
    <head>
        <title>App Name - @yield('title')</title>
    </head>
    <body>
        @section('sidebar')
            This is the master sidebar.
        @show

        <div class="container">
            @yield('content')
        </div>
    </body>
</html>
```

----------------------------------------

TITLE: Configuring MySQL Database in Laravel .env
DESCRIPTION: This configuration snippet for the `.env` file specifies the settings required for Laravel to connect to a MySQL database. It defines the connection type, host, port, database name, username, and password for the MySQL server.
SOURCE: https://github.com/laravel/docs/blob/12.x/installation.md#_snippet_4

LANGUAGE: ini
CODE:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=
```

----------------------------------------

TITLE: Upsert Records using Laravel Query Builder
DESCRIPTION: The `upsert` method inserts new records or updates existing ones based on unique column identification. It accepts values to insert/update, unique identifier columns, and columns to update if a match is found. Note that most databases require a primary or unique index on the identifier columns.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_65

LANGUAGE: PHP
CODE:
```
DB::table('flights')->upsert(
    [
        ['departure' => 'Oakland', 'destination' => 'San Diego', 'price' => 99],
        ['departure' => 'Chicago', 'destination' => 'New York', 'price' => 150]
    ],
    ['departure', 'destination'],
    ['price']
);
```

----------------------------------------

TITLE: Implementing Method Injection for HTTP Request in Laravel Controllers
DESCRIPTION: Illustrates how to inject the `Illuminate\Http\Request` instance directly into a controller method. This allows easy access to incoming request data, such as form inputs, for processing within the method.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_35

LANGUAGE: PHP
CODE:
```
<?php

namespace AppHttpControllers;

use IlluminateHttpRedirectResponse;
use IlluminateHttpRequest;

class UserController extends Controller
{
    /**
     * Store a new user.
     */
    public function store(Request $request): RedirectResponse
    {
        $name = $request->name;

        // Store the user...

        return redirect('/users');
    }
}
```

----------------------------------------

TITLE: Access Current Request or Input with request() Helper
DESCRIPTION: The `request` function provides a convenient way to access the current HTTP request instance or retrieve a specific input field's value from the request. A default value can be provided for input fields.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_140

LANGUAGE: php
CODE:
```
$request = request();
```

LANGUAGE: php
CODE:
```
$value = request('key', $default);
```

----------------------------------------

TITLE: Defining Basic BelongsTo Relationship in Laravel PHP
DESCRIPTION: Defines the inverse one-to-many relationship on the child model (`Comment`) to access its parent (`Post`) using the default foreign key convention (e.g., `post_id`). This method should be defined on the model that 'belongs to' another model.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_8

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Comment extends Model
{
    /**
     * Get the post that owns the comment.
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }
}
```

----------------------------------------

TITLE: Debug Raw SQL of Laravel Queries with dumpRawSql and ddRawSql
DESCRIPTION: Shows how to use `dumpRawSql()` and `ddRawSql()` methods on a Laravel query to output the query's SQL with all parameter bindings properly substituted. `ddRawSql()` halts execution after dumping, while `dumpRawSql()` allows it to continue.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_80

LANGUAGE: php
CODE:
```
DB::table('users')->where('votes', '>', 100)->dumpRawSql();

DB::table('users')->where('votes', '>', 100)->ddRawSql();
```

----------------------------------------

TITLE: Defining Local Scopes in Laravel Eloquent
DESCRIPTION: This snippet demonstrates how to define reusable query constraints as local scopes within an Eloquent model. The `#[Scope]` attribute is used, and the scope method receives a `Builder` instance, returning `void` or the builder itself.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_93

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    /**
     * Scope a query to only include popular users.
     */
    #[Scope]
    protected function popular(Builder $query): void
    {
        $query->where('votes', '>', 100);
    }

    /**
     * Scope a query to only include active users.
     */
    #[Scope]
    protected function active(Builder $query): void
    {
        $query->where('active', 1);
    }
}
```

----------------------------------------

TITLE: Creating Created/Updated Timestamps Columns - Laravel Schema Builder - PHP
DESCRIPTION: The `timestamps` method is a convenience method that creates both `created_at` and `updated_at` `TIMESTAMP` columns. It supports an optional `precision` parameter for fractional seconds, commonly used by Eloquent to automatically manage record creation and update times.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_76

LANGUAGE: PHP
CODE:
```
$table->timestamps(precision: 0);
```

----------------------------------------

TITLE: Dispatching Queued Listeners After Database Commit in Laravel PHP
DESCRIPTION: This snippet illustrates how to ensure a queued listener is dispatched only after all open database transactions have been committed. By implementing the `ShouldQueueAfterCommit` interface, the listener avoids issues where it might process data not yet persisted to the database.
SOURCE: https://github.com/laravel/docs/blob/12.x/events.md#_snippet_21

LANGUAGE: PHP
CODE:
```
<?php

namespace AppListeners;

use IlluminateContractsQueueShouldQueueAfterCommit;
use IlluminateQueueInteractsWithQueue;

class SendShipmentNotification implements ShouldQueueAfterCommit
{
    use InteractsWithQueue;
}
```

----------------------------------------

TITLE: Dynamically Determining Trusted Hosts via Configuration (PHP)
DESCRIPTION: This snippet illustrates how to dynamically determine trusted hosts by providing a closure to the `at` argument of the `trustHosts` method. This allows the application to fetch trusted hostnames from configuration files (e.g., `config('app.trusted_hosts')`) or even a database, providing flexibility for managing trusted hosts without hardcoding them.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_77

LANGUAGE: PHP
CODE:
```
->withMiddleware(function (Middleware $middleware) {
    $middleware->trustHosts(at: fn () => config('app.trusted_hosts'));
})
```

----------------------------------------

TITLE: Adding CSRF Protection to Forms
DESCRIPTION: This snippet demonstrates the use of the `@csrf` Blade directive within an HTML form. This directive generates a hidden input field containing a CSRF token, which is essential for Laravel's CSRF protection middleware to validate incoming requests and prevent cross-site request forgery attacks.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_136

LANGUAGE: blade
CODE:
```
<form method="POST" action="/profile">
    @csrf

    ...
</form>
```

----------------------------------------

TITLE: Laravel Request Validation in Controller Store Method
DESCRIPTION: Demonstrates how to use the `validate` method on the `Illuminate\Http\Request` object within a controller's `store` method. If validation passes, code execution continues; otherwise, a `ValidationException` is thrown, automatically handling redirects for HTTP requests or JSON responses for XHR requests.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_2

LANGUAGE: php
CODE:
```
/**
 * Store a new blog post.
 */
public function store(Request $request): RedirectResponse
{
    $validated = $request->validate([
        'title' => 'required|unique:posts|max:255',
        'body' => 'required',
    ]);

    // The blog post is valid...

    return redirect('/posts');
}
```

----------------------------------------

TITLE: Configuring Basic Vite Entry Points with Laravel Plugin (JS)
DESCRIPTION: Configures the `vite.config.js` file to use the Laravel Vite plugin, specifying the main JavaScript and CSS entry points for a standard application setup.
SOURCE: https://github.com/laravel/docs/blob/12.x/vite.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel([
            'resources/css/app.css',
            'resources/js/app.js',
        ]),
    ],
});
```

----------------------------------------

TITLE: Defining a Basic Blade View
DESCRIPTION: This snippet shows a simple Blade template file, `greeting.blade.php`, which displays a greeting using a passed `$name` variable. Views separate HTML presentation from application logic and are stored in `resources/views`.
SOURCE: https://github.com/laravel/docs/blob/12.x/views.md#_snippet_0

LANGUAGE: blade
CODE:
```
<!-- View stored in resources/views/greeting.blade.php -->

<html>
    <body>
        <h1>Hello, {{ $name }}</h1>
    </body>
</html>
```

----------------------------------------

TITLE: Trusting All Proxies in Laravel PHP
DESCRIPTION: This snippet shows how to configure Laravel to trust all proxies by using `*` as the `at` parameter in the `trustProxies` middleware. This approach is suitable for environments where proxy IP addresses are dynamic or unknown, such as with cloud load balancer providers like Amazon AWS.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_74

LANGUAGE: PHP
CODE:
```
->withMiddleware(function (Middleware $middleware) {
    $middleware->trustProxies(at: '*');
})
```

----------------------------------------

TITLE: Check if String Contains Substring with Str::contains() in Laravel
DESCRIPTION: The `Str::contains` method determines if a string includes a given value. By default, it is case-sensitive. It can also check for any value from an array or be configured for case-insensitivity.
SOURCE: https://github.com/laravel/docs/blob/12.x/strings.md#_snippet_18

LANGUAGE: php
CODE:
```
use Illuminate\Support\Str;

$contains = Str::contains('This is my name', 'my');

// true
```

LANGUAGE: php
CODE:
```
use Illuminate\Support\Str;

$contains = Str::contains('This is my name', ['my', 'foo']);

// true
```

LANGUAGE: php
CODE:
```
use Illuminate\Support\Str;

$contains = Str::contains('This is my name', 'MY', ignoreCase: true);

// true
```

----------------------------------------

TITLE: Defining Mass Assignable Attributes with $fillable
DESCRIPTION: Illustrates how to define the `$fillable` property on an Eloquent model. This property specifies which attributes are allowed to be mass assigned, protecting against unexpected data modification. In this example, only the 'name' attribute is mass assignable.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_51

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Flight extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['name'];
}
```

----------------------------------------

TITLE: Send Notification using Notifiable Trait Method
DESCRIPTION: This example shows how to send a notification to a notifiable entity (e.g., a user model) by calling the `notify` method, which is provided by the `Notifiable` trait. It expects an instance of a notification class.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_2

LANGUAGE: php
CODE:
```
use App\Notifications\InvoicePaid;

$user->notify(new InvoicePaid($invoice));
```

----------------------------------------

TITLE: Access Authenticated User via Request Instance in Laravel Controller
DESCRIPTION: Shows how to retrieve the authenticated user within a Laravel controller method by type-hinting the `Illuminate\Http\Request` instance. This provides convenient access to the authenticated user's data directly from the request object within specific controller actions.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class FlightController extends Controller
{
    /**
     * Update the flight information for an existing flight.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = $request->user();

        // ...

        return redirect('/flights');
    }
}
```

----------------------------------------

TITLE: Queueing Mail Message in Laravel PHP
DESCRIPTION: Demonstrates how to queue a mail message for background sending using the `Mail` facade's `queue` method. It shows specifying recipients via `to`, `cc`, and `bcc`. Requires queue configuration.
SOURCE: https://github.com/laravel/docs/blob/12.x/mail.md#_snippet_56

LANGUAGE: php
CODE:
```
Mail::to($request->user())
    ->cc($moreUsers)
    ->bcc($evenMoreUsers)
    ->queue(new OrderShipped($order));
```

----------------------------------------

TITLE: Conditional if Statements in Blade (Blade)
DESCRIPTION: Demonstrates the use of @if, @elseif, @else, and @endif directives in Blade, which provide a clean syntax for conditional logic, mirroring PHP's if statements.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_11

LANGUAGE: blade
CODE:
```
@if (count($records) === 1)
    I have one record!
@elseif (count($records) > 1)
    I have multiple records!
@else
    I don't have any records!
@endif
```

----------------------------------------

TITLE: Transforming Laravel Collection Items (PHP)
DESCRIPTION: The `map` method iterates over each item in the collection, applying a given callback function to it. The callback can modify the item and return a new value, resulting in a new collection composed of these transformed items. It does not modify the original collection.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_85

LANGUAGE: php
CODE:
```
$collection = collect([1, 2, 3, 4, 5]);

$multiplied = $collection->map(function (int $item, int $key) {
    return $item * 2;
});

$multiplied->all();

// [2, 4, 6, 8, 10]
```

----------------------------------------

TITLE: Retrieving All Request Input as Array (PHP)
DESCRIPTION: This PHP code uses the `all()` method on the `Illuminate\Http\Request` instance to retrieve all incoming request input data as a plain PHP array. This method is versatile and works for both traditional HTML form submissions and AJAX (XHR) requests, providing a comprehensive snapshot of all submitted data.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_23

LANGUAGE: php
CODE:
```
$input = $request->all();
```

----------------------------------------

TITLE: Running All Outstanding Database Migrations
DESCRIPTION: This Artisan command executes all database migrations that have not yet been applied. It processes the 'up' methods of the migration files in chronological order, applying the defined schema changes to the database.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_5

LANGUAGE: shell
CODE:
```
php artisan migrate
```

----------------------------------------

TITLE: Installing Laravel Sanctum via Artisan Command
DESCRIPTION: This command installs Laravel Sanctum, preparing the necessary database migrations and configuration for API authentication. It's the initial step to set up Sanctum in a Laravel application.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_0

LANGUAGE: shell
CODE:
```
php artisan install:api
```

----------------------------------------

TITLE: Accessing Configuration Values in Laravel (PHP)
DESCRIPTION: This snippet demonstrates how to retrieve configuration values using the `Config` facade or the global `config` helper function. It shows accessing values with dot notation and providing a default fallback value if the configuration option is not found.
SOURCE: https://github.com/laravel/docs/blob/12.x/configuration.md#_snippet_15

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Config;\n\n$value = Config::get('app.timezone');\n\n$value = config('app.timezone');\n\n// Retrieve a default value if the configuration value does not exist...\n$value = config('app.timezone', 'Asia/Seoul');
```

----------------------------------------

TITLE: Manipulating Data with Laravel Collections (PHP)
DESCRIPTION: This snippet demonstrates basic data manipulation using Laravel's `Collection` class. It initializes a collection, maps each element to its uppercase equivalent, and then filters out any empty elements, showcasing method chaining and immutability.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_0

LANGUAGE: PHP
CODE:
```
$collection = collect(['Taylor', 'Abigail', null])->map(function (?string $name) {
    return strtoupper($name);
})->reject(function (string $name) {
    return empty($name);
});
```

----------------------------------------

TITLE: Previewing Laravel Mailable in Browser (PHP)
DESCRIPTION: This snippet demonstrates how to return a Laravel Mailable instance directly from a route closure. Returning a Mailable from a route renders it in the browser, allowing for quick visual previewing of the email template without sending an actual email. It requires a route definition and an existing Mailable class.
SOURCE: https://github.com/laravel/docs/blob/12.x/mail.md#_snippet_63

LANGUAGE: php
CODE:
```
Route::get('/mailable', function () {
    $invoice = App\Models\Invoice::find(1);

    return new App\Mail\InvoicePaid($invoice);
});
```

----------------------------------------

TITLE: Performing a Basic GET Request with PHPUnit
DESCRIPTION: This snippet shows how to make a basic GET request to the application's root URL ('/') using PHPUnit within a feature test and assert that the response has an HTTP status code of 200 (OK).
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_1

LANGUAGE: PHP
CODE:
```
<?php

namespace Tests\Feature;

use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic test example.
     */
    public function test_the_application_returns_a_successful_response(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
    }
}
```

----------------------------------------

TITLE: Defining a HasMany Relationship in Laravel Eloquent
DESCRIPTION: This snippet shows how to define a one-to-many relationship on a parent model (Post) using the `hasMany` method. Eloquent automatically determines the foreign key based on the parent model's name.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Post extends Model
{
    /**
     * Get the comments for the blog post.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }
}
```

----------------------------------------

TITLE: Define Validation Rules in a Laravel Form Request
DESCRIPTION: Each Laravel form request class includes a `rules` method, which is responsible for returning the validation rules that should apply to the request's data. This example demonstrates how to define common validation rules for 'title' and 'body' fields, including 'required', 'unique', and 'max' constraints.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_15

LANGUAGE: php
CODE:
```
/**
 * Get the validation rules that apply to the request.
 *
 * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
 */
public function rules(): array
{
    return [
        'title' => 'required|unique:posts|max:255',
        'body' => 'required',
    ];
}
```

----------------------------------------

TITLE: Defining Named Route with Controller Action in Laravel
DESCRIPTION: This snippet illustrates how to assign a unique name, 'profile', to a route that points to a controller action. Naming routes associated with controllers simplifies referencing them for URL generation and redirects.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_40

LANGUAGE: PHP
CODE:
```
Route::get(
    '/user/profile',
    [UserProfileController::class, 'show']
)->name('profile');
```

----------------------------------------

TITLE: Dispatching Closures After HTTP Response in Laravel
DESCRIPTION: This snippet demonstrates dispatching a closure using the `dispatch` helper and chaining `afterResponse` to execute it after the HTTP response is sent. This allows for lightweight, non-queued tasks, such as sending an email, to run in the background without blocking the user's browser.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_41

LANGUAGE: PHP
CODE:
```
use App\Mail\WelcomeMessage;
use Illuminate\Support\Facades\Mail;

dispatch(function () {
    Mail::to('<EMAIL>')->send(new WelcomeMessage);
})->afterResponse();
```

----------------------------------------

TITLE: Schedule Closure Task Daily
DESCRIPTION: Demonstrates how to schedule a closure to run daily at midnight using Laravel's scheduler, clearing a database table within the closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/scheduling.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schedule;

Schedule::call(function () {
    DB::table('recent_users')->delete();
})->daily();
```

----------------------------------------

TITLE: Creating a New Laravel Application
DESCRIPTION: This command initiates the creation of a new Laravel project named `example-app` using the Laravel installer. It prompts the user to select their preferred testing framework, database, and starter kit during the setup process.
SOURCE: https://github.com/laravel/docs/blob/12.x/installation.md#_snippet_2

LANGUAGE: shell
CODE:
```
laravel new example-app
```

----------------------------------------

TITLE: Validate Password Complexity and Breach Status in Laravel
DESCRIPTION: Provides examples of using Laravel's `Password` rule object to enforce various password complexity requirements, such as minimum length, mixed case, numbers, symbols, and checking against public data breaches using the `uncompromised` method.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_183

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

$validator = Validator::make($request->all(), [
    'password' => ['required', 'confirmed', Password::min(8)],
]);
```

LANGUAGE: php
CODE:
```
// Require at least 8 characters...
Password::min(8)

// Require at least one letter...
Password::min(8)->letters()

// Require at least one uppercase and one lowercase letter...
Password::min(8)->mixedCase()

// Require at least one number...
Password::min(8)->numbers()

// Require at least one symbol...
Password::min(8)->symbols()
```

LANGUAGE: php
CODE:
```
Password::min(8)->uncompromised()
```

LANGUAGE: php
CODE:
```
// Ensure the password appears less than 3 times in the same data leak...
Password::min(8)->uncompromised(3);
```

LANGUAGE: php
CODE:
```
Password::min(8)
    ->letters()
    ->mixedCase()
    ->numbers()
    ->symbols()
    ->uncompromised()
```

----------------------------------------

TITLE: Laravel Query Builder: `where` Clause with Implicit Equality Operator
DESCRIPTION: This example shows a convenient way to use the `where` method when checking for equality. If the operator argument is omitted, Laravel automatically assumes the `=` operator, simplifying the syntax for common equality checks.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_24

LANGUAGE: php
CODE:
```
$users = DB::table('users')->where('votes', 100)->get();
```

----------------------------------------

TITLE: Optimizing Laravel Application for Production
DESCRIPTION: This Artisan command is a convenient way to cache various production-ready files, including configuration, events, routes, and views. It should be invoked as part of the application's deployment process to significantly improve performance by reducing filesystem access.
SOURCE: https://github.com/laravel/docs/blob/12.x/deployment.md#_snippet_2

LANGUAGE: Shell
CODE:
```
php artisan optimize
```

----------------------------------------

TITLE: Adding Request URL and Trace ID to Laravel Context via Middleware
DESCRIPTION: This PHP middleware demonstrates how to use the `Context` facade to add request-specific information, such as the URL and a unique trace ID, to the Laravel context. This data is then automatically included in subsequent log entries and propagated to queued jobs.
SOURCE: https://github.com/laravel/docs/blob/12.x/context.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

namespace AppHttpMiddleware;

use Closure;
use IlluminateHttpRequest;
use IlluminateSupportFacadesContext;
use IlluminateSupportStr;
use SymfonyComponentHttpFoundationResponse;

class AddContext
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        Context::add('url', $request->url());
        Context::add('trace_id', Str::uuid()->toString());

        return $next($request);
    }
}
```

----------------------------------------

TITLE: Retrieve Authenticated User with Auth Facade in Laravel
DESCRIPTION: Demonstrates how to access the currently authenticated user object and their unique ID using Laravel's `Auth` facade. This is a common and straightforward method for interacting with user data after successful authentication.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_0

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Auth;

// Retrieve the currently authenticated user...
$user = Auth::user();

// Retrieve the currently authenticated user's ID...
$id = Auth::id();
```

----------------------------------------

TITLE: Querying Laravel Eloquent Relationship Existence with Constraints and Count using whereHas
DESCRIPTION: Demonstrates using the `whereHas` method with a closure, operator, and count to filter models based on constraints applied to related models and a minimum quantity of matching related models. Requires `Illuminate\Database\Eloquent\Builder`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_88

LANGUAGE: php
CODE:
```
// Retrieve posts with at least ten comments containing words like code%...
$posts = Post::whereHas('comments', function (Builder $query) {
    $query->where('content', 'like', 'code%');
}, '>=', 10)->get();
```

----------------------------------------

TITLE: Listing All Defined Routes with Laravel Artisan
DESCRIPTION: This Artisan command provides a comprehensive overview of all routes defined in the Laravel application. It displays information such as HTTP method, URI, name, and action, helping developers quickly inspect and debug their routing configuration.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_14

LANGUAGE: shell
CODE:
```
php artisan route:list
```

----------------------------------------

TITLE: Displaying Variable Data in Blade Template
DESCRIPTION: Shows how to display the content of a variable passed to a Blade view using double curly braces. Blade automatically escapes HTML entities to prevent XSS attacks.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_1

LANGUAGE: blade
CODE:
```
Hello, {{ $name }}.
```

----------------------------------------

TITLE: Passing Data to View via Array
DESCRIPTION: This PHP snippet shows the common method of passing an associative array of data to a view. The `name` key's value, 'Victoria', becomes accessible as `$name` within the `greetings` view template.
SOURCE: https://github.com/laravel/docs/blob/12.x/views.md#_snippet_8

LANGUAGE: php
CODE:
```
return view('greetings', ['name' => 'Victoria']);
```

----------------------------------------

TITLE: Generate Fully Qualified URL in Laravel
DESCRIPTION: The `url` function generates a fully qualified URL to the given path. If no path is provided, an `Illuminate\Routing\UrlGenerator` instance is returned, allowing access to current, full, or previous URLs.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_98

LANGUAGE: php
CODE:
```
$url = url('user/profile');

$url = url('user/profile', [1]);
```

LANGUAGE: php
CODE:
```
$current = url()->current();

$full = url()->full();

$previous = url()->previous();
```

----------------------------------------

TITLE: Initial Registration of Service Providers (PHP)
DESCRIPTION: This PHP snippet shows the basic structure of the `bootstrap/providers.php` file, which is an array containing the class names of service providers that Laravel should load. This file serves as the central registry for all application service providers.
SOURCE: https://github.com/laravel/docs/blob/12.x/providers.md#_snippet_5

LANGUAGE: php
CODE:
```
<?php

return [
    App\Providers\AppServiceProvider::class,
];
```

----------------------------------------

TITLE: Enabling TrustHosts Middleware with Specific Hostnames (PHP)
DESCRIPTION: This snippet demonstrates how to enable the `TrustHosts` middleware in Laravel's `bootstrap/app.php` file. It configures the application to only accept requests from the specified host, 'laravel.test', rejecting any requests with different `Host` headers. This is crucial for security when direct web server configuration is not feasible.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_75

LANGUAGE: PHP
CODE:
```
->withMiddleware(function (Middleware $middleware) {
    $middleware->trustHosts(at: ['laravel.test']);
})
```

----------------------------------------

TITLE: Defining Single Required Route Parameter in Laravel
DESCRIPTION: This code demonstrates how to define a required route parameter in Laravel. The `{id}` placeholder captures a segment of the URI, which is then injected as a string argument into the route's callback function.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_22

LANGUAGE: PHP
CODE:
```
Route::get('/user/{id}', function (string $id) {
    return 'User '.$id;
});
```

----------------------------------------

TITLE: Displaying Flashed Session Data in Laravel Blade
DESCRIPTION: This Blade snippet illustrates how to retrieve and display flashed session data (e.g., a status message) in a view. It checks if a 'status' key exists in the session and, if so, displays its value within a success alert div.
SOURCE: https://github.com/laravel/docs/blob/12.x/redirects.md#_snippet_11

LANGUAGE: blade
CODE:
```
@if (session('status'))
    <div class="alert alert-success">
        {{ session('status') }}
    </div>
@endif
```

----------------------------------------

TITLE: Handling Password Reset Link Request Submission - Laravel PHP
DESCRIPTION: This route defines the POST endpoint for submitting the 'forgot password' form. It validates the email, uses Laravel's `Password` facade to send a reset link, and returns a response based on the status of the link sending operation, either a success message or validation errors. It requires `Illuminate\Http\Request` and `Illuminate\Support\Facades\Password`.
SOURCE: https://github.com/laravel/docs/blob/12.x/passwords.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;

Route::post('/forgot-password', function (Request $request) {
    $request->validate(['email' => 'required|email']);

    $status = Password::sendResetLink(
        $request->only('email')
    );

    return $status === Password::ResetLinkSent
        ? back()->with(['status' => __($status)])
        : back()->withErrors(['email' => __($status)]);
})->middleware('guest')->name('password.email');
```

----------------------------------------

TITLE: Dump Variables in Laravel
DESCRIPTION: The `dump` function is a debugging utility that outputs the given variables to the browser without halting script execution. It's useful for inspecting variable states while allowing the application flow to continue.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_120

LANGUAGE: php
CODE:
```
dump($value);

dump($value1, $value2, $value3, ...);
```

----------------------------------------

TITLE: Acquiring and Releasing Atomic Locks (PHP)
DESCRIPTION: Atomic locks prevent race conditions in distributed environments. This snippet demonstrates creating a lock with a timeout, acquiring it using `get()`, performing an operation, and then explicitly releasing it. The lock is acquired for the specified duration (e.g., 10 seconds).
SOURCE: https://github.com/laravel/docs/blob/12.x/cache.md#_snippet_30

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Cache;

$lock = Cache::lock('foo', 10);

if ($lock->get()) {
    // Lock acquired for 10 seconds...

    $lock->release();
}
```

----------------------------------------

TITLE: Assigning Middleware to a Laravel Route
DESCRIPTION: This example demonstrates how to assign middleware directly to a route definition using the `middleware` method. The `'auth'` middleware will be applied to the `/profile` route, ensuring that only authenticated users can access the `show` method of the `UserController`.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_6

LANGUAGE: php
CODE:
```
Route::get('/profile', [UserController::class, 'show'])->middleware('auth');
```

----------------------------------------

TITLE: Making Basic GET Request with Laravel HTTP Client (PHP)
DESCRIPTION: Demonstrates how to make a simple GET request to a URL using the `Http` facade in Laravel. The result is stored in a `$response` variable.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_0

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Http;

$response = Http::get('http://example.com');
```

----------------------------------------

TITLE: Sending a Laravel Mailable to a User (PHP)
DESCRIPTION: Illustrates how to send a mailable instance using the `Mail` facade's `to` and `send` methods within a controller. It shows fetching an order and sending an `OrderShipped` mailable to the authenticated user.
SOURCE: https://github.com/laravel/docs/blob/12.x/mail.md#_snippet_52

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Mail\OrderShipped;
use App\Models\Order;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class OrderShipmentController extends Controller
{
    /**
     * Ship the given order.
     */
    public function store(Request $request): RedirectResponse
    {
        $order = Order::findOrFail($request->order_id);

        // Ship the order...

        Mail::to($request->user())->send(new OrderShipped($order));

        return redirect('/orders');
    }
}
```

----------------------------------------

TITLE: Defining a Basic Eloquent Model Class in PHP
DESCRIPTION: This is the basic structure of an Eloquent model. Models extend `Illuminate\Database\Eloquent\Model` and typically reside in the `app\Models` directory, serving as the interface for a database table.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_4

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Flight extends Model
{
    // ...
}
```

----------------------------------------

TITLE: Automatic Request Object Injection in Laravel Routes
DESCRIPTION: Shows how Laravel automatically injects the `Illuminate\Http\Request` object into a route definition, allowing easy access to the current HTTP request without manual container interaction.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_2

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::get('/', function (Request $request) {
    // ...
});
```

----------------------------------------

TITLE: Inserting New Eloquent Models with Create Method in PHP
DESCRIPTION: This snippet demonstrates an alternative way to insert a new record using Laravel Eloquent's `create` method. This method allows for "saving" a new model in a single statement, returning the inserted model instance. It requires `fillable` or `guarded` properties on the model for mass assignment protection.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_42

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

$flight = Flight::create([
    'name' => 'London to Paris',
]);
```

----------------------------------------

TITLE: Execute Closure and Catch Exceptions with rescue()
DESCRIPTION: The `rescue` function executes a given closure and catches any exceptions that occur during its execution, sending them to the exception handler without stopping request processing. It can also return a default value or execute a fallback closure if an exception occurs, and allows conditional reporting of exceptions.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_141

LANGUAGE: php
CODE:
```
return rescue(function () {
    return $this->method();
});
```

LANGUAGE: php
CODE:
```
return rescue(function () {
    return $this->method();
}, false);
```

LANGUAGE: php
CODE:
```
return rescue(function () {
    return $this->method();
}, function () {
    return $this->failure();
});
```

LANGUAGE: php
CODE:
```
return rescue(function () {
    return $this->method();
}, report: function (Throwable $throwable) {
    return $throwable instanceof InvalidArgumentException;
});
```

----------------------------------------

TITLE: Defining a Basic Laravel Queue Job Class
DESCRIPTION: This snippet defines a `ProcessPodcast` job class in Laravel, demonstrating the basic structure of a queueable job. It includes a constructor to receive an Eloquent model (`Podcast`) and a `handle` method for processing, showcasing dependency injection for an `AudioProcessor` service. The `Queueable` trait ensures graceful serialization of Eloquent models.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_6

LANGUAGE: php
CODE:
```
<?php

namespace App\Jobs;

use App\Models\Podcast;
use App\Services\AudioProcessor;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ProcessPodcast implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Podcast $podcast,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(AudioProcessor $processor): void
    {
        // Process uploaded podcast...
    }
}
```

----------------------------------------

TITLE: Generating a Basic Eloquent Model in Laravel
DESCRIPTION: This command generates a new Eloquent model class named 'Flight' in the `app/Models` directory. It's the most basic way to create a model without additional associated files.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_0

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight
```

----------------------------------------

TITLE: Entering the Laravel Tinker REPL Environment
DESCRIPTION: This Artisan command launches the Tinker REPL, allowing interactive execution of PHP code within the context of the Laravel application. It's commonly used for debugging, testing, and exploring application components directly from the command line.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_4

LANGUAGE: shell
CODE:
```
php artisan tinker
```

----------------------------------------

TITLE: Installing Laravel Installer CLI Tool
DESCRIPTION: Installs the Laravel installer globally via Composer, which is a prerequisite for creating new Laravel applications with starter kits. This command makes the 'laravel' command available system-wide.
SOURCE: https://github.com/laravel/docs/blob/12.x/starter-kits.md#_snippet_0

LANGUAGE: shell
CODE:
```
composer global require laravel/installer
```

----------------------------------------

TITLE: Defining a Basic Laravel Controller Method
DESCRIPTION: This PHP snippet illustrates a basic controller class extending `Controller` with a public method `show`. This method handles an incoming HTTP request, retrieves a user by ID using `User::findOrFail`, and returns a view, demonstrating how controllers encapsulate request logic.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * Show the profile for a given user.
     */
    public function show(string $id): View
    {
        return view('user.profile', [
            'user' => User::findOrFail($id)
        ]);
    }
}
```

----------------------------------------

TITLE: Creating a New Database Table (Laravel PHP)
DESCRIPTION: This PHP snippet demonstrates how to create a new database table named 'users' using Laravel's Schema facade. It defines columns for ID, name, email, and timestamps within the provided closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_19

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

Schema::create('users', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('email');
    $table->timestamps();
});
```

----------------------------------------

TITLE: Retrieving Single Eloquent Models - PHP
DESCRIPTION: This snippet demonstrates common methods for retrieving a single Eloquent model instance. `find(1)` fetches a model by its primary key. `where('active', 1)->first()` retrieves the first model matching the specified query constraints. `firstWhere('active', 1)` provides a more concise alternative for the same purpose. All methods return a single model or `null` if not found.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_35

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

// Retrieve a model by its primary key...
$flight = Flight::find(1);

// Retrieve the first model matching the query constraints...
$flight = Flight::where('active', 1)->first();

// Alternative to retrieving the first model matching the query constraints...
$flight = Flight::firstWhere('active', 1);
```

----------------------------------------

TITLE: Storing Session Data - PHP
DESCRIPTION: This snippet shows the primary ways to store data in the session: using the `put` method on the `Request` instance's session object or by passing an associative array to the global `session` helper function.
SOURCE: https://github.com/laravel/docs/blob/12.x/session.md#_snippet_7

LANGUAGE: php
CODE:
```
// Via a request instance...
$request->session()->put('key', 'value');

// Via the global "session" helper...
session(['key' => 'value']);
```

----------------------------------------

TITLE: Retrieving or Creating Eloquent Models in PHP
DESCRIPTION: This snippet demonstrates Laravel Eloquent's `firstOrCreate` and `firstOrNew` methods. `firstOrCreate` attempts to find a record by given attributes; if not found, it creates and persists a new record. `firstOrNew` also attempts to find a record, but if not found, it returns a new model instance that must be manually saved.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_39

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

// Retrieve flight by name or create it if it doesn't exist...
$flight = Flight::firstOrCreate([
    'name' => 'London to Paris'
]);

// Retrieve flight by name or create it with the name, delayed, and arrival_time attributes...
$flight = Flight::firstOrCreate(
    ['name' => 'London to Paris'],
    ['delayed' => 1, 'arrival_time' => '11:30']
);

// Retrieve flight by name or instantiate a new Flight instance...
$flight = Flight::firstOrNew([
    'name' => 'London to Paris'
]);

// Retrieve flight by name or instantiate with the name, delayed, and arrival_time attributes...
$flight = Flight::firstOrNew(
    ['name' => 'Tokyo to Sydney'],
    ['delayed' => 1, 'arrival_time' => '11:30']
);
```

----------------------------------------

TITLE: Assign Multiple Middleware to a Laravel Route
DESCRIPTION: This snippet demonstrates how to assign multiple middleware classes to a single Laravel route by passing an array of middleware names to the `middleware` method. All specified middleware will be applied to the route in the order they are listed.
SOURCE: https://github.com/laravel/docs/blob/12.x/middleware.md#_snippet_7

LANGUAGE: php
CODE:
```
Route::get('/', function () {
    // ...
})->middleware([First::class, Second::class]);
```

----------------------------------------

TITLE: Paginate Query Builder Results in Laravel
DESCRIPTION: This PHP example shows how to paginate results from the Laravel query builder using the `paginate` method. It automatically handles setting the query's 'limit' and 'offset' based on the current page, displaying a specified number of items per page.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * Show all application users.
     */
    public function index(): View
    {
        return view('user.index', [
            'users' => DB::table('users')->paginate(15)
        ]);
    }
}
```

----------------------------------------

TITLE: Disabling Laravel Maintenance Mode (Shell)
DESCRIPTION: This Artisan command disables maintenance mode, making the application fully accessible to users again. It reverses the effect of the `php artisan down` command.
SOURCE: https://github.com/laravel/docs/blob/12.x/configuration.md#_snippet_28

LANGUAGE: shell
CODE:
```
php artisan up
```

----------------------------------------

TITLE: Laravel Validation Rule: confirmed
DESCRIPTION: The `confirmed` rule ensures that the field under validation has a matching field named `[field_name]_confirmation`. This is commonly used for password confirmation.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_83

LANGUAGE: APIDOC
CODE:
```
confirmed
  Description: Field must have a matching field named '[field_name]_confirmation'.
```

----------------------------------------

TITLE: Laravel Query Builder: Grouping `orWhere` Conditions with a Closure
DESCRIPTION: This example shows how to group 'OR' conditions using a closure passed to `orWhere`. This allows for complex logical groupings, ensuring that the conditions within the closure are evaluated together, similar to parentheses in SQL.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_28

LANGUAGE: php
CODE:
```
use Illuminate\Database\Query\Builder; 

$users = DB::table('users')
    ->where('votes', '>', 100)
    ->orWhere(function (Builder $query) {
        $query->where('name', 'Abigail')
            ->where('votes', '>', 50);
        })
    ->get();
```

----------------------------------------

TITLE: Defining Anonymous Blade Component with Props (Blade)
DESCRIPTION: This Blade snippet defines an anonymous component (`alert.blade.php`) using the `@props` directive. It specifies `type` with a default value and `message` as data variables, while other attributes will be available via the component's attribute bag.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_121

LANGUAGE: Blade
CODE:
```
<!-- /resources/views/components/alert.blade.php -->

@props(['type' => 'info', 'message'])

<div {{ $attributes->merge(['class' => 'alert alert-'.$type]) }}>
    {{ $message }}
</div>
```

----------------------------------------

TITLE: Display Laravel Paginated Results and Links in Blade
DESCRIPTION: Demonstrates how to iterate over paginated results and render the pagination links within a Blade template. The `links()` method automatically generates HTML compatible with Tailwind CSS, providing navigation for the result set.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_15

LANGUAGE: blade
CODE:
```
<div class="container">
    @foreach ($users as $user)
        {{ $user->name }}
    @endforeach
</div>

{{ $users->links() }}
```

----------------------------------------

TITLE: Rendering Collections with Blade's @each Directive
DESCRIPTION: This Blade directive efficiently renders a specified view for each item in an array or collection. It takes the view name, the collection, and the variable name for each item as arguments.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_42

LANGUAGE: Blade
CODE:
```
@each('view.name', $jobs, 'job')
```

----------------------------------------

TITLE: Defining Optional Route Parameter with String Default in Laravel
DESCRIPTION: This snippet shows how to define an optional route parameter with a default string value. If the `{name?}` parameter is omitted from the URI, the callback function will use 'John' as its value.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_26

LANGUAGE: PHP
CODE:
```
Route::get('/user/{name?}', function (?string $name = 'John') {
    return $name;
});
```

----------------------------------------

TITLE: Monitoring Long-Running Database Queries in Laravel
DESCRIPTION: This snippet demonstrates how to use `DB::whenQueryingForLongerThan` to set a threshold (in milliseconds) for query execution time. If a query exceeds this time, a specified closure is invoked, allowing for proactive performance monitoring and notifications.
SOURCE: https://github.com/laravel/docs/blob/12.x/database.md#_snippet_18

LANGUAGE: php
CODE:
```
<?php

namespace App\Providers;

use Illuminate\Database\Connection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Events\QueryExecuted;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // ...
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        DB::whenQueryingForLongerThan(500, function (Connection $connection, QueryExecuted $event) {
            // Notify development team...
        });
    }
}
```

----------------------------------------

TITLE: Asserting OK Status in Laravel PHP
DESCRIPTION: Asserts that the HTTP response has a 200 (OK) HTTP status code. This is the most common assertion for successful responses.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_93

LANGUAGE: php
CODE:
```
$response->assertOk();
```

----------------------------------------

TITLE: Defining Eloquent Book Model with BelongsTo Relationship (PHP)
DESCRIPTION: Defines a simple Eloquent `Book` model with a `belongsTo` relationship method named `author` that returns the related `Author` model. This model is used to illustrate the N+1 query problem.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_113

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Book extends Model
{
    /**
     *
     * Get the author that wrote the book.
     *
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(Author::class);
    }
}
```

----------------------------------------

TITLE: Registering a Singleton Binding in a Service Provider (PHP)
DESCRIPTION: This PHP snippet demonstrates a basic service provider's `register` method, which is used to bind a singleton instance of `App\Services\Riak\Connection` into Laravel's service container. The `config('riak')` helper is used to retrieve configuration values for the connection.
SOURCE: https://github.com/laravel/docs/blob/12.x/providers.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Providers;

use App\Services\Riak\Connection;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class RiakServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(Connection::class, function (Application $app) {
            return new Connection(config('riak'));
        });
    }
}
```

----------------------------------------

TITLE: Laravel: Dispatching Queued Notifications After Commit
DESCRIPTION: This snippet demonstrates how to ensure a queued notification is only dispatched after all open database transactions have successfully committed. This is achieved by chaining the `afterCommit()` method directly when sending the notification instance, preventing race conditions where the notification might be processed before database changes are finalized.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_12

LANGUAGE: php
CODE:
```
use AppNotificationsInvoicePaid;

$user->notify((new InvoicePaid($invoice))->afterCommit());
```

----------------------------------------

TITLE: Laravel Str::of()->length() Method
DESCRIPTION: The `length` method returns the length of the given string.
SOURCE: https://github.com/laravel/docs/blob/12.x/strings.md#_snippet_133

LANGUAGE: php
CODE:
```
use Illuminate\Support\Str;

$length = Str::of('Laravel')->length();

// 7
```

----------------------------------------

TITLE: Rendering JSON with Illuminate\Support\Js::from in Blade (Blade)
DESCRIPTION: Demonstrates using Illuminate\Support\Js::from to safely render an array as JSON within a Blade template. This method automatically ensures proper escaping for inclusion within HTML quotes and returns a JSON.parse statement.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_8

LANGUAGE: blade
CODE:
```
<script>
    var app = {{ Illuminate\Support\Js::from($array) }};
</script>
```

----------------------------------------

TITLE: Retrieving and Storing with Cache::remember (PHP)
DESCRIPTION: This method retrieves an item from the cache. If the item does not exist, it executes the provided closure, stores its result in the cache for a specified duration ($seconds), and then returns the value. It's useful for caching database queries.
SOURCE: https://github.com/laravel/docs/blob/12.x/cache.md#_snippet_11

LANGUAGE: PHP
CODE:
```
$value = Cache::remember('users', $seconds, function () {
    return DB::table('users')->get();
});
```

----------------------------------------

TITLE: Storing Items with Expiration using Cache::put (PHP)
DESCRIPTION: The `put` method stores an item in the cache for a specified number of seconds. This allows for temporary caching of data with a defined lifespan.
SOURCE: https://github.com/laravel/docs/blob/12.x/cache.md#_snippet_15

LANGUAGE: PHP
CODE:
```
Cache::put('key', 'value', $seconds = 10);
```

----------------------------------------

TITLE: Displaying Password Reset Request Form - Laravel PHP
DESCRIPTION: This route defines the GET endpoint for displaying the 'forgot password' form. It returns the `auth.forgot-password` view, ensuring the user is a guest and naming the route for easy reference. The view should contain a form with an `email` field.
SOURCE: https://github.com/laravel/docs/blob/12.x/passwords.md#_snippet_0

LANGUAGE: PHP
CODE:
```
Route::get('/forgot-password', function () {
    return view('auth.forgot-password');
})->middleware('guest')->name('password.request');
```

----------------------------------------

TITLE: Storing CSRF Token in Meta Tag (Blade)
DESCRIPTION: This Blade snippet shows how to embed the CSRF token within an HTML meta tag. This approach makes the token easily accessible via JavaScript, allowing client-side libraries to retrieve it and include it in AJAX request headers for convenient CSRF protection in single-page applications.
SOURCE: https://github.com/laravel/docs/blob/12.x/csrf.md#_snippet_4

LANGUAGE: blade
CODE:
```
<meta name="csrf-token" content="{{ csrf_token() }}">
```

----------------------------------------

TITLE: Generating a New Migration File (Artisan)
DESCRIPTION: This Artisan command generates a new database migration file. Laravel attempts to infer the table name (e.g., 'flights') from the migration name ('create_flights_table') and pre-fill the generated file with the appropriate schema creation stub.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_0

LANGUAGE: shell
CODE:
```
php artisan make:migration create_flights_table
```

----------------------------------------

TITLE: Conditionally Checking HTML Checkboxes with Blade
DESCRIPTION: The `@checked` directive simplifies setting the 'checked' attribute on HTML checkbox inputs. It echoes 'checked' if the provided condition evaluates to `true`, based on old input or model data, ensuring correct form state.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_32

LANGUAGE: Blade
CODE:
```
<input
    type="checkbox"
    name="active"
    value="active"
    @checked(old('active', $user->active))
/>
```

----------------------------------------

TITLE: Inject Current Authenticated User with CurrentUser Attribute
DESCRIPTION: Demonstrates how to use the `CurrentUser` attribute to inject the currently authenticated user directly into a route closure or class constructor. This attribute requires the `auth` middleware to be applied to the route or class.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_16

LANGUAGE: php
CODE:
```
use App\Models\User;
use Illuminate\Container\Attributes\CurrentUser;

Route::get('/user', function (#[CurrentUser] User $user) {
    return $user;
})->middleware('auth');
```

----------------------------------------

TITLE: Fluent JSON Assertions with `AssertableJson`
DESCRIPTION: This example showcases Laravel's fluent JSON testing capabilities using `AssertableJson`. It allows chaining assertions like `where`, `whereNot`, `missing`, and `etc` to comprehensively validate JSON responses in a readable manner.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_29

LANGUAGE: Pest
CODE:
```
use IlluminateTestingFluentAssertableJson;

test('fluent json', function () {
    $response = $this->getJson('/users/1');

    $response
        ->assertJson(fn (AssertableJson $json) =>
            $json->where('id', 1)
                ->where('name', 'Victoria Faith')
                ->where('email', fn (string $email) => str($email)->is('<EMAIL>'))
                ->whereNot('status', 'pending')
                ->missing('password')
                ->etc()
        );
});
```

LANGUAGE: PHPUnit
CODE:
```
use IlluminateTestingFluentAssertableJson;

/**
 * A basic functional test example.
 */
public function test_fluent_json(): void
{
    $response = $this->getJson('/users/1');

    $response
        ->assertJson(fn (AssertableJson $json) =>
            $json->where('id', 1)
                ->where('name', 'Victoria Faith')
                ->where('email', fn (string $email) => str($email)->is('<EMAIL>'))
                ->whereNot('status', 'pending')
                ->missing('password')
                ->etc()
        );
}
```

----------------------------------------

TITLE: Configuring Queue Connection for After Commit Dispatch in Laravel
DESCRIPTION: This configuration snippet sets the `after_commit` option to `true` for a Redis queue connection. When enabled, Laravel will defer job dispatching until all open database transactions have been successfully committed, preventing issues where jobs might process data not yet persisted.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_43

LANGUAGE: PHP
CODE:
```
'redis' => [
    'driver' => 'redis',
    // ...
    'after_commit' => true,
],
```

----------------------------------------

TITLE: Implementing Queued Event Listener in Laravel PHP
DESCRIPTION: This code demonstrates how to make an event listener queued by implementing the `Illuminate\Contracts\Queue\ShouldQueue` interface. When this interface is added, the listener will automatically be processed by Laravel's queue system when its associated event is dispatched, offloading slow tasks like sending emails or making HTTP requests.
SOURCE: https://github.com/laravel/docs/blob/12.x/events.md#_snippet_16

LANGUAGE: php
CODE:
```
<?php

namespace AppListeners;

use AppEventsOrderShipped;
use IlluminateContractsQueueShouldQueue;

class SendShipmentNotification implements ShouldQueue
{
    // ...
}
```

----------------------------------------

TITLE: Retrieve Value from Nested Array with Arr::get() in PHP
DESCRIPTION: The Arr::get method retrieves a value from a deeply nested array using "dot" notation. It also supports providing a default value to be returned if the specified key is not found, preventing errors when accessing potentially missing data.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_20

LANGUAGE: php
CODE:
```
use Illuminate\Support\Arr;

$array = ['products' => ['desk' => ['price' => 100]]];

$price = Arr::get($array, 'products.desk.price');

// 100
```

LANGUAGE: php
CODE:
```
use Illuminate\Support\Arr;

$discount = Arr::get($array, 'products.desk.discount', 0);

// 0
```

----------------------------------------

TITLE: Adding Soft Delete Timestamp Column - Laravel Schema Builder - PHP
DESCRIPTION: The `softDeletes` method adds a nullable `deleted_at` `TIMESTAMP` column with optional fractional seconds precision. This column is used by Eloquent's 'soft delete' feature to mark records as deleted, allowing for their retrieval later.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_68

LANGUAGE: PHP
CODE:
```
$table->softDeletes('deleted_at', precision: 0);
```

----------------------------------------

TITLE: Define a Laravel Artisan Class-based Command
DESCRIPTION: This example demonstrates the structure of a class-based Laravel Artisan command. It defines the command's `signature` and `description` properties, and implements the `handle` method where the command's logic resides. Dependencies can be type-hinted in the `handle` method for automatic injection by the service container.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_9

LANGUAGE: php
CODE:
```
<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Support\DripEmailer;
use Illuminate\Console\Command;

class SendEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:send {user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a marketing email to a user';

    /**
     * Execute the console command.
     */
    public function handle(DripEmailer $drip): void
    {
        $drip->send(User::find($this->argument('user')));
    }
}
```

----------------------------------------

TITLE: Generate URL Friendly Slug in PHP with Str::slug
DESCRIPTION: The `Str::slug` method generates a URL-friendly 'slug' from the given string. It converts spaces to hyphens, removes special characters, and makes the string lowercase, suitable for URLs.
SOURCE: https://github.com/laravel/docs/blob/12.x/strings.md#_snippet_63

LANGUAGE: php
CODE:
```
use Illuminate\Support\Str;

$slug = Str::slug('Laravel 5 Framework', '-');

// laravel-5-framework
```

----------------------------------------

TITLE: Testing Laravel Facades with PHPUnit
DESCRIPTION: Demonstrates how to test a Laravel facade using PHPUnit. It sets up a mock for the `Cache` facade to expect a call to `get` with 'key' and return 'value', then performs an HTTP GET request to `/cache` and asserts the response content.
SOURCE: https://github.com/laravel/docs/blob/12.x/facades.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Cache;

/**
 * A basic functional test example.
 */
public function test_basic_example(): void
{
    Cache::shouldReceive('get')
        ->with('key')
        ->andReturn('value');

    $response = $this->get('/cache');

    $response->assertSee('value');
}
```

----------------------------------------

TITLE: Generate a Laravel Form Request Class
DESCRIPTION: For more complex validation and authorization scenarios, Laravel allows creating custom 'form request' classes. This Artisan CLI command generates a new form request class, placing it in the `app/Http/Requests` directory, which will be created if it doesn't exist.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_14

LANGUAGE: shell
CODE:
```
php artisan make:request StorePostRequest
```

----------------------------------------

TITLE: Implementing Dusk Attributes for Robust Selectors
DESCRIPTION: This example demonstrates the recommended approach for element selection in Dusk tests by using the `dusk` HTML attribute. This method provides more stable and readable selectors, making tests less prone to breaking from UI changes.
SOURCE: https://github.com/laravel/docs/blob/12.x/dusk.md#_snippet_36

LANGUAGE: html
CODE:
```
<button dusk="login-button">Login</button>
```

LANGUAGE: php
CODE:
```
$browser->click('@login-button');
```

----------------------------------------

TITLE: Performing Eloquent Operations Quietly (PHP)
DESCRIPTION: This PHP snippet showcases various 'quiet' methods available on Eloquent models, allowing operations like deletion, force deletion, and restoration to occur without dispatching their respective model events. These methods (`deleteQuietly()`, `forceDeleteQuietly()`, `restoreQuietly()`) are useful when you need to modify model states programmatically without triggering side effects from event listeners.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_114

LANGUAGE: php
CODE:
```
$user->deleteQuietly();
$user->forceDeleteQuietly();
$user->restoreQuietly();
```

----------------------------------------

TITLE: Faking Global Response Sequence in Laravel PHP
DESCRIPTION: Demonstrates using `Http::fakeSequence()` to define a global sequence of fake responses that are consumed by any outgoing HTTP request, regardless of the URL. Includes setting a default response for when the sequence is empty.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_48

LANGUAGE: php
CODE:
```
Http::fakeSequence()
    ->push('Hello World', 200)
    ->whenEmpty(Http::response());
```

----------------------------------------

TITLE: Redirecting to Previous Location with Input in Laravel PHP
DESCRIPTION: This example shows how to redirect a user back to their previous location using the global `back` helper function, typically after a form submission. The `withInput()` method flashes the current request's input data to the session, making it available on the next request. This feature requires the `web` middleware group or session middleware.
SOURCE: https://github.com/laravel/docs/blob/12.x/redirects.md#_snippet_1

LANGUAGE: php
CODE:
```
Route::post('/user/profile', function () {
    // Validate the request...

    return back()->withInput();
});
```

----------------------------------------

TITLE: Laravel Unique Validation: Ignoring Specific Records During Updates
DESCRIPTION: Illustrates how to configure the `unique` validation rule to ignore a specific record, which is crucial for update operations where the current record's value should not trigger a uniqueness error. Examples include ignoring by ID, by model instance, and specifying a custom primary key column or a different column for uniqueness check.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_164

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

Validator::make($data, [
    'email' => [
        'required',
        Rule::unique('users')->ignore($user->id),
    ],
]);
```

LANGUAGE: PHP
CODE:
```
Rule::unique('users')->ignore($user)
```

LANGUAGE: PHP
CODE:
```
Rule::unique('users')->ignore($user->id, 'user_id')
```

LANGUAGE: PHP
CODE:
```
Rule::unique('users', 'email_address')->ignore($user->id)
```

----------------------------------------

TITLE: Delayed Job Dispatching in Laravel
DESCRIPTION: This snippet shows how to dispatch a job with a delay using the `delay` method. The `ProcessPodcast` job will not be available for processing by a queue worker until 10 minutes after it has been dispatched. This is useful for scheduling tasks to run in the future.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_38

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Jobs\ProcessPodcast;
use App\Models\Podcast;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class PodcastController extends Controller
{
    /**
     * Store a new podcast.
     */
    public function store(Request $request): RedirectResponse
    {
        $podcast = Podcast::create(/* ... */);

        // ...

        ProcessPodcast::dispatch($podcast)
            ->delay(now()->addMinutes(10));

        return redirect('/podcasts');
    }
}
```

----------------------------------------

TITLE: Return Basic String or Array as HTTP Response in Laravel
DESCRIPTION: Demonstrates how Laravel automatically converts strings into basic HTTP responses and arrays into JSON responses when returned from routes or controllers. This provides a simple way to send data back to the client without explicit response object creation.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_0

LANGUAGE: php
CODE:
```
Route::get('/', function () {
    return 'Hello World';
});
```

LANGUAGE: php
CODE:
```
Route::get('/', function () {
    return [1, 2, 3];
});
```

----------------------------------------

TITLE: Accessing CSRF Token in Laravel (PHP)
DESCRIPTION: This PHP snippet illustrates two ways to retrieve the current CSRF token within a Laravel application. The token can be accessed directly from the request's session or by using the convenient csrf_token() helper function, both of which provide the same session-bound token for validation.
SOURCE: https://github.com/laravel/docs/blob/12.x/csrf.md#_snippet_1

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::get('/token', function (Request $request) {
    $token = $request->session()->token();

    $token = csrf_token();

    // ...
});
```

----------------------------------------

TITLE: Including CSRF Token in HTML Forms (Blade)
DESCRIPTION: This Blade snippet shows how to properly include the CSRF token in an HTML form for POST, PUT, PATCH, or DELETE requests. The @csrf Blade directive is a convenient way to generate a hidden input field named _token containing the session's CSRF token, which is essential for Laravel's CSRF protection middleware to validate the request.
SOURCE: https://github.com/laravel/docs/blob/12.x/csrf.md#_snippet_2

LANGUAGE: blade
CODE:
```
<form method="POST" action="/profile">
    @csrf

    <!-- Equivalent to... -->
    <input type="hidden" name="_token" value="{{ csrf_token() }}" />
</form>
```

----------------------------------------

TITLE: Check for Existence of Error Messages for a Field
DESCRIPTION: Explains how to use the `has()` method on the `MessageBag` instance to determine if any error messages exist for a specific field. This is useful for conditional rendering of error messages in views.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_51

LANGUAGE: php
CODE:
```
if ($errors->has('email')) {
    // ...
}
```

----------------------------------------

TITLE: Check User Authentication Status with Auth Facade in Laravel
DESCRIPTION: Illustrates how to determine if the current HTTP request is made by an authenticated user using the `Auth` facade's `check()` method. This method returns `true` if the user is logged in, allowing for conditional logic based on authentication status.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_2

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Auth;

if (Auth::check()) {
    // The user is logged in...
}
```

----------------------------------------

TITLE: Generating URLs for Named Routes with Parameters in Laravel
DESCRIPTION: This snippet demonstrates how to generate URLs for named routes that include parameters. By passing an associative array to the `route` function, the parameter values are automatically inserted into the correct positions in the generated URL.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_42

LANGUAGE: PHP
CODE:
```
Route::get('/user/{id}/profile', function (string $id) {
    // ...
})->name('profile');

$url = route('profile', ['id' => 1]);
```

----------------------------------------

TITLE: Performing a Basic GET Request with Pest
DESCRIPTION: This snippet demonstrates how to make a basic GET request to the application's root URL ('/') using the Pest testing framework and assert that the response has an HTTP status code of 200 (OK).
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

test('the application returns a successful response', function () {
    $response = $this->get('/');

    $response->assertStatus(200);
});
```

----------------------------------------

TITLE: Configuring Laravel Encryption Keys in INI
DESCRIPTION: This snippet demonstrates how to configure the current and previous encryption keys in the `.env` file using `APP_KEY` and `APP_PREVIOUS_KEYS`. `APP_KEY` is used for current encryption, while `APP_PREVIOUS_KEYS` allows graceful decryption of data encrypted with older keys, preventing user logout and data loss during key rotation.
SOURCE: https://github.com/laravel/docs/blob/12.x/encryption.md#_snippet_0

LANGUAGE: ini
CODE:
```
APP_KEY="base64:J63qRTDLub5NuZvP+kb8YIorGS6qFYHKVo6u7179stY="
APP_PREVIOUS_KEYS="base64:2nLsGFGzyoae2ax3EF2Lyq/hH6QghBGLIq5uL+Gp8/w="
```

----------------------------------------

TITLE: Implementing Loop Structures with Blade
DESCRIPTION: Blade offers directives for common PHP loop structures: `@for`, `@foreach`, `@forelse`, and `@while`. These directives function identically to their PHP counterparts, simplifying iteration over data in templates.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_25

LANGUAGE: Blade
CODE:
```
@for ($i = 0; $i < 10; $i++)
    The current value is {{ $i }}
@endfor

@foreach ($users as $user)
    <p>This is user {{ $user->id }}</p>
@endforeach

@forelse ($users as $user)
    <li>{{ $user->name }}</li>
@empty
    <p>No users</p>
@endforelse

@while (true)
    <p>I'm looping forever.</p>
@endwhile
```

----------------------------------------

TITLE: Saving Related Model using Eloquent save in PHP
DESCRIPTION: Demonstrates how to associate and persist a new related model (Comment) with a parent model (Post) using the relationship's `save` method. This automatically sets the foreign key on the related model.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_139

LANGUAGE: php
CODE:
```
use App\Models\Comment;
use App\Models\Post;

$comment = new Comment(['message' => 'A new comment.']);

$post = Post::find(1);

$post->comments()->save($comment);
```

----------------------------------------

TITLE: Update Existing Records with Laravel Query Builder
DESCRIPTION: The `update` method modifies existing database records. It takes an array of column-value pairs to update and can be constrained using `where` clauses. The method returns the number of affected rows.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_66

LANGUAGE: PHP
CODE:
```
$affected = DB::table('users')
    ->where('id', 1)
    ->update(['votes' => 1]);
```

----------------------------------------

TITLE: Repopulating Form Fields with Old Input (Blade)
DESCRIPTION: This Blade template snippet shows how to use the global `old` helper function to repopulate an HTML input field with previously flashed input. If no old input exists for the specified field, `null` will be returned, resulting in an empty value.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_62

LANGUAGE: blade
CODE:
```
<input type="text" name="username" value="{{ old('username') }}">
```

----------------------------------------

TITLE: Generating a New Laravel Job Class
DESCRIPTION: This shell command generates a new queueable job class named `ProcessPodcast` in the `app/Jobs` directory. The generated class will implement the `Illuminate\Contracts\Queue\ShouldQueue` interface, making it ready to be pushed onto a queue for asynchronous processing.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_5

LANGUAGE: Shell
CODE:
```
php artisan make:job ProcessPodcast
```

----------------------------------------

TITLE: Storing File on Default Disk in Laravel
DESCRIPTION: The `Storage` facade allows interaction with configured disks. Calling `put` directly on the facade stores content on the default disk. This example stores an avatar for user ID 1.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_17

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Storage;

Storage::put('avatars/1', $content);
```

----------------------------------------

TITLE: Creating Model with Mass Assignment (Post-Fillable)
DESCRIPTION: Shows the `create` method in action after the `$fillable` property has been properly configured on the model. This method inserts a new record into the database using the provided attributes and returns the newly created model instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_52

LANGUAGE: PHP
CODE:
```
$flight = Flight::create(['name' => 'London to Paris']);
```

----------------------------------------

TITLE: Defining Many-to-Many Relationship in Laravel Eloquent (PHP)
DESCRIPTION: Defines a many-to-many relationship from the `User` model to the `Role` model using the `belongsToMany` method. This method is provided by the Eloquent Model base class and establishes the link between the two models via an intermediate table.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_37

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class User extends Model
{
    /**
     * The roles that belong to the user.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class);
    }
}
```

----------------------------------------

TITLE: Querying Relationship with Constraints PHP
DESCRIPTION: Shows how to apply additional query constraints to an Eloquent relationship before retrieving the results. By calling the relationship method (`posts()`) without immediately calling `get()`, you obtain a query builder instance allowing method chaining like `where('active', 1)`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_78

LANGUAGE: php
CODE:
```
use App\Models\User;

$user = User::find(1);

$user->posts()->where('active', 1)->get();
```

----------------------------------------

TITLE: Directory Structure for Short Key Translations - Text
DESCRIPTION: This snippet illustrates the directory structure for storing translation strings using short keys in Laravel. Each language has its own subdirectory within the `lang` directory, containing PHP files (e.g., `messages.php`) that return an array of keyed strings.
SOURCE: https://github.com/laravel/docs/blob/12.x/localization.md#_snippet_0

LANGUAGE: Text
CODE:
```
/lang
    /en
        messages.php
    /es
        messages.php
```

----------------------------------------

TITLE: Defining a User Factory in PHP
DESCRIPTION: This snippet demonstrates the basic structure of a Laravel model factory, extending `Illuminate\Database\Eloquent\Factories\Factory`. It defines the `definition` method to return an array of default attribute values for the `User` model, utilizing the `fake()` helper for data generation, and includes an example of a `unverified` state method.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-factories.md#_snippet_0

LANGUAGE: php
CODE:
```
namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
```

----------------------------------------

TITLE: Conditionally Including Relationship with `whenLoaded` in Laravel Resource (PHP)
DESCRIPTION: This snippet demonstrates using the `whenLoaded` method to conditionally include a relationship (`posts`) in a resource response. The relationship is included only if it has already been loaded on the underlying model, helping to prevent N+1 query issues.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-resources.md#_snippet_32

LANGUAGE: PHP
CODE:
```
use AppHttpResourcesPostResource;

/**
 * Transform the resource into an array.
 *
 * @return array<string, mixed>
 */
public function toArray(Request $request): array
{
    return [
        'id' => $this->id,
        'name' => $this->name,
        'email' => $this->email,
        'posts' => PostResource::collection($this->whenLoaded('posts')),
        'created_at' => $this->created_at,
        'updated_at' => $this->updated_at,
    ];
}
```

----------------------------------------

TITLE: Returning a JSON Response in Laravel
DESCRIPTION: Creates a JSON response from a given array. The `Content-Type` header is automatically set to `application/json`, and the array is converted using `json_encode`.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_24

LANGUAGE: php
CODE:
```
return response()->json([
    'name' => 'Abigail',
    'state' => 'CA'
]);
```

----------------------------------------

TITLE: Using RefreshDatabase Trait with PHPUnit
DESCRIPTION: Illustrates how to apply the `RefreshDatabase` trait to a PHPUnit test class to ensure the database is reset after each test. This trait performs tests within a transaction, which is faster than full database migrations or truncations.
SOURCE: https://github.com/laravel/docs/blob/12.x/database-testing.md#_snippet_1

LANGUAGE: PHP
CODE:
```
<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    use RefreshDatabase;

    /**
     * A basic functional test example.
     */
    public function test_basic_example(): void
    {
        $response = $this->get('/');

        // ...
    }
}
```

----------------------------------------

TITLE: Conditionally Eager Loading Relationships for Models in Laravel Eloquent Collection (PHP)
DESCRIPTION: The `loadMissing` method eager loads specified relationships for models in the collection only if those relationships have not already been loaded. This prevents unnecessary database queries for already loaded data.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-collections.md#_snippet_11

LANGUAGE: PHP
CODE:
```
$users->loadMissing(['comments', 'posts']);

$users->loadMissing('comments.author');

$users->loadMissing(['comments', 'posts' => fn ($query) => $query->where('active', 1)]);
```

----------------------------------------

TITLE: Stream Database Results Lazily with lazy
DESCRIPTION: Introduces the `lazy` method, which, similar to `chunk`, executes queries in chunks but returns a `LazyCollection` for interacting with results as a single stream. The example iterates over users.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_8

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

DB::table('users')->orderBy('id')->lazy()->each(function (object $user) {
    // ...
});
```

----------------------------------------

TITLE: Defining Web Route with Controller Action in Laravel PHP
DESCRIPTION: This snippet shows how to define a web route in `routes/web.php` that maps a URI to a controller action. Routes in `web.php` are assigned the `web` middleware group, providing features like session state and CSRF protection. The route '/user' will invoke the `index` method of `UserController`.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_1

LANGUAGE: php
CODE:
```
use App\Http\Controllers\UserController;

Route::get('/user', [UserController::class, 'index']);
```

----------------------------------------

TITLE: Including CSRF Token in Laravel Blade Form
DESCRIPTION: This Blade snippet demonstrates how to include a CSRF token in an HTML form for `POST`, `PUT`, `PATCH`, or `DELETE` requests. The `@csrf` directive generates a hidden input field containing the token, which is essential for protecting against cross-site request forgery attacks in Laravel web routes.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_8

LANGUAGE: blade
CODE:
```
<form method="POST" action="/profile">
    @csrf
    ...
</form>
```

----------------------------------------

TITLE: Perform Various Join Operations with Laravel Query Builder
DESCRIPTION: Explains how to perform different types of SQL joins using Laravel's query builder, including `inner join` with the `join()` method, `left join` with `leftJoin()`, and `right join` with `rightJoin()`. It demonstrates joining multiple tables and specifying join conditions.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_16

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

$users = DB::table('users')
    ->join('contacts', 'users.id', '=', 'contacts.user_id')
    ->join('orders', 'users.id', '=', 'orders.user_id')
    ->select('users.*', 'contacts.phone', 'orders.price')
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->leftJoin('posts', 'users.id', '=', 'posts.user_id')
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->rightJoin('posts', 'users.id', '=', 'posts.user_id')
    ->get();
```

----------------------------------------

TITLE: Perform Simple Pagination with Eloquent Models in Laravel
DESCRIPTION: This PHP example demonstrates using the `simplePaginate` method with Eloquent models. Similar to the query builder, it provides an efficient way to paginate when only 'Next' and 'Previous' links are required, avoiding the overhead of counting all records.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_5

LANGUAGE: php
CODE:
```
$users = User::where('votes', '>', 100)->simplePaginate(15);
```

----------------------------------------

TITLE: Retrieve Validated Data from Request or Validator
DESCRIPTION: Demonstrates how to use the `validated()` method on a form request or validator instance to get an array of data that has passed validation. This method provides a direct way to access only the data that was successfully validated.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_42

LANGUAGE: php
CODE:
```
$validated = $request->validated();

$validated = $validator->validated();
```

----------------------------------------

TITLE: Registering Routes for Specific HTTP Verbs in Laravel PHP
DESCRIPTION: This snippet illustrates how to register routes that respond to specific HTTP verbs using Laravel's `Route` facade. Each method (`get`, `post`, `put`, `patch`, `delete`, `options`) corresponds to an HTTP verb, allowing precise control over how different types of requests are handled for a given URI.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_5

LANGUAGE: php
CODE:
```
Route::get($uri, $callback);
Route::post($uri, $callback);
Route::put($uri, $callback);
Route::patch($uri, $callback);
Route::delete($uri, $callback);
Route::options($uri, $callback);
```

----------------------------------------

TITLE: Rendering a View via Global Helper
DESCRIPTION: This PHP snippet shows how to render a view using the global `view` helper function within a route closure. It passes an array of data to the view, making variables accessible for display.
SOURCE: https://github.com/laravel/docs/blob/12.x/views.md#_snippet_3

LANGUAGE: php
CODE:
```
Route::get('/', function () {
    return view('greeting', ['name' => 'James']);
});
```

----------------------------------------

TITLE: Create Value Object from Multiple Eloquent Attributes using Accessor
DESCRIPTION: This PHP code demonstrates an advanced accessor use case where multiple model attributes are combined to form a single value object. The `address()` accessor's `get` closure receives an array of all model attributes, allowing it to construct an `Address` object using `address_line_one` and `address_line_two`. This pattern is useful for encapsulating complex data structures within a single attribute.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-mutators.md#_snippet_2

LANGUAGE: php
CODE:
```
use App\Support\Address;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * Interact with the user's address.
 */
protected function address(): Attribute
{
    return Attribute::make(
        get: fn (mixed $value, array $attributes) => new Address(
            $attributes['address_line_one'],
            $attributes['address_line_two'],
        ),
    );
}
```

----------------------------------------

TITLE: Retrieving Partial Input Data (`only` and `except` methods) in Laravel
DESCRIPTION: Demonstrates using the `only` and `except` methods to retrieve a subset of the request input data. Both methods accept either an array or a dynamic list of arguments. The `only` method will not return keys not present in the request.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_43

LANGUAGE: PHP
CODE:
```
$input = $request->only(['username', 'password']);
```

LANGUAGE: PHP
CODE:
```
$input = $request->only('username', 'password');
```

LANGUAGE: PHP
CODE:
```
$input = $request->except(['credit_card']);
```

LANGUAGE: PHP
CODE:
```
$input = $request->except('credit_card');
```

----------------------------------------

TITLE: Remove Key-Value Pair from Nested Array with Arr::forget() in PHP
DESCRIPTION: The Arr::forget method removes a specified key-value pair from a deeply nested array using "dot" notation. This allows for precise removal of elements within complex array structures.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_18

LANGUAGE: php
CODE:
```
use Illuminate\Support\Arr;

$array = ['products' => ['desk' => ['price' => 100]]];

Arr::forget($array, 'products.desk');

// ['products' => []]
```

----------------------------------------

TITLE: Making a Basic HTTP Request with PHPUnit
DESCRIPTION: This example demonstrates how to perform a simple GET request to the application's root path using PHPUnit and then verify that the response status is 200 (OK). Test request methods return an `Illuminate\Testing\TestResponse` instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_3

LANGUAGE: PHP
CODE:
```
<?php

namespace Tests\Feature;

use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic test example.
     */
    public function test_a_basic_request(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
    }
}
```

----------------------------------------

TITLE: Storing Uploaded Files with Custom Filename using putFileAs
DESCRIPTION: This snippet shows how to store an uploaded file with a custom filename using the `Storage` facade's `putFileAs` method. Similar to `storeAs`, it allows specifying the directory, the uploaded file instance, and the desired filename.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_45

LANGUAGE: php
CODE:
```
$path = Storage::putFileAs(
    'avatars', $request->file('avatar'), $request->user()->id
);
```

----------------------------------------

TITLE: Laravel Available Validation Rules Reference
DESCRIPTION: A comprehensive reference of all available validation rules in Laravel, categorized by data type (Booleans, Strings, Numbers, Arrays, Dates). This section outlines the various rules that can be applied to validate input data.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_58

LANGUAGE: APIDOC
CODE:
```
Booleans:
  Accepted
  Accepted If
  Boolean
  Declined
  Declined If

Strings:
  Active URL
  Alpha
  Alpha Dash
  Alpha Numeric
  Ascii
  Confirmed
  Current Password
  Different
  Doesnt Start With
  Doesnt End With
  Email
  Ends With
  Enum
  Hex Color
  In
  IP Address
  JSON
  Lowercase
  MAC Address
  Max
  Min
  Not In
  Regular Expression
  Not Regular Expression
  Same
  Size
  Starts With
  String
  Uppercase
  URL
  ULID
  UUID

Numbers:
  Between
  Decimal
  Different
  Digits
  Digits Between
  Greater Than
  Greater Than Or Equal
  Integer
  Less Than
  Less Than Or Equal
  Max
  Max Digits
  Min
  Min Digits
  Multiple Of
  Numeric
  Same
  Size

Arrays:
  Array
  Between
  Contains
  Distinct
  In Array
  In Array Keys
  List
  Max
  Min
  Size

Dates:
  After
  After Or Equal
  Before
  Before Or Equal
  Date
  Date Equals
  Date Format
  Different
  Timezone
```

----------------------------------------

TITLE: Defining Laravel Routes for Post Creation
DESCRIPTION: This PHP snippet defines two routes in `routes/web.php`: a GET route to display a form for creating a new blog post and a POST route to handle the submission and storage of the new post in the database.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_0

LANGUAGE: php
CODE:
```
use App\Http\Controllers\PostController;

Route::get('/post/create', [PostController::class, 'create']);
Route::post('/post', [PostController::class, 'store']);
```

----------------------------------------

TITLE: Laravel Validation Rule: required
DESCRIPTION: The `required` rule ensures that the field under validation is present in the input data and is not empty. 'Empty' is defined as null, an empty string, an empty array/Countable object, or an uploaded file with no path.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_150

LANGUAGE: APIDOC
CODE:
```
required

The field under validation must be present in the input data and not empty. A field is "empty" if it meets one of the following criteria:
- The value is `null`.
- The value is an empty string.
- The value is an empty array or empty `Countable` object.
- The value is an uploaded file with no path.
```

----------------------------------------

TITLE: Deleting Models by Primary Key with Destroy Method
DESCRIPTION: Demonstrates the `destroy` method for deleting models directly by their primary key(s) without explicit retrieval. It accepts a single ID, multiple IDs, an array of IDs, or a collection of IDs. This method loads each model individually and dispatches events.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_59

LANGUAGE: PHP
CODE:
```
Flight::destroy(1);

Flight::destroy(1, 2, 3);

Flight::destroy([1, 2, 3]);

Flight::destroy(collect([1, 2, 3]));
```

----------------------------------------

TITLE: Checking Input Presence and Non-Emptiness with `filled` Method (PHP)
DESCRIPTION: The `filled` method checks if a value is present on the request and is not an empty string. It returns `true` only if the input exists and contains a non-empty value.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_49

LANGUAGE: php
CODE:
```
if ($request->filled('name')) {
    // ...
}
```

----------------------------------------

TITLE: Executing a Basic SELECT Query in Laravel
DESCRIPTION: This PHP snippet shows how to run a basic SELECT query using the `DB` facade within a Laravel controller. It retrieves users where `active` is `1`, demonstrating parameter binding for SQL injection protection. The `select` method returns an array of `stdClass` objects representing database records.
SOURCE: https://github.com/laravel/docs/blob/12.x/database.md#_snippet_4

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * Show a list of all of the application's users.
     */
    public function index(): View
    {
        $users = DB::select('select * from users where active = ?', [1]);

        return view('user.index', ['users' => $users]);
    }
}
```

----------------------------------------

TITLE: Defining a Migration to Create a Table
DESCRIPTION: This PHP code defines a migration class with 'up' and 'down' methods. The 'up' method uses the Schema facade to create a 'flights' table with 'id', 'name', 'airline', and timestamp columns. The 'down' method reverses this operation by dropping the 'flights' table.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_2

LANGUAGE: php
CODE:
```
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flights', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('airline');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('flights');
    }
};
```

----------------------------------------

TITLE: Querying Eloquent Relationship with Constraints (PHP)
DESCRIPTION: Demonstrates how to chain additional query constraints onto an Eloquent relationship method to filter related models. This example filters a user's posts to only include active ones.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_0

LANGUAGE: php
CODE:
```
$user->posts()->where('active', 1)->get();
```

----------------------------------------

TITLE: Using Route and Cache Facades in Laravel
DESCRIPTION: Demonstrates how to use Laravel's `Route` and `Cache` facades. It defines a GET route `/cache` that retrieves a value from the cache using `Cache::get('key')`. This illustrates the "static" interface provided by facades to underlying service container classes.
SOURCE: https://github.com/laravel/docs/blob/12.x/facades.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;

Route::get('/cache', function () {
    return Cache::get('key');
});
```

----------------------------------------

TITLE: Defining a Reusable Layout Blade Component (Blade)
DESCRIPTION: This Blade snippet defines a `layout` component that serves as a reusable HTML structure for web pages. It includes a dynamic title that can be overridden and a `$slot` where page-specific content will be injected.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_129

LANGUAGE: Blade
CODE:
```
<!-- resources/views/components/layout.blade.php -->

<html>
    <head>
        <title>{{ $title ?? 'Todo Manager' }}</title>
    </head>
    <body>
        <h1>Todos</h1>
        <hr/>
        {{ $slot }}
    </body>
</html>
```

----------------------------------------

TITLE: Spoofing HTTP Methods in Forms
DESCRIPTION: This snippet illustrates how to use the `@method` Blade directive to spoof HTTP verbs like `PUT`, `PATCH`, or `DELETE` in HTML forms, which natively only support `GET` and `POST`. The directive generates a hidden `_method` input field, allowing Laravel to interpret the request as the specified HTTP verb.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_137

LANGUAGE: blade
CODE:
```
<form action="/foo/bar" method="POST">
    @method('PUT')

    ...
</form>
```

----------------------------------------

TITLE: Defining Basic GET Route with Closure in Laravel PHP
DESCRIPTION: This snippet demonstrates how to define a basic GET route in Laravel using a URI and a closure. It provides a simple and expressive way to define routes and their behavior without complex configuration. The route '/greeting' will return 'Hello World' when accessed.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_0

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Route;

Route::get('/greeting', function () {
    return 'Hello World';
});
```

----------------------------------------

TITLE: Accessing Request in Laravel Route Closure (PHP)
DESCRIPTION: Illustrates how to inject the `Illuminate\Http\Request` instance directly into a route closure. Laravel's service container automatically provides the request object, enabling direct interaction with the incoming HTTP request within the route definition.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_1

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::get('/', function (Request $request) {
    // ...
});
```

----------------------------------------

TITLE: Using Explicit Model Binding in Laravel PHP
DESCRIPTION: This example shows a route definition that utilizes an explicitly bound model. Because `user` is bound to `App\Models\User`, Laravel automatically injects the `User` instance corresponding to the ID in the URI.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_63

LANGUAGE: PHP
CODE:
```
use App\Models\User;

Route::get('/users/{user}', function (User $user) {
    // ...
});
```

----------------------------------------

TITLE: Perform Cursor Pagination with Eloquent Models in Laravel
DESCRIPTION: This PHP snippet illustrates the use of the `cursorPaginate` method with Eloquent models. Cursor pagination is an efficient method for large datasets, providing 'Next' and 'Previous' links based on a cursor, which is ideal for infinite scrolling interfaces.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_6

LANGUAGE: php
CODE:
```
$users = User::where('votes', '>', 100)->cursorPaginate(15);
```

----------------------------------------

TITLE: Laravel Validation Rule: required_if and Rule::requiredIf Method
DESCRIPTION: The `required_if` rule makes the field under validation present and not empty if the `_anotherfield_` matches any of the specified `_value_`s. For more complex conditions, the `Rule::requiredIf` method can be used, accepting a boolean or a closure that returns `true` or `false` to dynamically determine if the field is required.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_151

LANGUAGE: APIDOC
CODE:
```
required_if:_anotherfield_,_value_,...
```

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

Validator::make($request->all(), [
    'role_id' => Rule::requiredIf($request->user()->is_admin),
]);

Validator::make($request->all(), [
    'role_id' => Rule::requiredIf(fn () => $request->user()->is_admin),
]);
```

----------------------------------------

TITLE: SQL Comparison: Offset vs. Cursor Pagination Queries
DESCRIPTION: This snippet illustrates the fundamental difference between offset and cursor pagination by comparing their underlying SQL queries. Offset pagination uses `LIMIT` and `OFFSET` clauses, while cursor pagination uses `WHERE` clauses based on ordered column values, offering better performance for large, indexed datasets and improved consistency with frequent data writes.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_10

LANGUAGE: sql
CODE:
```
# Offset Pagination...
select * from users order by id asc limit 15 offset 15;

# Cursor Pagination...
select * from users where id > 15 order by id asc limit 15;
```

----------------------------------------

TITLE: Validate and Store Data Using a Laravel Form Request in a Controller
DESCRIPTION: Laravel automatically validates incoming form requests before the controller method is called, simplifying controller logic by removing explicit validation. This example shows how to type-hint a form request in a controller method to trigger validation and how to retrieve validated input data using `validated()`, `safe()->only()`, and `safe()->except()`.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_16

LANGUAGE: php
CODE:
```
/**
 * Store a new blog post.
 */
public function store(StorePostRequest $request): RedirectResponse
{
    // The incoming request is valid...

    // Retrieve the validated input data...
    $validated = $request->validated();

    // Retrieve a portion of the validated input data...
    $validated = $request->safe()->only(['name', 'email']);
    $validated = $request->safe()->except(['name', 'email']);

    // Store the blog post...

    return redirect('/posts');
}
```

----------------------------------------

TITLE: Returning a Single Resource from a Route in Laravel
DESCRIPTION: This example shows how to return a single `UserResource` directly from a Laravel route. It fetches a User model by ID and uses the `toUserResource()` helper method to automatically transform it into its corresponding resource representation.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-resources.md#_snippet_14

LANGUAGE: php
CODE:
```
use App\Models\User;

Route::get('/user/{id}', function (string $id) {
    return User::findOrFail($id)->toUserResource();
});
```

----------------------------------------

TITLE: Accessing Request in Laravel Controller (PHP)
DESCRIPTION: Demonstrates how to inject the `Illuminate\Http\Request` instance into a Laravel controller method using dependency injection. This allows access to incoming request data like input fields. The method stores a new user and redirects.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Store a new user.
     */
    public function store(Request $request): RedirectResponse
    {
        $name = $request->input('name');

        // Store the user...

        return redirect('/users');
    }
}
```

----------------------------------------

TITLE: Hashing Passwords in a Controller - PHP
DESCRIPTION: This PHP code snippet demonstrates how to hash a user's new password using the `Hash::make` method within a Laravel controller. It fills the user's password attribute with the securely hashed value and saves it to the database, ensuring that passwords are never stored in plain text. The `Request` object provides access to the `newPassword` input from the user.
SOURCE: https://github.com/laravel/docs/blob/12.x/hashing.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class PasswordController extends Controller
{
    /**
     * Update the password for the user.
     */
    public function update(Request $request): RedirectResponse
    {
        // Validate the new password length...

        $request->user()->fill([
            'password' => Hash::make($request->newPassword)
        ])->save();

        return redirect('/profile');
    }
}
```

----------------------------------------

TITLE: Debug Laravel Queries with dd and dump Methods
DESCRIPTION: Illustrates the use of `dd()` and `dump()` methods on a Laravel query builder to inspect the current query bindings and SQL. `dd()` will display the debug information and halt execution, while `dump()` will display it and allow the request to continue.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_79

LANGUAGE: php
CODE:
```
DB::table('users')->where('votes', '>', 100)->dd();

DB::table('users')->where('votes', '>', 100)->dump();
```

----------------------------------------

TITLE: Laravel Miscellaneous Global Helper Functions Reference
DESCRIPTION: A comprehensive list of global helper functions available in Laravel for various common tasks, including application context, authentication, caching, and more. These functions provide quick access to core framework functionalities.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_4

LANGUAGE: APIDOC
CODE:
```
abort()
abort_if()
abort_unless()
app()
auth()
back()
bcrypt()
blank()
broadcast()
cache()
class_uses_recursive()
collect()
config()
context()
cookie()
csrf_field()
csrf_token()
decrypt()
dd()
dispatch()
dispatch_sync()
dump()
encrypt()
env()
event()
fake()
filled()
info()
literal()
logger()
method_field()
now()
old()
once()
optional()
policy()
redirect()
report()
report_if()
report_unless()
request()
rescue()
resolve()
response()
retry()
session()
tap()
throw_if()
throw_unless()
today()
trait_uses_recursive()
transform()
validator()
value()
view()
with()
when()
```

----------------------------------------

TITLE: Clearing Cached Laravel Configuration (Shell)
DESCRIPTION: This Artisan command purges the previously cached configuration, allowing the application to load configuration directly from files again. It's useful during development or after configuration changes.
SOURCE: https://github.com/laravel/docs/blob/12.x/configuration.md#_snippet_19

LANGUAGE: shell
CODE:
```
php artisan config:clear
```

----------------------------------------

TITLE: Accessing Many-to-Many Relationship Data in Laravel Eloquent (PHP)
DESCRIPTION: Demonstrates how to access the related models (roles) for a specific user instance using the dynamic relationship property (`$user->roles`). This allows iterating through the collection of related models.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_38

LANGUAGE: php
CODE:
```
use App\Models\User;

$user = User::find(1);

foreach ($user->roles as $role) {
    // ...
}
```

----------------------------------------

TITLE: Adding Various Index Types in Laravel
DESCRIPTION: Demonstrates how to add different types of indexes (primary, unique, basic, full-text, spatial) to a table using Laravel's schema builder blueprint methods. Each method accepts an optional index name.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_105

LANGUAGE: php
CODE:
```
$table->primary('id');
```

LANGUAGE: php
CODE:
```
$table->primary(['id', 'parent_id']);
```

LANGUAGE: php
CODE:
```
$table->unique('email');
```

LANGUAGE: php
CODE:
```
$table->index('state');
```

LANGUAGE: php
CODE:
```
$table->fullText('body');
```

LANGUAGE: php
CODE:
```
$table->fullText('body')->language('english');
```

LANGUAGE: php
CODE:
```
$table->spatialIndex('location');
```

----------------------------------------

TITLE: Defining Blade Component Class Constructor
DESCRIPTION: Illustrates how to define a Blade component's data attributes in its PHP class constructor. Public properties are automatically available to the component's view. The `render` method returns the view.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_69

LANGUAGE: PHP
CODE:
```
<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;

class Alert extends Component
{
    /**
     * Create the component instance.
     */
    public function __construct(
        public string $type,
        public string $message,
    ) {}

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('components.alert');
    }
}
```

----------------------------------------

TITLE: Running Database Transactions with Laravel DB Facade
DESCRIPTION: This example shows how to execute a series of database operations within a transaction using the `DB::transaction` method. If any exception occurs within the closure, the transaction is automatically rolled back; otherwise, it's committed upon successful execution.
SOURCE: https://github.com/laravel/docs/blob/12.x/database.md#_snippet_19

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

DB::transaction(function () {
    DB::update('update users set votes = 1');

    DB::delete('delete from posts');
});
```

----------------------------------------

TITLE: Verifying Password Against Hash - PHP
DESCRIPTION: This snippet illustrates how to verify if a given plain-text password corresponds to a stored hash using the `Hash::check` method. This method returns `true` if the passwords match, indicating successful authentication, and `false` otherwise. It is a critical component for implementing secure user login processes.
SOURCE: https://github.com/laravel/docs/blob/12.x/hashing.md#_snippet_4

LANGUAGE: php
CODE:
```
if (Hash::check('plain-text', $hashedPassword)) {
    // The passwords match...
}
```

----------------------------------------

TITLE: Creating Public Storage Symlink in Laravel
DESCRIPTION: This Artisan command creates a symbolic link from the `public/storage` directory to `storage/app/public`, allowing user-generated files to be publicly accessible via the web server.
SOURCE: https://github.com/laravel/docs/blob/12.x/structure.md#_snippet_0

LANGUAGE: Bash
CODE:
```
php artisan storage:link
```

----------------------------------------

TITLE: Applying Nullable Modifier to Column in Laravel
DESCRIPTION: Demonstrates how to make a database column nullable using the `nullable()` method on a string column within a Laravel schema migration. This allows `NULL` values to be inserted into the 'email' column of the 'users' table.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_91

LANGUAGE: php
CODE:
```
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

Schema::table('users', function (Blueprint $table) {
    $table->string('email')->nullable();
});
```

----------------------------------------

TITLE: Handling Missing Eloquent Models in Routes - PHP
DESCRIPTION: This snippet shows how to use `findOrFail` within a Laravel route to automatically handle cases where an Eloquent model is not found. If `Flight::findOrFail($id)` does not find a flight, it throws a `ModelNotFoundException`, which Laravel's exception handler automatically converts into a 404 HTTP response, simplifying error handling for missing resources.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_38

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

Route::get('/api/flights/{id}', function (string $id) {
    return Flight::findOrFail($id);
});
```

----------------------------------------

TITLE: Returning Laravel Resources Directly from Routes (PHP)
DESCRIPTION: This example shows the basic method of returning a Laravel resource directly from a route or controller. It fetches a `User` model by ID and converts it into a resource instance using `toResource()`, which Laravel then automatically serializes into a JSON response.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-resources.md#_snippet_42

LANGUAGE: PHP
CODE:
```
use App\Models\User;

Route::get('/user/{id}', function (string $id) {
    return User::findOrFail($id)->toResource();
});
```

----------------------------------------

TITLE: Laravel Validation: `mimes` Rule for File Extensions
DESCRIPTION: The `mimes` rule validates that an uploaded file has a MIME type corresponding to one of the listed extensions. It determines the MIME type by reading the file's contents, not just relying on the extension.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_124

LANGUAGE: APIDOC
CODE:
```
mimes:_foo_,_bar_,...
```

LANGUAGE: php
CODE:
```
'photo' => 'mimes:jpg,bmp,png'
```

----------------------------------------

TITLE: Dispatching Job Batches with Callbacks (PHP)
DESCRIPTION: This snippet demonstrates how to dispatch a batch of jobs using the `Bus` facade, defining various callbacks (`before`, `progress`, `then`, `catch`, `finally`) to manage the batch's lifecycle and respond to its completion status. Each callback receives an `Illuminate\Bus\Batch` instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_70

LANGUAGE: php
CODE:
```
use App\Jobs\ImportCsv;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Throwable;

$batch = Bus::batch([
    new ImportCsv(1, 100),
    new ImportCsv(101, 200),
    new ImportCsv(201, 300),
    new ImportCsv(301, 400),
    new ImportCsv(401, 500),
])->before(function (Batch $batch) {
    // The batch has been created but no jobs have been added...
})->progress(function (Batch $batch) {
    // A single job has completed successfully...
})->then(function (Batch $batch) {
    // All jobs completed successfully...
})->catch(function (Batch $batch, Throwable $e) {
    // First batch job failure detected...
})->finally(function (Batch $batch) {
    // The batch has finished executing...
})->dispatch();

return $batch->id;
```

----------------------------------------

TITLE: Laravel URL Helper Methods
DESCRIPTION: Lists global helper functions provided by Laravel for generating URLs, assets, and routes.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_3

LANGUAGE: APIDOC
CODE:
```
action
asset
route
secure_asset
secure_url
to_route
uri
url
```

----------------------------------------

TITLE: Allowing Null Values with Laravel's nullable Validation Rule
DESCRIPTION: By default, Laravel includes `TrimStrings` and `ConvertEmptyStringsToNull` middleware. To prevent the validator from considering `null` values as invalid for optional fields, the `nullable` rule should be applied. This PHP example shows how to mark the `publish_at` field as optional and allow `null`.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_12

LANGUAGE: php
CODE:
```
$request->validate([
    'title' => 'required|unique:posts|max:255',
    'body' => 'required',
    'publish_at' => 'nullable|date',
]);
```

----------------------------------------

TITLE: Defining Foreign Key Constraints Terser Syntax in Laravel
DESCRIPTION: Shows a more concise way to define foreign key constraints using Laravel's `foreignId` and `constrained` methods. This approach leverages conventions to simplify the syntax for common foreign key setups.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_110

LANGUAGE: php
CODE:
```
Schema::table('posts', function (Blueprint $table) {
    $table->foreignId('user_id')->constrained();
});
```

----------------------------------------

TITLE: Defining HasMany Relationship Method PHP
DESCRIPTION: Provides an example of defining a standard `HasMany` relationship method within an Eloquent model class (`User`), returning the result of the `hasMany` method call to establish the relationship with the `Post` model.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_77

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Model
{
    /**
     *
     * Get all of the posts for the user.
     *
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }
}
```

----------------------------------------

TITLE: Remembering Enumerated Items in Laravel LazyCollection (PHP)
DESCRIPTION: The `remember` method creates a new `LazyCollection` that caches values as they are enumerated, preventing re-retrieval from the original source on subsequent enumerations. This optimizes performance by avoiding redundant database queries for already accessed items.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_199

LANGUAGE: php
CODE:
```
// No query has been executed yet...
$users = User::cursor()->remember();

// The query is executed...
// The first 5 users are hydrated from the database...
$users->take(5)->all();

// First 5 users come from the collection's cache...
// The rest are hydrated from the database...
$users->take(20)->all();
```

----------------------------------------

TITLE: Laravel Query Builder: Add LIKE/NOT LIKE Clauses
DESCRIPTION: These methods allow adding 'LIKE' or 'NOT LIKE' conditions to a database query for pattern matching. They support case-insensitive matching by default and can be made case-sensitive. `orWhereLike` and `orWhereNotLike` add these conditions with an 'OR' operator.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_37

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->whereLike('name', '%John%')
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->whereLike('name', '%John%', caseSensitive: true)
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->where('votes', '>', 100)
    ->orWhereLike('name', '%John%')
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->whereNotLike('name', '%John%')
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->where('votes', '>', 100)
    ->orWhereNotLike('name', '%John%')
    ->get();
```

----------------------------------------

TITLE: Form Method Spoofing with Hidden Input (HTML)
DESCRIPTION: This HTML snippet demonstrates how to spoof HTTP methods like PUT, PATCH, or DELETE in forms, which natively only support GET and POST. It uses a hidden `_method` input field to specify the desired HTTP verb, allowing forms to interact with RESTful routes.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_78

LANGUAGE: blade
CODE:
```
<form action="/example" method="POST">
    <input type="hidden" name="_method" value="PUT">
    <input type="hidden" name="_token" value="{{ csrf_token() }}">
</form>
```

----------------------------------------

TITLE: Listening for Broadcast Events with Laravel Echo (React)
DESCRIPTION: This React snippet demonstrates how to listen for broadcast events using Laravel Echo's `useEcho` hook. It subscribes to a private channel and logs the event data when an `OrderShipmentStatusUpdated` event is received.
SOURCE: https://github.com/laravel/docs/blob/12.x/broadcasting.md#_snippet_30

LANGUAGE: javascript
CODE:
```
import { useEcho } from "@laravel/echo-react";

useEcho(
    `orders.${orderId}`,
    "OrderShipmentStatusUpdated",
    (e) => {
        console.log(e.order);
    },
);
```

----------------------------------------

TITLE: Publishing Package Configuration in Laravel Service Provider (PHP)
DESCRIPTION: This PHP snippet, placed in the `boot` method of a Laravel service provider, uses the `publishes` method to make a package's configuration file available for publishing to the application's `config` directory via the `vendor:publish` Artisan command.
SOURCE: https://github.com/laravel/docs/blob/12.x/packages.md#_snippet_3

LANGUAGE: PHP
CODE:
```
/**
 * Bootstrap any package services.
 */
public function boot(): void
{
    $this->publishes([
        __DIR__.'/../config/courier.php' => config_path('courier.php'),
    ]);
}
```

----------------------------------------

TITLE: Chunking Eloquent Results for Large Datasets - PHP
DESCRIPTION: This snippet demonstrates using the `chunk` method to process a large number of Eloquent models in smaller batches, reducing memory consumption. It retrieves 200 `Flight` models at a time and passes each chunk to a closure for processing. This is ideal for operations that don't require loading all records into memory simultaneously.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_26

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;
use Illuminate\Database\Eloquent\Collection;

Flight::chunk(200, function (Collection $flights) {
    foreach ($flights as $flight) {
        // ...
    }
});
```

----------------------------------------

TITLE: Defining Global Route Parameter Constraints in Laravel
DESCRIPTION: This code shows how to define a global regular expression constraint for a route parameter, such as `id` being numeric. This pattern is typically defined in the `boot` method of `AppServiceProvider` and automatically applies to all routes using that parameter name.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_36

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Route;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Route::pattern('id', '[0-9]+');
}
```

----------------------------------------

TITLE: Creating Basic HTTP Redirects in Laravel PHP
DESCRIPTION: This snippet demonstrates the simplest way to create an HTTP redirect in Laravel using the global `redirect` helper. It redirects a GET request from `/dashboard` to `/home/<USER>
SOURCE: https://github.com/laravel/docs/blob/12.x/redirects.md#_snippet_0

LANGUAGE: php
CODE:
```
Route::get('/dashboard', function () {
    return redirect('/home/<USER>');
});
```

----------------------------------------

TITLE: Dropping All Tables and Migrating (Laravel)
DESCRIPTION: This command drops all tables from the database and then executes the `migrate` command, effectively rebuilding the database from scratch. The `--seed` option can be used to run seeders afterwards.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_17

LANGUAGE: Shell
CODE:
```
php artisan migrate:fresh

php artisan migrate:fresh --seed
```

----------------------------------------

TITLE: Creating an ID Column (Big Increments Alias) in Laravel
DESCRIPTION: The `id` method is an alias for the `bigIncrements` method, creating an auto-incrementing `UNSIGNED BIGINT` primary key. By default, it creates an 'id' column, but a custom name can be provided.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_48

LANGUAGE: PHP
CODE:
```
$table->id();
```

----------------------------------------

TITLE: Manually Create and Use Laravel Validator Instances
DESCRIPTION: Demonstrates how to manually create a validator instance using the `Validator` facade's `make` method, providing data and rules. It also shows how to handle validation failures, retrieve validated data, and use `withErrors` for session flashing.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_30

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PostController extends Controller
{
    /**
     * Store a new blog post.
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|unique:posts|max:255',
            'body' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect('/post/create')
                ->withErrors($validator)
                ->withInput();
        }

        // Retrieve the validated input...
        $validated = $validator->validated();

        // Retrieve a portion of the validated input...
        $validated = $validator->safe()->only(['name', 'email']);
        $validated = $validator->safe()->except(['name', 'email']);

        // Store the blog post...

        return redirect('/posts');
    }
}
```

----------------------------------------

TITLE: Solving N+1 Problem with Eager Loading Relationship (PHP)
DESCRIPTION: Resolves the N+1 query problem by eager loading the `author` relationship when retrieving `Book` models using the `with` method. This reduces the number of queries to two: one for books and one for all related authors.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_115

LANGUAGE: php
CODE:
```
$books = Book::with('author')->get();

foreach ($books as $book) {
    echo $book->author->name;
}
```

----------------------------------------

TITLE: Tapping into Laravel Collection with `tap()` in PHP
DESCRIPTION: The `tap` method allows you to execute a callback on a collection at a specific point without modifying the collection itself. It's useful for debugging or logging intermediate states. The method returns the original collection after the callback is executed.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_157

LANGUAGE: php
CODE:
```
collect([2, 4, 3, 1, 5])
    ->sort()
    ->tap(function (Collection $collection) {
        Log::debug('Values after sorting', $collection->values()->all());
    })
    ->shift();

// 1
```

----------------------------------------

TITLE: Creating a Stripe Checkout Session for Subscriptions in Laravel
DESCRIPTION: This PHP route demonstrates how to initiate a Stripe Checkout session for a new subscription. It uses Cashier's `newSubscription` method, allows for trial days and promotion codes, and specifies success and cancel redirection URLs.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_15

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::get('/subscription-checkout', function (Request $request) {
    return $request->user()
        ->newSubscription('default', 'price_basic_monthly')
        ->trialDays(5)
        ->allowPromotionCodes()
        ->checkout([
            'success_url' => route('your-success-route'),
            'cancel_url' => route('your-cancel-route'),
        ]);
});
```

----------------------------------------

TITLE: Retrieving Single Input Value (PHP)
DESCRIPTION: This PHP code uses the `input()` method to retrieve a single input value by its name, 'name', from the `Illuminate\Http\Request` instance. This method is agnostic to the HTTP verb used (GET, POST, PUT, etc.), making it a universal way to access user input from various request types.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_26

LANGUAGE: php
CODE:
```
$name = $request->input('name');
```

----------------------------------------

TITLE: Define Boolean Attribute Cast in Laravel Model
DESCRIPTION: Demonstrates how to cast an integer attribute (`is_admin`) to a boolean using the `casts` method in a Laravel Eloquent model. This automatically converts database `0` or `1` values to PHP booleans upon retrieval.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-mutators.md#_snippet_9

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_admin' => 'boolean',
        ];
    }
}
```

----------------------------------------

TITLE: Starting Laravel Development Servers
DESCRIPTION: These commands navigate into the newly created application directory, install frontend dependencies, build assets, and then start Laravel's local development server, queue worker, and Vite development server. This prepares the application for local development access via a web browser.
SOURCE: https://github.com/laravel/docs/blob/12.x/installation.md#_snippet_3

LANGUAGE: shell
CODE:
```
cd example-app
npm install && npm run build
composer run dev
```

----------------------------------------

TITLE: Laravel Query Builder: `where` Clause with Different Operators
DESCRIPTION: This snippet illustrates the flexibility of the `where` method by demonstrating its use with various comparison operators such as `>=`, `<>`, and `like`. It allows for complex filtering based on different conditions beyond simple equality.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_25

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->where('votes', '>=', 100)
    ->get();

$users = DB::table('users')
    ->where('votes', '<>', 100)
    ->get();

$users = DB::table('users')
    ->where('name', 'like', 'T%')
    ->get();
```

----------------------------------------

TITLE: Displaying Validation Errors for an Attribute
DESCRIPTION: This snippet demonstrates using the `@error` Blade directive to check for and display validation error messages associated with a specific attribute (`title`). It conditionally applies a CSS class (`is-invalid`) to the input field and displays the error message using the `$message` variable within a `div`.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_138

LANGUAGE: blade
CODE:
```
<!-- /resources/views/post/create.blade.php -->

<label for="title">Post Title</label>

<input
    id="title"
    type="text"
    class="@error('title') is-invalid @enderror"
/>

@error('title')
    <div class="alert alert-danger">{{ $message }}</div>
@enderror
```

----------------------------------------

TITLE: Managing Flashed Session Data with Redirects in Laravel
DESCRIPTION: Demonstrates how to redirect with flashed session data in Laravel (PHP) and how to display that data using Blade syntax. Useful for showing temporary messages after an action.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_21

LANGUAGE: php
CODE:
```
Route::post('/user/profile', function () {
    // ...

    return redirect('/dashboard')->with('status', 'Profile updated!');
});
```

LANGUAGE: blade
CODE:
```
@if (session('status'))
    <div class="alert alert-success">
        {{ session('status') }}
    </div>
@endif
```

----------------------------------------

TITLE: Conditionally Merging CSS Classes in Component
DESCRIPTION: Shows how to use the `$attributes->class()` method to conditionally merge CSS classes based on a boolean expression, allowing dynamic class application. Numeric keys are always included.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_86

LANGUAGE: Blade
CODE:
```
<div {{ $attributes->class(['p-4', 'bg-red' => $hasError]) }}>
    {{ $message }}
</div>
```

----------------------------------------

TITLE: Configuring Nginx for Laravel Application Deployment
DESCRIPTION: This Nginx configuration serves as a starting point for a Laravel application, directing all requests to `public/index.php` to prevent exposure of sensitive files. It includes settings for listening ports, server name, root directory, headers, index file, and error pages, along with `fastcgi_pass` for PHP-FPM integration and security rules to deny access to hidden files.
SOURCE: https://github.com/laravel/docs/blob/12.x/deployment.md#_snippet_0

LANGUAGE: Nginx
CODE:
```
server {
    listen 80;
    listen [::]:80;
    server_name example.com;
    root /srv/example.com/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

----------------------------------------

TITLE: Registering a Route to a Basic Laravel Controller
DESCRIPTION: This route definition maps a GET request to the `/user/{id}` URI to the `show` method within the `UserController` class. When the route matches, Laravel invokes the specified controller method, passing any route parameters (like `id`) as arguments.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_2

LANGUAGE: php
CODE:
```
use App\Http\Controllers\UserController;

Route::get('/user/{id}', [UserController::class, 'show']);
```

----------------------------------------

TITLE: Generate URL for Named Route in Laravel
DESCRIPTION: The `route` function generates a URL for a given named route. If the route accepts parameters, you may pass them as the second argument. By default, it generates an absolute URL, but you can generate a relative URL by passing `false` as the third argument.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_93

LANGUAGE: php
CODE:
```
$url = route('route.name');
```

LANGUAGE: php
CODE:
```
$url = route('route.name', ['id' => 1]);
```

LANGUAGE: php
CODE:
```
$url = route('route.name', ['id' => 1], false);
```

----------------------------------------

TITLE: Returning Blade View with Data in Laravel Route (PHP)
DESCRIPTION: Demonstrates how to define a basic Laravel route that returns a Blade view named 'greeting' and passes a 'name' variable to it. This is a fundamental way to render dynamic content using Blade.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_0

LANGUAGE: php
CODE:
```
Route::get('/', function () {
    return view('greeting', ['name' => 'Finn']);
});
```

----------------------------------------

TITLE: Configuring Axios for CORS Credentials and XSRF Token
DESCRIPTION: This JavaScript snippet configures the global Axios instance to send credentials (like cookies) with cross-origin requests and to include the XSRF token. This is essential for successful SPA authentication with Laravel Sanctum when the SPA and API are on different subdomains, ensuring that session cookies and CSRF protection work correctly.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_19

LANGUAGE: javascript
CODE:
```
axios.defaults.withCredentials = true;
axios.defaults.withXSRFToken = true;
```

----------------------------------------

TITLE: Syncing Many To Many Relationships with Pivot Data in PHP
DESCRIPTION: Illustrates how to use the `sync` method while also specifying pivot table data for the attached relationships by providing an associative array where keys are IDs and values are pivot data arrays.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_154

LANGUAGE: php
CODE:
```
$user->roles()->sync([1 => ['expires' => true], 2, 3]);
```

----------------------------------------

TITLE: Defining a Named Route - PHP
DESCRIPTION: This snippet illustrates how to define a named route in Laravel using `Route::get()`. Named routes allow for URL generation without being tightly coupled to the actual URL path, improving maintainability.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_6

LANGUAGE: PHP
CODE:
```
Route::get('/post/{post}', function (Post $post) {
    // ...
})->name('post.show');
```

----------------------------------------

TITLE: Echoing PHP Function Results in Blade Template
DESCRIPTION: Illustrates how to display the result of any PHP function directly within a Blade template using double curly braces. This demonstrates Blade's flexibility in allowing raw PHP code.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_2

LANGUAGE: blade
CODE:
```
The current UNIX timestamp is {{ time() }}.
```

----------------------------------------

TITLE: Get Laravel service container instance (PHP)
DESCRIPTION: The `app` function returns the Laravel service container instance. You can also pass a class or interface name to resolve it from the container.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_102

LANGUAGE: PHP
CODE:
```
$container = app();

$api = app('HelpSpot\API');
```

----------------------------------------

TITLE: Generating Eloquent Models with Associated Classes in Laravel
DESCRIPTION: These commands demonstrate how to generate an Eloquent model along with various related classes such as factories, seeders, controllers, policies, and form requests. Options can be combined for simultaneous generation of multiple files.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_2

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight --factory
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight -f
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight --seed
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight -s
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight --controller
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight -c
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight --controller --resource --requests
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight -crR
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight --policy
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight -mfsc
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight --all
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Flight -a
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Member --pivot
```

LANGUAGE: Shell
CODE:
```
php artisan make:model Member -p
```

----------------------------------------

TITLE: Generating a Basic Laravel Controller
DESCRIPTION: This Artisan command is used to quickly generate a new controller class. By default, the generated controller will be stored in the `app/Http/Controllers` directory, providing a structured location for your application's request handling logic.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_0

LANGUAGE: shell
CODE:
```
php artisan make:controller UserController
```

----------------------------------------

TITLE: Asserting JSON Structure in Laravel PHP
DESCRIPTION: Asserts that the HTTP response has a specific JSON structure. This method allows verifying the presence of keys and their nesting, optionally using `*` for array elements.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_82

LANGUAGE: php
CODE:
```
$response->assertJsonStructure(array $structure);
```

LANGUAGE: json
CODE:
```
{
    "user": {
        "name": "Steve Schoger"
    }
}
```

LANGUAGE: php
CODE:
```
$response->assertJsonStructure([
    'user' => [
        'name',
    ]
]);
```

LANGUAGE: json
CODE:
```
{
    "user": [
        {
            "name": "Steve Schoger",
            "age": 55,
            "location": "Earth"
        },
        {
            "name": "Mary Schoger",
            "age": 60,
            "location": "Earth"
        }
    ]
}
```

LANGUAGE: php
CODE:
```
$response->assertJsonStructure([
    'user' => [
        '*' => [
             'name',
             'age',
             'location'
        ]
    ]
]);
```

----------------------------------------

TITLE: Laravel Zero Configuration Dependency Resolution
DESCRIPTION: Illustrates how Laravel's service container automatically resolves and injects concrete class dependencies into route handlers without explicit configuration, simplifying development.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

class Service
{
    // ...
}

Route::get('/', function (Service $service) {
    dd($service::class);
});
```

----------------------------------------

TITLE: Preventing Lazy Loading in Laravel PHP
DESCRIPTION: Configures Eloquent to prevent lazy loading of relationships, typically within the `boot` method of a service provider. It accepts a boolean to conditionally apply the prevention, often based on the application environment.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_137

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Model::preventLazyLoading(! $this->app->isProduction());
}
```

----------------------------------------

TITLE: Rendering Basic Anonymous Blade Components (Blade)
DESCRIPTION: This snippet shows how to render a simple anonymous Blade component. If a Blade template is placed in `resources/views/components/alert.blade.php`, it can be rendered directly using the `<x-alert/>` syntax without an associated PHP class.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_118

LANGUAGE: Blade
CODE:
```
<x-alert/>
```

----------------------------------------

TITLE: Creating Single and Multiple Model Instances with Laravel Factories
DESCRIPTION: The `create` method instantiates model instances and persists them to the database using Eloquent's `save` method. This snippet demonstrates creating a single `User` instance and three `User` instances using `count(3)`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-factories.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use App\Models\User;

// Create a single App\Models\User instance...
$user = User::factory()->create();

// Create three App\Models\User instances...
$users = User::factory()->count(3)->create();
```

----------------------------------------

TITLE: Preventing Lazy Loading in Eloquent (PHP)
DESCRIPTION: Illustrates how to configure Eloquent to prevent lazy loading of relationships, typically invoked in an application service provider's `boot` method. This helps identify and prevent N+1 query issues, especially in non-production environments, by throwing an exception when lazy loading occurs.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Model::preventLazyLoading(! $this->app->isProduction());
}
```

----------------------------------------

TITLE: Redirecting to Cashier Payment Confirmation Page
DESCRIPTION: When a payment fails, Cashier throws an `IncompletePayment` exception. This snippet demonstrates how to catch this exception and redirect the user to Cashier's built-in payment confirmation page. The page prompts the customer for re-entry of payment details and handles 3D Secure, redirecting back to a specified URL upon success.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_184

LANGUAGE: PHP
CODE:
```
use Laravel\Cashier\Exceptions\IncompletePayment;

try {
    $subscription = $user->newSubscription('default', 'price_monthly')
        ->create($paymentMethod);
} catch (IncompletePayment $exception) {
    return redirect()->route(
        'cashier.payment',
        [$exception->payment->id, 'redirect' => route('home')]
    );
}
```

----------------------------------------

TITLE: Laravel Unique Validation: Adding Custom Query Conditions
DESCRIPTION: Explains how to extend the `unique` validation query with additional `WHERE` clauses using a closure, allowing for more granular control over the uniqueness check based on other attributes.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_165

LANGUAGE: PHP
CODE:
```
'email' => Rule::unique('users')->where(fn (Builder $query) => $query->where('account_id', 1))
```

----------------------------------------

TITLE: Dumping Laravel Collection Items and Exiting in PHP
DESCRIPTION: This snippet illustrates the `dd` method, which is used to dump the collection's items to the browser or console and then terminate the script's execution. It's useful for quick debugging.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_30

LANGUAGE: PHP
CODE:
```
$collection = collect(['John Doe', 'Jane Doe']);

$collection->dd();

/*
    array:2 [
        0 => "John Doe"
        1 => "Jane Doe"
    ]
*/
```

----------------------------------------

TITLE: Processing Large Files with Lazy Collections (PHP)
DESCRIPTION: This code demonstrates using `LazyCollection` to process a large log file efficiently by leveraging PHP generators. It reads the file in chunks, maps lines to `LogEntry` objects, and processes each entry, keeping memory usage low.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_191

LANGUAGE: php
CODE:
```
use App\Models\LogEntry;
use Illuminate\Support\LazyCollection;

LazyCollection::make(function () {
    $handle = fopen('log.txt', 'r');

    while (($line = fgets($handle)) !== false) {
        yield $line;
    }

    fclose($handle);
})->chunk(4)->map(function (array $lines) {
    return LogEntry::fromLines($lines);
})->each(function (LogEntry $logEntry) {
    // Process the log entry...
});
```

----------------------------------------

TITLE: Authenticate User with 'Remember Me' Functionality
DESCRIPTION: Demonstrates how to enable 'remember me' functionality by passing a boolean `true` as the second argument to the `attempt` method. This keeps the user authenticated indefinitely or until they manually log out, requiring a `remember_token` column in the users table.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_12

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Auth;

if (Auth::attempt(['email' => $email, 'password' => $password], $remember)) {
    // The user is being remembered...
}
```

----------------------------------------

TITLE: Testing Time-Sensitive Logic with Pest
DESCRIPTION: This snippet provides a practical example of using time manipulation in a Pest test to verify time-sensitive application behavior. It simulates advancing time by one week to test if a forum thread is correctly locked due to inactivity, demonstrating the utility of `travel` for such scenarios.
SOURCE: https://github.com/laravel/docs/blob/12.x/mocking.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use App\Models\Thread;

test('forum threads lock after one week of inactivity', function () {
    $thread = Thread::factory()->create();

    $this->travel(1)->week();

    expect($thread->isLockedByInactivity())->toBeTrue();
});
```

----------------------------------------

TITLE: Segmenting Rate Limits by User ID or IP (PHP)
DESCRIPTION: This snippet demonstrates segmenting rate limits based on whether a user is authenticated. Authenticated users are limited to 100 requests per minute by their user ID, while guests are limited to 10 requests per minute by their IP address, providing distinct policies for different user types.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_73

LANGUAGE: php
CODE:
```
RateLimiter::for('uploads', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(100)->by($request->user()->id)
        : Limit::perMinute(10)->by($request->ip());
});
```

----------------------------------------

TITLE: Create HTTP Responses with response() Helper
DESCRIPTION: The `response` function is a versatile helper for creating HTTP response instances or obtaining an instance of the response factory. It allows for custom content, status codes, and headers, and can be used to generate various response types like JSON.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_143

LANGUAGE: php
CODE:
```
return response('Hello World', 200, $headers);
```

LANGUAGE: php
CODE:
```
return response()->json(['foo' => 'bar'], 200, $headers);
```

----------------------------------------

TITLE: Asserting Response Content in Laravel PHP
DESCRIPTION: Asserts that the HTTP response content exactly matches the given string value. This is a direct comparison of the response body.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_88

LANGUAGE: php
CODE:
```
$response->assertContent($value);
```

----------------------------------------

TITLE: Return Eloquent Models and Collections as JSON in Laravel
DESCRIPTION: Shows how Laravel automatically converts Eloquent ORM models and collections into JSON responses when returned directly from routes or controllers. The framework respects the model's hidden attributes during serialization, providing a convenient way to expose data.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_2

LANGUAGE: php
CODE:
```
use App\Models\User;

Route::get('/user/{user}', function (User $user) {
    return $user;
});
```

----------------------------------------

TITLE: Executing Artisan Commands with Laravel Sail
DESCRIPTION: When using Laravel Sail as your local development environment, this command invokes Artisan commands within the application's Docker containers. It ensures commands run in the correct isolated environment provided by Sail.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_2

LANGUAGE: shell
CODE:
```
./vendor/bin/sail artisan list
```

----------------------------------------

TITLE: Asserting JSON Data Presence in Laravel PHP
DESCRIPTION: Assert that the response contains the given JSON data. The `assertJson` method converts the response to an array to verify that the given array exists within the JSON response returned by the application, allowing the test to pass even if other properties are present.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_72

LANGUAGE: PHP
CODE:
```
$response->assertJson(array $data, $strict = false);
```

----------------------------------------

TITLE: Authorizing Action with Gate::authorize in PHP
DESCRIPTION: This snippet uses `Gate::authorize` to perform an authorization check. If the action is not authorized, Laravel automatically throws an `Illuminate\Auth\Access\AuthorizationException`. The error message from a policy's `Response::deny()` method will be propagated to the HTTP response, typically resulting in a 403 status.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_22

LANGUAGE: php
CODE:
```
Gate::authorize('update', $post);

// The action is authorized...
```

----------------------------------------

TITLE: Create Stripe Payment Method Identifier
DESCRIPTION: This JavaScript snippet demonstrates how to use Stripe's `createPaymentMethod` method to securely verify card details and retrieve a payment method identifier. It listens for a button click, collects billing details, and handles success or error responses from Stripe.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_46

LANGUAGE: javascript
CODE:
```
const cardHolderName = document.getElementById('card-holder-name');
const cardButton = document.getElementById('card-button');

cardButton.addEventListener('click', async (e) => {
    const { paymentMethod, error } = await stripe.createPaymentMethod(
        'card', cardElement, {
            billing_details: { name: cardHolderName.value }
        }
    );

    if (error) {
        // Display "error.message" to the user...
    } else {
        // The card has been verified successfully...
    }
});
```

----------------------------------------

TITLE: Laravel Validation Rule: exists
DESCRIPTION: This rule verifies that the field under validation's value exists in a specified database table and column. It is crucial for validating foreign key relationships and ensuring data integrity.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_98

LANGUAGE: APIDOC
CODE:
```
exists:_table_,_column_
```

----------------------------------------

TITLE: Conditional Styling Based on Validation Errors
DESCRIPTION: This snippet shows how to use the `@error` directive with an `@else` clause to apply conditional CSS classes based on the presence or absence of validation errors for an attribute (`email`). It applies `is-invalid` if an error exists, and `is-valid` otherwise.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_139

LANGUAGE: blade
CODE:
```
<!-- /resources/views/auth.blade.php -->

<label for="email">Email address</label>

<input
    id="email"
    type="email"
    class="@error('email') is-invalid @else is-valid @enderror"
/>
```

----------------------------------------

TITLE: Adding Specific Log Context to a Custom Exception Class in Laravel
DESCRIPTION: This code demonstrates how to add unique contextual data to a specific exception type by defining a `context` method directly on the exception class. This method returns an array of data, such as `order_id`, which will be included in the log entry only when this particular exception is reported.
SOURCE: https://github.com/laravel/docs/blob/12.x/errors.md#_snippet_3

LANGUAGE: PHP
CODE:
```
<?php

namespace AppExceptions;

use Exception;

class InvalidOrderException extends Exception
{
    // ...

    /**
     * Get the exception's context information.
     *
     * @return array<string, mixed>
     */
    public function context(): array
    {
        return ['order_id' => $this->orderId];
    }
}
```

----------------------------------------

TITLE: Define Eloquent Accessor for `first_name` Attribute
DESCRIPTION: This PHP code demonstrates how to define an accessor for an Eloquent model attribute. The `firstName()` method, returning an `Illuminate\Database\Eloquent\Casts\Attribute` instance, uses a `get` closure to automatically capitalize the `first_name` value when it's retrieved from the model. This allows for on-the-fly data transformation without altering the stored database value.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-mutators.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    /**
     * Get the user's first name.
     */
    protected function firstName(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => ucfirst($value),
        );
    }
}
```

----------------------------------------

TITLE: Retrieving Items from Laravel Collection using `get()` in PHP
DESCRIPTION: The `get` method retrieves an item from a Laravel Collection by its key. If the key does not exist, it returns `null` by default, or an optional default value/callback result if provided. This allows for safe and flexible access to collection elements.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_62

LANGUAGE: PHP
CODE:
```
$collection = collect(['name' => 'Taylor', 'framework' => 'Laravel']);

$value = $collection->get('name');

// Taylor
```

LANGUAGE: PHP
CODE:
```
$collection = collect(['name' => 'Taylor', 'framework' => 'Laravel']);

$value = $collection->get('age', 34);

// 34
```

LANGUAGE: PHP
CODE:
```
$collection->get('email', function () {
    return '<EMAIL>';
});

// <EMAIL>
```

----------------------------------------

TITLE: Process Database Records in Chunks using chunk
DESCRIPTION: Illustrates using the `chunk` method from the `DB` facade to process large datasets in smaller, manageable chunks. It retrieves 100 records at a time and feeds them into a closure for processing.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_4

LANGUAGE: php
CODE:
```
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

DB::table('users')->orderBy('id')->chunk(100, function (Collection $users) {
    foreach ($users as $user) {
        // ...
    }
});
```

----------------------------------------

TITLE: Inject Various Services using Contextual Attributes
DESCRIPTION: Provides examples of various Laravel contextual attributes (`Auth`, `Cache`, `Config`, `Context`, `DB`, `Give`, `Log`, `RouteParameter`, `Tag`) for injecting different types of values or services directly into a class constructor, streamlining dependency injection for common application concerns.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_15

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Contracts\UserRepository;
use App\Models\Photo;
use App\Repositories\DatabaseRepository;
use Illuminate\Container\Attributes\Auth;
use Illuminate\Container\Attributes\Cache;
use Illuminate\Container\Attributes\Config;
use Illuminate\Container\Attributes\Context;
use Illuminate\Container\Attributes\DB;
use Illuminate\Container\Attributes\Give;
use Illuminate\Container\Attributes\Log;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Container\Attributes\Tag;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Database\Connection;
use Psr\Log\LoggerInterface;

class PhotoController extends Controller
{
    public function __construct(
        #[Auth('web')] protected Guard $auth,
        #[Cache('redis')] protected Repository $cache,
        #[Config('app.timezone')] protected string $timezone,
        #[Context('uuid')] protected string $uuid,
        #[DB('mysql')] protected Connection $connection,
        #[Give(DatabaseRepository::class)] protected UserRepository $users,
        #[Log('daily')] protected LoggerInterface $log,
        #[RouteParameter('photo')] protected Photo $photo,
        #[Tag('reports')] protected iterable $reports,
    ) {
        // ...
    }
}
```

----------------------------------------

TITLE: Defining a Policy Update Method Returning Boolean in PHP
DESCRIPTION: This `update` method within a `PostPolicy` determines if a `User` can update a `Post`. It receives both the `User` and `Post` instances and returns `true` if the user's ID matches the post's `user_id`, indicating ownership, otherwise `false`. This is a common pattern for resource ownership checks.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_19

LANGUAGE: php
CODE:
```
<?php

namespace App\Policies;

use App\Models\Post;
use App\Models\User;

class PostPolicy
{
    /**
     * Determine if the given post can be updated by the user.
     */
    public function update(User $user, Post $post): bool
    {
        return $user->id === $post->user_id;
    }
}
```

----------------------------------------

TITLE: Basic Usage of Laravel Exists Validation Rule
DESCRIPTION: Demonstrates the fundamental application of the `exists` rule, where the field name is implicitly used as the column name for validation against a specified database table.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_99

LANGUAGE: php
CODE:
```
'state' => 'exists:states'
```

----------------------------------------

TITLE: Laravel Throw If Helper for Conditional Exceptions
DESCRIPTION: The `throw_if()` helper function conditionally throws an exception. It takes a boolean expression and an exception class (or instance). If the expression evaluates to `true`, the specified exception is thrown. An optional message can be provided.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_147

LANGUAGE: php
CODE:
```
throw_if(! Auth::user()->isAdmin(), AuthorizationException::class);
```

LANGUAGE: php
CODE:
```
throw_if(
    ! Auth::user()->isAdmin(),
    AuthorizationException::class,
    'You are not allowed to access this page.'
);
```

----------------------------------------

TITLE: Generating Route Cache in Laravel
DESCRIPTION: This Artisan command generates a cached routes file, significantly reducing the time required to register all application routes, especially beneficial in production environments. It should be run during deployment, and a fresh cache must be generated if routes are modified.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_82

LANGUAGE: shell
CODE:
```
php artisan route:cache
```

----------------------------------------

TITLE: Configuring Redis for Rate Limiting (PHP)
DESCRIPTION: This snippet shows how to configure Laravel to use Redis as the cache driver for rate limiting. By calling `throttleWithRedis()` within the `withMiddleware` callback in `bootstrap/app.php`, the `throttle` middleware will utilize Redis for managing rate limits, improving performance and scalability for distributed applications.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_77

LANGUAGE: php
CODE:
```
->withMiddleware(function (Middleware $middleware) {
    $middleware->throttleWithRedis();
    // ...
})
```

----------------------------------------

TITLE: Manually Beginning Database Transactions in Laravel
DESCRIPTION: This code shows how to manually start a database transaction using the `DB::beginTransaction` method. This provides explicit control over when a transaction begins, allowing for custom rollback and commit logic.
SOURCE: https://github.com/laravel/docs/blob/12.x/database.md#_snippet_21

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

DB::beginTransaction();
```

----------------------------------------

TITLE: Basic Pest Test Example
DESCRIPTION: This snippet demonstrates a basic test case using Pest. It defines a simple test that asserts `true` is `true`, serving as a minimal example for a new test file.
SOURCE: https://github.com/laravel/docs/blob/12.x/testing.md#_snippet_2

LANGUAGE: PHP
CODE:
```
<?php

test('basic', function () {
    expect(true)->toBeTrue();
});
```

----------------------------------------

TITLE: Authorizing Actions via Middleware on Laravel Route
DESCRIPTION: This PHP snippet demonstrates authorizing an action using the `can` middleware directly on a Laravel route. The middleware takes the action name ('update') and the route parameter ('post') as arguments. Laravel's implicit model binding automatically resolves the `post` parameter to a `Post` model instance, which is then passed to the policy. If unauthorized, a 403 HTTP response is returned.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_32

LANGUAGE: PHP
CODE:
```
use AppModelsPost;

Route::put('/post/{post}', function (Post $post) {
    // The current user may update the post...
})->middleware('can:update,post');
```

----------------------------------------

TITLE: Restricting Routes with Email Verification Middleware
DESCRIPTION: This PHP snippet demonstrates how to restrict access to specific routes until a user's email address has been verified. By applying the `verified` middleware within a route group, routes like the dashboard will only be accessible to authenticated users whose emails have been confirmed.
SOURCE: https://github.com/laravel/docs/blob/12.x/starter-kits.md#_snippet_23

LANGUAGE: php
CODE:
```
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});
```

----------------------------------------

TITLE: Define Laravel Middleware for Token Validation
DESCRIPTION: This PHP code defines an `EnsureTokenIsValid` middleware that checks for a specific 'token' in the incoming request. If the token doesn't match, it redirects the user to '/home'; otherwise, it allows the request to proceed.
SOURCE: https://github.com/laravel/docs/blob/12.x/middleware.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureTokenIsValid
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->input('token') !== 'my-secret-token') {
            return redirect('/home');
        }

        return $next($request);
    }
}
```

----------------------------------------

TITLE: Customize Laravel Validation Error Messages
DESCRIPTION: Explains how to override the default validation error messages by implementing the `messages` method in a form request. This method should return an array mapping attribute-rule pairs to custom error strings.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_26

LANGUAGE: php
CODE:
```
/**
 * Get the error messages for the defined validation rules.
 *
 * @return array<string, string>
 */
public function messages(): array
{
    return [
        'title.required' => 'A title is required',
        'body.required' => 'A message is required',
    ];
}
```

----------------------------------------

TITLE: Updating Existing Eloquent Models with Save Method in PHP
DESCRIPTION: This snippet illustrates how to update an existing record in the database using Laravel Eloquent. It involves retrieving the model, modifying its attributes, and then calling the `save()` method. The `updated_at` timestamp is automatically updated.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_43

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

$flight = Flight::find(1);

$flight->name = 'Paris to London';

$flight->save();
```

----------------------------------------

TITLE: Inserting New Eloquent Models with Save Method in PHP
DESCRIPTION: This snippet shows how to insert a new record into the database using Laravel Eloquent. It involves instantiating a new model, setting its attributes, and then calling the `save()` method. Timestamps (`created_at`, `updated_at`) are automatically managed by Eloquent.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_41

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Models\Flight;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class FlightController extends Controller
{
    /**
     * Store a new flight in the database.
     */
    public function store(Request $request): RedirectResponse
    {
        // Validate the request...

        $flight = new Flight;

        $flight->name = $request->name;

        $flight->save();

        return redirect('/flights');
    }
}
```

----------------------------------------

TITLE: Extending a Blade Layout
DESCRIPTION: This snippet demonstrates how a child Blade view (`child.blade.php`) extends a base layout using the `@extends` directive. It injects content into the layout's defined sections (`title`, `sidebar`, `content`) using `@section` directives, and uses `@@parent` to append content to the master sidebar.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_134

LANGUAGE: blade
CODE:
```
<!-- resources/views/child.blade.php -->

@extends('layouts.app')

@section('title', 'Page Title')

@section('sidebar')
    @@parent

    <p>This is appended to the master sidebar.</p>
@endsection

@section('content')
    <p>This is my body content.</p>
@endsection
```

----------------------------------------

TITLE: Laravel Session Helper Function Usage
DESCRIPTION: The `session()` helper function in Laravel is used to interact with session values. It can retrieve a specific key's value, set multiple key-value pairs, or return the entire session store instance for more advanced operations like `get()` and `put()`.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_145

LANGUAGE: php
CODE:
```
$value = session('key');
```

LANGUAGE: php
CODE:
```
session(['chairs' => 7, 'instruments' => 3]);
```

LANGUAGE: php
CODE:
```
$value = session()->get('key');
```

LANGUAGE: php
CODE:
```
session()->put('key', $value);
```

----------------------------------------

TITLE: Accessing Parent Model via BelongsTo Relationship in Laravel PHP
DESCRIPTION: Demonstrates how to retrieve the parent model (`Post`) from a child model instance (`Comment`) by accessing the relationship method as a dynamic property. Eloquent automatically loads the related model.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_9

LANGUAGE: php
CODE:
```
use App\Models\Comment;

$comment = Comment::find(1);

return $comment->post->title;
```

----------------------------------------

TITLE: Defining Controller Middleware in Laravel
DESCRIPTION: This PHP snippet shows how to define middleware directly within a controller class by implementing the `HasMiddleware` interface. The static `middleware` method returns an array of middleware, allowing fine-grained control over which actions specific middleware apply to using `only` and `except` options.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_7

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class UserController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            'auth',
            new Middleware('log', only: ['index']),
            new Middleware('subscribed', except: ['store']),
        ];
    }

    // ...
}
```

----------------------------------------

TITLE: Authenticate User with Scopes for Testing in Laravel Passport
DESCRIPTION: The `actingAs` method in Laravel Passport allows you to specify the currently authenticated user and grant them specific scopes for testing purposes. It takes a user instance as the first argument and an array of desired scopes as the second, enabling you to simulate user authentication and test API endpoint access based on assigned permissions.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_68

LANGUAGE: php
CODE:
```
use App\Models\User;
use Laravel\Passport\Passport;

test('orders can be created', function () {
    Passport::actingAs(
        User::factory()->create(),
        ['orders:create']
    );

    $response = $this->post('/api/orders');

    $response->assertStatus(201);
});
```

LANGUAGE: php
CODE:
```
use App\Models\User;
use Laravel\Passport\Passport;

public function test_orders_can_be_created(): void
{
    Passport::actingAs(
        User::factory()->create(),
        ['orders:create']
    );

    $response = $this->post('/api/orders');

    $response->assertStatus(201);
}
```

----------------------------------------

TITLE: Acquiring Lock and Dispatching Job with Owner Token in Laravel
DESCRIPTION: This snippet demonstrates how to acquire a cache lock for a specific duration (120 seconds) and, if successful, dispatch a `ProcessPodcast` queued job. It passes the lock's unique owner token to the job, allowing the lock to be restored and released in a separate process.
SOURCE: https://github.com/laravel/docs/blob/12.x/cache.md#_snippet_34

LANGUAGE: php
CODE:
```
$podcast = Podcast::find($id);

$lock = Cache::lock('processing', 120);

if ($lock->get()) {
    ProcessPodcast::dispatch($podcast, $lock->owner());
}
```

----------------------------------------

TITLE: Touching Parent Timestamps in Laravel Eloquent (PHP)
DESCRIPTION: Demonstrates how to configure a child model (e.g., `Comment`) to automatically update the `updated_at` timestamp of its parent model (e.g., `Post`) when the child is updated, using the `touches` property. This is useful for cache invalidation or showing recent activity.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_160

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Comment extends Model
{
    /**
     * All of the relationships to be touched.
     *
     * @var array
     */
    protected $touches = ['post'];

    /**
     * Get the post that the comment belongs to.
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }
}
```

----------------------------------------

TITLE: Apply Conditional Query Clauses with Laravel's `when` Method
DESCRIPTION: This snippet shows how to conditionally apply query clauses using the `when` method in Laravel's Query Builder. It allows you to execute a given closure only if a specified condition is true, and optionally provides an 'else' closure for when the condition is false, enabling dynamic query construction.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_63

LANGUAGE: php
CODE:
```
$role = $request->input('role');

$users = DB::table('users')
    ->when($role, function (Builder $query, string $role) {
        $query->where('role_id', $role);
    })
    ->get();
```

LANGUAGE: php
CODE:
```
$sortByVotes = $request->boolean('sort_by_votes');

$users = DB::table('users')
    ->when($sortByVotes, function (Builder $query, bool $sortByVotes) {
        $query->orderBy('votes');
    }, function (Builder $query) {
        $query->orderBy('name');
    })
    ->get();
```

----------------------------------------

TITLE: Attaching Many To Many Relationship with Pivot Data in PHP
DESCRIPTION: Illustrates how to attach a related model in a many-to-many relationship while also inserting additional data into the intermediate pivot table by passing an array as the second argument to the `attach` method.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_150

LANGUAGE: php
CODE:
```
$user->roles()->attach($roleId, ['expires' => $expires]);
```

----------------------------------------

TITLE: Request OAuth Password Grant Token in Laravel
DESCRIPTION: Shows how to make a `POST` request to the `/oauth/token` route to obtain an access token using a user's email/username and password. This is suitable for first-party clients.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_35

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Http;

$response = Http::asForm()->post('https://passport-app.test/oauth/token', [
    'grant_type' => 'password',
    'client_id' => 'your-client-id',
    'client_secret' => 'your-client-secret', // Required for confidential clients only...
    'username' => '<EMAIL>',
    'password' => 'my-password',
    'scope' => 'user:read orders:create',
]);

return $response->json();
```

----------------------------------------

TITLE: Retrieving Eloquent Models or Executing Callback - PHP
DESCRIPTION: This snippet shows how to use `findOr` and `firstOr` to retrieve a single Eloquent model, or execute a provided closure if no model is found. `findOr(1, ...)` attempts to find a model by primary key, while `firstOr(...)` attempts to find the first model matching query constraints. The closure's return value becomes the method's result if no model is found.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_36

LANGUAGE: PHP
CODE:
```
$flight = Flight::findOr(1, function () {
    // ...
});

$flight = Flight::where('legs', '>', 3)->firstOr(function () {
    // ...
});
```

----------------------------------------

TITLE: Laravel Form Request: Authorize Resource Access with Policy/Gate
DESCRIPTION: Implements authorization logic within the `authorize` method of a Laravel form request. It checks if the authenticated user has permission to update a resource, typically by interacting with authorization gates or policies based on a retrieved model.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_22

LANGUAGE: php
CODE:
```
use App\Models\Comment;

/**
 * Determine if the user is authorized to make this request.
 */
public function authorize(): bool
{
    $comment = Comment::find($this->route('comment'));

    return $comment && $this->user()->can('update', $comment);
}
```

----------------------------------------

TITLE: Customizing Policy Deny HTTP Status with denyAsNotFound in PHP
DESCRIPTION: This policy method uses the `Response::denyAsNotFound()` convenience method to return a 404 HTTP status code when authorization is denied. This is a common pattern for web applications to effectively hide resources that a user is not authorized to access, making them appear as if they don't exist.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_24

LANGUAGE: php
CODE:
```
use App\Models\Post;
use App\Models\User;
use Illuminate\Auth\Access\Response;

/**
 * Determine if the given post can be updated by the user.
 */
public function update(User $user, Post $post): Response
{
    return $user->id === $post->user_id
        ? Response::allow()
        : Response::denyAsNotFound();
}
```

----------------------------------------

TITLE: Redirecting with Flashed Input Data in Laravel PHP
DESCRIPTION: This snippet demonstrates using the `withInput()` method on a `RedirectResponse` instance. It flashes all the current request's input data to the session, which is particularly useful when redirecting back after a form validation failure, allowing the form to be repopulated.
SOURCE: https://github.com/laravel/docs/blob/12.x/redirects.md#_snippet_10

LANGUAGE: php
CODE:
```
return back()->withInput();
```

----------------------------------------

TITLE: Iterating Eloquent Models with `cursor()` and Lazy Collections (PHP)
DESCRIPTION: This example demonstrates using the query builder's `cursor()` method to return a `LazyCollection`, allowing iteration over 10,000 Eloquent models while keeping only one model in memory at a time. The `filter` callback is executed lazily, significantly reducing memory usage.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_193

LANGUAGE: php
CODE:
```
use App\Models\User;

$users = User::cursor()->filter(function (User $user) {
    return $user->id > 500;
});

foreach ($users as $user) {
    echo $user->id;
}
```

----------------------------------------

TITLE: Defining Custom Authentication Pipeline with Fortify (PHP)
DESCRIPTION: This snippet illustrates how to define a custom authentication pipeline using `Fortify::authenticateThrough`. It accepts a closure that returns an array of invokable classes, each processing the login request sequentially. This allows for fine-grained control over the authentication flow, including throttling, username canonicalization, and two-factor authentication checks.
SOURCE: https://github.com/laravel/docs/blob/12.x/fortify.md#_snippet_7

LANGUAGE: PHP
CODE:
```
use Laravel\Fortify\Actions\AttemptToAuthenticate;
use Laravel\Fortify\Actions\CanonicalizeUsername;
use Laravel\Fortify\Actions\EnsureLoginIsNotThrottled;
use Laravel\Fortify\Actions\PrepareAuthenticatedSession;
use Laravel\Fortify\Actions\RedirectIfTwoFactorAuthenticatable;
use Laravel\Fortify\Features;
use Laravel\Fortify\Fortify;
use Illuminate\Http\Request;

Fortify::authenticateThrough(function (Request $request) {
    return array_filter([
            config('fortify.limiters.login') ? null : EnsureLoginIsNotThrottled::class,
            config('fortify.lowercase_usernames') ? CanonicalizeUsername::class : null,
            Features::enabled(Features::twoFactorAuthentication()) ? RedirectIfTwoFactorAuthenticatable::class : null,
            AttemptToAuthenticate::class,
            PrepareAuthenticatedSession::class,
    ]);
});
```

----------------------------------------

TITLE: Making Concurrent HTTP Requests (Named) (Laravel PHP)
DESCRIPTION: Dispatch multiple HTTP requests concurrently using the `pool` method and name each request using the `as` method to access the corresponding responses by name.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_36

LANGUAGE: php
CODE:
```
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

$responses = Http::pool(fn (Pool $pool) => [
    $pool->as('first')->get('http://localhost/first'),
    $pool->as('second')->get('http://localhost/second'),
    $pool->as('third')->get('http://localhost/third'),
]);

return $responses['first']->ok();
```

----------------------------------------

TITLE: Handling Failed File Writes in Laravel Storage
DESCRIPTION: By default, the `put` method returns `false` on write failure. Optionally, set the `throw` option to `true` in the disk's configuration to make write operations throw a `League\Flysystem\UnableToWriteFile` exception on failure, allowing for more robust error handling.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_37

LANGUAGE: php
CODE:
```
if (! Storage::put('file.jpg', $contents)) {
    // The file could not be written to disk...
}
```

LANGUAGE: php
CODE:
```
'public' => [
    'driver' => 'local',
    // ...
    'throw' => true,
],
```

----------------------------------------

TITLE: Livewire Component Blade Template
DESCRIPTION: This Blade template corresponds to the Livewire `Counter` component. It uses the `wire:click` directive to bind a button click to the `increment` method of the Livewire component and displays the current `$count` value using Blade's echo syntax, enabling dynamic UI interactions.
SOURCE: https://github.com/laravel/docs/blob/12.x/frontend.md#_snippet_3

LANGUAGE: Blade
CODE:
```
<div>
    <button wire:click="increment">+</button>
    <h1>{{ $count }}</h1>
</div>
```

----------------------------------------

TITLE: Generating Temporary URL for File in Laravel
DESCRIPTION: The `temporaryUrl` method creates a temporary, time-limited URL for files stored using the `local` and `s3` drivers. It requires a path and a `DateTime` instance for expiration.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_27

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Storage;

$url = Storage::temporaryUrl(
    'file.jpg', now()->addMinutes(5)
);
```

----------------------------------------

TITLE: Chaining orWhere on Laravel Eloquent Relationship (Incorrect)
DESCRIPTION: Demonstrates how chaining an `orWhere` clause directly after a relationship constraint can lead to the `orWhere` being applied at the same logical level as the relationship constraint, potentially returning records not belonging to the related model.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_79

LANGUAGE: php
CODE:
```
$user->posts()
    ->where('active', 1)
    ->orWhere('votes', '>=', 100)
    ->get();
```

----------------------------------------

TITLE: Syncing Value Object Changes to Laravel Eloquent Model
DESCRIPTION: This PHP example demonstrates how changes made to a value object, once retrieved from an Eloquent model, are automatically synced back to the model when `save()` is called. It shows modifying a property of the `address` value object and then persisting the change to the database.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-mutators.md#_snippet_35

LANGUAGE: php
CODE:
```
use AppModelsUser;

$user = User::find(1);

$user->address->lineOne = 'Updated Address Value';

$user->save();
```

----------------------------------------

TITLE: Supplementing Resource Controllers with Custom Routes (PHP)
DESCRIPTION: This example shows how to add custom routes to a resource controller beyond the default set. It's crucial to define these supplemental routes *before* the `Route::resource` call to ensure they take precedence.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_27

LANGUAGE: php
CODE:
```
use App\Http\Controller\PhotoController;

Route::get('/photos/popular', [PhotoController::class, 'popular']);
Route::resource('photos', PhotoController::class);
```

----------------------------------------

TITLE: Dumping Response Contents for Debugging with Pest
DESCRIPTION: This snippet demonstrates how to use `dumpHeaders`, `dumpSession`, and `dump` methods in Pest to inspect and debug the response contents, headers, and session data after an HTTP request.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_13

LANGUAGE: PHP
CODE:
```
<?php

test('basic test', function () {
    $response = $this->get('/');

    $response->dumpHeaders();

    $response->dumpSession();

    $response->dump();
});
```

----------------------------------------

TITLE: Check Record Existence with Laravel Query Builder
DESCRIPTION: Demonstrates how to efficiently check if records exist or do not exist in a database table using Laravel's `exists()` and `doesntExist()` methods, avoiding the overhead of `count()` for performance.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_12

LANGUAGE: php
CODE:
```
if (DB::table('orders')->where('finalized', 1)->exists()) {
    // ...
}

if (DB::table('orders')->where('finalized', 1)->doesntExist()) {
    // ...
}
```

----------------------------------------

TITLE: Applying Column Modifiers Before Foreign Key Constraint in Laravel
DESCRIPTION: Illustrates the importance of applying column modifiers, such as `nullable()`, before calling the `constrained()` method when defining foreign key constraints.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_114

LANGUAGE: php
CODE:
```
$table->foreignId('user_id')
    ->nullable()
    ->constrained();
```

----------------------------------------

TITLE: Redirecting Back with Flashed Input Data in Laravel
DESCRIPTION: Redirects the user to the previous location while flashing the current request's input data to the session. This is typically used after validation errors to repopulate forms.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_22

LANGUAGE: php
CODE:
```
return back()->withInput();
```

----------------------------------------

TITLE: Configuring Supervisor for Laravel Queue Workers (INI)
DESCRIPTION: Provides an example Supervisor configuration file for monitoring and managing multiple Laravel queue worker processes. It defines the command to run, automatic restart behavior, user, number of processes, and logging, ensuring robust and continuous queue operation.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_109

LANGUAGE: INI
CODE:
```
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /home/<USER>/app.com/artisan queue:work sqs --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=forge
numprocs=8
redirect_stderr=true
stdout_logfile=/home/<USER>/app.com/worker.log
stopwaitsecs=3600
```

----------------------------------------

TITLE: Issuing API Token with Specific Abilities (PHP)
DESCRIPTION: This example demonstrates creating an API token with assigned 'abilities' (scopes). The `createToken` method accepts an array of strings as its second argument, defining the permissions granted to the token.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_6

LANGUAGE: php
CODE:
```
return $user->createToken('token-name', ['server:update'])->plainTextToken;
```

----------------------------------------

TITLE: Apply Default Password Rules in Laravel Validation
DESCRIPTION: Once default password rules are defined, this example shows how to apply them to a specific validation field by simply calling 'Password::defaults()' without any arguments in your validation array.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_185

LANGUAGE: php
CODE:
```
'password' => ['required', Password::defaults()],
```

----------------------------------------

TITLE: Testing File Uploads with Laravel Storage (PHPUnit)
DESCRIPTION: This snippet illustrates how to test file uploads in Laravel using the `Storage::fake()` method within a PHPUnit test class. It covers simulating image uploads, asserting file presence or absence on the fake disk, and verifying file counts in directories. It depends on `Illuminate\Http\UploadedFile`, `Illuminate\Support\Facades\Storage`, and `Tests\TestCase`.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_61

LANGUAGE: PHP
CODE:
```
<?php

namespace Tests\Feature;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    public function test_albums_can_be_uploaded(): void
    {
        Storage::fake('photos');

        $response = $this->json('POST', '/photos', [
            UploadedFile::fake()->image('photo1.jpg'),
            UploadedFile::fake()->image('photo2.jpg')
        ]);

        // Assert one or more files were stored...
        Storage::disk('photos')->assertExists('photo1.jpg');
        Storage::disk('photos')->assertExists(['photo1.jpg', 'photo2.jpg']);

        // Assert one or more files were not stored...
        Storage::disk('photos')->assertMissing('missing.jpg');
        Storage::disk('photos')->assertMissing(['missing.jpg', 'non-existing.jpg']);

        // Assert that the number of files in a given directory matches the expected count...
        Storage::disk('photos')->assertCount('/wallpapers', 2);

        // Assert that a given directory is empty...
        Storage::disk('photos')->assertDirectoryEmpty('/wallpapers');
    }
}
```

----------------------------------------

TITLE: Encrypting Data with Laravel Crypt Facade in PHP
DESCRIPTION: This PHP snippet shows how to encrypt a user's DigitalOcean API token using `Crypt::encryptString`. It demonstrates storing an encrypted value in the database, ensuring sensitive data is protected. The `encryptString` method uses OpenSSL with AES-256-CBC and includes a MAC for integrity.
SOURCE: https://github.com/laravel/docs/blob/12.x/encryption.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class DigitalOceanTokenController extends Controller
{
    /**
     * Store a DigitalOcean API token for the user.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->user()->fill([
            'token' => Crypt::encryptString($request->token),
        ])->save();

        return redirect('/secrets');
    }
}
```

----------------------------------------

TITLE: Preventing Silent Discarding of Unfillable Attributes
DESCRIPTION: Demonstrates how to configure Laravel to throw an exception when attempting to mass assign an attribute not listed in `$fillable`. This method, typically called in `AppServiceProvider`'s `boot` method, helps identify mass assignment issues during local development.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_56

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Model::preventSilentlyDiscardingAttributes($this->app->isLocal());
}
```

----------------------------------------

TITLE: Configure Stripe API Keys
DESCRIPTION: Sets up Stripe API keys in the `.env` file for secure communication with the Stripe API. Includes public, secret, and webhook secret keys.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_6

LANGUAGE: ini
CODE:
```
STRIPE_KEY=your-stripe-key
STRIPE_SECRET=your-stripe-secret
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
```

----------------------------------------

TITLE: Authorizing Action or Throwing Exception with Gate
DESCRIPTION: This snippet demonstrates `Gate::authorize`, which attempts to authorize an action and automatically throws an `Illuminate\Auth\Access\AuthorizationException` if the user is not allowed. This exception is then converted to a 403 HTTP response by Laravel, simplifying error handling.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_5

LANGUAGE: PHP
CODE:
```
Gate::authorize('update-post', $post);

// The action is authorized...
```

----------------------------------------

TITLE: Preventing Job Overlaps with WithoutOverlapping Middleware - PHP
DESCRIPTION: This snippet illustrates how to use Laravel's `Illuminate\Queue\Middleware\WithoutOverlapping` middleware to prevent multiple instances of the same job from processing concurrently for a given key. Here, it prevents credit score update job overlaps for the same user ID, ensuring data consistency.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_23

LANGUAGE: PHP
CODE:
```
use Illuminate\Queue\Middleware\WithoutOverlapping;

/**
 * Get the middleware the job should pass through.
 *
 * @return array<int, object>
 */
public function middleware(): array
{
    return [new WithoutOverlapping($this->user->id)];
}
```

----------------------------------------

TITLE: Configuring Sanctum Stateful API Middleware in Laravel
DESCRIPTION: This PHP snippet demonstrates how to enable Sanctum's stateful API middleware in Laravel's `bootstrap/app.php` file. It instructs Laravel to allow incoming requests from SPAs to authenticate using session cookies, while still supporting API token authentication for other clients. This is crucial for cookie-based SPA authentication.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_17

LANGUAGE: php
CODE:
```
->withMiddleware(function (Middleware $middleware) {
    $middleware->statefulApi();
})
```

----------------------------------------

TITLE: Cast Model Attribute to PHP Enum in Laravel Eloquent
DESCRIPTION: Laravel Eloquent allows casting model attributes directly to PHP Enums. This snippet shows how to map the `status` attribute to the `ServerStatus` enum class within the model's `casts` method, enabling automatic conversion during attribute access and assignment.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-mutators.md#_snippet_26

LANGUAGE: PHP
CODE:
```
use App\Enums\ServerStatus;

/**
 * Get the attributes that should be cast.
 *
 * @return array<string, string>
 */
protected function casts(): array
{
    return [
        'status' => ServerStatus::class,
    ];
}
```

----------------------------------------

TITLE: Iterating Over Eloquent Collection - PHP
DESCRIPTION: This example shows how to iterate over an Eloquent collection using a standard PHP `foreach` loop. Since Eloquent collections implement PHP's iterable interfaces, they can be treated like arrays for simple iteration, allowing access to individual model properties.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_25

LANGUAGE: PHP
CODE:
```
foreach ($flights as $flight) {
    echo $flight->name;
}
```

----------------------------------------

TITLE: Basic Rendering of Blade Components
DESCRIPTION: This Blade syntax shows the standard way to render a component using its kebab-cased name prefixed with `x-`, assuming the component class is directly under `app/View/Components`.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_64

LANGUAGE: Blade
CODE:
```
<x-alert/>

<x-user-profile/>
```

----------------------------------------

TITLE: Implicit Model Binding in Laravel PHP (Function)
DESCRIPTION: This snippet shows Laravel's implicit model binding, where a type-hinted Eloquent model in a route closure automatically resolves to an instance matching the URI segment. If no model is found, a 404 response is generated.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_50

LANGUAGE: PHP
CODE:
```
use App\Models\User;

Route::get('/users/{user}', function (User $user) {
    return $user->email;
});
```

----------------------------------------

TITLE: Generating URL for a Named Route - PHP
DESCRIPTION: This snippet demonstrates how to generate a URL for a previously defined named route (`post.show`) using the `route` helper. It shows how to pass route parameters to construct the correct URL.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_7

LANGUAGE: PHP
CODE:
```
echo route('post.show', ['post' => 1]);

// http://example.com/post/1
```

----------------------------------------

TITLE: Accessing JSON Response Data with Laravel HTTP Client (PHP)
DESCRIPTION: Shows how to access specific data points from a JSON response returned by an HTTP request using array access directly on the response object.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_1

LANGUAGE: php
CODE:
```
return Http::get('http://example.com/users/1')['name'];
```

----------------------------------------

TITLE: Generating URL for Named Route with Eloquent Model - PHP
DESCRIPTION: This snippet demonstrates how to pass an Eloquent model directly as a parameter value to the `route` helper. Laravel automatically extracts the model's route key (typically the primary key) to construct the URL.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_11

LANGUAGE: PHP
CODE:
```
echo route('post.show', ['post' => $post]);
```

----------------------------------------

TITLE: Hiding Eloquent Model Attributes from JSON/Array in PHP
DESCRIPTION: Defines attributes that should be excluded from the model's array or JSON representation by adding them to the `$hidden` property. This is crucial for security, preventing sensitive data like passwords from being exposed.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-serialization.md#_snippet_6

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string>
     */
    protected $hidden = ['password'];
}
```

----------------------------------------

TITLE: Validate Current User Password (Laravel)
DESCRIPTION: The `current_password` rule verifies if the field under validation matches the authenticated user's password. An authentication guard can be specified as a parameter to check against a specific guard's password.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_85

LANGUAGE: PHP
CODE:
```
'password' => 'current_password:api'
```

----------------------------------------

TITLE: Checking Collection for Key-Value Pair (PHP)
DESCRIPTION: The `contains` method can determine if a specific key/value pair exists within the collection, useful for checking associative arrays or objects within the collection.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_24

LANGUAGE: php
CODE:
```
$collection = collect([
    ['product' => 'Desk', 'price' => 200],
    ['product' => 'Chair', 'price' => 100]
]);

$collection->contains('product', 'Bookcase');

// false
```

----------------------------------------

TITLE: Authorizing Any Action with @canany in Laravel Blade
DESCRIPTION: This Blade snippet demonstrates the `@canany` directive, which checks if the current user is authorized to perform *any* action from a given array of abilities. It can be used with a model instance (e.g., `['update', 'view', 'delete']` for `$post`) or a class name (e.g., `['create']` for `\App\Models\Post::class`), providing flexible conditional rendering for multiple authorization checks.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_38

LANGUAGE: Blade
CODE:
```
@canany(['update', 'view', 'delete'], $post)
    <!-- The current user can update, view, or delete the post... -->
@elsecanany(['create'], \App\Models\Post::class)
    <!-- The current user can create a post... -->
@endcanany
```

----------------------------------------

TITLE: Laravel Form Request: Authorize Resource Access via Route Model Binding
DESCRIPTION: Demonstrates a concise way to authorize requests when Laravel's route model binding is in use. The resolved model is directly accessible as a property of the request, simplifying authorization checks against user permissions.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_24

LANGUAGE: php
CODE:
```
return $this->user()->can('update', $this->comment);
```

----------------------------------------

TITLE: Safely Stream and Update Records Lazily with lazyById
DESCRIPTION: Recommends `lazyById` or `lazyByIdDesc` for updating records while iterating lazily, as they paginate based on the primary key, ensuring consistent results. The example updates 'active' status.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_9

LANGUAGE: php
CODE:
```
DB::table('users')->where('active', false)
    ->lazyById()->each(function (object $user) {
        DB::table('users')
            ->where('id', $user->id)
            ->update(['active' => true]);
    });
```

----------------------------------------

TITLE: Generate Passport Encryption Keys for Deployment
DESCRIPTION: When deploying Passport to your application's servers for the first time, run the `passport:keys` command. This command generates the encryption keys Passport needs to generate access tokens, and these keys are not typically kept in source control.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_3

LANGUAGE: shell
CODE:
```
php artisan passport:keys
```

----------------------------------------

TITLE: Filtering Collection by Key-Value Pair with `where` Method in PHP
DESCRIPTION: Demonstrates the `where` method, which filters a collection based on a specified key and value. It uses 'loose' comparisons by default. The method returns a new collection containing only the items that match the criteria.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_178

LANGUAGE: PHP
CODE:
```
$collection = collect([
    ['product' => 'Desk', 'price' => 200],
    ['product' => 'Chair', 'price' => 100],
    ['product' => 'Bookcase', 'price' => 150],
    ['product' => 'Door', 'price' => 100],
]);

$filtered = $collection->where('price', 100);

$filtered->all();

/*
    [
        ['product' => 'Chair', 'price' => 100],
        ['product' => 'Door', 'price' => 100],
    ]
*/
```

----------------------------------------

TITLE: Create a new basic subscription for a user in Laravel Cashier
DESCRIPTION: To create a new subscription, retrieve a billable model instance (e.g., `App\Models\User`) and use the `newSubscription` method. The first argument is an internal subscription type (e.g., 'default'), and the second is the Stripe price identifier. The `create` method initiates the subscription and updates the database with billing information.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_61

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::post('/user/subscribe', function (Request $request) {
    $request->user()->newSubscription(
        'default', 'price_monthly'
    )->create($request->paymentMethodId);

    // ...
});
```

----------------------------------------

TITLE: Issuing a New API Token via Route (PHP)
DESCRIPTION: This code defines a POST route to create a new API token for the authenticated user. It uses the `createToken` method, passing a token name from the request, and returns the plain-text token value which should be immediately displayed to the user.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_4

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::post('/tokens/create', function (Request $request) {
    $token = $request->user()->createToken($request->token_name);

    return ['token' => $token->plainTextToken];
});
```

----------------------------------------

TITLE: Intercepting Gate Checks with Gate::before in PHP
DESCRIPTION: This snippet defines a `before` closure for Laravel's Gate facade. This closure is executed before any other authorization checks. If the authenticated user is an administrator, it immediately grants `true` for any ability, bypassing further checks. The closure receives the `User` instance and the `ability` string.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_12

LANGUAGE: php
CODE:
```
use App\Models\User;
use Illuminate\Support\Facades\Gate;

Gate::before(function (User $user, string $ability) {
    if ($user->isAdministrator()) {
        return true;
    }
});
```

----------------------------------------

TITLE: Create Custom HTTP Response Objects in Laravel
DESCRIPTION: Illustrates how to return a full `Illuminate\Http\Response` instance to gain more control over the HTTP response, such as setting the status code and custom headers. This allows for more complex response customization beyond simple strings or arrays.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_1

LANGUAGE: php
CODE:
```
Route::get('/home', function () {
    return response('Hello World', 200)
        ->header('Content-Type', 'text/plain');
});
```

----------------------------------------

TITLE: Listing All Available Artisan Commands
DESCRIPTION: This command displays a comprehensive list of all Artisan commands registered within the Laravel application, providing an overview of available functionalities and their descriptions.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_0

LANGUAGE: shell
CODE:
```
php artisan list
```

----------------------------------------

TITLE: Applying a Layout Blade Component to a View (Blade)
DESCRIPTION: This Blade snippet shows how to apply a previously defined `layout` component to a view (`tasks.blade.php`). The content within the `<x-layout>` tags is automatically injected into the `$slot` variable of the layout component.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_130

LANGUAGE: Blade
CODE:
```
<!-- resources/views/tasks.blade.php -->

<x-layout>
    @foreach ($tasks as $task)
        <div>{{ $task }}</div>
    @endforeach
</x-layout>
```

----------------------------------------

TITLE: Demonstrating N+1 Issue - Eloquent Polymorphic - PHP
DESCRIPTION: This PHP example illustrates an N+1 query problem that occurs when accessing the polymorphic parent (`commentable`) from within a loop over eager-loaded child models (`comments`). Although comments are eager loaded, the parent model is fetched individually for each comment, leading to excessive queries.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_63

LANGUAGE: php
CODE:
```
$posts = Post::with('comments')->get();

foreach ($posts as $post) {
    foreach ($post->comments as $comment) {
        echo $comment->commentable->title;
    }
}
```

----------------------------------------

TITLE: Defining Authenticated API Route with Sanctum in Laravel PHP
DESCRIPTION: This snippet defines an API route in `routes/api.php` that returns the authenticated user. Routes in `api.php` are stateless and automatically prefixed with `/api`. The `middleware('auth:sanctum')` ensures that only authenticated requests using Laravel Sanctum can access this route.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_3

LANGUAGE: php
CODE:
```
Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');
```

----------------------------------------

TITLE: Retrieve Data with `data_get()` in PHP
DESCRIPTION: The `data_get` function retrieves a value from a nested array or object using 'dot' notation. It supports default values for missing keys, wildcards for multiple matches, and special placeholders like `{first}` and `{last}` to access specific array elements.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_59

LANGUAGE: php
CODE:
```
$data = ['products' => ['desk' => ['price' => 100]]];

$price = data_get($data, 'products.desk.price');

// 100
```

LANGUAGE: php
CODE:
```
$discount = data_get($data, 'products.desk.discount', 0);

// 0
```

LANGUAGE: php
CODE:
```
$data = [
    'product-one' => ['name' => 'Desk 1', 'price' => 100],
    'product-two' => ['name' => 'Desk 2', 'price' => 150],
];

data_get($data, '*.name');

// ['Desk 1', 'Desk 2'];
```

LANGUAGE: php
CODE:
```
$flight = [
    'segments' => [
        ['from' => 'LHR', 'departure' => '9:00', 'to' => 'IST', 'arrival' => '15:00'],
        ['from' => 'IST', 'departure' => '16:00', 'to' => 'PKX', 'arrival' => '20:00'],
    ],
];

data_get($flight, 'segments.{first}.arrival');

// 15:00
```

----------------------------------------

TITLE: Asserting Specific User Authentication in Laravel PHP
DESCRIPTION: This method asserts that a particular user instance is currently authenticated. It can optionally check authentication against a specific guard. This is vital for verifying that the correct user is logged in after an authentication attempt.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_128

LANGUAGE: PHP
CODE:
```
$this->assertAuthenticatedAs($user, $guard = null);
```

----------------------------------------

TITLE: Caching Laravel Configuration
DESCRIPTION: This Artisan command combines all Laravel configuration files into a single, cached file. This significantly reduces the number of filesystem reads required to load configuration values, improving application performance in production environments. It's crucial to only use `env()` within configuration files when this cache is active.
SOURCE: https://github.com/laravel/docs/blob/12.x/deployment.md#_snippet_4

LANGUAGE: Shell
CODE:
```
php artisan config:cache
```

----------------------------------------

TITLE: Laravel Unique Validation: Basic Usage with Table, Model, and Connection
DESCRIPTION: Demonstrates the fundamental usage of Laravel's `unique` validation rule, showing how to specify the target database table, use an Eloquent model to infer the table, and define a custom database connection for the validation query.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_163

LANGUAGE: PHP
CODE:
```
'email' => 'unique:App\Models\User,email_address'
```

LANGUAGE: PHP
CODE:
```
'email' => 'unique:users,email_address'
```

LANGUAGE: PHP
CODE:
```
'email' => 'unique:connection.users,email_address'
```

----------------------------------------

TITLE: Running Parallel Tests with Custom Process Count
DESCRIPTION: This command runs tests in parallel using exactly four processes. It allows fine-grained control over the number of concurrent test runners, overriding the default CPU core count.
SOURCE: https://github.com/laravel/docs/blob/12.x/testing.md#_snippet_10

LANGUAGE: Shell
CODE:
```
php artisan test --parallel --processes=4
```

----------------------------------------

TITLE: Asserting Redirect Response in Laravel PHP
DESCRIPTION: Asserts that the HTTP response is a redirect, optionally to a specific URI. This is used to confirm that the application is redirecting the user.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_96

LANGUAGE: php
CODE:
```
$response->assertRedirect($uri = null);
```

----------------------------------------

TITLE: Asserting JSON Path Values
DESCRIPTION: This example illustrates `assertJsonPath` for verifying specific data at a nested path within a JSON response. It allows targeting deeply nested properties using dot notation (e.g., 'team.owner.name').
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_27

LANGUAGE: Pest
CODE:
```
<?php

test('asserting a json path value', function () {
    $response = $this->postJson('/user', ['name' => 'Sally']);

    $response
        ->assertStatus(201)
        ->assertJsonPath('team.owner.name', 'Darian');
});
```

LANGUAGE: PHPUnit
CODE:
```
<?php

namespace TestsFeature;

use TestsTestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic functional test example.
     */
    public function test_asserting_a_json_paths_value(): void
    {
        $response = $this->postJson('/user', ['name' => 'Sally']);

        $response
            ->assertStatus(201)
            ->assertJsonPath('team.owner.name', 'Darian');
    }
}
```

----------------------------------------

TITLE: Setting Up Laravel Scheduler Cron Job
DESCRIPTION: To execute Laravel's scheduled tasks on a server, a single cron entry is required. This cron job runs the `schedule:run` Artisan command every minute, which then evaluates and dispatches any tasks due to run based on the server's current time.
SOURCE: https://github.com/laravel/docs/blob/12.x/scheduling.md#_snippet_23

LANGUAGE: shell
CODE:
```
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

----------------------------------------

TITLE: Defining Named Route with Closure in Laravel
DESCRIPTION: This code demonstrates how to assign a unique name, 'profile', to a route defined by a closure. Named routes allow for convenient URL generation and redirects using the route's name instead of its URI.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_39

LANGUAGE: PHP
CODE:
```
Route::get('/user/profile', function () {
    // ...
})->name('profile');
```

----------------------------------------

TITLE: Customizing Validator Error Messages in Laravel
DESCRIPTION: Illustrates how to provide custom error messages directly to the Validator::make method, replacing default Laravel messages. Placeholders like ':attribute' are automatically replaced with field names.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_36

LANGUAGE: php
CODE:
```
$validator = Validator::make($input, $rules, $messages = [
    'required' => 'The :attribute field is required.',
]);
```

----------------------------------------

TITLE: Create Stripe Webhook with Laravel Cashier Artisan Command
DESCRIPTION: Cashier provides a convenient Artisan command to create a Stripe webhook that listens to all events necessary for Cashier's functionality. By default, the webhook points to the `APP_URL` environment variable, but you can specify a custom URL, a different Stripe API version, or create a disabled webhook using command options.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_142

LANGUAGE: shell
CODE:
```
php artisan cashier:webhook
php artisan cashier:webhook --url "https://example.com/stripe/webhook"
php artisan cashier:webhook --api-version="2019-12-03"
php artisan cashier:webhook --disabled
```

----------------------------------------

TITLE: Making a Basic HTTP Request with Pest
DESCRIPTION: This example illustrates how to perform a simple GET request to the application's root path using Pest and then verify that the response status is 200 (OK). Test request methods return an `Illuminate\Testing\TestResponse` instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_2

LANGUAGE: PHP
CODE:
```
<?php

test('basic request', function () {
    $response = $this->get('/');

    $response->assertStatus(200);
});
```

----------------------------------------

TITLE: Displaying Validation Errors with Blade's @error Directive
DESCRIPTION: The `@error` Blade directive allows quick determination and display of validation error messages for a given attribute. It echoes the `$message` variable to show the error. This snippet demonstrates its basic usage for a 'title' attribute and also how to use it with a named error bag.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_9

LANGUAGE: blade
CODE:
```
<!-- /resources/views/post/create.blade.php -->

<label for="title">Post Title</label>

<input
    id="title"
    type="text"
    name="title"
    class="@error('title') is-invalid @enderror"
/>

@error('title')
    <div class="alert alert-danger">{{ $message }}</div>
@enderror
```

LANGUAGE: blade
CODE:
```
<input ... class="@error('title', 'post') is-invalid @enderror">
```

----------------------------------------

TITLE: Generating HTTP Exceptions with Laravel's Abort Helper
DESCRIPTION: This simple snippet demonstrates how to use Laravel's `abort` helper function to immediately generate an HTTP exception with a specified status code, such as 404 (Not Found). This helper can be called from anywhere in your application to trigger an HTTP error response.
SOURCE: https://github.com/laravel/docs/blob/12.x/errors.md#_snippet_23

LANGUAGE: php
CODE:
```
abort(404);
```

----------------------------------------

TITLE: Manage Personal Access Tokens for a User
DESCRIPTION: Illustrates how to create, retrieve, and filter personal access tokens for a user, including examples for tokens with and without scopes, and retrieving valid, unrevoked tokens.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_47

LANGUAGE: php
CODE:
```
use App\Models\User;
use Illuminate\Support\Facades\Date;
use Laravel\Passport\Token;

$user = User::find($userId);

// Creating a token without scopes...
$token = $user->createToken('My Token')->accessToken;

// Creating a token with scopes...
$token = $user->createToken('My Token', ['user:read', 'orders:create'])->accessToken;

// Creating a token with all scopes...
$token = $user->createToken('My Token', ['*'])->accessToken;

// Retrieving all the valid personal access tokens that belong to the user...
$tokens = $user->tokens()
    ->with('client')
    ->where('revoked', false)
    ->where('expires_at', '>', Date::now())
    ->get()
    ->filter(fn (Token $token) => $token->client->hasGrantType('personal_access'));
```

----------------------------------------

TITLE: Revoking Sanctum API Tokens (PHP)
DESCRIPTION: This snippet provides examples for revoking Sanctum API tokens. It demonstrates how to delete all tokens for a user, revoke the specific token used for the current request, or revoke a token by its ID, leveraging the `tokens` relationship from the `HasApiTokens` trait.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_13

LANGUAGE: php
CODE:
```
// Revoke all tokens...
$user->tokens()->delete();

// Revoke the token that was used to authenticate the current request...
$request->user()->currentAccessToken()->delete();

// Revoke a specific token...
$user->tokens()->where('id', $tokenId)->delete();
```

----------------------------------------

TITLE: Performing Upsert Operations with Eloquent
DESCRIPTION: Illustrates the `upsert` method for atomically updating or creating records. The first argument is an array of values, the second specifies unique columns for identification, and the third lists columns to update if a match is found. Timestamps are automatically handled.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_57

LANGUAGE: PHP
CODE:
```
Flight::upsert([
    ['departure' => 'Oakland', 'destination' => 'San Diego', 'price' => 99],
    ['departure' => 'Chicago', 'destination' => 'New York', 'price' => 150]
], uniqueBy: ['departure', 'destination'], update: ['price']);
```

----------------------------------------

TITLE: Retrieving Input with Default Value (PHP)
DESCRIPTION: This PHP snippet demonstrates retrieving an input value using the `input()` method, providing a default value ('Sally') as the second argument. If the 'name' input is not present in the request, the specified default value will be returned, ensuring a fallback and preventing potential errors.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_27

LANGUAGE: php
CODE:
```
$name = $request->input('name', 'Sally');
```

----------------------------------------

TITLE: Overriding Built-in Laravel/Symfony Exception Rendering
DESCRIPTION: This example shows how to override the rendering behavior for built-in Laravel or Symfony exceptions, such as `Symfony\Component\HttpKernel\Exception\NotFoundHttpException`. It allows for conditional rendering, like returning a JSON response for API requests, while falling back to Laravel's default rendering if no value is returned from the closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/errors.md#_snippet_12

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

->withExceptions(function (Exceptions $exceptions) {
    $exceptions->render(function (NotFoundHttpException $e, Request $request) {
        if ($request->is('api/*')) {
            return response()->json([
                'message' => 'Record not found.'
            ], 404);
        }
    });
})
```

----------------------------------------

TITLE: Loading Multiple Assets with Vite Blade Directive
DESCRIPTION: Demonstrates how to use the `@vite` Blade directive in a Laravel template's `<head>` section to load multiple JavaScript and CSS entry points configured in Vite. This directive automatically handles development server HMR and production builds.
SOURCE: https://github.com/laravel/docs/blob/12.x/vite.md#_snippet_6

LANGUAGE: Blade
CODE:
```
<!DOCTYPE html>
<head>
    {{-- ... --}}

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
```

----------------------------------------

TITLE: Accessing JSON Response Data as Array
DESCRIPTION: This example shows how to conveniently access individual values from a JSON response by treating the `$response` object as an array. This allows for direct inspection and assertion against specific keys within the JSON payload.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_25

LANGUAGE: Pest
CODE:
```
expect($response['created'])->toBeTrue();
```

LANGUAGE: PHPUnit
CODE:
```
$this->assertTrue($response['created']);
```

----------------------------------------

TITLE: Sending GET Request with Query Parameters (Array) - Laravel HTTP Client - PHP
DESCRIPTION: Demonstrates sending a GET request using the Laravel HTTP client by passing an array of key-value pairs as the second argument to the `get` method. This array is automatically converted into URL query parameters.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_5

LANGUAGE: php
CODE:
```
$response = Http::get('http://example.com/users', [
    'name' => 'Taylor',
    'page' => 1,
]);
```

----------------------------------------

TITLE: Storing Uploaded Files to a Specific Disk using store
DESCRIPTION: This snippet illustrates how to store an uploaded file to a specific disk (e.g., 's3') using the `store` method. The disk name is passed as the second argument, overriding the default disk configured in `filesystems.php`.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_46

LANGUAGE: php
CODE:
```
$path = $request->file('avatar')->store(
    'avatars/'.$request->user()->id, 's3'
);
```

----------------------------------------

TITLE: Excluding Paddle Webhook Routes from CSRF (PHP)
DESCRIPTION: This configuration snippet, typically placed in `bootstrap/app.php`, excludes routes starting with `paddle/*` from Laravel's CSRF protection. This is crucial for allowing Paddle's incoming webhook requests to bypass CSRF verification and be processed correctly by Cashier's webhook controller.
SOURCE: https://github.com/laravel/docs/blob/12.x/cashier-paddle.md#_snippet_96

LANGUAGE: PHP
CODE:
```
->withMiddleware(function (Middleware $middleware) {
    $middleware->validateCsrfTokens(except: [
        'paddle/*',
    ]);
})
```

----------------------------------------

TITLE: Generate CSRF Hidden Input Field in Laravel Blade
DESCRIPTION: The `csrf_field` function generates an HTML hidden input field containing the current CSRF token's value. This is typically used within HTML forms to protect against cross-site request forgery attacks, especially when using Blade templating.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_114

LANGUAGE: blade
CODE:
```
{{ csrf_field() }}
```

----------------------------------------

TITLE: Perform Basic HTTP Redirect in Laravel
DESCRIPTION: Shows the simplest way to generate a `RedirectResponse` using the global `redirect` helper, directing the user to a specified URL.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_12

LANGUAGE: PHP
CODE:
```
Route::get('/dashboard', function () {
    return redirect('/home/<USER>');
});
```

----------------------------------------

TITLE: Re-hydrating an Existing Model with refresh() (PHP)
DESCRIPTION: Shows how to re-hydrate an existing model instance with fresh data from the database using the `refresh()` method. This method updates the *current* model instance and its loaded relationships, discarding any unsaved changes made to its attributes.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_23

LANGUAGE: PHP
CODE:
```
$flight = Flight::where('number', 'FR 900')->first();

$flight->number = 'FR 456';

$flight->refresh();

$flight->number; // "FR 900"
```

----------------------------------------

TITLE: Lazy Eager Loading Missing Relationships (PHP)
DESCRIPTION: Illustrates how to load a relationship only if it has not already been loaded on the model instance. Useful to avoid redundant queries. Requires a model instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_132

LANGUAGE: php
CODE:
```
$book->loadMissing('author');
```

----------------------------------------

TITLE: Defining Private Channel Authorization Callbacks in Laravel (PHP)
DESCRIPTION: This snippet illustrates how to define authorization logic for private broadcast channels using `Broadcast::channel`. The callback receives the authenticated user and any wildcard parameters from the channel name (e.g., `orderId`), returning `true` if the user is authorized to subscribe to the channel, and `false` otherwise. This secures access to sensitive channel data.
SOURCE: https://github.com/laravel/docs/blob/12.x/broadcasting.md#_snippet_42

LANGUAGE: php
CODE:
```
use AppModelsUser;

Broadcast::channel('orders.{orderId}', function (User $user, int $orderId) {
    return $user->id === Order::findOrNew($orderId)->user_id;
});
```

----------------------------------------

TITLE: Defining a Policy Filter (before method) in Laravel
DESCRIPTION: This PHP snippet illustrates how to implement a `before` method within a Laravel policy. This method executes prior to any other policy methods, allowing for global authorization checks, such as granting full access to administrators. Returning `true` authorizes the action immediately, while `null` allows the check to proceed to the specific policy method.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_27

LANGUAGE: PHP
CODE:
```
use AppModelsUser;

/**
 * Perform pre-authorization checks.
 */
public function before(User $user, string $ability): bool|null
{
    if ($user->isAdministrator()) {
        return true;
    }

    return null;
}
```

----------------------------------------

TITLE: Getting First Element from Laravel Collection (PHP)
DESCRIPTION: This snippet demonstrates retrieving the very first element of a Laravel Collection without any arguments. If the collection is empty, the method returns `null`.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_49

LANGUAGE: PHP
CODE:
```
collect([1, 2, 3, 4])->first();

// 1
```

----------------------------------------

TITLE: Defining a Policy Method with Additional Context in PHP
DESCRIPTION: Illustrates a `PostPolicy` method that accepts an additional `$category` parameter, allowing for more granular authorization decisions based on extra context beyond the user and the primary model.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_40

LANGUAGE: php
CODE:
```
/**
 * Determine if the given post can be updated by the user.
 */
public function update(User $user, Post $post, int $category): bool
{
    return $user->id === $post->user_id &&
           $user->canUpdateCategory($category);
}
```

----------------------------------------

TITLE: Including Soft Deleted Models in Eloquent Query - PHP
DESCRIPTION: Demonstrates how to include soft-deleted models in query results using the `withTrashed` method on an Eloquent model. This method overrides the default behavior of excluding soft-deleted records.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_71

LANGUAGE: php
CODE:
```
use App\Models\Flight;

$flights = Flight::withTrashed()
    ->where('account_id', 1)
    ->get();
```

----------------------------------------

TITLE: Retrieve Single Column Values with pluck
DESCRIPTION: Demonstrates how to use the `pluck` method on `DB::table` to retrieve an `Illuminate\Support\Collection` of values from a single column. The example shows iterating over the collected titles.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_2

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

$titles = DB::table('users')->pluck('title');

foreach ($titles as $title) {
    echo $title;
}
```

----------------------------------------

TITLE: Displaying Laravel Validation Errors in Blade View
DESCRIPTION: Shows a Blade template snippet for displaying validation error messages to the user. The `$errors` variable, an instance of `Illuminate\Support\MessageBag`, is automatically shared with all views, allowing easy iteration and display of errors.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!-- /resources/views/post/create.blade.php -->

<h1>Create Post</h1>

@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<!-- Create Post Form -->
```

----------------------------------------

TITLE: Conditional Rendering with @can and @cannot in Laravel Blade
DESCRIPTION: This Blade snippet demonstrates using the `@can` and `@cannot` directives for conditional rendering based on user authorization. It checks if the current user can 'update' a specific `$post` or 'create' a `Post` (using the class name). Content within these directives is only rendered if the authorization check passes or fails, respectively, providing a clean way to control UI visibility.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_36

LANGUAGE: Blade
CODE:
```
@can('update', $post)
    <!-- The current user can update the post... -->
@elsecan('create', App\Models\Post::class)
    <!-- The current user can create new posts... -->
@else
    <!-- ... -->
@endcan

@cannot('update', $post)
    <!-- The current user cannot update the post... -->
@elsecannot('create', App\Models\Post::class)
    <!-- The current user cannot create new posts... -->
@endcannot
```

----------------------------------------

TITLE: Laravel Middleware for Pre-Request Processing
DESCRIPTION: This middleware demonstrates how to execute logic before the HTTP request is passed deeper into the application. The `// Perform action` comment indicates where the pre-processing code would be placed.
SOURCE: https://github.com/laravel/docs/blob/12.x/middleware.md#_snippet_2

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BeforeMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        // Perform action

        return $next($request);
    }
}
```

----------------------------------------

TITLE: Example of Malicious CSRF Form Submission (HTML/Blade)
DESCRIPTION: This snippet demonstrates a hypothetical malicious HTML form that could be used in a CSRF attack. It shows how an attacker might craft a form to automatically submit a POST request to a target application, changing a user's email without their explicit consent, if CSRF protection is absent.
SOURCE: https://github.com/laravel/docs/blob/12.x/csrf.md#_snippet_0

LANGUAGE: blade
CODE:
```
<form action="https://your-application.com/user/email" method="POST">
    <input type="email" value="<EMAIL>">
</form>

<script>
    document.forms[0].submit();
</script>
```

----------------------------------------

TITLE: Configuring Markdown Security to Prevent XSS in Laravel
DESCRIPTION: This snippet demonstrates how to configure Markdown to prevent Cross-Site Scripting (XSS) vulnerabilities. By default, Markdown supports raw HTML, which can be dangerous with user input. The `html_input` option can be set to 'strip' or 'escape' to sanitize HTML, and `allow_unsafe_links` can control link safety. For more complex scenarios, an HTML Purifier should be used.
SOURCE: https://github.com/laravel/docs/blob/12.x/strings.md#_snippet_137

LANGUAGE: php
CODE:
```
use Illuminate\Support\Str;

Str::of('Inject: <script>alert("Hello XSS!");</script>')->markdown([
    'html_input' => 'strip',
    'allow_unsafe_links' => false,
]);

// <p>Inject: alert(&quot;Hello XSS!&quot;);</p>
```

----------------------------------------

TITLE: Checking Request Path Pattern in Laravel (PHP)
DESCRIPTION: Illustrates using the `is()` method to check if the incoming request path matches a specified pattern, supporting wildcards (`*`). This example verifies if the path starts with `admin/`, useful for conditional logic based on URL segments.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_5

LANGUAGE: php
CODE:
```
if ($request->is('admin/*')) {
    // ...
}
```

----------------------------------------

TITLE: Syncing Many To Many Relationships with sync in PHP
DESCRIPTION: Shows how to use the `sync` method to manage many-to-many relationships. It accepts an array of IDs; any IDs not in the array are detached, and those in the array are attached, resulting in the intermediate table containing only the specified IDs.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_153

LANGUAGE: php
CODE:
```
$user->roles()->sync([1, 2, 3]);
```

----------------------------------------

TITLE: Installing Laravel Horizon via Composer (Shell)
DESCRIPTION: This command installs the Laravel Horizon package into your project using Composer. It's the first step to integrate Horizon for managing Redis queues.
SOURCE: https://github.com/laravel/docs/blob/12.x/horizon.md#_snippet_0

LANGUAGE: shell
CODE:
```
composer require laravel/horizon
```

----------------------------------------

TITLE: Publishing Telescope Assets and Migrating Database
DESCRIPTION: After installing the Composer package, these Artisan commands publish Telescope's configuration and assets, and then run database migrations to create the necessary tables for storing Telescope's data.
SOURCE: https://github.com/laravel/docs/blob/12.x/telescope.md#_snippet_1

LANGUAGE: shell
CODE:
```
php artisan telescope:install

php artisan migrate
```

----------------------------------------

TITLE: Deleting a Model Instance
DESCRIPTION: Shows how to delete a specific model instance by calling the `delete` method on it. This requires retrieving the model from the database first. This method dispatches the `deleting` and `deleted` model events.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_58

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

$flight = Flight::find(1);

$flight->delete();
```

----------------------------------------

TITLE: Running Insert Statements with Laravel DB
DESCRIPTION: This snippet shows how to use the `insert` method on the `DB` facade to execute an SQL `insert` statement. It accepts the SQL query as the first argument and an array of bindings as the second.
SOURCE: https://github.com/laravel/docs/blob/12.x/database.md#_snippet_9

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

DB::insert('insert into users (id, name) values (?, ?)', [1, 'Marc']);
```

----------------------------------------

TITLE: Testing Mailable Content Assertions in Laravel
DESCRIPTION: This snippet demonstrates how to use Laravel's built-in assertion methods to test various aspects of a Mailable, including recipients (from, to, cc, bcc, reply-to), subject, tags, metadata, HTML and plain text content, and attachments. Examples are provided for both Pest and PHPUnit testing frameworks.
SOURCE: https://github.com/laravel/docs/blob/12.x/mail.md#_snippet_67

LANGUAGE: php
CODE:
```
use App\Mail\InvoicePaid;\nuse App\Models\User;\n\ntest('mailable content', function () {\n    $user = User::factory()->create();\n\n    $mailable = new InvoicePaid($user);\n\n    $mailable->assertFrom('<EMAIL>');\n    $mailable->assertTo('<EMAIL>');\n    $mailable->assertHasCc('<EMAIL>');\n    $mailable->assertHasBcc('<EMAIL>');\n    $mailable->assertHasReplyTo('<EMAIL>');\n    $mailable->assertHasSubject('Invoice Paid');\n    $mailable->assertHasTag('example-tag');\n    $mailable->assertHasMetadata('key', 'value');\n\n    $mailable->assertSeeInHtml($user->email);\n    $mailable->assertSeeInHtml('Invoice Paid');\n    $mailable->assertSeeInOrderInHtml(['Invoice Paid', 'Thanks']);\n\n    $mailable->assertSeeInText($user->email);\n    $mailable->assertSeeInOrderInText(['Invoice Paid', 'Thanks']);\n\n    $mailable->assertHasAttachment('/path/to/file');\n    $mailable->assertHasAttachment(Attachment::fromPath('/path/to/file'));\n    $mailable->assertHasAttachedData($pdfData, 'name.pdf', ['mime' => 'application/pdf']);\n    $mailable->assertHasAttachmentFromStorage('/path/to/file', 'name.pdf', ['mime' => 'application/pdf']);\n    $mailable->assertHasAttachmentFromStorageDisk('s3', '/path/to/file', 'name.pdf', ['mime' => 'application/pdf']);\n});
```

LANGUAGE: php
CODE:
```
use App\Mail\InvoicePaid;\nuse App\Models\User;\n\npublic function test_mailable_content(): void\n{\n    $user = User::factory()->create();\n\n    $mailable = new InvoicePaid($user);\n\n    $mailable->assertFrom('<EMAIL>');\n    $mailable->assertTo('<EMAIL>');\n    $mailable->assertHasCc('<EMAIL>');\n    $mailable->assertHasBcc('<EMAIL>');\n    $mailable->assertHasReplyTo('<EMAIL>');\n    $mailable->assertHasSubject('Invoice Paid');\n    $mailable->assertHasTag('example-tag');\n    $mailable->assertHasMetadata('key', 'value');\n\n    $mailable->assertSeeInHtml($user->email);\n    $mailable->assertSeeInHtml('Invoice Paid');\n    $mailable->assertSeeInOrderInHtml(['Invoice Paid', 'Thanks']);\n\n    $mailable->assertSeeInText($user->email);\n    $mailable->assertSeeInOrderInText(['Invoice Paid', 'Thanks']);\n\n    $mailable->assertHasAttachment('/path/to/file');\n    $mailable->assertHasAttachment(Attachment::fromPath('/path/to/file'));\n    $mailable->assertHasAttachedData($pdfData, 'name.pdf', ['mime' => 'application/pdf']);\n    $mailable->assertHasAttachmentFromStorage('/path/to/file', 'name.pdf', ['mime' => 'application/pdf']);\n    $mailable->assertHasAttachmentFromStorageDisk('s3', '/path/to/file', 'name.pdf', ['mime' => 'application/pdf']);\n}
```

----------------------------------------

TITLE: Eager Loading Multiple Eloquent Relationships (PHP)
DESCRIPTION: Demonstrates how to eager load multiple relationships (`author` and `publisher`) on a model by passing an array of relationship names to the `with` method.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_117

LANGUAGE: php
CODE:
```
$books = Book::with(['author', 'publisher'])->get();
```

----------------------------------------

TITLE: Acquiring and Auto-Releasing Atomic Locks with Closure (PHP)
DESCRIPTION: The `get` method for atomic locks can accept a closure. When a closure is provided, Laravel automatically releases the lock after the closure has been executed, simplifying lock management and ensuring release even if errors occur within the closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/cache.md#_snippet_31

LANGUAGE: PHP
CODE:
```
Cache::lock('foo', 10)->get(function () {
    // Lock acquired for 10 seconds and automatically released...
});
```

----------------------------------------

TITLE: Eager Loading Multiple Nested Eloquent Relationships (PHP)
DESCRIPTION: Illustrates an alternative syntax for eager loading multiple nested relationships by providing a nested array structure to the `with` method, allowing specification of multiple relationships under a parent relationship.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_119

LANGUAGE: php
CODE:
```
$books = Book::with([
    'author' => [
        'contacts',
        'publisher',
    ],
])->get();
```

----------------------------------------

TITLE: Eager Loading Nested Relationships on MorphTo Relationship (PHP)
DESCRIPTION: Demonstrates how to eager load a `morphTo` relationship (`parentable`) and specific nested relationships for each potential parent type (`Event` with `calendar`, `Photo` with `tags`, `Post` with `author`) using the `morphWith` method within the `with` closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_121

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Relations\MorphTo;

$activities = ActivityFeed::query()
    ->with(['parentable' => function (MorphTo $morphTo) {
        $morphTo->morphWith([
            Event::class => ['calendar'],
            Photo::class => ['tags'],
            Post::class => ['author'],
        ]);
    }])->get();
```

----------------------------------------

TITLE: Running Vite Development and Build Commands
DESCRIPTION: Provides the standard npm commands for running the Vite development server (`npm run dev`) for local development with HMR and building production-ready assets (`npm run build`) for deployment.
SOURCE: https://github.com/laravel/docs/blob/12.x/vite.md#_snippet_10

LANGUAGE: Shell
CODE:
```
# Run the Vite development server...
npm run dev

# Build and version the assets for production...
npm run build
```

----------------------------------------

TITLE: Dispatching Events After Database Commit - PHP
DESCRIPTION: This snippet demonstrates how to configure an event to be dispatched only after the active database transaction has successfully committed. This is achieved by implementing the `ShouldDispatchAfterCommit` interface on the event class, ensuring transactional integrity.
SOURCE: https://github.com/laravel/docs/blob/12.x/events.md#_snippet_30

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderShipped implements ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Order $order,
    ) {}
}
```

----------------------------------------

TITLE: Applying Name Prefixes to Route Groups in Laravel PHP
DESCRIPTION: This example demonstrates how to prefix route names within a group using the `name` method. This helps in organizing and referencing routes programmatically, ensuring unique and structured route names.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_49

LANGUAGE: PHP
CODE:
```
Route::name('admin.')->group(function () {
    Route::get('/users', function () {
        // Route assigned name "admin.users"...
    })->name('users');
});
```

----------------------------------------

TITLE: Creating Model with Mass Assignment (Initial)
DESCRIPTION: Demonstrates the basic usage of the `create` method to save a new model instance. This method requires either the `$fillable` or `$guarded` property to be defined on the model to prevent mass assignment vulnerabilities. It returns the newly created model instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_50

LANGUAGE: PHP
CODE:
```
use App\Models\Flight;

$flight = Flight::create([
    'name' => 'London to Paris',
]);
```

----------------------------------------

TITLE: Fake and Assert Laravel Notifications in Tests
DESCRIPTION: This snippet demonstrates how to use `Notification::fake()` to prevent notifications from being sent during testing. It then shows various assertion methods like `assertNothingSent`, `assertSentTo`, `assertNotSentTo`, and `assertCount` to verify notification behavior in both Pest and PHPUnit testing frameworks.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_81

LANGUAGE: php
CODE:
```
<?php

use AppNotificationsOrderShipped;
use IlluminateSupportFacadesNotification;

test('orders can be shipped', function () {
    Notification::fake();

    // Perform order shipping...

    // Assert that no notifications were sent...
    Notification::assertNothingSent();

    // Assert a notification was sent to the given users...
    Notification::assertSentTo(
        [$user], OrderShipped::class
    );

    // Assert a notification was not sent...
    Notification::assertNotSentTo(
        [$user], AnotherNotification::class
    );

    // Assert that a given number of notifications were sent...
    Notification::assertCount(3);
});
```

LANGUAGE: php
CODE:
```
<?php

namespace TestsFeature;

use AppNotificationsOrderShipped;
use IlluminateSupportFacadesNotification;
use TestsTestCase;

class ExampleTest extends TestCase
{
    public function test_orders_can_be_shipped(): void
    {
        Notification::fake();

        // Perform order shipping...

        // Assert that no notifications were sent...
        Notification::assertNothingSent();

        // Assert a notification was sent to the given users...
        Notification::assertSentTo(
            [$user], OrderShipped::class
        );

        // Assert a notification was not sent...
        Notification::assertNotSentTo(
            [$user], AnotherNotification::class
        );

        // Assert that a given number of notifications were sent...
        Notification::assertCount(3);
    }
}
```

----------------------------------------

TITLE: Updating an Existing Database Table (Laravel PHP)
DESCRIPTION: This PHP snippet demonstrates how to modify an existing database table, in this case, adding an 'integer' column named 'votes' to the 'users' table. The `table` method is used for schema alterations.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_26

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

Schema::table('users', function (Blueprint $table) {
    $table->integer('votes');
});
```

----------------------------------------

TITLE: Defining Parent Blade Component with Props (Blade)
DESCRIPTION: This Blade snippet defines a parent `<x-menu>` component. It accepts a `color` prop with a default value and uses `$attributes->merge` to apply classes, demonstrating how to structure a reusable parent component that renders its `$slot` content.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_123

LANGUAGE: Blade
CODE:
```
<!-- /resources/views/components/menu/index.blade.php -->

@props(['color' => 'gray'])

<ul {{ $attributes->merge(['class' => 'bg-'.$color.'-200']) }}>
    {{ $slot }}
</ul>
```

----------------------------------------

TITLE: Adding Soft Delete Timestamp with Timezone Column - Laravel Schema Builder - PHP
DESCRIPTION: The `softDeletesTz` method adds a nullable `deleted_at` `TIMESTAMP` column with timezone support and optional fractional seconds precision. This column is crucial for implementing Eloquent's 'soft delete' functionality, marking records as deleted without actually removing them from the database.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_67

LANGUAGE: PHP
CODE:
```
$table->softDeletesTz('deleted_at', precision: 0);
```

----------------------------------------

TITLE: Dispatch an Event in Laravel
DESCRIPTION: The `event` function dispatches a given event to all its registered listeners. This is a core mechanism in Laravel for decoupling application logic and reacting to specific occurrences within the system.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_123

LANGUAGE: php
CODE:
```
event(new UserRegistered($user));
```

----------------------------------------

TITLE: Storing Uploaded Files to a Specific Disk using storeAs
DESCRIPTION: This snippet demonstrates storing an uploaded file to a specific disk (e.g., 's3') while also specifying a custom filename using the `storeAs` method. The disk name is provided as the third argument, after the directory and filename.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_47

LANGUAGE: php
CODE:
```
$path = $request->file('avatar')->storeAs(
    'avatars',
    $request->user()->id,
    's3'
);
```

----------------------------------------

TITLE: Retrieving Session Data via Request Instance - PHP
DESCRIPTION: This PHP example shows how to retrieve session data within a controller method by type-hinting the `Illuminate\Http\Request` instance. The `get` method is used to fetch a value associated with a specific key from the session.
SOURCE: https://github.com/laravel/docs/blob/12.x/session.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     *
     * Show the profile for the given user.
     */
    public function show(Request $request, string $id): View
    {
        $value = $request->session()->get('key');

        // ...

        $user = $this->users->find($id);

        return view('user.profile', ['user' => $user]);
    }
}
```

----------------------------------------

TITLE: Making JSON API Requests and Asserting Partial JSON
DESCRIPTION: This snippet demonstrates how to issue a JSON POST request to an API endpoint and then use `assertJson` to verify that a specific fragment of JSON data is present within the response. This method is flexible and passes even if other properties exist in the response.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_24

LANGUAGE: Pest
CODE:
```
<?php

test('making an api request', function () {
    $response = $this->postJson('/api/user', ['name' => 'Sally']);

    $response
        ->assertStatus(201)
        ->assertJson([
            'created' => true,
        ]);
});
```

LANGUAGE: PHPUnit
CODE:
```
<?php

namespace TestsFeature;

use TestsTestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic functional test example.
     */
    public function test_making_an_api_request(): void
    {
        $response = $this->postJson('/api/user', ['name' => 'Sally']);

        $response
            ->assertStatus(201)
            ->assertJson([
                'created' => true,
            ]);
    }
}
```

----------------------------------------

TITLE: Dispatching Laravel Jobs After HTTP Response
DESCRIPTION: This snippet shows how to dispatch a job using `dispatchAfterResponse`, which delays the job's execution until after the HTTP response has been sent to the user's browser. This is ideal for short, non-critical tasks like sending notifications, allowing the user to continue interacting with the application immediately.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_40

LANGUAGE: PHP
CODE:
```
use App\Jobs\SendNotification;

SendNotification::dispatchAfterResponse();
```

----------------------------------------

TITLE: Conditionally Executing Callback with Fallback using `when` Method in PHP
DESCRIPTION: Illustrates the `when` method with an optional second callback. The first callback executes if the condition is `true`, and the second (fallback) callback executes if the condition is `false`. Both callbacks receive the collection and the condition value.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_173

LANGUAGE: PHP
CODE:
```
$collection = collect([1, 2, 3]);

$collection->when(false, function (Collection $collection, bool $value) {
    return $collection->push(4);
}, function (Collection $collection, bool $value) {
    return $collection->push(5);
});

$collection->all();

// [1, 2, 3, 5]
```

----------------------------------------

TITLE: Retrieving and Storing Session Data via Global Helper - PHP
DESCRIPTION: This example demonstrates using the global `session` helper function to interact with session data. It shows how to retrieve a value by passing a single key, specify a default value, and store multiple key-value pairs by passing an associative array.
SOURCE: https://github.com/laravel/docs/blob/12.x/session.md#_snippet_3

LANGUAGE: php
CODE:
```
Route::get('/home', function () {
    // Retrieve a piece of data from the session...
    $value = session('key');

    // Specifying a default value...
    $value = session('key', 'default');

    // Store a piece of data in the session...
    session(['key' => 'value']);
});
```

----------------------------------------

TITLE: Chunking Eloquent Results by ID with Updates - PHP
DESCRIPTION: This example uses `chunkById` to efficiently process and update a large number of Eloquent models, specifically when the filtering column is also being updated. It retrieves `Flight` models where `departed` is true in chunks of 200, ensuring consistent results by using the `id` column for internal pagination, and then updates each flight's `departed` status.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_27

LANGUAGE: PHP
CODE:
```
Flight::where('departed', true)
    ->chunkById(200, function (Collection $flights) {
        $flights->each->update(['departed' => false]);
    }, column: 'id');
```

----------------------------------------

TITLE: Creating Models with Factories in Pest
DESCRIPTION: Shows how to use a model factory to create a new Eloquent model instance within a Pest test. This simplifies the creation of test data by leveraging predefined factory attributes, making test setup more efficient.
SOURCE: https://github.com/laravel/docs/blob/12.x/database-testing.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use App\Models\User;

test('models can be instantiated', function () {
    $user = User::factory()->create();

    // ...
});
```

----------------------------------------

TITLE: Querying Laravel Eloquent Relationship Existence with Constraints using whereHas
DESCRIPTION: Shows how to use the `whereHas` method to filter models based on constraints applied to the related models within a closure. This allows filtering based on the content or properties of the related models. Requires `Illuminate\Database\Eloquent\Builder`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_87

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Builder;

// Retrieve posts with at least one comment containing words like code%...
$posts = Post::whereHas('comments', function (Builder $query) {
    $query->where('content', 'like', 'code%');
})->get();
```

----------------------------------------

TITLE: Using RefreshDatabase Trait with Pest
DESCRIPTION: Demonstrates how to use the `RefreshDatabase` trait in a Pest test file to automatically reset the database after each test. This ensures that data from previous tests does not interfere with subsequent tests and is generally faster than full migrations or truncations.
SOURCE: https://github.com/laravel/docs/blob/12.x/database-testing.md#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('basic example', function () {
    $response = $this->get('/');

    // ...
});
```

----------------------------------------

TITLE: Redirect to Named Route in Laravel
DESCRIPTION: Explains how to redirect to a named route by calling the `route` method on the `Redirector` instance returned by the `redirect` helper.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_14

LANGUAGE: PHP
CODE:
```
return redirect()->route('login');
```

----------------------------------------

TITLE: Clearing Laravel Optimization Caches
DESCRIPTION: This Artisan command removes all cached files generated by the `optimize` command, such as configuration, event, route, and view caches. It also clears all keys in the default cache driver, making it useful for development or after deployment updates.
SOURCE: https://github.com/laravel/docs/blob/12.x/deployment.md#_snippet_3

LANGUAGE: Shell
CODE:
```
php artisan optimize:clear
```

----------------------------------------

TITLE: Applying Specific Authentication Guards to Routes
DESCRIPTION: Shows how to protect a route using a specific authentication guard (e.g., `auth:api-customers`), ensuring that incoming requests are authenticated against the associated user provider defined in the guard configuration.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_50

LANGUAGE: php
CODE:
```
Route::get('/customer', function () {
    // ...
})->middleware('auth:api-customers');
```

----------------------------------------

TITLE: Resolve Class Instance with `make` Method
DESCRIPTION: Shows how to retrieve a class instance from the Laravel container using the `make` method, accepting the name of the class or interface you wish to resolve.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_28

LANGUAGE: php
CODE:
```
use App\Services\Transistor;

$transistor = $this->app->make(Transistor::class);
```

----------------------------------------

TITLE: Asserting Forbidden HTTP Status in Laravel PHP
DESCRIPTION: Assert that the response has a forbidden (403) HTTP status code.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_66

LANGUAGE: PHP
CODE:
```
$response->assertForbidden();
```

----------------------------------------

TITLE: Implementing ShouldBroadcast Interface in PHP
DESCRIPTION: This snippet demonstrates how to mark a Laravel event with the `ShouldBroadcast` interface, instructing Laravel to broadcast the event when it is fired. It includes the event's public property `order` which will be broadcast.
SOURCE: https://github.com/laravel/docs/blob/12.x/broadcasting.md#_snippet_26

LANGUAGE: php
CODE:
```
<?php

namespace AppEvents;

use AppModelsOrder;
use IlluminateBroadcastingChannel;
use IlluminateBroadcastingInteractsWithSockets;
use IlluminateBroadcastingPresenceChannel;
use IlluminateContractsBroadcastingShouldBroadcast;
use IlluminateQueueSerializesModels;

class OrderShipmentStatusUpdated implements ShouldBroadcast
{
    /**
     * The order instance.
     *
     * @var \App\Models\Order
     */
    public $order;
}
```

----------------------------------------

TITLE: Authenticating and Storing Socialite User Data in Laravel
DESCRIPTION: This example demonstrates how to process the OAuth callback, retrieve user details from Socialite, and then either update an existing user record or create a new one in the database. Finally, it logs the user into the application and redirects them to a dashboard.
SOURCE: https://github.com/laravel/docs/blob/12.x/socialite.md#_snippet_3

LANGUAGE: php
CODE:
```
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

Route::get('/auth/callback', function () {
    $githubUser = Socialite::driver('github')->user();

    $user = User::updateOrCreate([
        'github_id' => $githubUser->id,
    ], [
        'name' => $githubUser->name,
        'email' => $githubUser->email,
        'github_token' => $githubUser->token,
        'github_refresh_token' => $githubUser->refreshToken,
    ]);

    Auth::login($user);

    return redirect('/dashboard');
});
```

----------------------------------------

TITLE: GitHub Actions Workflow for Laravel Pint
DESCRIPTION: This YAML configuration defines a GitHub Actions workflow (`lint.yml`) that automatically runs Laravel Pint on every push to fix code style. It checks out the code, sets up PHP, installs Pint globally, runs it, and then commits any fixed files back to the repository.
SOURCE: https://github.com/laravel/docs/blob/12.x/pint.md#_snippet_16

LANGUAGE: YAML
CODE:
```
name: Fix Code Style

on: [push]

jobs:
  lint:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        php: [8.4]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: json, dom, curl, libxml, mbstring
          coverage: none

      - name: Install Pint
        run: composer global require laravel/pint

      - name: Run Pint
        run: pint

      - name: Commit linted files
        uses: stefanzweifel/git-auto-commit-action@v5
```

----------------------------------------

TITLE: Refreshing Database and Seeding (Artisan)
DESCRIPTION: These Artisan commands are used to completely rebuild the database by dropping all tables, re-running all migrations, and then seeding the database. The `--seed` option triggers the default seeder, while `--seeder` allows specifying a particular seeder class after migration.
SOURCE: https://github.com/laravel/docs/blob/12.x/seeding.md#_snippet_6

LANGUAGE: shell
CODE:
```
php artisan migrate:fresh --seed

php artisan migrate:fresh --seed --seeder=UserSeeder
```

----------------------------------------

TITLE: Assigning Request ID with Context in Laravel Middleware
DESCRIPTION: This middleware demonstrates using `Log::withContext` to add a unique `request-id` to all subsequent log entries within the current request's lifecycle. This helps in tracing logs related to a specific HTTP request.
SOURCE: https://github.com/laravel/docs/blob/12.x/logging.md#_snippet_9

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class AssignRequestId
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestId = (string) Str::uuid();

        Log::withContext([
            'request-id' => $requestId
        ]);

        $response = $next($request);

        $response->headers->set('Request-Id', $requestId);

        return $response;
    }
}
```

----------------------------------------

TITLE: Scoped Implicit Binding with Custom Keys in Laravel PHP
DESCRIPTION: This snippet illustrates how Laravel automatically scopes nested implicit bindings when a custom key is used. It assumes a relationship (e.g., `posts` on `User`) to retrieve the child model, ensuring it belongs to the parent.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_55

LANGUAGE: PHP
CODE:
```
use App\Models\Post;
use App\Models\User;

Route::get('/users/{user}/posts/{post:slug}', function (User $user, Post $post) {
    return $post;
});
```

----------------------------------------

TITLE: JavaScript: Confirm Stripe Card Setup and Handle Result
DESCRIPTION: This JavaScript snippet demonstrates how to use Stripe.js's `confirmCardSetup` method to finalize the payment method setup process. It captures user input, sends card details to Stripe for verification, and includes logic to handle both successful verification and errors, providing the `setupIntent.payment_method` identifier upon success.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_43

LANGUAGE: javascript
CODE:
```
const cardHolderName = document.getElementById('card-holder-name');
const cardButton = document.getElementById('card-button');
const clientSecret = cardButton.dataset.secret;

cardButton.addEventListener('click', async (e) => {
    const { setupIntent, error } = await stripe.confirmCardSetup(
        clientSecret, {
            payment_method: {
                card: cardElement,
                billing_details: { name: cardHolderName.value }
            }
        }
    );

    if (error) {
        // Display "error.message" to the user...
    } else {
        // The card has been verified successfully...
    }
});
```

----------------------------------------

TITLE: Checking User and Token Permissions in Laravel Policy (PHP)
DESCRIPTION: This snippet demonstrates how to combine user ownership checks with Sanctum's `tokenCan` method within an authorization policy. It ensures that the authenticated user owns the resource and that their token has the necessary permission to perform the action, providing granular access control.
SOURCE: https://github.com/laravel/docs/blob/12.x/sanctum.md#_snippet_11

LANGUAGE: php
CODE:
```
return $request->user()->id === $server->user_id &&
       $request->user()->tokenCan('server:update')
```

----------------------------------------

TITLE: Writing a Global Eloquent Query Scope - PHP
DESCRIPTION: Demonstrates how to write a global Eloquent query scope by implementing the `Illuminate\Database\Eloquent\Scope` interface. The `apply` method adds constraints to the query builder, such as a `where` clause for `created_at`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_84

LANGUAGE: php
CODE:
```
<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class AncientScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        $builder->where('created_at', '<', now()->subYears(2000));
    }
}
```

----------------------------------------

TITLE: Iterating Over Collection Items with a Closure in PHP
DESCRIPTION: The `each` method iterates over every item in the collection, passing each item and its corresponding key to a provided closure. Returning `false` from within the closure will immediately stop the iteration process.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_41

LANGUAGE: PHP
CODE:
```
$collection = collect([1, 2, 3, 4]);

$collection->each(function (int $item, int $key) {
    // ...
});
```

LANGUAGE: PHP
CODE:
```
$collection->each(function (int $item, int $key) {
    if (/* condition */) {
        return false;
    }
});
```

----------------------------------------

TITLE: Displaying Password Reset Form with Token - Laravel PHP
DESCRIPTION: This route defines the GET endpoint for displaying the password reset form after a user clicks a reset link. It accepts a `token` parameter, passes it to the `auth.reset-password` view, ensures the user is a guest, and names the route. The view should display a form containing `email`, `password`, `password_confirmation`, and a hidden `token` field.
SOURCE: https://github.com/laravel/docs/blob/12.x/passwords.md#_snippet_2

LANGUAGE: PHP
CODE:
```
Route::get('/reset-password/{token}', function (string $token) {
    return view('auth.reset-password', ['token' => $token]);
})->middleware('guest')->name('password.reset');
```

----------------------------------------

TITLE: Demonstrating N+1 Query Problem with Laravel Eager Loading
DESCRIPTION: This example shows a potential N+1 query problem when accessing the parent model from child models within a loop, even after eager loading the children.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_5

LANGUAGE: php
CODE:
```
$posts = Post::with('comments')->get();

foreach ($posts as $post) {
    foreach ($post->comments as $comment) {
        echo $comment->post->title;
    }
}
```

----------------------------------------

TITLE: Merging Default Attributes in Component Template
DESCRIPTION: Illustrates using the attribute bag's `merge` method to specify default values for attributes or merge additional values, particularly useful for defining a set of default CSS classes.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_84

LANGUAGE: Blade
CODE:
```
<div {{ $attributes->merge(['class' => 'alert alert-'.$type]) }}>
    {{ $message }}
</div>
```

----------------------------------------

TITLE: Customizing Email Verification Mail Message (PHP)
DESCRIPTION: This snippet demonstrates how to customize the email verification notification's content and appearance. By calling `toMailUsing` on the `VerifyEmail` notification within `AppServiceProvider`'s `boot` method, developers can return a custom `MailMessage` instance, tailoring the subject, lines, and action button.
SOURCE: https://github.com/laravel/docs/blob/12.x/verification.md#_snippet_6

LANGUAGE: php
CODE:
```
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    // ...

    VerifyEmail::toMailUsing(function (object $notifiable, string $url) {
        return (new MailMessage)
            ->subject('Verify Email Address')
            ->line('Click the button below to verify your email address.')
            ->action('Verify Email Address', $url);
    });
}
```

----------------------------------------

TITLE: Retrieving Uploaded Files from Request (PHP)
DESCRIPTION: This PHP snippet demonstrates two ways to retrieve an uploaded file from an `Illuminate\Http\Request` instance: using the `file` method with the input name or via dynamic properties. Both methods return an `Illuminate\Http\UploadedFile` instance for further interaction.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_66

LANGUAGE: php
CODE:
```
$file = $request->file('photo');

$file = $request->photo;
```

----------------------------------------

TITLE: Use Fluent Email Validation Rule Builder in Laravel
DESCRIPTION: Illustrates how to construct an email validation rule using Laravel's fluent rule builder. This allows for chaining methods to specify RFC compliance, MX record validation, and spoofing prevention.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_92

LANGUAGE: PHP
CODE:
```
use Illuminate\Validation\Rule;

$request->validate([
    'email' => [
        'required',
        Rule::email()
            ->rfcCompliant(strict: false)
            ->validateMxRecord()
            ->preventSpoofing()
    ]
]);
```

----------------------------------------

TITLE: Customizing Laravel Pagination View with Blade
DESCRIPTION: Shows how to specify a custom Blade view for rendering pagination links by passing the view name to the `$paginator->links()` method. It also demonstrates how to pass additional data to the custom view.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_19

LANGUAGE: blade
CODE:
```
{{ $paginator->links('view.name') }}

<!-- Passing additional data to the view... -->
{{ $paginator->links('view.name', ['foo' => 'bar']) }}
```

----------------------------------------

TITLE: Retry HTTP Request with Fixed Delay (Laravel)
DESCRIPTION: Configures the Laravel HTTP client to retry a request up to 3 times with a 100ms delay between attempts if a client or server error occurs.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_19

LANGUAGE: php
CODE:
```
$response = Http::retry(3, 100)->post(/* ... */);
```

----------------------------------------

TITLE: Expecting Specific Database Query Count
DESCRIPTION: The `expectsDatabaseQueryCount` method may be invoked at the beginning of your test to specify the total number of database queries that you expect to be run during the test. If the actual number of executed queries does not exactly match this expectation, the test will fail, helping to identify N+1 problems or inefficient queries.
SOURCE: https://github.com/laravel/docs/blob/12.x/database-testing.md#_snippet_16

LANGUAGE: PHP
CODE:
```
$this->expectsDatabaseQueryCount(5);

// Test...
```

----------------------------------------

TITLE: Defining BelongsTo Relationships in Laravel Factories by Assigning Factory Instances
DESCRIPTION: This snippet shows how to define a `belongsTo` relationship (e.g., `user_id`) within a Laravel model factory by assigning a new `User::factory()` instance. This ensures a new `User` model is created and associated when the parent model is instantiated.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-factories.md#_snippet_37

LANGUAGE: PHP
CODE:
```
use App\Models\User;

/**
 * Define the model's default state.
 *
 * @return array<string, mixed>
 */
public function definition(): array
{
    return [
        'user_id' => User::factory(),
        'title' => fake()->title(),
        'content' => fake()->paragraph(),
    ];
}
```

----------------------------------------

TITLE: Generating URL for Named Route with Multiple Parameters - PHP
DESCRIPTION: This snippet demonstrates generating a URL for a named route that requires multiple parameters (`comment.show`). It shows how to pass an associative array with all required parameters to the `route` helper.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_9

LANGUAGE: PHP
CODE:
```
echo route('comment.show', ['post' => 1, 'comment' => 3]);

// http://example.com/post/1/comment/3
```

----------------------------------------

TITLE: Retrieving Input as Integer (PHP)
DESCRIPTION: This PHP snippet uses the `integer()` method to retrieve an input value and cast it to an integer. This method is ideal for numeric inputs like pagination limits (`per_page`), automatically handling type conversion and providing a default value if the input is missing or cannot be cast, ensuring data integrity for numeric operations.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_36

LANGUAGE: php
CODE:
```
$perPage = $request->integer('per_page');
```

----------------------------------------

TITLE: Spying on Cache Facade with Pest
DESCRIPTION: This snippet demonstrates how to use `Cache::spy()` in a Pest test to record interactions with the `Cache` facade. After performing an HTTP request, it asserts that the `put` method was called with specific arguments, allowing verification of facade usage after the code under test has executed.
SOURCE: https://github.com/laravel/docs/blob/12.x/mocking.md#_snippet_8

LANGUAGE: PHP
CODE:
```
<?php

use Illuminate\Support\Facades\Cache;

test('values are be stored in cache', function () {
    Cache::spy();

    $response = $this->get('/');

    $response->assertStatus(200);

    Cache::shouldHaveReceived('put')->with('name', 'Taylor', 10);
});
```

----------------------------------------

TITLE: Laravel Middleware for Post-Request Processing
DESCRIPTION: This middleware illustrates how to execute logic after the HTTP request has been handled by the application. It captures the response from the `$next` callback, performs an action, and then returns the modified or original response.
SOURCE: https://github.com/laravel/docs/blob/12.x/middleware.md#_snippet_3

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AfterMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Perform action

        return $response;
    }
}
```

----------------------------------------

TITLE: Registering Custom Exception Renderer in Laravel
DESCRIPTION: This snippet demonstrates how to register a custom rendering closure for a specific exception type, such as `App\Exceptions\InvalidOrderException`, using the `render` method within the `withExceptions` configuration. The closure receives the exception and request, and should return an `Illuminate\Http\Response` instance to customize the HTTP response.
SOURCE: https://github.com/laravel/docs/blob/12.x/errors.md#_snippet_11

LANGUAGE: php
CODE:
```
use App\Exceptions\InvalidOrderException;
use Illuminate\Http\Request;

->withExceptions(function (Exceptions $exceptions) {
    $exceptions->render(function (InvalidOrderException $e, Request $request) {
        return response()->view('errors.invalid-order', status: 500);
    });
})
```

----------------------------------------

TITLE: Broadcast event to listeners (Laravel PHP)
DESCRIPTION: The `broadcast` function broadcasts the given event to its listeners. It can also be chained with `toOthers()` to exclude the current user from receiving the broadcast.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_107

LANGUAGE: PHP
CODE:
```
broadcast(new UserRegistered($user));

broadcast(new UserRegistered($user))->toOthers();
```

----------------------------------------

TITLE: Defining Resend Verification Email Route (PHP)
DESCRIPTION: This route allows users to request a new email verification link if the original one is lost or deleted. It is a POST route named `verification.send`, protected by `auth` and `throttle` middleware to prevent abuse, and sends a new verification notification to the authenticated user.
SOURCE: https://github.com/laravel/docs/blob/12.x/verification.md#_snippet_4

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::post('/email/verification-notification', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');
```

----------------------------------------

TITLE: Wait for Text to Appear or Disappear in Laravel Dusk
DESCRIPTION: The `waitForText` method pauses execution until specified text is displayed on the page. Conversely, `waitUntilMissingText` waits until the text is no longer present. Both methods accept an optional timeout in seconds.
SOURCE: https://github.com/laravel/docs/blob/12.x/dusk.md#_snippet_69

LANGUAGE: php
CODE:
```
// Wait a maximum of five seconds for the text...
$browser->waitForText('Hello World');

// Wait a maximum of one second for the text...
$browser->waitForText('Hello World', 1);
```

LANGUAGE: php
CODE:
```
// Wait a maximum of five seconds for the text to be removed...
$browser->waitUntilMissingText('Hello World');

// Wait a maximum of one second for the text to be removed...
$browser->waitUntilMissingText('Hello World', 1);
```

----------------------------------------

TITLE: Manually Authenticating Users in Laravel with Auth::attempt
DESCRIPTION: This PHP controller method demonstrates how to handle an authentication attempt using Laravel's `Auth` facade. It validates the incoming request's email and password, attempts to log in the user with `Auth::attempt()`, regenerates the session on success to prevent session fixation, and redirects the user to their intended destination or a fallback dashboard. If authentication fails, it redirects back with an error message. The `attempt` method automatically handles password hashing comparison against the database.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_7

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /**
     * Handle an authentication attempt.
     */
    public function authenticate(Request $request): RedirectResponse
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();

            return redirect()->intended('dashboard');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }
}
```

----------------------------------------

TITLE: Validate Image Files and Dimensions in Laravel
DESCRIPTION: Illustrates how to validate uploaded files as images (JPG, PNG, GIF, etc.) and enforce specific image dimensions (max width/height) using Laravel's `File::image()` and `Rule::dimensions()` rules. Includes a note on allowing SVG files.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_181

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

Validator::validate($input, [
    'photo' => [
        'required',
        File::image()
            ->min(1024)
            ->max(12 * 1024)
            ->dimensions(Rule::dimensions()->maxWidth(1000)->maxHeight(500)),
    ],
]);
```

----------------------------------------

TITLE: Asserting JSON Attribute Presence/Absence (Single)
DESCRIPTION: This snippet demonstrates how to assert that a JSON response contains a specific attribute ('data') and lacks another ('message') using the `has` and `missing` methods within a fluent `AssertableJson` closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_30

LANGUAGE: PHP
CODE:
```
$response->assertJson(fn (AssertableJson $json) =>
    $json->has('data')
        ->missing('message')
);
```

----------------------------------------

TITLE: Defining Mailable HTML and Plain Text Views (PHP)
DESCRIPTION: Shows how to define both an HTML view template (`view`) and a plain-text template (`text`) for a mailable within the `content` method, allowing the email client to choose the preferred format.
SOURCE: https://github.com/laravel/docs/blob/12.x/mail.md#_snippet_23

LANGUAGE: php
CODE:
```
/**
 * Get the message content definition.
 */
public function content(): Content
{
    return new Content(
        view: 'mail.orders.shipped',
        text: 'mail.orders.shipped-text'
    );
}
```

----------------------------------------

TITLE: Retrieving Pluralized Strings with `trans_choice` (PHP)
DESCRIPTION: This PHP snippet shows how to use the `trans_choice` function to retrieve a pluralized translation string based on a given count. It takes the translation key and the count as arguments, automatically selecting the appropriate singular or plural form defined in the language file.
SOURCE: https://github.com/laravel/docs/blob/12.x/localization.md#_snippet_18

LANGUAGE: php
CODE:
```
echo trans_choice('messages.apples', 10);
```

----------------------------------------

TITLE: Eager Loading Relationships for Models in Laravel Eloquent Collection (PHP)
DESCRIPTION: The `load` method eager loads specified relationships for all models within the collection. It can handle multiple relationships and nested relationships, as well as custom query constraints for eager loading.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-collections.md#_snippet_10

LANGUAGE: PHP
CODE:
```
$users->load(['comments', 'posts']);

$users->load('comments.author');

$users->load(['comments', 'posts' => fn ($query) => $query->where('active', 1)]);
```

----------------------------------------

TITLE: Laravel Validation Rule: numeric
DESCRIPTION: The field under validation must be numeric, as determined by PHP's `is_numeric` function.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_137

LANGUAGE: APIDOC
CODE:
```
numeric
```

----------------------------------------

TITLE: Logging within a Queued Job with Inherited Laravel Context
DESCRIPTION: This PHP job handler demonstrates logging within a queued job. The log entry automatically includes context information that was captured and propagated from the original request that dispatched the job, allowing for end-to-end tracing.
SOURCE: https://github.com/laravel/docs/blob/12.x/context.md#_snippet_3

LANGUAGE: php
CODE:
```
class ProcessPodcast implements ShouldQueue
{
    use Queueable;

    // ...

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing podcast.', [
            'podcast_id' => $this->podcast->id,
        ]);

        // ...
    }
}
```

----------------------------------------

TITLE: Append Global Middleware in Laravel
DESCRIPTION: This snippet demonstrates how to append a custom middleware class to the global middleware stack in a Laravel application's `bootstrap/app.php` file using the `append` method of the `Middleware` object. This ensures the middleware runs on every HTTP request.
SOURCE: https://github.com/laravel/docs/blob/12.x/middleware.md#_snippet_4

LANGUAGE: php
CODE:
```
use App\Http\Middleware\EnsureTokenIsValid;

->withMiddleware(function (Middleware $middleware) {
     $middleware->append(EnsureTokenIsValid::class);
})
```

----------------------------------------

TITLE: Enabling Laravel Maintenance Mode (Shell)
DESCRIPTION: This Artisan command puts the Laravel application into maintenance mode, displaying a custom view for all incoming requests. This is useful for performing updates or maintenance without users accessing the live application.
SOURCE: https://github.com/laravel/docs/blob/12.x/configuration.md#_snippet_21

LANGUAGE: shell
CODE:
```
php artisan down
```

----------------------------------------

TITLE: Populating Model Instance with Fill Method
DESCRIPTION: Demonstrates using the `fill` method on an existing model instance to populate its attributes with an array of values. This method respects the `$fillable` and `$guarded` properties for mass assignment protection.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_53

LANGUAGE: PHP
CODE:
```
$flight->fill(['name' => 'Amsterdam to Frankfurt']);
```

----------------------------------------

TITLE: Protecting Laravel Routes with Password Confirmation Middleware
DESCRIPTION: This snippet demonstrates how to apply the `password.confirm` middleware to routes. This middleware ensures that users re-confirm their password before accessing sensitive actions, automatically storing the intended destination and redirecting the user to the password confirmation page.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_28

LANGUAGE: php
CODE:
```
Route::get('/settings', function () {
    // ...
})->middleware(['password.confirm']);

Route::post('/settings', function () {
    // ...
})->middleware(['password.confirm']);
```

----------------------------------------

TITLE: Using Cache Facade in a Laravel Controller
DESCRIPTION: Demonstrates the usage of the `Cache` facade within a Laravel controller. The `showProfile` method retrieves user data from the cache using `Cache::get('user:'.$id)` and then passes it to a view. This exemplifies how facades provide a static-like interface to underlying services.
SOURCE: https://github.com/laravel/docs/blob/12.x/facades.md#_snippet_7

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * Show the profile for the given user.
     */
    public function showProfile(string $id): View
    {
        $user = Cache::get('user:'.$id);

        return view('profile', ['user' => $user]);
    }
}
```

----------------------------------------

TITLE: Calling Dynamic Scopes with Arguments in Laravel Eloquent
DESCRIPTION: This example demonstrates how to pass arguments to a dynamic scope when calling it, allowing the scope to filter results based on the provided parameter.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_98

LANGUAGE: PHP
CODE:
```
$users = User::ofType('admin')->get();
```

----------------------------------------

TITLE: Retrieving Nested JSON Input (PHP)
DESCRIPTION: This PHP snippet demonstrates accessing nested JSON data from the request payload using dot notation with the `input()` method. This works when the request's `Content-Type` header is `application/json`, allowing for direct retrieval of values like 'name' from a 'user' object within the JSON structure.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_34

LANGUAGE: php
CODE:
```
$name = $request->input('user.name');
```

----------------------------------------

TITLE: Laravel Validation: `in` Rule for List Inclusion
DESCRIPTION: The `in` rule validates that a field's value is present within a given list of values. It can be fluently constructed using `Rule::in` and also supports validating each element of an input array against the provided list.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_108

LANGUAGE: APIDOC
CODE:
```
in:_foo_,_bar_,...
```

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

Validator::make($data, [
    'zones' => [
        'required',
        Rule::in(['first-zone', 'second-zone']),
    ],
]);
```

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

$input = [
    'airports' => ['NYC', 'LAS'],
];

Validator::make($input, [
    'airports' => [
        'required',
        'array',
    ],
    'airports.*' => Rule::in(['NYC', 'LIT']),
]);
```

----------------------------------------

TITLE: Retrieve Single Row or Column Value with Laravel Query Builder
DESCRIPTION: This section illustrates various methods to fetch a single row or a specific column value from a database table. It covers `first` for a single `stdClass` object, `firstOrFail` for throwing an exception if no record is found, `value` for extracting a single column's value, and `find` for retrieving a row by its primary key.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_1

LANGUAGE: PHP
CODE:
```
$user = DB::table('users')->where('name', 'John')->first();

return $user->email;
```

LANGUAGE: PHP
CODE:
```
$user = DB::table('users')->where('name', 'John')->firstOrFail();
```

LANGUAGE: PHP
CODE:
```
$email = DB::table('users')->where('name', 'John')->value('email');
```

LANGUAGE: PHP
CODE:
```
$user = DB::table('users')->find(3);
```

----------------------------------------

TITLE: Checking Missing or Empty Input with `isNotFilled` Method (PHP)
DESCRIPTION: The `isNotFilled` method determines if a value is either missing from the request or is an empty string. It returns `true` if the input is absent or has no content.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_50

LANGUAGE: php
CODE:
```
if ($request->isNotFilled('name')) {
    // ...
}
```

----------------------------------------

TITLE: Configuring PHPUnit for Testing Database in Laravel
DESCRIPTION: This XML snippet shows the env setting within phpunit.xml that configures the DB_DATABASE environment variable to testing. This ensures that tests run against a dedicated testing database, preventing interference with the main application database.
SOURCE: https://github.com/laravel/docs/blob/12.x/sail.md#_snippet_25

LANGUAGE: xml
CODE:
```
<env name="DB_DATABASE" value="testing"/>
```

----------------------------------------

TITLE: Laravel Validation Rule: image
DESCRIPTION: Validates that the uploaded file is an image of a common type (jpg, jpeg, png, bmp, gif, or webp). By default, SVG files are not allowed due to potential XSS vulnerabilities, but can be explicitly permitted.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_107

LANGUAGE: APIDOC
CODE:
```
image
```

LANGUAGE: APIDOC
CODE:
```
image:allow_svg
```

----------------------------------------

TITLE: Perform Subquery Joins with Laravel Query Builder
DESCRIPTION: Explains the use of `joinSub`, `leftJoinSub`, and `rightJoinSub` methods to join a main query with a subquery. Each method requires the subquery, its table alias, and a closure to define related columns. The example retrieves users along with the `created_at` timestamp of their most recent published blog post.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_20

LANGUAGE: php
CODE:
```
$latestPosts = DB::table('posts')
    ->select('user_id', DB::raw('MAX(created_at) as last_post_created_at'))
    ->where('is_published', true)
    ->groupBy('user_id');

$users = DB::table('users')
    ->joinSub($latestPosts, 'latest_posts', function (JoinClause $join) {
        $join->on('users.id', '=', 'latest_posts.user_id');
    })->get();
```

----------------------------------------

TITLE: redirect() Usage Examples
DESCRIPTION: Examples demonstrating how to use the `redirect` function to return a redirect HTTP response to a specific path or a named route, or to obtain the redirector instance for more complex redirect operations.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_136

LANGUAGE: php
CODE:
```
return redirect('/home');

return redirect()->route('route.name');
```

----------------------------------------

TITLE: Retrieving Safe Uploaded File Name and Extension
DESCRIPTION: This snippet demonstrates how to safely retrieve a unique name and the determined extension for an uploaded file. `hashName()` generates a secure, random filename, while `extension()` determines the extension based on the file's MIME type, mitigating tampering risks.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_49

LANGUAGE: php
CODE:
```
$file = $request->file('avatar');

$name = $file->hashName(); // Generate a unique, random name...
$extension = $file->extension(); // Determine the file's extension based on the file's MIME type...
```

----------------------------------------

TITLE: Group OR Conditions in Laravel Query Builder
DESCRIPTION: Explains how to achieve logical grouping of 'where' clauses using a closure passed to the `where` method. This is crucial for correctly combining `AND` and `OR` conditions, especially when dealing with `orWhere` clauses, to avoid unexpected query behavior.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_49

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->where('name', '=', 'John')
    ->where(function (Builder $query) {
        $query->where('votes', '>', 100)
            ->orWhere('title', '=', 'Admin');
    })
    ->get();
```

LANGUAGE: sql
CODE:
```
select * from users where name = 'John' and (votes > 100 or title = 'Admin')
```

----------------------------------------

TITLE: Sorting Laravel Collection by Multiple Attributes with Closures - PHP
DESCRIPTION: This snippet demonstrates sorting a Laravel collection by multiple attributes using an array of custom closures. Each closure defines a comparison logic for a specific attribute, allowing for complex multi-level sorting. The example sorts by 'name' ascending and then by 'age' descending for matching names, using the spaceship operator (`<=>`). `values()` resets the keys.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_146

LANGUAGE: PHP
CODE:
```
$collection = collect([
    ['name' => 'Taylor Otwell', 'age' => 34],
    ['name' => 'Abigail Otwell', 'age' => 30],
    ['name' => 'Taylor Otwell', 'age' => 36],
    ['name' => 'Abigail Otwell', 'age' => 32],
]);

$sorted = $collection->sortBy([
    fn (array $a, array $b) => $a['name'] <=> $b['name'],
    fn (array $a, array $b) => $b['age'] <=> $a['age'],
]);

$sorted->values()->all();

/*
    [
        ['name' => 'Abigail Otwell', 'age' => 32],
        ['name' => 'Abigail Otwell', 'age' => 30],
        ['name' => 'Taylor Otwell', 'age' => 36],
        ['name' => 'Taylor Otwell', 'age' => 34],
    ]
*/
```

----------------------------------------

TITLE: Disabling Submit Button During Form Processing
DESCRIPTION: This HTML snippet demonstrates how to disable a form's submit button while a form submission request is in-flight. The `:disabled` attribute is bound to `form.processing`, which is `true` when the form is being submitted, preventing multiple submissions.
SOURCE: https://github.com/laravel/docs/blob/12.x/precognition.md#_snippet_38

LANGUAGE: html
CODE:
```
<button :disabled="form.processing">
    Submit
</button>
```

----------------------------------------

TITLE: Protect Laravel Routes with 'auth' Middleware
DESCRIPTION: Demonstrates how to restrict access to a specific route in Laravel to only authenticated users by attaching the built-in `auth` middleware. This is a fundamental way to secure application endpoints and ensure only authorized users can access certain functionalities.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_3

LANGUAGE: php
CODE:
```
Route::get('/flights', function () {
    // Only authenticated users may access this route...
})->middleware('auth');
```

----------------------------------------

TITLE: Handling Password Reset Form Submission in Laravel
DESCRIPTION: This PHP snippet defines a POST route for handling password reset requests. It validates the incoming token, email, and password, then uses Laravel's `Password` facade to reset the user's password. Upon successful reset, it updates the user's password, sets a new remember token, dispatches a `PasswordReset` event, and redirects to the login page with a status message. It also handles validation errors and redirects back with appropriate messages.
SOURCE: https://github.com/laravel/docs/blob/12.x/passwords.md#_snippet_3

LANGUAGE: php
CODE:
```
use App\Models\User;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

Route::post('/reset-password', function (Request $request) {
    $request->validate([
        'token' => 'required',
        'email' => 'required|email',
        'password' => 'required|min:8|confirmed',
    ]);

    $status = Password::reset(
        $request->only('email', 'password', 'password_confirmation', 'token'),
        function (User $user, string $password) {
            $user->forceFill([
                'password' => Hash::make($password)
            ])->setRememberToken(Str::random(60));

            $user->save();

            event(new PasswordReset($user));
        }
    );

    return $status === Password::PasswordReset
        ? redirect()->route('login')->with('status', __($status))
        : back()->withErrors(['email' => [__($status)]]);
})->middleware('guest')->name('password.update');
```

----------------------------------------

TITLE: Injecting Dependencies into a Laravel Controller
DESCRIPTION: Demonstrates how to inject a service, `AppleMusic`, into a Laravel controller's constructor for managing class dependencies and enabling easy testing through mocking.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Services\AppleMusic;
use Illuminate\View\View;

class PodcastController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected AppleMusic $apple,
    ) {}

    /**
     * Show information about the given podcast.
     */
    public function show(string $id): View
    {
        return view('podcasts.show', [
            'podcast' => $this->apple->findPodcast($id)
        ]);
    }
}
```

----------------------------------------

TITLE: Preventing Silently Discarding Unfillable Attributes (PHP)
DESCRIPTION: Demonstrates how to configure Eloquent to throw an exception when attempting to mass-assign an attribute that is not defined in the model's `$fillable` array. This helps prevent unexpected data loss or errors during development by making unfillable attribute assignments explicit.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_19

LANGUAGE: PHP
CODE:
```
Model::preventSilentlyDiscardingAttributes(! $this->app->isProduction());
```

----------------------------------------

TITLE: Defining a Gate with a Closure in Laravel
DESCRIPTION: This gate, 'update-post', checks if a user is authorized to update a post by comparing the user's ID with the post's user_id. It is typically defined in the `boot` method of `AppServiceProvider` and receives the authenticated user and the relevant model as arguments.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Facades\Gate;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Gate::define('update-post', function (User $user, Post $post) {
        return $user->id === $post->user_id;
    });
}
```

----------------------------------------

TITLE: Running Laravel Queue Worker with Priority Queues
DESCRIPTION: This shell command starts a Laravel queue worker. It specifies that the worker should process jobs from the 'high' queue first, followed by the 'default' queue. This allows for prioritizing critical jobs over less urgent ones.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_1

LANGUAGE: Shell
CODE:
```
php artisan queue:work --queue=high,default
```

----------------------------------------

TITLE: Generating URLs for Named Routes with Query String Parameters in Laravel
DESCRIPTION: This example shows how to include additional key-value pairs as query string parameters when generating URLs for named routes. Any extra parameters in the array passed to `route` will be appended to the URL's query string.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_43

LANGUAGE: PHP
CODE:
```
Route::get('/user/{id}/profile', function (string $id) {
    // ...
})->name('profile');

$url = route('profile', ['id' => 1, 'photos' => 'yes']);

// /user/1/profile?photos=yes
```

----------------------------------------

TITLE: Asserting Successful HTTP Status Code in Laravel PHP
DESCRIPTION: This method asserts that the HTTP response has a successful status code (between 200 and 299, inclusive). It provides a convenient way to check for general success without needing to specify an exact status code.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_115

LANGUAGE: PHP
CODE:
```
$response->assertSuccessful();
```

----------------------------------------

TITLE: Dispatching Registered Event After User Registration (PHP)
DESCRIPTION: When manually implementing user registration without a starter kit, it is essential to dispatch the `Illuminate\Auth\Events\Registered` event after a user's successful registration. This action triggers the email verification notification process, sending the verification link to the newly registered user.
SOURCE: https://github.com/laravel/docs/blob/12.x/verification.md#_snippet_1

LANGUAGE: php
CODE:
```
use Illuminate\Auth\Events\Registered;

event(new Registered($user));
```

----------------------------------------

TITLE: Implementing Single Product Checkout with Stripe and Cashier
DESCRIPTION: This example illustrates how to integrate Stripe Checkout with Laravel Cashier to sell non-recurring, single-charge products. It outlines the process of redirecting customers to Stripe Checkout for payment, automatically creating Stripe customer records if needed, and handling post-payment redirects to success or cancellation URLs within your application.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_12

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;

Route::get('/checkout', function (Request $request) {
    $stripePriceId = 'price_deluxe_album';

    $quantity = 1;

    return $request->user()->checkout([$stripePriceId => $quantity], [
        'success_url' => route('checkout-success'),
        'cancel_url' => route('checkout-cancel'),
    ]);
})->name('checkout');

Route::view('/checkout/success', 'checkout.success')->name('checkout-success');
Route.view('/checkout/cancel', 'checkout.cancel')->name('checkout-cancel');
```

----------------------------------------

TITLE: Laravel Query Builder: Basic `where` Clause with Multiple Conditions
DESCRIPTION: This snippet demonstrates how to apply multiple 'where' clauses to a Laravel query. Each 'where' clause is implicitly joined by an 'AND' operator, filtering users based on their votes and age.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_23

LANGUAGE: php
CODE:
```
$users = DB::table('users')
    ->where('votes', '=', 100)
    ->where('age', '>', 35)
    ->get();
```

----------------------------------------

TITLE: Starting a Laravel Queue Worker (Shell)
DESCRIPTION: Starts a Laravel queue worker using the `queue:work` Artisan command. This command processes new jobs from the queue and continues running until manually stopped, requiring a process monitor for production.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_93

LANGUAGE: shell
CODE:
```
php artisan queue:work
```

----------------------------------------

TITLE: Defining an Auto-Incrementing Integer Primary Key in Laravel
DESCRIPTION: The `increments` method creates an auto-incrementing `UNSIGNED INTEGER` column as a primary key. This is suitable for tables with a moderate number of records.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_49

LANGUAGE: PHP
CODE:
```
$table->increments('id');
```

----------------------------------------

TITLE: Creating String (VARCHAR) Column - Laravel Schema Builder - PHP
DESCRIPTION: The `string` method creates a `VARCHAR` equivalent column in the database. It requires a column name and an optional `length` parameter, defining the maximum number of characters the column can store.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_69

LANGUAGE: PHP
CODE:
```
$table->string('name', length: 100);
```

----------------------------------------

TITLE: Get or set cache values (Laravel PHP)
DESCRIPTION: The `cache` function is used to retrieve values from the cache, returning an optional default if the key doesn't exist. It can also add items to the cache by passing an array of key/value pairs along with a duration in seconds or a `DateTime` instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_108

LANGUAGE: PHP
CODE:
```
$value = cache('key');

$value = cache('key', 'default');

cache(['key' => 'value'], 300);

cache(['key' => 'value'], now()->addSeconds(10));
```

----------------------------------------

TITLE: Iterating Eloquent Collections in PHP
DESCRIPTION: This snippet demonstrates how to iterate over an Eloquent collection of `User` models using a `foreach` loop, similar to iterating over a simple PHP array. It retrieves active users from the database and then echoes the `name` attribute for each user in the collection.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-collections.md#_snippet_0

LANGUAGE: php
CODE:
```
use App\Models\User;

$users = User::where('active', 1)->get();

foreach ($users as $user) {
    echo $user->name;
}
```

----------------------------------------

TITLE: Rendering HTML with Modern Blade Syntax
DESCRIPTION: This snippet illustrates the use of Laravel's Blade templating engine, providing a more concise and convenient syntax for displaying data and iterating over collections. It achieves the same result as traditional PHP but with Blade's `@foreach` directive and `{{ }}` echo syntax.
SOURCE: https://github.com/laravel/docs/blob/12.x/frontend.md#_snippet_1

LANGUAGE: Blade
CODE:
```
<div>
    @foreach ($users as $user)
        Hello, {{ $user->name }} <br />
    @endforeach
</div>
```

----------------------------------------

TITLE: Creating a Laravel Post Controller with Create and Store Methods
DESCRIPTION: This PHP snippet outlines a basic `PostController` with `create` and `store` methods. The `create` method returns a view for post creation, while the `store` method is a placeholder for validation and storage logic, redirecting after successful post creation.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PostController extends Controller
{
    /**
     * Show the form to create a new blog post.
     */
    public function create(): View
    {
        return view('post.create');
    }

    /**
     * Store a new blog post.
     */
    public function store(Request $request): RedirectResponse
    {
        // Validate and store the blog post...

        $post = /** ... */

        return to_route('post.show', ['post' => $post->id]);
    }
}
```

----------------------------------------

TITLE: Escape HTML Entities with Laravel's e() Function
DESCRIPTION: The `e` function runs PHP's `htmlspecialchars` function, with the `double_encode` option set to `true` by default. It's primarily used to prevent Cross-Site Scripting (XSS) attacks by converting special characters to HTML entities, ensuring safe output in web pages.
SOURCE: https://github.com/laravel/docs/blob/12.x/strings.md#_snippet_4

LANGUAGE: php
CODE:
```
echo e('<html>foo</html>');

// &lt;html&gt;foo&lt;/html&gt;
```

----------------------------------------

TITLE: Querying a HasMany Relationship in Laravel
DESCRIPTION: Illustrates how to add constraints to a one-to-many relationship query by calling the relationship method and chaining query builder methods.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_3

LANGUAGE: php
CODE:
```
$comment = Post::find(1)->comments()
    ->where('title', 'foo')
    ->first();
```

----------------------------------------

TITLE: Accessing Request and Route Parameter in Laravel Controller (PHP)
DESCRIPTION: Shows how to inject both the `Illuminate\Http\Request` instance and a route parameter (`id`) into a controller method. The route parameter is listed after the request dependency, allowing the method to access both the HTTP request data and specific URL segments.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_3

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Update the specified user.
     */
    public function update(Request $request, string $id): RedirectResponse
    {
        // Update the user...

        return redirect('/users');
    }
}
```

----------------------------------------

TITLE: Generating URLs and Redirects for Named Routes in Laravel
DESCRIPTION: This example shows how to generate URLs and perform redirects using Laravel's `route` and `to_route` helper functions. By referencing a named route ('profile'), developers can easily create dynamic links and redirects without hardcoding URIs.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_41

LANGUAGE: PHP
CODE:
```
// Generating URLs...
$url = route('profile');

// Generating Redirects...
return redirect()->route('profile');

return to_route('profile');
```

----------------------------------------

TITLE: Insert Records into Database Tables with Laravel Query Builder
DESCRIPTION: This snippet covers various methods for inserting data into database tables using Laravel's Query Builder. It demonstrates inserting single records, multiple records, ignoring duplicate errors with `insertOrIgnore`, inserting data from subqueries using `insertUsing`, and retrieving auto-incrementing IDs after insertion with `insertGetId`.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_64

LANGUAGE: php
CODE:
```
DB::table('users')->insert([
    'email' => '<EMAIL>',
    'votes' => 0
]);
```

LANGUAGE: php
CODE:
```
DB::table('users')->insert([
    ['email' => '<EMAIL>', 'votes' => 0],
    ['email' => '<EMAIL>', 'votes' => 0]
]);
```

LANGUAGE: php
CODE:
```
DB::table('users')->insertOrIgnore([
    ['id' => 1, 'email' => '<EMAIL>'],
    ['id' => 2, 'email' => '<EMAIL>']
]);
```

LANGUAGE: php
CODE:
```
DB::table('pruned_users')->insertUsing(
    ['id', 'name', 'email', 'email_verified_at'],
    DB::table('users')->select(
        'id', 'name', 'email', 'email_verified_at'
    )->where('updated_at', '<=', now()->subMonth())
);
```

LANGUAGE: php
CODE:
```
$id = DB::table('users')->insertGetId(
    ['email' => '<EMAIL>', 'votes' => 0]
);
```

----------------------------------------

TITLE: Defining a Basic Eloquent API User Resource (PHP)
DESCRIPTION: This PHP snippet defines a simple `UserResource` class, extending `JsonResource`. The `toArray` method specifies which model attributes should be converted to JSON, with direct access to model properties via `$this`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-resources.md#_snippet_2

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

----------------------------------------

TITLE: Returning an Eloquent API Resource Collection from a Route (PHP)
DESCRIPTION: This example illustrates how to return a collection of resources from a Laravel route. The static `collection` method on the resource class is used to transform an Eloquent collection into a JSON array of resources.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-resources.md#_snippet_5

LANGUAGE: php
CODE:
```
use App\Http\Resources\UserResource;
use App\Models\User;

Route::get('/users', function () {
    return UserResource::collection(User::all());
});
```

----------------------------------------

TITLE: Publishing a Podcast (Without Real-Time Facades) - PHP
DESCRIPTION: This snippet illustrates a `publish` method in a `Podcast` model that requires an explicit `Publisher` instance via dependency injection. While this approach enhances testability by allowing the `Publisher` to be mocked, it necessitates passing the instance every time the method is invoked.
SOURCE: https://github.com/laravel/docs/blob/12.x/facades.md#_snippet_9

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use App\Contracts\Publisher;
use Illuminate\Database\Eloquent\Model;

class Podcast extends Model
{
    /**
     * Publish the podcast.
     */
    public function publish(Publisher $publisher): void
    {
        $this->update(['publishing' => now()]);

        $publisher->publish($this);
    }
}
```

----------------------------------------

TITLE: Counting Multiple Relationships with withCount and Constraints in PHP
DESCRIPTION: Shows how to use withCount with an array to count multiple relationships (votes, comments) and apply additional constraints to the count query for one of the relationships (comments).
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_102

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Builder;

$posts = Post::withCount(['votes', 'comments' => function (Builder $query) {
    $query->where('content', 'like', 'code%');
}])->get();

echo $posts[0]->votes_count;
echo $posts[0]->comments_count;
```

----------------------------------------

TITLE: Laravel Validation Rule: present_unless
DESCRIPTION: The field under validation must be present unless the 'anotherfield' field is equal to any of the specified values.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_140

LANGUAGE: APIDOC
CODE:
```
present_unless:_anotherfield_,_value_
```

----------------------------------------

TITLE: Get or set configuration variables (Laravel PHP)
DESCRIPTION: The `config` function retrieves the value of a configuration variable using dot notation (e.g., `file.option`), with an optional default value. It can also set configuration variables at runtime by passing an array, though this only affects the current request.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_111

LANGUAGE: PHP
CODE:
```
$value = config('app.timezone');

$value = config('app.timezone', $default);

config(['app.debug' => true]);
```

----------------------------------------

TITLE: Loading Package Routes in Laravel Service Provider (PHP)
DESCRIPTION: This PHP snippet, placed in the `boot` method of a Laravel service provider, uses `loadRoutesFrom` to register the package's web routes. Laravel automatically handles route caching, preventing unnecessary loading if routes are already cached.
SOURCE: https://github.com/laravel/docs/blob/12.x/packages.md#_snippet_5

LANGUAGE: PHP
CODE:
```
/**
 * Bootstrap any package services.
 */
public function boot(): void
{
    $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
}
```

----------------------------------------

TITLE: Apply Where Clauses to Joins in Laravel Query Builder
DESCRIPTION: Shows how to incorporate 'where' clauses directly into join conditions using the `where` and `orWhere` methods provided by the `JoinClause` instance. These methods compare a column against a specific value, rather than another column, within the join.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_19

LANGUAGE: php
CODE:
```
DB::table('users')
    ->join('contacts', function (JoinClause $join) {
        $join->on('users.id', '=', 'contacts.user_id')
            ->where('contacts.user_id', '>', 5);
    })
    ->get();
```

----------------------------------------

TITLE: Building Complex Queries with Eloquent (PHP)
DESCRIPTION: Illustrates how to construct more complex database queries using Eloquent's query builder capabilities. This example demonstrates chaining `where`, `orderBy`, and `take` methods to filter, sort, and limit results before retrieving them with `get()`.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_21

LANGUAGE: PHP
CODE:
```
$flights = Flight::where('active', 1)
    ->orderBy('name')
    ->take(10)
    ->get();
```

----------------------------------------

TITLE: Define Custom Validation Messages for Specific Attributes
DESCRIPTION: Provides an example of how to customize validation error messages for specific attribute and rule combinations by adding them to the `custom` array in your application's `lang/xx/validation.php` language file. This allows for highly specific and user-friendly error messages.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_52

LANGUAGE: php
CODE:
```
'custom' => [
    'email' => [
        'required' => 'We need to know your email address!',
        'max' => 'Your email address is too long!'
    ]
]
```

----------------------------------------

TITLE: Creating Temporary Signed URLs in Laravel PHP
DESCRIPTION: This snippet illustrates how to generate a temporary signed URL that expires after a specified duration. The `URL::temporarySignedRoute` method ensures that the URL is only valid for 30 minutes, adding an extra layer of security for time-sensitive links.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\URL;

return URL::temporarySignedRoute(
    'unsubscribe', now()->addMinutes(30), ['user' => 1]
);
```

----------------------------------------

TITLE: Get Base Path with base_path (PHP)
DESCRIPTION: The `base_path` function returns the fully qualified path to your application's root directory. You may also use the `base_path` function to generate a fully qualified path to a given file relative to the project root directory.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_83

LANGUAGE: php
CODE:
```
$path = base_path();

$path = base_path('vendor/bin');
```

----------------------------------------

TITLE: Automatically Localize Laravel Notifications by User Preference
DESCRIPTION: This example demonstrates how to automatically localize Laravel notifications based on a user's preferred locale. By implementing the `Illuminate\Contracts\Translation\HasLocalePreference` contract and defining the `preferredLocale` method on your notifiable model, Laravel will automatically use the stored locale when sending notifications, eliminating the need for explicit `locale` method calls.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_80

LANGUAGE: php
CODE:
```
use Illuminate\Contracts\Translation\HasLocalePreference;

class User extends Model implements HasLocalePreference
{
    /**
     * Get the user's preferred locale.
     */
    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
```

LANGUAGE: php
CODE:
```
$user->notify(new InvoicePaid($invoice));
```

----------------------------------------

TITLE: Handling Stripe Checkout Success and Updating Order Status in Laravel
DESCRIPTION: This PHP code illustrates how to handle the redirect after a successful Stripe Checkout. It retrieves the `session_id` from the request, fetches the Stripe Checkout session, checks its payment status, extracts the `order_id` from the session's `metadata`, and updates the corresponding internal `Order` status to 'completed'.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use App\Models\Order;
use Illuminate\Http\Request;
use Laravel\Cashier\Cashier;

Route::get('/checkout/success', function (Request $request) {
    $sessionId = $request->get('session_id');

    if ($sessionId === null) {
        return;
    }

    $session = Cashier::stripe()->checkout->sessions->retrieve($sessionId);

    if ($session->payment_status !== 'paid') {
        return;
    }

    $orderId = $session['metadata']['order_id'] ?? null;

    $order = Order::findOrFail($orderId);

    $order->update(['status' => 'completed']);

    return view('checkout-success', ['order' => $order]);
})->name('checkout-success');
```

----------------------------------------

TITLE: Constructing URLs with URI Templates using Laravel HTTP Client (PHP)
DESCRIPTION: Illustrates how to use the `withUrlParameters` method to define parameters for a URI template, allowing for dynamic URL construction based on the RFC 6570 specification.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_2

LANGUAGE: php
CODE:
```
Http::withUrlParameters([
    'endpoint' => 'https://laravel.com',
    'page' => 'docs',
    'version' => '11.x',
    'topic' => 'validation',
])->get('{+endpoint}/{page}/{version}/{topic}');
```

----------------------------------------

TITLE: Counting Related Models with withCount in PHP
DESCRIPTION: Demonstrates the basic usage of the withCount method to add a count of a relationship (comments) as an attribute (comments_count) to the parent model (Post).
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_101

LANGUAGE: php
CODE:
```
use App\Models\Post;

$posts = Post::withCount('comments')->get();

foreach ($posts as $post) {
    echo $post->comments_count;
}
```

----------------------------------------

TITLE: Laravel Validation Rule: present
DESCRIPTION: The field under validation must exist in the input data.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_138

LANGUAGE: APIDOC
CODE:
```
present
```

----------------------------------------

TITLE: Creating Models with Factories in PHPUnit
DESCRIPTION: Demonstrates how to use a model factory to create a new Eloquent model instance within a PHPUnit test method. This approach streamlines the setup of test data by utilizing pre-configured model attributes, reducing boilerplate code.
SOURCE: https://github.com/laravel/docs/blob/12.x/database-testing.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use App\Models\User;

public function test_models_can_be_instantiated(): void
{
    $user = User::factory()->create();

    // ...
}
```

----------------------------------------

TITLE: Defining Many-to-Many Polymorphic Relation (PHP)
DESCRIPTION: Defines the forward side of a many-to-many polymorphic relationship on a parent model (`Post`). It uses the `morphToMany` method to link the `Post` model to the `Tag` model via the intermediate 'taggable' relationship name.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_70

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Post extends Model
{
    /**
     * Get all of the tags for the post.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }
}
```

----------------------------------------

TITLE: Authorizing Actions Without Model via Middleware in Laravel
DESCRIPTION: This PHP snippet demonstrates authorizing an action that doesn't require a model instance, such as 'create', using the `can` middleware on a Laravel route. Instead of a route parameter, the fully qualified class name `App\Models\Post` is passed. This tells Laravel to use the policy associated with the `Post` model for the 'create' action.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_34

LANGUAGE: PHP
CODE:
```
Route::post('/post', function () {
    // The current user may create posts...
})->middleware('can:create,App\Models\Post');
```

----------------------------------------

TITLE: Sharing Context Across All Log Channels in Laravel Middleware
DESCRIPTION: This middleware uses `Log::shareContext` to provide contextual information, such as a `request-id`, to all existing and newly created logging channels. This ensures consistent context across the entire application's logging infrastructure.
SOURCE: https://github.com/laravel/docs/blob/12.x/logging.md#_snippet_10

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class AssignRequestId
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestId = (string) Str::uuid();

        Log::shareContext([
            'request-id' => $requestId
        ]);

        // ...
    }
}
```

----------------------------------------

TITLE: Asserting JSON Property Types
DESCRIPTION: This snippet demonstrates how to assert that specific properties in a JSON response are of a certain type. It uses `whereType` to check 'id' as an integer and `whereAllType` to check 'users.0.name' as a string and 'meta' as an array.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_38

LANGUAGE: PHP
CODE:
```
$response->assertJson(fn (AssertableJson $json) =>
    $json->whereType('id', 'integer')
        ->whereAllType([
            'users.0.name' => 'string',
            'meta' => 'array'
        ])
);
```

----------------------------------------

TITLE: Laravel Tap Helper Function for Chaining Operations
DESCRIPTION: The `tap()` helper function allows you to perform operations on a given value and then return that same value, regardless of the closure's return. This is useful for chaining methods or performing side effects while maintaining the original object reference. It can also be used as a trait on classes to enable a `tap` method.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_146

LANGUAGE: php
CODE:
```
$user = tap(User::first(), function (User $user) {
    $user->name = 'Taylor';

    $user->save();
});
```

LANGUAGE: php
CODE:
```
$user = tap($user)->update([
    'name' => $name,
    'email' => $email,
]);
```

LANGUAGE: php
CODE:
```
return $user->tap(function (User $user) {
    // ...
});
```

----------------------------------------

TITLE: Laravel Validation Rule: required_without
DESCRIPTION: The `required_without` rule makes the field under validation present and not empty only when any of the specified `_foo_`, `_bar_`, etc., fields are empty or not present.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_157

LANGUAGE: APIDOC
CODE:
```
required_without:_foo_,_bar_,...
```

----------------------------------------

TITLE: Cast Arrays of Enums in Laravel Eloquent
DESCRIPTION: Laravel provides `AsEnumArrayObject` and `AsEnumCollection` casts for storing and retrieving arrays of enum values in a single database column. This example uses `AsEnumCollection` to cast the `statuses` attribute as a collection of `ServerStatus` enums.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-mutators.md#_snippet_28

LANGUAGE: PHP
CODE:
```
use App\Enums\ServerStatus;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;

/**
 * Get the attributes that should be cast.
 *
 * @return array<string, string>
 */
protected function casts(): array
{
    return [
        'statuses' => AsEnumCollection::of(ServerStatus::class),
    ];
}
```

----------------------------------------

TITLE: Setting Laravel Broadcasting Connection to Pusher
DESCRIPTION: This environment variable configures Laravel's broadcasting system to use Pusher Channels as the default driver for sending events. Setting `BROADCAST_CONNECTION` to `pusher` ensures that all broadcast events are routed through the Pusher service.
SOURCE: https://github.com/laravel/docs/blob/12.x/broadcasting.md#_snippet_7

LANGUAGE: ini
CODE:
```
BROADCAST_CONNECTION=pusher
```

----------------------------------------

TITLE: Add HasApiTokens Trait to Laravel User Model
DESCRIPTION: After installation, add the `Laravel\Passport\HasApiTokens` trait and `Laravel\Passport\Contracts\OAuthenticatable` interface to your `App\Models\User` model. This provides helper methods for token and scope inspection.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\Contracts\OAuthenticatable;
use Laravel\Passport\HasApiTokens;

class User extends Authenticatable implements OAuthenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
}
```

----------------------------------------

TITLE: Enable Laravel Passport Password Grant Type
DESCRIPTION: Illustrates how to enable the password grant type in your Laravel application's `AppServiceProvider`. This allows first-party clients to obtain access tokens using user credentials directly.
SOURCE: https://github.com/laravel/docs/blob/12.x/passport.md#_snippet_33

LANGUAGE: php
CODE:
```
/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Passport::enablePasswordGrant();
}
```

----------------------------------------

TITLE: Advanced Usage of Laravel Exists Validation Rule
DESCRIPTION: Illustrates various methods to customize the `exists` validation rule, including specifying a custom database column name, a specific database connection, an Eloquent model, or defining a custom query using the `Rule` class. It also shows how to validate an array of values.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_100

LANGUAGE: php
CODE:
```
'state' => 'exists:states,abbreviation'
```

LANGUAGE: php
CODE:
```
'email' => 'exists:connection.staff,email'
```

LANGUAGE: php
CODE:
```
'user_id' => 'exists:App\Models\User,id'
```

LANGUAGE: php
CODE:
```
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

Validator::make($data, [
    'email' => [
        'required',
        Rule::exists('staff')->where(function (Builder $query) {
            $query->where('account_id', 1);
        }),
    ],
]);
```

LANGUAGE: php
CODE:
```
'state' => Rule::exists('states', 'abbreviation'),
```

LANGUAGE: php
CODE:
```
'states' => ['array', Rule::exists('states', 'abbreviation')],
```

----------------------------------------

TITLE: Handling Open Transactions with Queue Looping in Laravel
DESCRIPTION: This snippet demonstrates using `Queue::looping` to register a callback that executes before a worker fetches a new job. It's particularly useful for rolling back any open database transactions that might have been left by a previously failed job, ensuring a clean state for the next job.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_154

LANGUAGE: PHP
CODE:
```
use IlluminateSupportFacadesDB;
use IlluminateSupportFacadesQueue;

Queue::looping(function () {
    while (DB::transactionLevel() > 0) {
        DB::rollBack();
    }
});
```

----------------------------------------

TITLE: Inspecting Database Overview with Laravel Artisan
DESCRIPTION: This command provides a high-level overview of your database, including its size, type, number of open connections, and a summary of its tables. It's a quick way to get insights into your database's state.
SOURCE: https://github.com/laravel/docs/blob/12.x/database.md#_snippet_26

LANGUAGE: shell
CODE:
```
php artisan db:show
```

----------------------------------------

TITLE: Partition an Array based on a Truth Test using Arr::partition (PHP)
DESCRIPTION: The `Arr::partition` method separates elements of an array into two new arrays: one containing elements that pass a given truth test, and another for those that do not. It can be combined with PHP array destructuring for convenient assignment, making it easy to categorize array elements.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_34

LANGUAGE: php
CODE:
```
<?php

use Illuminate\Support\Arr;

$numbers = [1, 2, 3, 4, 5, 6];

[$underThree, $equalOrAboveThree] = Arr::partition($numbers, function (int $i) {
    return $i < 3;
});

dump($underThree);

// [1, 2]

dump($equalOrAboveThree);

// [3, 4, 5, 6]
```

----------------------------------------

TITLE: Defining Basic Event Listener in Laravel PHP
DESCRIPTION: This snippet defines a basic event listener class, `SendShipmentNotification`, which handles the `OrderShipped` event. The `handle` method receives the event instance, allowing access to its properties (e.g., `$event->order`). Listeners are resolved via Laravel's service container, enabling automatic dependency injection in their constructors.
SOURCE: https://github.com/laravel/docs/blob/12.x/events.md#_snippet_15

LANGUAGE: php
CODE:
```
<?php

namespace AppListeners;

use AppEventsOrderShipped;

class SendShipmentNotification
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(OrderShipped $event): void
    {
        // Access the order using $event->order...
    }
}
```

----------------------------------------

TITLE: Resetting All Migrations (Laravel)
DESCRIPTION: This command rolls back all migrations that have been run for the application. It effectively reverts the database to its initial state before any migrations were applied.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_14

LANGUAGE: Shell
CODE:
```
php artisan migrate:reset
```

----------------------------------------

TITLE: Constraining Route Parameters with `whereNumber` and `whereAlpha` Helpers in Laravel
DESCRIPTION: This example demonstrates using Laravel's convenient helper methods, `whereNumber` and `whereAlpha`, to apply common regular expression constraints to route parameters, ensuring `id` is numeric and `name` is alphabetic.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_30

LANGUAGE: PHP
CODE:
```
Route::get('/user/{id}/{name}', function (string $id, string $name) {
    // ...
})->whereNumber('id')->whereAlpha('name');
```

----------------------------------------

TITLE: Retrieve First Error Message for a Field
DESCRIPTION: Shows how to get the first error message for a specific field from the `Illuminate\Support\MessageBag` instance using the `first()` method. This is commonly used for displaying a single error per input field.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_47

LANGUAGE: php
CODE:
```
$errors = $validator->errors();

echo $errors->first('email');
```

----------------------------------------

TITLE: Asserting HTTP Status Code in Laravel PHP
DESCRIPTION: This method asserts that the HTTP response has a specific status code. It is a fundamental assertion for verifying the outcome of an HTTP request, such as a successful response (200), a redirect (302), or a client error (404).
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_114

LANGUAGE: PHP
CODE:
```
$response->assertStatus($code);
```

----------------------------------------

TITLE: Specify Columns and Distinct Results in Laravel Select Clauses
DESCRIPTION: Illustrates how to select specific columns from a database table using the `select()` method, including aliasing columns. It also shows how to retrieve distinct results with `distinct()` and add columns to an existing select clause with `addSelect()`.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_13

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

$users = DB::table('users')
    ->select('name', 'email as user_email')
    ->get();
```

LANGUAGE: php
CODE:
```
$users = DB::table('users')->distinct()->get();
```

LANGUAGE: php
CODE:
```
$query = DB::table('users')->select('name');

$users = $query->addSelect('age')->get();
```

----------------------------------------

TITLE: Generating Resource Controller with Form Requests (Shell)
DESCRIPTION: This command generates a resource controller along with dedicated form request classes for the `store` and `update` methods, facilitating robust validation logic for incoming requests.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_16

LANGUAGE: shell
CODE:
```
php artisan make:controller PhotoController --model=Photo --resource --requests
```

----------------------------------------

TITLE: Scheduling Daily Batch Pruning in Laravel (PHP)
DESCRIPTION: Schedules the `queue:prune-batches` Artisan command to run daily, removing finished job batches older than 24 hours from the `job_batches` table. This helps prevent table bloat.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_83

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Schedule;

Schedule::command('queue:prune-batches')->daily();
```

----------------------------------------

TITLE: Attaching Rate Limiters to Routes (PHP)
DESCRIPTION: This code demonstrates how to apply a defined rate limiter, 'uploads', to a group of routes using the `throttle` middleware. This ensures that all routes within the group adhere to the rate limiting policies defined for 'uploads', protecting multiple endpoints with a single configuration.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_76

LANGUAGE: php
CODE:
```
Route::middleware(['throttle:uploads'])->group(function () {
    Route::post('/audio', function () {
        // ...
    });

    Route::post('/video', function () {
        // ...
    });
});
```

----------------------------------------

TITLE: Enabling Maintenance Mode with Secret Bypass Token (Shell)
DESCRIPTION: This command enables maintenance mode and allows bypassing it using a secret token. Navigating to the application URL with this token issues a bypass cookie, enabling normal browsing for authorized users.
SOURCE: https://github.com/laravel/docs/blob/12.x/configuration.md#_snippet_24

LANGUAGE: shell
CODE:
```
php artisan down --secret="1630542a-246b-4b66-afa1-dd72a4c43515"
```

----------------------------------------

TITLE: Abort if condition is true (Laravel PHP)
DESCRIPTION: The `abort_if` function throws an HTTP exception if a given boolean expression evaluates to `true`. It supports providing the exception's response text as the third argument and an array of custom response headers as the fourth argument.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_100

LANGUAGE: PHP
CODE:
```
abort_if(! Auth::user()->isAdmin(), 403);
```

----------------------------------------

TITLE: Key Array by Specific Field (Arr::keyBy)
DESCRIPTION: The `Arr::keyBy` method transforms an array of arrays (or objects) into an associative array, using the value of a specified key from each inner item as the new top-level key. If multiple items share the same key, only the last one encountered will be present in the resulting array, effectively overwriting previous entries.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_28

LANGUAGE: php
CODE:
```
use Illuminate\Support\Arr;

$array = [
    ['product_id' => 'prod-100', 'name' => 'Desk'],
    ['product_id' => 'prod-200', 'name' => 'Chair']
];

$keyed = Arr::keyBy($array, 'product_id');

/*
    [
        'prod-100' => ['product_id' => 'prod-100', 'name' => 'Desk'],
        'prod-200' => ['product_id' => 'prod-200', 'name' => 'Chair']
    ]
*/
```

----------------------------------------

TITLE: Setting Foreign Key Constraint Actions (onUpdate/onDelete) in Laravel
DESCRIPTION: Demonstrates how to define 'on update' and 'on delete' actions for foreign key constraints, such as `cascade`, `restrict`, or `null`, to control referential integrity behavior.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_112

LANGUAGE: php
CODE:
```
$table->foreignId('user_id')
    ->constrained()
    ->onUpdate('cascade')
    ->onDelete('cascade');
```

----------------------------------------

TITLE: Setting Default URL Parameters in Laravel Middleware PHP
DESCRIPTION: This middleware demonstrates how to use `URL::defaults` to set a request-wide default value for a URL parameter, specifically `locale`. This eliminates the need to explicitly pass the `locale` parameter when generating URLs via the `route` helper, making URL generation more convenient. It accesses the current user's locale from the request.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_25

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Symfony\Component\HttpFoundation\Response;

class SetDefaultLocaleForUrls
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        URL::defaults(['locale' => $request->user()->locale]);

        return $next($request);
    }
}
```

----------------------------------------

TITLE: Generating Policy with Model Methods in Shell
DESCRIPTION: This Artisan command generates a `PostPolicy` class along with pre-defined example policy methods (e.g., `viewAny`, `view`, `create`, `update`, `delete`, `restore`, `forceDelete`) related to the `Post` model. This option provides a convenient starting point for common CRUD authorization logic.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_16

LANGUAGE: shell
CODE:
```
php artisan make:policy PostPolicy --model=Post
```

----------------------------------------

TITLE: Handling Eloquent Events After Database Commit (PHP)
DESCRIPTION: This PHP snippet demonstrates how to defer observer event handling until after a database transaction has been successfully committed. By implementing the `ShouldHandleEventsAfterCommit` interface on the observer class, the `created` event handler (and other event handlers) will only execute if the transaction completes, preventing issues with uncommitted data.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_111

LANGUAGE: php
CODE:
```
<?php

namespace AppObservers;

use AppModelsUser;
use IlluminateContractsEventsShouldHandleEventsAfterCommit;

class UserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // ...
    }
}
```

----------------------------------------

TITLE: Dispatch a Job to the Laravel Queue
DESCRIPTION: The `dispatch` function pushes a specified job onto Laravel's job queue for asynchronous processing. This is commonly used for long-running tasks that shouldn't block the user's request.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_118

LANGUAGE: php
CODE:
```
dispatch(new App\Jobs\SendEmails);
```

----------------------------------------

TITLE: Manipulating Time in PHPUnit Tests
DESCRIPTION: This snippet showcases the `travel`, `travelTo`, and `travelBack` methods available in Laravel's base test case for manipulating time within PHPUnit tests. These helpers enable testing of time-dependent features by allowing the simulation of different dates and times.
SOURCE: https://github.com/laravel/docs/blob/12.x/mocking.md#_snippet_11

LANGUAGE: PHP
CODE:
```
public function test_time_can_be_manipulated(): void
{
    // Travel into the future...
    $this->travel(5)->milliseconds();
    $this->travel(5)->seconds();
    $this->travel(5)->minutes();
    $this->travel(5)->hours();
    $this->travel(5)->days();
    $this->travel(5)->weeks();
    $this->travel(5)->years();

    // Travel into the past...
    $this->travel(-5)->hours();

    // Travel to an explicit time...
    $this->travelTo(now()->subHours(6));

    // Return back to the present time...
    $this->travelBack();
}
```

----------------------------------------

TITLE: Asserting Unprocessable Entity HTTP Status Code in Laravel PHP
DESCRIPTION: This method asserts that the HTTP response has an 'Unprocessable Entity' (422) status code. This status typically indicates that the server understands the content type of the request entity, but was unable to process the contained instructions, often due to validation errors.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_118

LANGUAGE: PHP
CODE:
```
$response->assertUnprocessable();
```

----------------------------------------

TITLE: Laravel Validation Rule: required_with
DESCRIPTION: The `required_with` rule makes the field under validation present and not empty only if any of the specified `_foo_`, `_bar_`, etc., fields are present and not empty.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_155

LANGUAGE: APIDOC
CODE:
```
required_with:_foo_,_bar_,...
```

----------------------------------------

TITLE: Resolve Class or Interface Instances with resolve()
DESCRIPTION: The `resolve` function uses Laravel's service container to resolve a given class or interface name into an instantiated object. This is useful for dependency injection and accessing services.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_142

LANGUAGE: php
CODE:
```
$api = resolve('HelpSpot\API');
```

----------------------------------------

TITLE: Retrieving User Details from Socialite Callback
DESCRIPTION: This comprehensive snippet demonstrates how to access various user properties and methods from the `User` object returned by Socialite's `user()` method after an OAuth callback. It covers tokens, refresh tokens, expiration, and basic profile information for both OAuth 1.0 and 2.0 providers.
SOURCE: https://github.com/laravel/docs/blob/12.x/socialite.md#_snippet_9

LANGUAGE: php
CODE:
```
use Laravel\Socialite\Facades\Socialite;

Route::get('/auth/callback', function () {
    $user = Socialite::driver('github')->user();

    // OAuth 2.0 providers...
    $token = $user->token;
    $refreshToken = $user->refreshToken;
    $expiresIn = $user->expiresIn;

    // OAuth 1.0 providers...
    $token = $user->token;
    $tokenSecret = $user->tokenSecret;

    // All providers...
    $user->getId();
    $user->getNickname();
    $user->getName();
    $user->getEmail();
    $user->getAvatar();
});
```

----------------------------------------

TITLE: Log User Out, Invalidate Session, and Regenerate CSRF Token in Laravel
DESCRIPTION: Provides a comprehensive method for logging a user out of a Laravel application. It uses `Auth::logout()` to remove authentication info, invalidates the session, regenerates the CSRF token, and redirects the user to the application root.
SOURCE: https://github.com/laravel/docs/blob/12.x/authentication.md#_snippet_23

LANGUAGE: php
CODE:
```
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

/**
 * Log the user out of the application.
 */
public function logout(Request $request): RedirectResponse
{
    Auth::logout();

    $request->session()->invalidate();

    $request->session()->regenerateToken();

    return redirect('/');
}
```

----------------------------------------

TITLE: Mocking Cache Facade with PHPUnit
DESCRIPTION: This snippet shows how to mock the `Cache` facade's `get` method within a PHPUnit feature test. It configures an expectation for `Cache::get('key')` to return 'value', enabling isolated testing of controller actions that interact with the cache without relying on actual cache storage.
SOURCE: https://github.com/laravel/docs/blob/12.x/mocking.md#_snippet_7

LANGUAGE: PHP
CODE:
```
<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class UserControllerTest extends TestCase
{
    public function test_get_index(): void
    {
        Cache::expects('get')
            ->with('key')
            ->andReturn('value');

        $response = $this->get('/users');

        // ...
    }
}
```

----------------------------------------

TITLE: Creating a Date Column in Laravel Migrations
DESCRIPTION: The `date` method defines a `DATE` equivalent column. This type is used to store only the date part (year, month, day) without any time component.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_37

LANGUAGE: PHP
CODE:
```
$table->date('created_at');
```

----------------------------------------

TITLE: Check if User is on Trial Period with `onTrial` Method (PHP)
DESCRIPTION: The `onTrial` method determines if a user is still within their trial period. This can be useful for displaying warnings or specific UI elements to users during their trial.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_77

LANGUAGE: PHP
CODE:
```
if ($user->subscription('default')->onTrial()) {
    // ...
}
```

----------------------------------------

TITLE: Retrieve User's Trial Ending Date
DESCRIPTION: Illustrates how to retrieve the trial ending date for a user. This method returns a Carbon date instance if the user is on a trial or `null` if they aren't. An optional subscription type parameter can be passed to get the trial ending date for a specific subscription.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_138

LANGUAGE: PHP
CODE:
```
if ($user->onTrial()) {
    $trialEndsAt = $user->trialEndsAt('main');
}
```

----------------------------------------

TITLE: Checking User's General Trial Status (PHP)
DESCRIPTION: This method checks if the user is currently within any trial period, regardless of whether it's a subscription-specific trial or a generic trial. It returns `true` if the user is on trial, `false` otherwise.
SOURCE: https://github.com/laravel/docs/blob/12.x/cashier-paddle.md#_snippet_85

LANGUAGE: PHP
CODE:
```
if ($user->onTrial()) {
    // ...
}
```

----------------------------------------

TITLE: Viewing Help Screen for an Artisan Command
DESCRIPTION: This command shows the help documentation for a specific Artisan command, detailing its arguments, options, and usage examples. It's useful for understanding how to properly use a particular command.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_1

LANGUAGE: shell
CODE:
```
php artisan help migrate
```

----------------------------------------

TITLE: Faking Laravel Processes for Testing with PHPUnit
DESCRIPTION: This PHPUnit test snippet demonstrates faking all Laravel processes using `Process::fake()` to prevent actual command execution. It includes assertions to verify that a specific command (`bash import.sh`) was invoked, showing both basic and advanced assertion capabilities.
SOURCE: https://github.com/laravel/docs/blob/12.x/processes.md#_snippet_34

LANGUAGE: php
CODE:
```
<?php

namespace Tests\Feature;

use Illuminate\Process\PendingProcess;
use Illuminate\Contracts\Process\ProcessResult;
use Illuminate\Support\Facades\Process;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    public function test_process_is_invoked(): void
    {
        Process::fake();

        $response = $this->get('/import');

        // Simple process assertion...
        Process::assertRan('bash import.sh');

        // Or, inspecting the process configuration...
        Process::assertRan(function (PendingProcess $process, ProcessResult $result) {
            return $process->command === 'bash import.sh' &&
                   $process->timeout === 60;
        });
    }
}
```

----------------------------------------

TITLE: Laravel Prompts Multi-select Function Usage
DESCRIPTION: Examples demonstrating the `multiselect` function for multiple-choice prompts in Laravel Prompts. Covers basic usage, setting default selected options and hints, using associative arrays for options, customizing scroll behavior, enforcing required selections, and implementing custom validation logic.
SOURCE: https://github.com/laravel/docs/blob/12.x/prompts.md#_snippet_24

LANGUAGE: php
CODE:
```
use function Laravel\Prompts\multiselect;

$permissions = multiselect(
    label: 'What permissions should be assigned?',
    options: ['Read', 'Create', 'Update', 'Delete']
);
```

LANGUAGE: php
CODE:
```
use function Laravel\Prompts\multiselect;

$permissions = multiselect(
    label: 'What permissions should be assigned?',
    options: ['Read', 'Create', 'Update', 'Delete'],
    default: ['Read', 'Create'],
    hint: 'Permissions may be updated at any time.'
);
```

LANGUAGE: php
CODE:
```
$permissions = multiselect(
    label: 'What permissions should be assigned?',
    options: [
        'read' => 'Read',
        'create' => 'Create',
        'update' => 'Update',
        'delete' => 'Delete'
    ],
    default: ['read', 'create']
);
```

LANGUAGE: php
CODE:
```
$categories = multiselect(
    label: 'What categories should be assigned?',
    options: Category::pluck('name', 'id'),
    scroll: 10
);
```

LANGUAGE: php
CODE:
```
$categories = multiselect(
    label: 'What categories should be assigned?',
    options: Category::pluck('name', 'id'),
    required: true
);
```

LANGUAGE: php
CODE:
```
$categories = multiselect(
    label: 'What categories should be assigned?',
    options: Category::pluck('name', 'id'),
    required: 'You must select at least one category'
);
```

LANGUAGE: php
CODE:
```
$permissions = multiselect(
    label: 'What permissions should the user have?',
    options: [
        'read' => 'Read',
        'create' => 'Create',
        'update' => 'Update',
        'delete' => 'Delete'
    ],
    validate: fn (array $values) => ! in_array('read', $values)
        ? 'All users require the read permission.'
        : null
);
```

----------------------------------------

TITLE: Manually Failing a Laravel Job with Exception or Message
DESCRIPTION: This PHP snippet illustrates how to manually fail a job while providing an exception object or a string message. The message will be converted to an exception, providing more context for the failure in the failed jobs table.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_67

LANGUAGE: php
CODE:
```
$this->fail($exception);

$this->fail('Something went wrong.');
```

----------------------------------------

TITLE: Defining Inverse Many-to-Many Relationship in Laravel Eloquent (PHP)
DESCRIPTION: Defines the inverse of the many-to-many relationship on the related model (`Role`), allowing access back to the defining models (`User`) using the `belongsToMany` method, mirroring the definition on the `User` model.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_42

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    /**
     * The users that belong to the role.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
}
```

----------------------------------------

TITLE: Laravel Validation: `max` Rule
DESCRIPTION: The `max` rule validates that the field under validation is less than or equal to a specified maximum `_value_`. Evaluation for strings, numerics, arrays, and files follows the same conventions as the `size` rule.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_121

LANGUAGE: APIDOC
CODE:
```
max:_value_
```

----------------------------------------

TITLE: Demonstrating Memory Leak with Static Array in Laravel Octane - PHP
DESCRIPTION: This snippet demonstrates a common memory leak pattern in Octane applications where data is continuously added to a statically maintained array. Since Octane keeps the application in memory between requests, this leads to increasing memory consumption over time.
SOURCE: https://github.com/laravel/docs/blob/12.x/octane.md#_snippet_34

LANGUAGE: php
CODE:
```
use App\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * Handle an incoming request.
 */
public function index(Request $request): array
{
    Service::$data[] = Str::random(10);

    return [
        // ...
    ];
}
```

----------------------------------------

TITLE: Implement Isolatable Interface for Laravel Commands
DESCRIPTION: To ensure only one instance of a Laravel Artisan command runs at a time, implement the `Illuminate\Contracts\Console\Isolatable` interface on the command class. This automatically enables the `--isolated` option, allowing Laravel to acquire an atomic lock using the configured cache driver.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_15

LANGUAGE: php
CODE:
```
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Contracts\Console\Isolatable;

class SendEmails extends Command implements Isolatable
{
    // ...
}
```

----------------------------------------

TITLE: Checking Specific Product or Price Subscription Status in Blade
DESCRIPTION: These Blade examples illustrate how to verify if a user is subscribed to a particular product or price. The `subscribedToProduct()` and `subscribedToPrice()` methods allow for granular control over content access based on specific subscription plans.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_17

LANGUAGE: blade
CODE:
```
@if ($user->subscribedToProduct('pro_basic'))
    <p>You are subscribed to our Basic product.</p>
@endif

@if ($user->subscribedToPrice('price_basic_monthly'))
    <p>You are subscribed to our monthly Basic plan.</p>
@endif
```

----------------------------------------

TITLE: Check if value is blank (Laravel PHP)
DESCRIPTION: The `blank` function determines whether the given value is considered 'blank'. This includes empty strings, strings with only whitespace, null, and empty collections. It returns `true` for blank values and `false` for non-blank values like 0, true, or false.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_106

LANGUAGE: PHP
CODE:
```
blank('');
blank('   ');
blank(null);
blank(collect());

// true

blank(0);
blank(true);
blank(false);

// false
```

----------------------------------------

TITLE: Defining Time-Based Retries in Laravel Job Class
DESCRIPTION: This PHP method specifies a time limit for job retries instead of a fixed number of attempts. The `retryUntil()` method should return a `DateTime` instance, after which the job will no longer be attempted, regardless of how many times it has run.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_59

LANGUAGE: php
CODE:
```
use DateTime;

/**
 * Determine the time at which the job should timeout.
 */
public function retryUntil(): DateTime
{
    return now()->addMinutes(10);
}
```

----------------------------------------

TITLE: Defining Retry Timeout for Queued Listeners in Laravel PHP
DESCRIPTION: This snippet shows an alternative to `$tries` for controlling listener retries. By implementing the `retryUntil` method, a listener can be attempted any number of times within a specified timeframe, returning a `DateTime` instance indicating when retries should cease.
SOURCE: https://github.com/laravel/docs/blob/12.x/events.md#_snippet_24

LANGUAGE: PHP
CODE:
```
use DateTime;

/**
 * Determine the time at which the listener should timeout.
 */
public function retryUntil(): DateTime
{
    return now()->addMinutes(5);
}
```

----------------------------------------

TITLE: Dynamic Rate Limiting by User Status (PHP)
DESCRIPTION: This snippet demonstrates dynamic rate limiting where VIP customers have no limit (`Limit::none()`), while others are limited to 100 requests per minute. This allows for flexible throttling policies based on user attributes or request characteristics.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_71

LANGUAGE: php
CODE:
```
RateLimiter::for('uploads', function (Request $request) {
    return $request->user()->vipCustomer()
        ? Limit::none()
        : Limit::perMinute(100);
});
```

----------------------------------------

TITLE: Caching Laravel Routes
DESCRIPTION: This Artisan command reduces all route registrations into a single method call within a cached file. For large applications with many routes, this significantly improves the performance of route registration by avoiding repeated parsing of route definitions.
SOURCE: https://github.com/laravel/docs/blob/12.x/deployment.md#_snippet_6

LANGUAGE: Shell
CODE:
```
php artisan route:cache
```

----------------------------------------

TITLE: Adding Bearer Token - Laravel HTTP Client - PHP
DESCRIPTION: Shows how to quickly add a bearer token to the `Authorization` header of a request using the `withToken` method. This method automatically formats the header value as 'Bearer [token]'.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_16

LANGUAGE: php
CODE:
```
$response = Http::withToken('token')->post(/* ... */);
```

----------------------------------------

TITLE: Executing Artisan Commands Programmatically with Parameters in Laravel
DESCRIPTION: Shows how to execute an Artisan command from outside the CLI, such as from a route or controller, using `Artisan::call` with the command's signature and an array of parameters. Returns the exit code.
SOURCE: https://github.com/laravel/docs/blob/12.x/artisan.md#_snippet_60

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Artisan;

Route::post('/user/{user}/mail', function (string $user) {
    $exitCode = Artisan::call('mail:send', [
        'user' => $user, '--queue' => 'default'
    ]);

    // ...
});
```

----------------------------------------

TITLE: Creating a Feature Test in Laravel
DESCRIPTION: This command generates a new test class named `UserTest` within the `tests/Feature` directory. Feature tests are typically used for testing larger portions of the application, including HTTP requests.
SOURCE: https://github.com/laravel/docs/blob/12.x/testing.md#_snippet_0

LANGUAGE: Shell
CODE:
```
php artisan make:test UserTest
```

----------------------------------------

TITLE: Hiding Attributes on Models in Laravel Eloquent Collection (PHP)
DESCRIPTION: The `makeHidden` method modifies the visibility of attributes on each model within the collection, hiding specified attributes even if they are typically visible. This is useful for preventing sensitive data from being serialized.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-collections.md#_snippet_14

LANGUAGE: PHP
CODE:
```
$users = $users->makeHidden(['address', 'phone_number']);
```

----------------------------------------

TITLE: Route Laravel Slack Notifications to a Specific Channel
DESCRIPTION: This snippet demonstrates how to direct Laravel Slack notifications to a predefined channel within the configured workspace. By implementing the `routeNotificationForSlack` method on your notifiable model and returning a channel string like `#support-channel`, notifications will be sent to that specific channel. This method defers to the `services.php` configuration for the associated workspace's Bot User OAuth token.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_77

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * Route notifications for the Slack channel.
     */
    public function routeNotificationForSlack(Notification $notification): mixed
    {
        return '#support-channel';
    }
}
```

----------------------------------------

TITLE: Sorting Laravel Collection with sortBy and Custom Closure - PHP
DESCRIPTION: This snippet demonstrates advanced sorting using `sortBy` by providing a custom closure. The closure defines the sorting logic, in this case, sorting products based on the number of colors they have. The `values()` method is used to reset the collection's keys after sorting.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_144

LANGUAGE: PHP
CODE:
```
$collection = collect([
    ['name' => 'Desk', 'colors' => ['Black', 'Mahogany']],
    ['name' => 'Chair', 'colors' => ['Black']],
    ['name' => 'Bookcase', 'colors' => ['Red', 'Beige', 'Brown']],
]);

$sorted = $collection->sortBy(function (array $product, int $key) {
    return count($product['colors']);
});

$sorted->values()->all();

/*
    [
        ['name' => 'Chair', 'colors' => ['Black']],
        ['name' => 'Desk', 'colors' => ['Black', 'Mahogany']],
        ['name' => 'Bookcase', 'colors' => ['Red', 'Beige', 'Brown']],
    ]
*/
```

----------------------------------------

TITLE: Defining Basic View Route in Laravel PHP
DESCRIPTION: This snippet demonstrates how to define a simple route that directly returns a view using `Route::view()`. It takes a URI (`/welcome`) and a view name (`welcome`) as arguments, providing a convenient shortcut for displaying static views without requiring a controller or closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_12

LANGUAGE: php
CODE:
```
Route::view('/welcome', 'welcome');
```

----------------------------------------

TITLE: Testing Console Command with Mocked Input and Output
DESCRIPTION: This snippet illustrates how to test a Laravel console command that expects user input and produces specific output. It uses `expectsQuestion` to mock answers for prompts, `expectsOutput` to assert expected output, `doesntExpectOutput` to assert the absence of specific output, and `assertExitCode` to verify successful execution.
SOURCE: https://github.com/laravel/docs/blob/12.x/console-tests.md#_snippet_4

LANGUAGE: Pest
CODE:
```
test('console command', function () {
    $this->artisan('question')
        ->expectsQuestion('What is your name?', 'Taylor Otwell')
        ->expectsQuestion('Which language do you prefer?', 'PHP')
        ->expectsOutput('Your name is Taylor Otwell and you prefer PHP.')
        ->doesntExpectOutput('Your name is Taylor Otwell and you prefer Ruby.')
        ->assertExitCode(0);
});
```

LANGUAGE: PHPUnit
CODE:
```
/**
 * Test a console command.
 */
public function test_console_command(): void
{
    $this->artisan('question')
        ->expectsQuestion('What is your name?', 'Taylor Otwell')
        ->expectsQuestion('Which language do you prefer?', 'PHP')
        ->expectsOutput('Your name is Taylor Otwell and you prefer PHP.')
        ->doesntExpectOutput('Your name is Taylor Otwell and you prefer Ruby.')
        ->assertExitCode(0);
}
```

----------------------------------------

TITLE: Use a Laravel Dusk component within a test
DESCRIPTION: After defining a component, it can be easily used within Dusk tests. The `within` method scopes browser actions to the component, allowing its encapsulated methods, like `selectDate`, to be called. This centralizes UI interaction logic, simplifying test maintenance.
SOURCE: https://github.com/laravel/docs/blob/12.x/dusk.md#_snippet_170

LANGUAGE: Pest
CODE:
```
<?php

use Illuminate\Foundation\Testing\DatabaseMigrations;
use Laravel\Dusk\Browser;
use Tests\Browser\Components\DatePicker;

uses(DatabaseMigrations::class);

test('basic example', function () {
    $this->browse(function (Browser $browser) {
        $browser->visit('/')
            ->within(new DatePicker, function (Browser $browser) {
                $browser->selectDate(2019, 1, 30);
            })
            ->assertSee('January');
    });
});
```

LANGUAGE: PHPUnit
CODE:
```
<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use Laravel\Dusk\Browser;
use Tests\Browser\Components\DatePicker;
use Tests\DuskTestCase;

class ExampleTest extends DuskTestCase
{
    /**
     * A basic component test example.
     */
    public function test_basic_example(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                ->within(new DatePicker, function (Browser $browser) {
                    $browser->selectDate(2019, 1, 30);
                })
                ->assertSee('January');
        });
    }
}
```

----------------------------------------

TITLE: Dump Variables and Die in Laravel
DESCRIPTION: The `dd` (dump and die) function is a debugging utility that dumps the given variables to the browser and then halts script execution. It's useful for quickly inspecting variable states during development.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_117

LANGUAGE: php
CODE:
```
dd($value);

dd($value1, $value2, $value3, ...);
```

----------------------------------------

TITLE: Escaping Blade Echo for JavaScript Frameworks (Blade)
DESCRIPTION: Shows how to prevent Blade from processing curly braces intended for JavaScript frameworks by prefixing the expression with an @ symbol. Blade removes the @ and leaves the expression untouched for the frontend framework.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_5

LANGUAGE: blade
CODE:
```
<h1>Laravel</h1>

Hello, @{{ name }}.
```

----------------------------------------

TITLE: Laravel Validation Rule: accepted_if
DESCRIPTION: The `accepted_if` rule validates that a field is accepted ('yes', 'on', 1, '1', true, or 'true') only if another specified field matches a given value. This allows for conditional acceptance validation.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_60

LANGUAGE: APIDOC
CODE:
```
accepted_if:anotherfield,value,...
  Description: The field under validation must be "yes", "on", 1, "1", true, or "true" if another field under validation is equal to a specified value. Useful for validating "Terms of Service" acceptance or similar fields.
```

----------------------------------------

TITLE: Define Has One Relationship with Custom Criteria (Max Price) in Laravel
DESCRIPTION: Defines a 'has one' relationship to retrieve a single related `Order` model based on a custom criteria, specifically the order with the maximum `price`. The `ofMany` method accepts the sorting column and the aggregate function (`min` or `max`).
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_21

LANGUAGE: php
CODE:
```
/**
 * Get the user's largest order.
 */
public function largestOrder(): HasOne
{
    return $this->hasOne(Order::class)->ofMany('price', 'max');
}
```

----------------------------------------

TITLE: Automatic Dependency Injection in Controller Constructor
DESCRIPTION: Shows the common practice of type-hinting dependencies in a class constructor (e.g., a controller) for automatic resolution and injection by the Laravel container. This is the preferred way to resolve most objects.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_33

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Services\AppleMusic;

class PodcastController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected AppleMusic $apple,
    ) {}

    /**
     * Show information about the given podcast.
     */
    public function show(string $id): Podcast
    {
        return $this->apple->findPodcast($id);
    }
}
```

----------------------------------------

TITLE: Resolve Class Instance using `App` Facade or `app` Helper
DESCRIPTION: Provides examples of resolving class instances from the container when outside a service provider, using the `App` facade or the global `app` helper function.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_31

LANGUAGE: php
CODE:
```
use App\Services\Transistor;
use Illuminate\Support\Facades\App;

$transistor = App::make(Transistor::class);

$transistor = app(Transistor::class);
```

----------------------------------------

TITLE: Laravel: Creating a Cursor Paginator Instance
DESCRIPTION: This snippet shows how to create a cursor-based paginator instance using the `cursorPaginate` method from the query builder. This method returns an `Illuminate\Pagination\CursorPaginator` instance, which is ideal for large datasets and infinite scrolling UIs, provided an 'order by' clause is present.
SOURCE: https://github.com/laravel/docs/blob/12.x/pagination.md#_snippet_9

LANGUAGE: php
CODE:
```
$users = DB::table('users')->orderBy('id')->cursorPaginate(15);
```

----------------------------------------

TITLE: Implementing Constructor Injection in Laravel Controllers
DESCRIPTION: Demonstrates how to inject dependencies into a Laravel controller's constructor. The Laravel service container automatically resolves and injects type-hinted dependencies, such as a `UserRepository`, into the controller instance upon creation.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_34

LANGUAGE: PHP
CODE:
```
<?php

namespace AppHttpControllers;

use AppRepositoriesUserRepository;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected UserRepository $users,
    ) {}
}
```

----------------------------------------

TITLE: Rendering a Nested View
DESCRIPTION: This PHP snippet illustrates how to render a view located in a subdirectory (e.g., `resources/views/admin/profile.blade.php`). Dot notation (`admin.profile`) is used to reference views within nested directories, passing `$data` to it.
SOURCE: https://github.com/laravel/docs/blob/12.x/views.md#_snippet_5

LANGUAGE: php
CODE:
```
return view('admin.profile', $data);
```

----------------------------------------

TITLE: Retrieving All Query String Values (PHP)
DESCRIPTION: This PHP code calls the `query()` method without any arguments to retrieve all query string parameters as an associative array. This provides a complete collection of all data passed through the URL's query component.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_33

LANGUAGE: php
CODE:
```
$query = $request->query();
```

----------------------------------------

TITLE: Configuring Laravel Echo Instance for Reverb
DESCRIPTION: Initializes a new Laravel Echo instance, configuring it to use the 'reverb' broadcaster. It sets up the WebSocket host, port, and TLS settings using environment variables, enabling both 'ws' and 'wss' transports. This configuration is typically placed in `resources/js/bootstrap.js`.
SOURCE: https://github.com/laravel/docs/blob/12.x/broadcasting.md#_snippet_13

LANGUAGE: JavaScript
CODE:
```
import Echo from 'laravel-echo';

import Pusher from 'pusher-js';
window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
    wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});
```

LANGUAGE: React
CODE:
```
import { configureEcho } from "@laravel/echo-react";

configureEcho({
    broadcaster: "reverb",
    // key: import.meta.env.VITE_REVERB_APP_KEY,
    // wsHost: import.meta.env.VITE_REVERB_HOST,
    // wsPort: import.meta.env.VITE_REVERB_PORT,
    // wssPort: import.meta.env.VITE_REVERB_PORT,
    // forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    // enabledTransports: ['ws', 'wss'],
});
```

LANGUAGE: Vue
CODE:
```
import { configureEcho } from "@laravel/echo-vue";

configureEcho({
    broadcaster: "reverb",
    // key: import.meta.env.VITE_REVERB_APP_KEY,
    // wsHost: import.meta.env.VITE_REVERB_HOST,
    // wsPort: import.meta.env.VITE_REVERB_PORT,
    // wssPort: import.meta.env.VITE_REVERB_PORT,
    // forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    // enabledTransports: ['ws', 'wss'],
});
```

----------------------------------------

TITLE: Passing Content to Default Slot in Blade
DESCRIPTION: Illustrates how to pass content to a Blade component's default slot by placing it directly between the component's opening and closing tags.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_103

LANGUAGE: blade
CODE:
```
<x-alert>
    <strong>Whoops!</strong> Something went wrong!
</x-alert>
```

----------------------------------------

TITLE: Laravel Task Schedule Frequency Methods
DESCRIPTION: A comprehensive list of methods available in Laravel's task scheduler to define the frequency of task execution, ranging from every second to yearly, including custom cron schedules and timezone settings. Each entry specifies a method call and its purpose.
SOURCE: https://github.com/laravel/docs/blob/12.x/scheduling.md#_snippet_9

LANGUAGE: APIDOC
CODE:
```
Method: ->cron('* * * * *');
Description: Run the task on a custom cron schedule.
---
Method: ->everySecond();
Description: Run the task every second.
---
Method: ->everyTwoSeconds();
Description: Run the task every two seconds.
---
Method: ->everyFiveSeconds();
Description: Run the task every five seconds.
---
Method: ->everyTenSeconds();
Description: Run the task every ten seconds.
---
Method: ->everyFifteenSeconds();
Description: Run the task every fifteen seconds.
---
Method: ->everyTwentySeconds();
Description: Run the task every twenty seconds.
---
Method: ->everyThirtySeconds();
Description: Run the task every thirty seconds.
---
Method: ->everyMinute();
Description: Run the task every minute.
---
Method: ->everyTwoMinutes();
Description: Run the task every two minutes.
---
Method: ->everyThreeMinutes();
Description: Run the task every three minutes.
---
Method: ->everyFourMinutes();
Description: Run the task every four minutes.
---
Method: ->everyFiveMinutes();
Description: Run the task every five minutes.
---
Method: ->everyTenMinutes();
Description: Run the task every ten minutes.
---
Method: ->everyFifteenMinutes();
Description: Run the task every fifteen minutes.
---
Method: ->everyThirtyMinutes();
Description: Run the task every thirty minutes.
---
Method: ->hourly();
Description: Run the task every hour.
---
Method: ->hourlyAt(17);
Description: Run the task every hour at 17 minutes past the hour.
---
Method: ->everyOddHour($minutes = 0);
Description: Run the task every odd hour.
---
Method: ->everyTwoHours($minutes = 0);
Description: Run the task every two hours.
---
Method: ->everyThreeHours($minutes = 0);
Description: Run the task every three hours.
---
Method: ->everyFourHours($minutes = 0);
Description: Run the task every four hours.
---
Method: ->everySixHours($minutes = 0);
Description: Run the task every six hours.
---
Method: ->daily();
Description: Run the task every day at midnight.
---
Method: ->dailyAt('13:00');
Description: Run the task every day at 13:00.
---
Method: ->twiceDaily(1, 13);
Description: Run the task daily at 1:00 & 13:00.
---
Method: ->twiceDailyAt(1, 13, 15);
Description: Run the task daily at 1:15 & 13:15.
---
Method: ->weekly();
Description: Run the task every Sunday at 00:00.
---
Method: ->weeklyOn(1, '8:00');
Description: Run the task every week on Monday at 8:00.
---
Method: ->monthly();
Description: Run the task on the first day of every month at 00:00.
---
Method: ->monthlyOn(4, '15:00');
Description: Run the task every month on the 4th at 15:00.
---
Method: ->twiceMonthly(1, 16, '13:00');
Description: Run the task monthly on the 1st and 16th at 13:00.
---
Method: ->lastDayOfMonth('15:00');
Description: Run the task on the last day of the month at 15:00.
---
Method: ->quarterly();
Description: Run the task on the first day of every quarter at 00:00.
---
Method: ->quarterlyOn(4, '14:00');
Description: Run the task every quarter on the 4th at 14:00.
---
Method: ->yearly();
Description: Run the task on the first day of every year at 00:00.
---
Method: ->yearlyOn(6, 1, '17:00');
Description: Run the task every year on June 1st at 17:00.
---
Method: ->timezone('America/New_York');
Description: Set the timezone for the task.
```

----------------------------------------

TITLE: Redirecting to a Controller Action with Parameters in Laravel
DESCRIPTION: Generates a redirect to a controller action, passing route parameters as the second argument. Useful for actions requiring specific IDs or other data.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_19

LANGUAGE: php
CODE:
```
return redirect()->action(
    [UserController::class, 'profile'], ['id' => 1]
);
```

----------------------------------------

TITLE: Logging User Profile Access in Laravel Controller
DESCRIPTION: This example demonstrates logging an informational message within a Laravel controller's `show` method. It uses `Log::info` with contextual data to record when a user profile is accessed, including the user's ID.
SOURCE: https://github.com/laravel/docs/blob/12.x/logging.md#_snippet_7

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * Show the profile for the given user.
     */
    public function show(string $id): View
    {
        Log::info('Showing the user profile for user: {id}', ['id' => $id]);

        return view('user.profile', [
            'user' => User::findOrFail($id)
        ]);
    }
}
```

----------------------------------------

TITLE: Defining Model Structure - Eloquent Polymorphic - PHP
DESCRIPTION: These PHP classes define the models required for a one-to-many polymorphic relationship where a Comment can belong to either a Post or a Video. The `Comment` model uses `morphTo` to define the inverse relationship, while `Post` and `Video` use `morphMany` to define the relationship to comments.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_60

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Comment extends Model
{
    /**
     * Get the parent commentable model (post or video).
     */
    public function commentable(): MorphTo
    {
        return $this->morphTo();
    }
}

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Post extends Model
{
    /**
     * Get all of the post's comments.
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }
}

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Video extends Model
{
    /**
     * Get all of the video's comments.
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }
}
```

----------------------------------------

TITLE: Laravel `__` Function for Localization
DESCRIPTION: The `__` function in Laravel is a global helper used to translate a given translation string or key using the application's language files. If the specified translation string or key does not exist, the function will return the original value, making it safe for direct use.
SOURCE: https://github.com/laravel/docs/blob/12.x/strings.md#_snippet_2

LANGUAGE: PHP
CODE:
```
echo __("Welcome to our application");

echo __("messages.welcome");
```

----------------------------------------

TITLE: Inspecting Gate Authorization Response in PHP
DESCRIPTION: This snippet demonstrates how to use `Gate::inspect` to retrieve the full `Illuminate\Auth\Access\Response` object from an authorization check. This allows access to the detailed message provided by a policy's `Response::deny()` method, enabling more informative feedback to the user beyond a simple boolean `allowed()` status.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_21

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Gate;

$response = Gate::inspect('update', $post);

if ($response->allowed()) {
    // The action is authorized...
} else {
    echo $response->message();
}
```

----------------------------------------

TITLE: Generating Resource Controller with Model Binding (Shell)
DESCRIPTION: This command generates a new resource controller named `PhotoController` and automatically configures it for route model binding with the `Photo` model, allowing controller methods to type-hint model instances.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_15

LANGUAGE: shell
CODE:
```
php artisan make:controller PhotoController --model=Photo --resource
```

----------------------------------------

TITLE: Setting Request Timeout - Laravel HTTP Client - PHP
DESCRIPTION: Illustrates setting the maximum number of seconds to wait for a response using the `timeout` method. If the timeout is exceeded, a `ConnectionException` is thrown.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_17

LANGUAGE: php
CODE:
```
$response = Http::timeout(3)->get(/* ... */);
```

----------------------------------------

TITLE: Pushing Content to a Blade Stack
DESCRIPTION: This snippet demonstrates the `@push` Blade directive, which allows content (like JavaScript `<script>` tags) to be 'pushed' onto a named stack (`scripts`). This content can then be rendered elsewhere in the layout, typically in the `<head>` or before the closing `</body>` tag.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_141

LANGUAGE: blade
CODE:
```
@push('scripts')
    <script src="/example.js"></script>
@endpush
```

----------------------------------------

TITLE: Throttling Laravel LazyCollection Items (PHP)
DESCRIPTION: The `throttle` method on a Laravel `LazyCollection` introduces a delay, returning each item after a specified number of seconds. This is particularly useful for managing interactions with external APIs that impose rate limits, preventing excessive requests within a short period.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_198

LANGUAGE: php
CODE:
```
use App\Models\User;

User::where('vip', true)
    ->cursor()
    ->throttle(seconds: 1)
    ->each(function (User $user) {
        // Call external API...
    });
```

----------------------------------------

TITLE: Registering a Resource Route in Laravel
DESCRIPTION: This single route declaration automatically creates multiple routes for common CRUD operations (index, create, store, show, edit, update, destroy) and maps them to the corresponding methods within the `PhotoController`. This simplifies routing for resource-based applications.
SOURCE: https://github.com/laravel/docs/blob/12.x/controllers.md#_snippet_10

LANGUAGE: php
CODE:
```
use App\Http\Controllers\PhotoController;

Route::resource('photos', PhotoController::class);
```

----------------------------------------

TITLE: Checking User Authentication with @auth Directive in Blade (Blade)
DESCRIPTION: Demonstrates the @auth directive, which allows quick checking if the current user is authenticated, useful for displaying content only to logged-in users.
SOURCE: https://github.com/laravel/docs/blob/12.x/blade.md#_snippet_14

LANGUAGE: blade
CODE:
```
@auth
    // The user is authenticated...
@endauth
```

----------------------------------------

TITLE: Generating Basic URLs with `url` helper - PHP
DESCRIPTION: This snippet demonstrates how to generate a basic URL using the `url` helper in Laravel. It automatically uses the current request's scheme and host. The example shows generating a URL for a specific post ID.
SOURCE: https://github.com/laravel/docs/blob/12.x/urls.md#_snippet_0

LANGUAGE: PHP
CODE:
```
$post = App\Models\Post::find(1);

echo url("/posts/{$post->id}");

// http://example.com/posts/1
```

----------------------------------------

TITLE: Retrieve Current CSRF Token in Laravel
DESCRIPTION: The `csrf_token` function allows you to retrieve the value of the current CSRF token. This token is essential for protecting web applications from cross-site request forgery.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_115

LANGUAGE: php
CODE:
```
$token = csrf_token();
```

----------------------------------------

TITLE: Validate Image Dimensions (Laravel)
DESCRIPTION: The `dimensions` rule validates an image file against specified dimension constraints like width, height, or aspect ratio. It supports fluent construction via `Rule::dimensions` for complex dimension requirements.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_88

LANGUAGE: PHP
CODE:
```
'avatar' => 'dimensions:min_width=100,min_height=200'
```

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

Validator::make($data, [
    'avatar' => [
        'required',
        Rule::dimensions()
            ->maxWidth(1000)
            ->maxHeight(500)
            ->ratio(3 / 2),
    ],
]);
```

----------------------------------------

TITLE: Cancel Subscription Before Deleting User in Laravel Cashier
DESCRIPTION: Emphasizes the best practice of canceling a user's subscription, specifically using `cancelNow`, before deleting the associated user model. This ensures proper cleanup and avoids orphaned subscription data.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_128

LANGUAGE: php
CODE:
```
$user->subscription('default')->cancelNow();

$user->delete();
```

----------------------------------------

TITLE: Log Debug Messages with logger()
DESCRIPTION: The `logger` function is used to write `debug` level messages to the application log. It also supports contextual data arrays. If called without arguments, it returns a logger instance, allowing for more specific log level messages.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_128

LANGUAGE: php
CODE:
```
logger('Debug message');
```

LANGUAGE: php
CODE:
```
logger('User has logged in.', ['id' => $user->id]);
```

LANGUAGE: php
CODE:
```
logger()->error('You are not allowed here.');
```

----------------------------------------

TITLE: Accessing BelongsToMany Relationship PHP
DESCRIPTION: Demonstrates how to retrieve related models for a many-to-many relationship (e.g., tags for a post) by accessing the dynamic relationship property on an Eloquent model instance.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_72

LANGUAGE: php
CODE:
```
use App\Models\Post;

$post = Post::find(1);

foreach ($post->tags as $tag) {
    // ...
}
```

----------------------------------------

TITLE: Customize Mail Recipient for Laravel Notifications
DESCRIPTION: To override the default email address lookup for mail notifications, define a `routeNotificationForMail` method on your notifiable entity. This method allows you to specify a custom email address or an array containing both an email address and a name for the recipient.
SOURCE: https://github.com/laravel/docs/blob/12.x/notifications.md#_snippet_24

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * Route notifications for the mail channel.
     *
     * @return  array<string, string>|string
     */
    public function routeNotificationForMail(Notification $notification): array|string
    {
        // Return email address only...
        return $this->email_address;

        // Return email address and name...
        return [$this->email_address => $this->name];
    }
}
```

----------------------------------------

TITLE: Check if Canceled Subscription has Ended with `ended` Method (PHP)
DESCRIPTION: The `ended` method determines if the user has canceled their subscription and is no longer within their 'grace period', meaning the subscription has fully expired.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_84

LANGUAGE: PHP
CODE:
```
if ($user->subscription('default')->ended()) {
    // ...
}
```

----------------------------------------

TITLE: Defining a Basic User Resource in Laravel
DESCRIPTION: This snippet demonstrates how to define a basic Laravel API resource for a User model. The `toArray` method transforms the model's attributes into a structured array, making it suitable for API responses. It extends `JsonResource` and includes common user fields like id, name, email, and timestamps.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-resources.md#_snippet_13

LANGUAGE: php
CODE:
```
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

----------------------------------------

TITLE: Laravel Query Builder: Order Results by Single Column
DESCRIPTION: This example demonstrates using the `orderBy` method to sort query results by a single column. It orders users from the 'users' table by their 'name' in descending order.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_54

LANGUAGE: PHP
CODE:
```
$users = DB::table('users')
    ->orderBy('name', 'desc')
    ->get();
```

----------------------------------------

TITLE: Defining a Gate with a Class Callback in Laravel
DESCRIPTION: This gate, 'update-post', is defined using a class callback array, pointing to the `update` method of the `PostPolicy` class. This approach allows for grouping authorization logic within dedicated policy classes, promoting better organization.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use App\Policies\PostPolicy;
use Illuminate\Support\Facades\Gate;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    Gate::define('update-post', [PostPolicy::class, 'update']);
}
```

----------------------------------------

TITLE: Creating Text Column - Laravel Schema Builder - PHP
DESCRIPTION: The `text` method creates a `TEXT` equivalent column, suitable for storing longer strings of text. For MySQL/MariaDB, it can be combined with `charset('binary')` to create a `BLOB` equivalent column for binary data.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_70

LANGUAGE: PHP
CODE:
```
$table->text('description');
```

LANGUAGE: PHP
CODE:
```
$table->text('data')->charset('binary'); // BLOB
```

----------------------------------------

TITLE: Retrying Multiple Specific Failed Jobs - Shell
DESCRIPTION: Retries multiple failed jobs by providing a space-separated list of their unique IDs. This command allows for re-dispatching several specific jobs back to their queues in a single operation.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_120

LANGUAGE: shell
CODE:
```
php artisan queue:retry ce7bb17c-cdd8-41f0-a8ec-7b4fef4e5ece 91401d2c-0784-4f43-824c-34f94a33c24d
```

----------------------------------------

TITLE: Storing Uploaded Files with Custom Filename using storeAs
DESCRIPTION: This snippet demonstrates how to store an uploaded file with a manually specified filename using the `storeAs` method. It takes the directory, the desired filename, and an optional disk name as arguments, preventing automatic filename generation.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_44

LANGUAGE: php
CODE:
```
$path = $request->file('avatar')->storeAs(
    'avatars', $request->user()->id
);
```

----------------------------------------

TITLE: Adding Items to a Context Stack in Laravel
DESCRIPTION: This PHP snippet demonstrates how to use `Context::push` to add items to a named 'stack' within the Laravel context. Stacks maintain the order of added items, useful for tracking historical data like breadcrumbs or event sequences.
SOURCE: https://github.com/laravel/docs/blob/12.x/context.md#_snippet_10

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Context;

Context::push('breadcrumbs', 'first_value');

Context::push('breadcrumbs', 'second_value', 'third_value');

Context::get('breadcrumbs');
// [
//     'first_value',
//     'second_value',
//     'third_value',
// ]
```

----------------------------------------

TITLE: Asserting JSON Validation Errors in Laravel PHP
DESCRIPTION: Asserts that the HTTP response contains specific JSON validation errors for the given keys. This method is designed for scenarios where validation errors are returned as a JSON structure.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_83

LANGUAGE: php
CODE:
```
$response->assertJsonValidationErrors(array $data, $responseKey = 'errors');
```

----------------------------------------

TITLE: Bind an Interface to an Implementation in Laravel
DESCRIPTION: Demonstrates how to register an interface with a concrete implementation in the Laravel service container. This ensures that when a class type-hints the interface, the container automatically injects the specified implementation. It also shows how to type-hint the interface in a class constructor for container resolution.
SOURCE: https://github.com/laravel/docs/blob/12.x/container.md#_snippet_12

LANGUAGE: php
CODE:
```
use App\Contracts\EventPusher;
use App\Services\RedisEventPusher;

$this->app->bind(EventPusher::class, RedisEventPusher::class);
```

LANGUAGE: php
CODE:
```
use App\Contracts\EventPusher;

/**
 * Create a new class instance.
 */
public function __construct(
    protected EventPusher $pusher,
) {}
```

----------------------------------------

TITLE: Conditionally Update or Insert Records with Laravel
DESCRIPTION: The `updateOrInsert` method updates a record if it exists based on provided conditions, or inserts a new record if no match is found. It merges attributes from both condition and update arrays for new insertions. A closure can be used to customize attributes based on existence.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_67

LANGUAGE: PHP
CODE:
```
DB::table('users')
    ->updateOrInsert(
        ['email' => '<EMAIL>', 'name' => 'John'],
        ['votes' => '2']
    );
```

LANGUAGE: PHP
CODE:
```
DB::table('users')->updateOrInsert(
    ['user_id' => $user_id],
    fn ($exists) => $exists ? [
        'name' => $data['name'],
        'email' => $data['email'],
    ] : [
        'name' => $data['name'],
        'email' => $data['email'],
        'marketable' => true,
    ],
);
```

----------------------------------------

TITLE: Setting String Primary Key Type in Eloquent Model (PHP)
DESCRIPTION: This snippet shows how to define the data type of an Eloquent model's primary key as a string. This is necessary when the primary key is not an integer, such as with UUIDs or ULIDs, by setting the `protected $keyType` property.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_8

LANGUAGE: PHP
CODE:
```
<?php

class Flight extends Model
{
    /**
     * The data type of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';
}
```

----------------------------------------

TITLE: Asserting Missing JSON Validation Errors in Laravel PHP
DESCRIPTION: Asserts that the HTTP response has no JSON validation errors for the specified keys. This method is used to confirm that no validation failures related to the given fields are present in the JSON response.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-tests.md#_snippet_79

LANGUAGE: php
CODE:
```
$response->assertJsonMissingValidationErrors($keys);
```

----------------------------------------

TITLE: Retrieving and Deleting with Cache::pull (PHP)
DESCRIPTION: The `pull` method retrieves an item from the cache and then immediately deletes it. If the item does not exist, it returns `null` or the specified default value. This is an atomic operation for 'get and delete'.
SOURCE: https://github.com/laravel/docs/blob/12.x/cache.md#_snippet_14

LANGUAGE: PHP
CODE:
```
$value = Cache::pull('key');

$value = Cache::pull('key', 'default');
```

----------------------------------------

TITLE: Faking Specific URLs with Http::response in Laravel PHP
DESCRIPTION: Demonstrates how to use `Http::fake` with an array mapping URL patterns to fake responses constructed using `Http::response`. Wildcards (`*`) can be used in URL patterns. Requires the Laravel HTTP client.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_41

LANGUAGE: php
CODE:
```
Http::fake([
    // Stub a JSON response for GitHub endpoints...
    'github.com/*' => Http::response(['foo' => 'bar'], 200, $headers),

    // Stub a string response for Google endpoints...
    'google.com/*' => Http::response('Hello World', 200, $headers),
]);
```

----------------------------------------

TITLE: Defining a Job Rate Limiter in AppServiceProvider - PHP
DESCRIPTION: This PHP code demonstrates how to define a named job rate limiter using `RateLimiter::for` within the `boot` method of `AppServiceProvider`. It sets up a conditional rate limit for 'backups', allowing VIP customers unlimited access while limiting others to one backup per hour, segmented by user ID.
SOURCE: https://github.com/laravel/docs/blob/12.x/queues.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;

/**
 * Bootstrap any application services.
 */
public function boot(): void
{
    RateLimiter::for('backups', function (object $job) {
        return $job->user->vipCustomer()
            ? Limit::none()
            : Limit::perHour(1)->by($job->user->id);
    });
}
```

----------------------------------------

TITLE: Constraining Multiple Route Parameters with Array `where` Method in Laravel
DESCRIPTION: This code shows how to apply regular expression constraints to multiple route parameters simultaneously. An associative array is passed to the `where` method, mapping parameter names to their respective regex patterns.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_29

LANGUAGE: PHP
CODE:
```
Route::get('/user/{id}/{name}', function (string $id, string $name) {
    // ...
})->where(['id' => '[0-9]+', 'name' => '[a-z]+']);
```

----------------------------------------

TITLE: Defining Default Eager Loads in Laravel Eloquent Model (PHP)
DESCRIPTION: Shows how to define relationships that should always be loaded by default for a model by setting the `$with` protected property on the model class. Includes example relationship definitions.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_123

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Book extends Model
{
    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['author'];

    /**
     * Get the author that wrote the book.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(Author::class);
    }

    /**
     * Get the genre of the book.
     */
    public function genre(): BelongsTo
    {
        return $this->belongsTo(Genre::class);
    }
}
```

----------------------------------------

TITLE: Laravel Validation: Complex Conditional Rules with `sometimes` Method
DESCRIPTION: Explains how to add validation rules dynamically based on complex conditional logic using the `sometimes` method on the `Validator` instance. This allows for flexible rule application, such as requiring a field only if another field meets a specific condition. The closure receives an `Illuminate\Support\Fluent` instance for accessing input data.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_171

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Facades\Validator;

$validator = Validator::make($request->all(), [
    'email' => 'required|email',
    'games' => 'required|integer|min:0',
]);
```

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Fluent;

$validator->sometimes('reason', 'required|max:500', function (Fluent $input) {
    return $input->games >= 100;
});
```

LANGUAGE: PHP
CODE:
```
$validator->sometimes(['reason', 'cost'], 'required', function (Fluent $input) {
    return $input->games >= 100;
});
```

----------------------------------------

TITLE: Retrieving Eloquent Models or Throwing Exception - PHP
DESCRIPTION: This snippet demonstrates using `findOrFail` and `firstOrFail` to retrieve a single Eloquent model, throwing an `Illuminate\Database\Eloquent\ModelNotFoundException` if no matching model is found. `findOrFail(1)` retrieves by primary key, and `firstOrFail()` retrieves the first model matching query constraints. This is useful for scenarios where a missing model indicates an error condition.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_37

LANGUAGE: PHP
CODE:
```
$flight = Flight::findOrFail(1);

$flight = Flight::where('legs', '>', 3)->firstOrFail();
```

----------------------------------------

TITLE: Adding Custom Headers (withHeaders) - Laravel HTTP Client - PHP
DESCRIPTION: Demonstrates adding custom headers to a request using the `withHeaders` method. This method accepts an associative array of header names and values, merging them with existing headers.
SOURCE: https://github.com/laravel/docs/blob/12.x/http-client.md#_snippet_11

LANGUAGE: php
CODE:
```
$response = Http::withHeaders([
    'X-First' => 'foo',
    'X-Second' => 'bar'
])->post('http://example.com/users', [
    'name' => 'Taylor',
]);
```

----------------------------------------

TITLE: Querying BelongsTo Relationship with whereBelongsTo in Laravel PHP
DESCRIPTION: Demonstrates using the convenient `whereBelongsTo` method to automatically determine the proper relationship and foreign key when querying for models (`Post`) related to a single parent model instance (`$user`).
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_16

LANGUAGE: php
CODE:
```
$posts = Post::whereBelongsTo($user)->get();
```

----------------------------------------

TITLE: Running Laravel Tests in Parallel
DESCRIPTION: This Artisan command executes your test suite simultaneously across multiple processes, significantly reducing the total test execution time. It requires the `brianium/paratest` package.
SOURCE: https://github.com/laravel/docs/blob/12.x/testing.md#_snippet_9

LANGUAGE: Shell
CODE:
```
php artisan test --parallel
```

----------------------------------------

TITLE: Customizing Implicit Binding Key in Laravel PHP (Model)
DESCRIPTION: This snippet demonstrates how to globally configure an Eloquent model to use a specific database column (e.g., `slug`) as its route key instead of `id` by overriding the `getRouteKeyName` method.
SOURCE: https://github.com/laravel/docs/blob/12.x/routing.md#_snippet_54

LANGUAGE: PHP
CODE:
```
/**
 * Get the route key for the model.
 */
public function getRouteKeyName(): string
{
    return 'slug';
}
```

----------------------------------------

TITLE: Storing File Contents on Disk in Laravel
DESCRIPTION: The `put` method is used to store file contents on a disk. It can accept either a string of contents or a PHP resource (stream) for efficient handling of large files. Paths are relative to the disk's root.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_36

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\Storage;

Storage::put('file.jpg', $contents);

Storage::put('file.jpg', $resource);
```

----------------------------------------

TITLE: Publishing Laravel Configuration Files (Shell)
DESCRIPTION: These Artisan commands publish configuration files that are not included by default in the `config` directory. The first command publishes specific files, while `--all` publishes all available un-published configuration files.
SOURCE: https://github.com/laravel/docs/blob/12.x/configuration.md#_snippet_20

LANGUAGE: shell
CODE:
```
php artisan config:publish
```

LANGUAGE: shell
CODE:
```
php artisan config:publish --all
```

----------------------------------------

TITLE: Grouping Laravel Collection Items using `groupBy()` in PHP
DESCRIPTION: The `groupBy` method organizes the collection's items into groups based on a specified key or a callback function. It can handle single or multiple grouping criteria, creating multi-dimensional arrays for complex grouping scenarios while maintaining data integrity.
SOURCE: https://github.com/laravel/docs/blob/12.x/collections.md#_snippet_63

LANGUAGE: PHP
CODE:
```
$collection = collect([
    ['account_id' => 'account-x10', 'product' => 'Chair'],
    ['account_id' => 'account-x10', 'product' => 'Bookcase'],
    ['account_id' => 'account-x11', 'product' => 'Desk'],
]);

$grouped = $collection->groupBy('account_id');

$grouped->all();

/*
    [
        'account-x10' => [
            ['account_id' => 'account-x10', 'product' => 'Chair'],
            ['account_id' => 'account-x10', 'product' => 'Bookcase'],
        ],
        'account-x11' => [
            ['account_id' => 'account-x11', 'product' => 'Desk'],
        ],
    ]
*/
```

LANGUAGE: PHP
CODE:
```
$grouped = $collection->groupBy(function (array $item, int $key) {
    return substr($item['account_id'], -3);
});

$grouped->all();

/*
    [
        'x10' => [
            ['account_id' => 'account-x10', 'product' => 'Chair'],
            ['account_id' => 'account-x10', 'product' => 'Bookcase'],
        ],
        'x11' => [
            ['account_id' => 'account-x11', 'product' => 'Desk'],
        ],
    ]
*/
```

LANGUAGE: PHP
CODE:
```
$data = new Collection([
    10 => ['user' => 1, 'skill' => 1, 'roles' => ['Role_1', 'Role_3']],
    20 => ['user' => 2, 'skill' => 1, 'roles' => ['Role_1', 'Role_2']],
    30 => ['user' => 3, 'skill' => 2, 'roles' => ['Role_1']],
    40 => ['user' => 4, 'skill' => 2, 'roles' => ['Role_2']],
]);

$result = $data->groupBy(['skill', function (array $item) {
    return $item['roles'];
}], preserveKeys: true);

/*
[
    1 => [
        'Role_1' => [
            10 => ['user' => 1, 'skill' => 1, 'roles' => ['Role_1', 'Role_3']],
            20 => ['user' => 2, 'skill' => 1, 'roles' => ['Role_1', 'Role_2']],
        ],
        'Role_2' => [
            20 => ['user' => 2, 'skill' => 1, 'roles' => ['Role_1', 'Role_2']],
        ],
        'Role_3' => [
            10 => ['user' => 1, 'skill' => 1, 'roles' => ['Role_1', 'Role_3']],
        ],
    ],
    2 => [
        'Role_1' => [
            30 => ['user' => 3, 'skill' => 2, 'roles' => ['Role_1']],
        ],
        'Role_2' => [
            40 => ['user' => 4, 'skill' => 2, 'roles' => ['Role_2']],
        ],
    ],
];
*/
```

----------------------------------------

TITLE: Redirect to Named Route with Parameters in Laravel
DESCRIPTION: Shows how to pass parameters as the second argument to the `route` method when redirecting to a named route that expects specific values.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_15

LANGUAGE: PHP
CODE:
```
// For a route with the following URI: /profile/{id}

return redirect()->route('profile', ['id' => 1]);
```

----------------------------------------

TITLE: Attach Additional Rules to Laravel Default Password Rules
DESCRIPTION: This snippet illustrates how to extend the default password validation rules by attaching additional custom rules using the 'rules' method within the 'Password::defaults' closure.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_186

LANGUAGE: php
CODE:
```
use App\Rules\ZxcvbnRule;

Password::defaults(function () {
    $rule = Password::min(8)->rules([new ZxcvbnRule]);

    // ...
});
```

----------------------------------------

TITLE: Retrieve Aggregate Values (count, max) with Query Builder
DESCRIPTION: Demonstrates using query builder methods like `count`, `max`, `min`, `avg`, and `sum` to retrieve aggregate values from database tables. Examples show counting users and finding the maximum order price.
SOURCE: https://github.com/laravel/docs/blob/12.x/queries.md#_snippet_10

LANGUAGE: php
CODE:
```
use Illuminate\Support\Facades\DB;

$users = DB::table('users')->count();

$price = DB::table('orders')->max('price');
```

----------------------------------------

TITLE: Copying and Moving Files with Laravel Storage
DESCRIPTION: This snippet illustrates file duplication and relocation using Laravel's `Storage` facade. The `copy` method duplicates a file from a source path to a destination, while the `move` method renames or moves a file, effectively deleting the original.
SOURCE: https://github.com/laravel/docs/blob/12.x/filesystem.md#_snippet_39

LANGUAGE: php
CODE:
```
Storage::copy('old/file.jpg', 'new/file.jpg');

Storage::move('old/file.jpg', 'new/file.jpg');
```

----------------------------------------

TITLE: Mapping Eloquent Model Events to Event Classes in Laravel
DESCRIPTION: This example demonstrates how to define the `$dispatchesEvents` property on an Eloquent model. This property maps various lifecycle events (e.g., `saved`, `deleted`) to custom event classes, enabling structured event handling.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent.md#_snippet_104

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Models;

use App\Events\UserDeleted;
use App\Events\UserSaved;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * The event map for the model.
     *
     * @var array<string, string>
     */
    protected $dispatchesEvents = [
        'saved' => UserSaved::class,
        'deleted' => UserDeleted::class,
    ];
}
```

----------------------------------------

TITLE: Redirect to Previous URL with Input in Laravel
DESCRIPTION: Demonstrates how to redirect a user back to their previous location, often used after form validation, by utilizing the `back` helper function and optionally flashing input to the session.
SOURCE: https://github.com/laravel/docs/blob/12.x/responses.md#_snippet_13

LANGUAGE: PHP
CODE:
```
Route::post('/user/profile', function () {
    // Validate the request...

    return back()->withInput();
});
```

----------------------------------------

TITLE: Start Laravel and Vite Development Servers (Shell)
DESCRIPTION: Executes the `composer run dev` command, which typically starts both the Laravel development server and the Vite development server concurrently, enabling live reloading and asset compilation during development.
SOURCE: https://github.com/laravel/docs/blob/12.x/vite.md#_snippet_20

LANGUAGE: shell
CODE:
```
composer run dev
```

----------------------------------------

TITLE: Laravel Validation: `size` Rule Examples
DESCRIPTION: Demonstrates the `size` validation rule in Laravel, which ensures a field's value matches a specific size. The interpretation of 'size' varies by data type: character count for strings, integer value for numbers, element count for arrays, and kilobytes for files.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_161

LANGUAGE: php
CODE:
```
// Validate that a string is exactly 12 characters long...
'title' => 'size:12';

// Validate that a provided integer equals 10...
'seats' => 'integer|size:10';

// Validate that an array has exactly 5 elements...
'tags' => 'array|size:5';

// Validate that an uploaded file is exactly 512 kilobytes...
'image' => 'file|size:512';
```

----------------------------------------

TITLE: Checking General Subscription Status in Blade
DESCRIPTION: This Blade snippet shows how to check if a user is generally subscribed using the `subscribed()` method provided by Cashier's `Billable` trait. It's useful for displaying content conditionally based on a user's overall subscription status.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_16

LANGUAGE: blade
CODE:
```
@if ($user->subscribed())
    <p>You are subscribed.</p>
@endif
```

----------------------------------------

TITLE: Dispatching Queued Mailable After DB Commit (Send Method) in Laravel PHP
DESCRIPTION: Demonstrates how to ensure a queued mailable is dispatched only after database transactions are committed by calling the `afterCommit` method on the mailable instance when using the `send` method. Useful when the queue connection's `after_commit` is false.
SOURCE: https://github.com/laravel/docs/blob/12.x/mail.md#_snippet_60

LANGUAGE: php
CODE:
```
Mail::to($request->user())->send(
    (new OrderShipped($order))->afterCommit()
);
```

----------------------------------------

TITLE: Attaching Many To Many Relationship with attach in PHP
DESCRIPTION: Shows how to attach a related model to a parent model in a many-to-many relationship using the `attach` method. This inserts a record into the intermediate pivot table.
SOURCE: https://github.com/laravel/docs/blob/12.x/eloquent-relationships.md#_snippet_149

LANGUAGE: php
CODE:
```
use App\Models\User;

$user = User::find(1);

$user->roles()->attach($roleId);
```

----------------------------------------

TITLE: Configuring OAuth Provider Credentials in Laravel
DESCRIPTION: This configuration snippet shows how to define credentials for an OAuth provider like GitHub in your `config/services.php` file. It requires a client ID, client secret, and a redirect URL, which are obtained from the provider's developer dashboard.
SOURCE: https://github.com/laravel/docs/blob/12.x/socialite.md#_snippet_1

LANGUAGE: php
CODE:
```
'github' => [
    'client_id' => env('GITHUB_CLIENT_ID'),
    'client_secret' => env('GITHUB_CLIENT_SECRET'),
    'redirect' => 'http://example.com/callback-url',
],
```

----------------------------------------

TITLE: Retrieve Subsets of Validated Data using `safe()`
DESCRIPTION: Illustrates how to use the `safe()` method, which returns an `Illuminate\Support\ValidatedInput` instance. This object allows retrieval of specific fields using `only()`, exclusion of fields with `except()`, or all validated data with `all()`.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_43

LANGUAGE: php
CODE:
```
$validated = $request->safe()->only(['name', 'email']);

$validated = $request->safe()->except(['name', 'email']);

$validated = $request->safe()->all();
```

----------------------------------------

TITLE: Defining a Foreign ID Column in Laravel Migrations
DESCRIPTION: The `foreignId` method creates an `UNSIGNED BIGINT` column, typically used as a foreign key. This method is a convenient way to define relationships to other tables.
SOURCE: https://github.com/laravel/docs/blob/12.x/migrations.md#_snippet_42

LANGUAGE: PHP
CODE:
```
$table->foreignId('user_id');
```

----------------------------------------

TITLE: Create a collection instance (Laravel PHP)
DESCRIPTION: The `collect` function creates a new collection instance from the given value.
SOURCE: https://github.com/laravel/docs/blob/12.x/helpers.md#_snippet_110

LANGUAGE: PHP
CODE:
```
$collection = collect(['Taylor', 'Abigail']);
```

----------------------------------------

TITLE: Laravel Validation: Applying Rules Only When Field is Present
DESCRIPTION: Illustrates the use of the `sometimes` validation rule in Laravel. This rule ensures that validation checks are performed on a field only if that field is explicitly present in the data being validated, making it suitable for optional fields.
SOURCE: https://github.com/laravel/docs/blob/12.x/validation.md#_snippet_170

LANGUAGE: PHP
CODE:
```
$validator = Validator::make($data, [
    'email' => 'sometimes|required|email',
]);
```

----------------------------------------

TITLE: Create Middleware to Ensure User is Subscribed (PHP)
DESCRIPTION: This middleware example demonstrates how to use the `subscribed` method to filter access to routes and controllers based on the user's subscription status, redirecting non-paying customers.
SOURCE: https://github.com/laravel/docs/blob/12.x/billing.md#_snippet_76

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsSubscribed
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user() && ! $request->user()->subscribed('default')) {
            // This user is not a paying customer...
            return redirect('/billing');
        }

        return $next($request);
    }
```

----------------------------------------

TITLE: Authorizing Actions with Additional Context in Controller (PHP)
DESCRIPTION: Shows how to invoke a policy method using `Gate::authorize` in a controller, passing an array as the second argument to supply additional context (like a category ID) to the policy for authorization checks.
SOURCE: https://github.com/laravel/docs/blob/12.x/authorization.md#_snippet_41

LANGUAGE: php
CODE:
```
/**
 * Update the given blog post.
 *
 * @throws \Illuminate\Auth\Access\AuthorizationException
 */
public function update(Request $request, Post $post): RedirectResponse
{
    Gate::authorize('update', [$post, $request->category]);

    // The current user can update the blog post...

    return redirect('/posts');
}
```

----------------------------------------

TITLE: Laravel Dusk: Basic Browser Creation and Login Test
DESCRIPTION: This snippet demonstrates how to create a browser instance using the `browse` method and perform a basic user login test. It covers navigating to a login page, typing user credentials, submitting the form, and asserting the redirection to the home page. Both Pest and PHPUnit testing frameworks are shown.
SOURCE: https://github.com/laravel/docs/blob/12.x/dusk.md#_snippet_19

LANGUAGE: Pest
CODE:
```
<?php

use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Laravel\Dusk\Browser;

uses(DatabaseMigrations::class);

test('basic example', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $this->browse(function (Browser $browser) use ($user) {
        $browser->visit('/login')
            ->type('email', $user->email)
            ->type('password', 'password')
            ->press('Login')
            ->assertPathIs('/home');
    });
});
```

LANGUAGE: PHPUnit
CODE:
```
<?php

namespace Tests\Browser;

use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Laravel\Dusk\Browser;
use Tests\DuskTestCase;

class ExampleTest extends DuskTestCase
{
    use DatabaseMigrations;

    /**
     *
     * A basic browser test example.
     */
    public function test_basic_example(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->browse(function (Browser $browser) use ($user) {
            $browser->visit('/login')
                ->type('email', $user->email)
                ->type('password', 'password')
                ->press('Login')
                ->assertPathIs('/home');
        });
    }
}
```

----------------------------------------

TITLE: Retrieving Input as Stringable Object (PHP)
DESCRIPTION: This PHP code uses the `string()` method to retrieve an input value as an `Illuminate\Support\Stringable` object, rather than a primitive string. This allows for chaining string manipulation methods, such as `trim()`, directly on the input value, providing a more fluent and powerful way to process string inputs.
SOURCE: https://github.com/laravel/docs/blob/12.x/requests.md#_snippet_35

LANGUAGE: php
CODE:
```
$name = $request->string('name')->trim();
```

----------------------------------------

TITLE: Authorizing Private Broadcast Channels in PHP
DESCRIPTION: This code defines a channel authorization rule in `routes/channels.php`. It verifies that a user attempting to listen on a private `orders.{orderId}` channel is the actual creator of the order, ensuring secure access to real-time updates.
SOURCE: https://github.com/laravel/docs/blob/12.x/broadcasting.md#_snippet_29

LANGUAGE: php
CODE:
```
use AppModelsOrder;
use AppModelsUser;

Broadcast::channel('orders.{orderId}', function (User $user, int $orderId) {
    return $user->id === Order::findOrNew($orderId)->user_id;
});
```
