# Contractly - API Documentation

## API Overview

Contractly provides both internal API endpoints for the web application and external integrations for third-party services. This document covers the API structure, authentication, and available endpoints.

## 🔐 Authentication

### Admin Panel Authentication
- **Type**: Session-based authentication
- **Guard**: `web` (default Laravel guard)
- **Login**: `/admin/login`
- **Logout**: `/admin/logout`

### Client Panel Authentication
- **Type**: Session-based with OTP verification
- **Guard**: `client` (custom guard)
- **Login Flow**:
  1. POST `/client/login` - Send phone number
  2. OTP sent via SMS/WhatsApp
  3. POST `/client/verify-otp` - Verify OTP code
  4. Session established

### OTP Authentication Flow
```php
// Step 1: Request OTP
POST /client/auth/request-otp
{
    "phone": "+************"
}

// Response
{
    "success": true,
    "message": "OTP sent successfully",
    "expires_in": 300
}

// Step 2: Verify OTP
POST /client/auth/verify-otp
{
    "phone": "+************",
    "otp": "123456"
}

// Response
{
    "success": true,
    "message": "Login successful",
    "user": {
        "id": 1,
        "name": "Ahmed Mohammed",
        "phone": "+************"
    }
}
```

## 🌐 Web Routes

### Admin Panel Routes
```php
// Admin Authentication
Route::group(['prefix' => 'admin'], function () {
    Route::get('/login', [CustomLogin::class, 'render']);
    Route::post('/login', [CustomLogin::class, 'authenticate']);
    Route::post('/logout', [CustomLogin::class, 'logout']);
});

// Admin Dashboard
Route::group(['middleware' => ['auth:web']], function () {
    Route::get('/admin', [Dashboard::class, 'render']);
    Route::get('/admin/tenant-settings', [ManageTenantSettings::class, 'render']);
});
```

### Client Portal Routes
```php
// Client Authentication
Route::group(['prefix' => 'client'], function () {
    Route::get('/login', [ClientPortalController::class, 'login']);
    Route::post('/auth/request-otp', [ClientPortalController::class, 'requestOtp']);
    Route::post('/auth/verify-otp', [ClientPortalController::class, 'verifyOtp']);
    Route::post('/logout', [ClientPortalController::class, 'logout']);
});

// Client Dashboard
Route::group(['middleware' => ['auth:client']], function () {
    Route::get('/client/dashboard', [ClientPortalController::class, 'dashboard']);
    Route::get('/client/contracts', [ClientPortalController::class, 'contracts']);
    Route::get('/client/maintenance', [ClientPortalController::class, 'maintenance']);
    Route::get('/client/visits', [ClientPortalController::class, 'visits']);
    Route::get('/client/payments', [ClientPortalController::class, 'payments']);
});
```

## 📄 Document Generation API

### Contract Document Generation
```php
POST /contracts/{contract}/generate-document
Authorization: Bearer {token} | Session

// Request
{
    "template_id": 1,
    "type": "contract",
    "variables": {
        "client_name": "أحمد محمد",
        "contract_number": "CNT-2025-001",
        "start_date": "2025-01-01",
        "end_date": "2025-12-31",
        "total_amount": "50000.00"
    }
}

// Response
{
    "success": true,
    "document": {
        "id": 123,
        "document_number": "DOC-2025-001",
        "type": "contract",
        "file_path": "/documents/contracts/CNT-2025-001.pdf",
        "download_url": "/documents/123/download",
        "generated_at": "2025-01-15T10:30:00Z"
    }
}
```

### Maintenance Request Document
```php
POST /maintenance-requests/{request}/generate-document
Authorization: Bearer {token} | Session

// Request
{
    "template_id": 2,
    "type": "maintenance_report",
    "variables": {
        "request_number": "MNT-2025-001",
        "client_name": "أحمد محمد",
        "description": "صيانة نظام التكييف",
        "completion_date": "2025-01-15",
        "technician_notes": "تم إنجاز المهمة بنجاح"
    }
}

// Response
{
    "success": true,
    "document": {
        "id": 124,
        "document_number": "DOC-2025-002",
        "type": "maintenance_report",
        "file_path": "/documents/maintenance/MNT-2025-001.pdf",
        "download_url": "/documents/124/download",
        "generated_at": "2025-01-15T14:45:00Z"
    }
}
```

### Document Download
```php
GET /documents/{document}/download
Authorization: Bearer {token} | Session

// Response: Binary PDF file
Content-Type: application/pdf
Content-Disposition: attachment; filename="contract-CNT-2025-001.pdf"
```

## 🔧 Maintenance Request API

### Create Maintenance Request
```php
POST /api/maintenance-requests
Authorization: Bearer {token} | Session

// Request
{
    "client_id": 123,
    "contract_id": 456, // optional
    "title": "صيانة نظام التكييف",
    "description": "يحتاج النظام إلى فحص وتنظيف",
    "priority": "medium",
    "requested_date": "2025-01-20",
    "estimated_cost": "500.00"
}

// Response
{
    "success": true,
    "maintenance_request": {
        "id": 789,
        "request_number": "MNT-2025-003",
        "title": "صيانة نظام التكييف",
        "status": "pending",
        "priority": "medium",
        "requested_date": "2025-01-20",
        "created_at": "2025-01-15T09:00:00Z"
    }
}
```

### Update Maintenance Request Status
```php
PATCH /api/maintenance-requests/{request}/status
Authorization: Bearer {token}

// Request
{
    "status": "in_progress",
    "technician_notes": "بدء العمل على الطلب",
    "assigned_to": 5
}

// Response
{
    "success": true,
    "maintenance_request": {
        "id": 789,
        "status": "in_progress",
        "assigned_to": 5,
        "updated_at": "2025-01-15T10:00:00Z"
    }
}
```

## 📅 Visit Scheduling API

### Schedule Visit
```php
POST /api/visits
Authorization: Bearer {token}

// Request
{
    "client_id": 123,
    "contract_id": 456, // optional
    "maintenance_request_id": 789, // optional
    "title": "زيارة صيانة دورية",
    "visit_type": "maintenance",
    "scheduled_date": "2025-01-25T10:00:00Z",
    "technician_id": 5,
    "description": "فحص النظام وتنظيفه"
}

// Response
{
    "success": true,
    "visit": {
        "id": 101,
        "visit_number": "VST-2025-001",
        "title": "زيارة صيانة دورية",
        "status": "scheduled",
        "scheduled_date": "2025-01-25T10:00:00Z",
        "technician": {
            "id": 5,
            "name": "محمد أحمد"
        }
    }
}
```

### Reschedule Visit
```php
PATCH /api/visits/{visit}/reschedule
Authorization: Bearer {token}

// Request
{
    "new_date": "2025-01-26T14:00:00Z",
    "reason": "تضارب في المواعيد"
}

// Response
{
    "success": true,
    "visit": {
        "id": 101,
        "status": "rescheduled",
        "scheduled_date": "2025-01-26T14:00:00Z",
        "reschedule_reason": "تضارب في المواعيد"
    }
}
```

## 💰 Payment API

### Record Payment
```php
POST /api/payments
Authorization: Bearer {token}

// Request
{
    "client_id": 123,
    "contract_id": 456, // optional
    "maintenance_request_id": 789, // optional
    "amount": "750.00",
    "payment_method": "bank_transfer",
    "payment_date": "2025-01-15",
    "reference_number": "BNK-*********",
    "notes": "دفعة أولى من قيمة الصيانة"
}

// Response
{
    "success": true,
    "payment": {
        "id": 201,
        "payment_number": "PAY-2025-001",
        "amount": "750.00",
        "status": "completed",
        "payment_method": "bank_transfer",
        "payment_date": "2025-01-15"
    }
}
```

### Payment History
```php
GET /api/payments?client_id=123&limit=10&page=1
Authorization: Bearer {token}

// Response
{
    "success": true,
    "payments": [
        {
            "id": 201,
            "payment_number": "PAY-2025-001",
            "amount": "750.00",
            "status": "completed",
            "payment_date": "2025-01-15",
            "contract": {
                "id": 456,
                "contract_number": "CNT-2025-001"
            }
        }
    ],
    "pagination": {
        "current_page": 1,
        "total_pages": 3,
        "total_records": 25
    }
}
```

## 🏆 Certificate API

### Request Certificate
```php
POST /api/certificate-requests
Authorization: Bearer {token} | Session

// Request
{
    "client_id": 123,
    "contract_id": 456, // optional
    "certificate_type": "completion_certificate",
    "purpose": "للاستخدام في الجهات الحكومية",
    "requested_date": "2025-01-15"
}

// Response
{
    "success": true,
    "certificate_request": {
        "id": 301,
        "request_number": "CRT-2025-001",
        "certificate_type": "completion_certificate",
        "status": "pending",
        "requested_date": "2025-01-15"
    }
}
```

### Certificate Status
```php
GET /api/certificate-requests/{request}
Authorization: Bearer {token} | Session

// Response
{
    "success": true,
    "certificate_request": {
        "id": 301,
        "request_number": "CRT-2025-001",
        "status": "approved",
        "issue_date": "2025-01-16",
        "expiry_date": "2025-01-16",
        "certificate_data": {
            "certificate_number": "CERT-2025-001",
            "issued_by": "شركة التعاقد",
            "verification_code": "VER-123456"
        }
    }
}
```

## 📢 Notification API

### Send Notification
```php
POST /api/notifications/send
Authorization: Bearer {token}

// Request
{
    "recipient_type": "client",
    "recipient_id": 123,
    "channel": "sms", // sms, whatsapp, email
    "template": "maintenance_reminder",
    "variables": {
        "client_name": "أحمد محمد",
        "appointment_date": "2025-01-25",
        "service_type": "صيانة تكييف"
    }
}

// Response
{
    "success": true,
    "notification": {
        "id": "uuid-notification-id",
        "status": "sent",
        "sent_at": "2025-01-15T12:00:00Z",
        "channel": "sms",
        "recipient": "+************"
    }
}
```

## 🔌 External API Integrations

### DocKing Integration
```php
// Internal service call
$docKingService = app(DocKingService::class);

// Generate document
$document = $docKingService->generateDocument([
    'template' => 'contract_template',
    'data' => [
        'client_name' => 'أحمد محمد',
        'contract_details' => [...]
    ]
]);

// Convert to PDF
$pdf = $docKingService->convertToPdf($document['html']);
```

### SMS Provider Integration
```php
// Msegat SMS
$msegat = new MsegatChannel();
$msegat->send($notifiable, new OTPNotification($otp));

// Taqnyat SMS
$taqnyat = new TaqnyatChannel();
$taqnyat->send($notifiable, new MaintenanceReminder($visit));

// Waha WhatsApp
$waha = new WahaChannel();
$waha->send($notifiable, new ContractSigned($contract));
```

## 🛠️ Utility Endpoints

### Health Check
```php
GET /api/health
// Response
{
    "status": "healthy",
    "timestamp": "2025-01-15T12:00:00Z",
    "database": "connected",
    "cache": "operational",
    "queue": "active"
}
```

### System Information
```php
GET /api/system/info
Authorization: Bearer {admin_token}

// Response
{
    "application": {
        "name": "Contractly",
        "version": "1.0.0",
        "environment": "production"
    },
    "tenant": {
        "id": "uuid-tenant-id",
        "name": "شركة الخدمات المتقدمة"
    },
    "statistics": {
        "total_clients": 150,
        "active_contracts": 75,
        "pending_maintenance": 25
    }
}
```

## 📊 Error Handling

### Standard Error Response
```php
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "البيانات المدخلة غير صحيحة",
        "details": {
            "phone": ["رقم الهاتف مطلوب"],
            "amount": ["المبلغ يجب أن يكون أكبر من صفر"]
        }
    },
    "timestamp": "2025-01-15T12:00:00Z"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## 🔒 Rate Limiting

### API Rate Limits
- **Authentication endpoints**: 5 requests per minute
- **OTP requests**: 3 requests per 5 minutes
- **General API**: 60 requests per minute
- **File uploads**: 10 requests per minute

### Rate Limit Headers
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642252800
```

## 🔍 API Versioning

### Current Version
- **Version**: v1
- **Base URL**: `/api/v1/`
- **Content Type**: `application/json`
- **Charset**: `UTF-8`

### Future Versioning Strategy
- Version headers: `Accept: application/vnd.contractly.v2+json`
- URL versioning: `/api/v2/`
- Backward compatibility maintained for 2 major versions

---

*This API documentation provides comprehensive coverage of Contractly's API endpoints. The API is designed for reliability, security, and ease of integration with external systems.*