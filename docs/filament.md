TITLE: Install Filament for New Laravel Projects Bash
DESCRIPTION: Installs Filament with scaffolding and Table Builder assets for a new Laravel project. It also includes commands to install NPM dependencies and compile assets.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install --scaffold --tables

npm install

npm run dev
```

----------------------------------------

TITLE: Defining a Basic Filament Form Schema in PHP
DESCRIPTION: This snippet demonstrates how to define a basic form schema in Filament using PHP. It initializes a `Form` object and adds `TextInput` fields for 'title' and 'slug', and a `RichEditor` field for 'content' to the form's schema array. This sets up the fundamental structure for data input.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/02-getting-started.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;

public function form(Form $form): Form
{
    return $form
        ->schema([
            TextInput::make('title'),
            TextInput::make('slug'),
            RichEditor::make('content'),
        ]);
}
```

----------------------------------------

TITLE: Creating a New Filament Panel (CLI)
DESCRIPTION: This Bash command is used to generate a new Filament panel. It creates a dedicated configuration file (a Laravel service provider) for the new panel, allowing it to have its own set of resources, pages, and widgets, accessible at a default path derived from its name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:filament-panel app
```

----------------------------------------

TITLE: Creating Basic Filament Resource
DESCRIPTION: This command generates a new basic FilamentPHP resource class and associated page classes for a given Eloquent model (e.g., `App\Models\Customer`). It creates files for listing, creating, and editing records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:filament-resource Customer
```

----------------------------------------

TITLE: Implementing Filament Form in Livewire Component (PHP)
DESCRIPTION: This comprehensive PHP class demonstrates how to integrate a Filament form into a Livewire component. It includes implementing `HasForms`, using `InteractsWithForms`, defining a `$data` property, configuring the form schema in `form()`, initializing the form in `mount()`, and handling form submission in `create()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_3

LANGUAGE: php
CODE:
```
<?php

namespace App\Livewire;

use App\Models\Post;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class CreatePost extends Component implements HasForms
{
    use InteractsWithForms;
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill();
    }
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('title')
                    ->required(),
                MarkdownEditor::make('content'),
                // ...
            ])
            ->statePath('data');
    }
    
    public function create(): void
    {
        dd($this->form->getState());
    }
    
    public function render(): View
    {
        return view('livewire.create-post');
    }
}
```

----------------------------------------

TITLE: Scaffolding Filament in New Laravel Projects
DESCRIPTION: These commands quickly set up Filament, Livewire, Alpine.js, and Tailwind CSS in a *new* Laravel project. `filament:install --scaffold --forms` installs core Filament components, `npm install` fetches Node.js dependencies, and `npm run dev` compiles assets.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install --scaffold --forms

npm install

npm run dev
```

----------------------------------------

TITLE: Passing Data to an Infolist Action in PHP with Pest
DESCRIPTION: This example shows how to pass an array of data to an infolist action using the `data` parameter in `callInfolistAction()`. It simulates sending an invoice with a specific email address, asserts that no validation errors occur, and verifies that the invoice is sent and the recipient email is correctly updated.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/08-testing.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can send invoices', function () {
    $invoice = Invoice::factory()->create();

    livewire(EditInvoice::class, [
        'invoice' => $invoice,
    ])
        ->callInfolistAction('customer', 'send', data: [
            'email' => $email = fake()->email(),
        ])
        ->assertHasNoInfolistActionErrors();

    expect($invoice->refresh())
        ->isSent()->toBeTrue()
        ->recipient_email->toBe($email);
});
```

----------------------------------------

TITLE: Creating Filament Resource with Soft Deletes Support
DESCRIPTION: This command generates a FilamentPHP resource and includes built-in support for handling soft-deleted records. This adds functionality to restore, force delete, and filter trashed records within the resource interface.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_3

LANGUAGE: bash
CODE:
```
php artisan make:filament-resource Customer --soft-deletes
```

----------------------------------------

TITLE: Setting Form Model with Instance in Filament PHP
DESCRIPTION: This snippet demonstrates how to associate an existing Eloquent model instance with a Filament form. By passing `$this->post` to `$form->model()`, fields can automatically load data, manage relationships, and leverage model-based validation rules. This is the recommended approach when the model is available.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_7

LANGUAGE: PHP
CODE:
```
use App\Models\Post;
use Filament\Forms\Form;

public Post $post;

public function form(Form $form): Form
{
    return $form
        ->schema([
            // ...
        ])
        ->statePath('data')
        ->model($this->post);
}
```

----------------------------------------

TITLE: Installing Filament Form Builder Package with Composer
DESCRIPTION: This command adds the Filament Form Builder package to your Laravel project using Composer, specifying version 3.3 or higher. The `-W` flag resolves conflicts by updating dependencies.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require filament/forms:"^3.3" -W
```

----------------------------------------

TITLE: Custom Closure Validation with Field State Injection in Filament (PHP)
DESCRIPTION: This snippet shows how to create a custom closure validation rule that injects the `Get` utility, allowing access to the state of other fields within the form. This enables complex validation logic where a field's validity depends on the values of other form components, such as 'other_field'.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_57

LANGUAGE: PHP
CODE:
```
use Closure;
use FilamentFormsGet;

TextInput::make('slug')->rules([
    fn (Get $get): Closure => function (string $attribute, $value, Closure $fail) use ($get) {
        if ($get('other_field') === 'foo' && $value !== 'bar') {
            $fail("The {$attribute} is invalid.");
        }
    },
])
```

----------------------------------------

TITLE: Installing Filament Panel Builder
DESCRIPTION: This command installs the core Filament Panel Builder package, which serves as the foundation for building Laravel admin panels, customer-facing applications, and SaaS platforms. It combines various Filament packages for rapid CRUD interface development.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require filament/filament
```

----------------------------------------

TITLE: Defining Resource Form Schema (PHP)
DESCRIPTION: This PHP code snippet demonstrates the `form()` method in a Filament resource, which defines the structure and components of the form used for creating and editing records. It uses the `schema()` method to arrange form fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_8

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;

public static function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('name')->required(),
            Forms\Components\TextInput::make('email')->email()->required(),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Defining Row Actions in Filament Table
DESCRIPTION: This snippet demonstrates how to define row actions within a Filament table by using the `actions()` method on the `Table` instance. This method is where individual action buttons for each table row are registered.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->actions([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Generating Slug on FilamentPHP Title Update
DESCRIPTION: Automatically generates a slug for a slug field based on the title field's value. Uses `afterStateUpdated()` on the `title` field, injecting `Set` to update the `slug` field with a Laravel `Str::slug()` of the new state.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_21

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Illuminate\Support\Str;

TextInput::make('title')
    ->live(onBlur: true)
    ->afterStateUpdated(fn (Set $set, ?string $state) => $set('slug', Str::slug($state)))
    
TextInput::make('slug')
```

----------------------------------------

TITLE: Mutating Casted State in Import Columns (PHP)
DESCRIPTION: This snippet demonstrates how to mutate the state of an import column after it has been cast, by chaining `castStateUsing()` after a built-in casting method like `numeric()`. This allows for further processing of the casted value, such as rounding a price multiplied by 100, providing fine-grained control over data transformation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Imports\ImportColumn;

ImportColumn::make('price')
    ->numeric()
    ->castStateUsing(function (float $state): ?float {
        if (blank($state)) {
            return null;
        }
    
        return round($state * 100);
    })
```

----------------------------------------

TITLE: Enabling Multiple Authentication Features PHP
DESCRIPTION: Use chainable methods on the Filament panel configuration to quickly enable standard authentication features like login, registration, password reset, email verification, and profile management.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/08-users.md#_snippet_6

LANGUAGE: php
CODE:
```
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->login()
        ->registration()
        ->passwordReset()
        ->emailVerification()
        ->profile();
}
```

----------------------------------------

TITLE: Extending and Customizing an Authentication Page Form PHP
DESCRIPTION: Extend a base Filament authentication page class and override the `form` method to customize the fields displayed on that page. You can include default components by calling their respective getter methods (e.g., `getNameFormComponent`).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/08-users.md#_snippet_8

LANGUAGE: php
CODE:
```
<?php

namespace App\Filament\Pages\Auth;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Auth\EditProfile as BaseEditProfile;

class EditProfile extends BaseEditProfile
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('username')
                    ->required()
                    ->maxLength(255),
                $this->getNameFormComponent(),
                $this->getEmailFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getPasswordConfirmationFormComponent(),
            ]);
    }
}
```

----------------------------------------

TITLE: Rendering Livewire Component in Blade
DESCRIPTION: This Blade directive renders the `create-post` Livewire component within a Blade view. It's a common way to embed Livewire components into traditional Laravel views.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_1

LANGUAGE: blade
CODE:
```
@livewire('create-post')
```

----------------------------------------

TITLE: Rendering Table in Livewire View (Blade)
DESCRIPTION: This Blade snippet shows how to display the Filament table within the Livewire component's view. The `$this->table` property automatically renders the table configured in the Livewire component's `table()` method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/11-adding-a-table-to-a-livewire-component.md#_snippet_4

LANGUAGE: blade
CODE:
```
<div>
    {{ $this->table }}
</div>
```

----------------------------------------

TITLE: Implementing Table in Livewire Component (PHP)
DESCRIPTION: This comprehensive PHP code demonstrates how to integrate a Filament table into a Livewire component. It requires implementing `HasForms` and `HasTable` interfaces, using their respective traits, and defining a `table()` method to configure the table's query, columns, filters, actions, and bulk actions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/11-adding-a-table-to-a-livewire-component.md#_snippet_3

LANGUAGE: php
CODE:
```
<?php

namespace App\Livewire;

use App\Models\Shop\Product;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class ListProducts extends Component implements HasForms, HasTable
{
    use InteractsWithTable;
    use InteractsWithForms;
    
    public function table(Table $table): Table
    {
        return $table
            ->query(Product::query())
            ->columns([
                TextColumn::make('name'),
            ])
            ->filters([
                // ...
            ])
            ->actions([
                // ...
            ])
            ->bulkActions([
                // ...
            ]);
    }
    
    public function render(): View
    {
        return view('livewire.list-products');
    }
}
```

----------------------------------------

TITLE: Implementing Multi-Step Wizard Forms in Filament Modals (PHP)
DESCRIPTION: This example shows how to integrate a multi-step wizard form into a Filament action modal. Instead of a single `form()`, the `steps()` method is used to define an array of `Step` objects, each representing a stage in the wizard with its own schema of form components. This allows for complex data collection processes within a modal.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use FilamentFormsComponentsMarkdownEditor;
use FilamentFormsComponentsTextInput;
use FilamentFormsComponentsToggle;
use FilamentFormsComponentsWizardStep;

Action::make('create')
    ->steps([
        Step::make('Name')
            ->description('Give the category a unique name')
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn ($state, callable $set) => $set('slug', Str::slug($state))),
                TextInput::make('slug')
                    ->disabled()
                    ->required()
                    ->unique(Category::class, 'slug'),
            ])
            ->columns(2),
        Step::make('Description')
            ->description('Add some extra details')
            ->schema([
                MarkdownEditor::make('description'),
            ]),
        Step::make('Visibility')
            ->description('Control who can view it')
            ->schema([
                Toggle::make('is_visible')
                    ->label('Visible to customers.')
                    ->default(true),
            ]),
    ])
```

----------------------------------------

TITLE: Configuring Filament Repeater with HasMany Relationship in PHP
DESCRIPTION: This PHP snippet demonstrates how to configure a Filament `Repeater` component to manage `orderProducts` (the `HasMany` relationship to the pivot model). It uses `relationship()` to link the repeater to the Eloquent relationship and defines a `Select` field for `product_id` within the repeater's schema, allowing users to select products for each order item.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_19

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;

Repeater::make('orderProducts')
    ->relationship()
    ->schema([
        Select::make('product_id')
            ->relationship('product', 'name')
            ->required(),
        // ...
    ])
```

----------------------------------------

TITLE: Creating a Blade Layout File for Livewire Components
DESCRIPTION: This Blade template defines the main HTML structure for Livewire components. It includes meta tags, title, CSRF token, viewport settings, and integrates Filament's styles and scripts, along with the application's CSS and JavaScript assets.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">

        <meta name="application-name" content="{{ config('app.name') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        @vite('resources/css/app.css')
    </head>

    <body class="antialiased">
        {{ $slot }}

        @filamentScripts
        @vite('resources/js/app.js')
    </body>
</html>
```

----------------------------------------

TITLE: Testing Hidden Delete Action in Filament PHP
DESCRIPTION: This snippet shows how to verify that a `DeleteAction` is hidden for a specific user or scenario. It mounts the Livewire component and then uses `assertActionHidden()` to confirm that the delete action is not visible, enforcing access control.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_12

LANGUAGE: PHP
CODE:
```
use Filament\Actions\DeleteAction;
use function Pest\Livewire\livewire;

it('can not delete', function () {
    $post = Post::factory()->create();

    livewire(PostResource\Pages\EditPost::class, [
        'record' => $post->getRouteKey(),
    ])
        ->assertActionHidden(DeleteAction::class);
});
```

----------------------------------------

TITLE: Conditionally Showing FilamentPHP Field (visible)
DESCRIPTION: Shows a field based on the value of another field. Uses the `visible()` method as an alternative to `hidden()`. The function injects the `Get` utility to check the state of the `is_company` checkbox, making the `company_name` field visible when `is_company` is true.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_19

LANGUAGE: php
CODE:
```
use Filament\Forms\Get;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\TextInput;

Checkbox::make('is_company')
    ->live()
    
TextInput::make('company_name')
    ->visible(fn (Get $get): bool => $get('is_company'))
```

----------------------------------------

TITLE: Basic Select Component Usage with Input Wrapper (Blade)
DESCRIPTION: This snippet illustrates the fundamental usage of the Filament select component. It demonstrates how to wrap the select element within an `x-filament::input.wrapper` for proper styling and how to define multiple options for selection. The `wire:model="status"` attribute binds the selected value to a Livewire property.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-select.md#_snippet_0

LANGUAGE: Blade
CODE:
```
<x-filament::input.wrapper>
    <x-filament::input.select wire:model="status">
        <option value="draft">Draft</option>
        <option value="reviewing">Reviewing</option>
        <option value="published">Published</option>
    </x-filament::input.select>
</x-filament::input.wrapper>
```

----------------------------------------

TITLE: Defining Table Actions in Filament PHP
DESCRIPTION: Illustrates how to define row actions and bulk actions for a Filament table. It shows custom row actions ('feature', 'unfeature') with conditional visibility/hidden states and a built-in `DeleteBulkAction` grouped within `BulkActionGroup`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/02-getting-started.md#_snippet_5

LANGUAGE: php
CODE:
```
use App\Models\Post;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;

public function table(Table $table): Table
{
    return $table
        ->columns([
            // ...
        ])
        ->actions([
            Action::make('feature')
                ->action(function (Post $record) {
                    $record->is_featured = true;
                    $record->save();
                })
                ->hidden(fn (Post $record): bool => $record->is_featured),
            Action::make('unfeature')
                ->action(function (Post $record) {
                    $record->is_featured = false;
                    $record->save();
                })
                ->visible(fn (Post $record): bool => $record->is_featured),
        ])
        ->bulkActions([
            BulkActionGroup::make([
                DeleteBulkAction::make(),
            ]),
        ]);
}
```

----------------------------------------

TITLE: Implementing Lifecycle Hooks for SelectColumn in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to utilize lifecycle hooks with a Filament `SelectColumn` to execute custom logic before or after the state is updated. `beforeStateUpdated` runs prior to saving, while `afterStateUpdated` runs immediately after the state has been persisted to the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/06-select.md#_snippet_3

LANGUAGE: PHP
CODE:
```
SelectColumn::make()
    ->beforeStateUpdated(function ($record, $state) {
        // Runs before the state is saved to the database.
    })
    ->afterStateUpdated(function ($record, $state) {
        // Runs after the state is saved to the database.
    })
```

----------------------------------------

TITLE: Prohibiting Other Fields Based on Current Field in FilamentPHP (PHP)
DESCRIPTION: The `prohibits` rule ensures that if the current field is not empty, then all other specified fields must be empty. This is useful for mutually exclusive inputs, where selecting one option makes others invalid.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_33

LANGUAGE: PHP
CODE:
```
Field::make('name')->prohibits('field')
```

LANGUAGE: PHP
CODE:
```
Field::make('name')->prohibits(['field', 'another_field'])
```

----------------------------------------

TITLE: Persisting Table State in URL Query String (PHP)
DESCRIPTION: This code defines Livewire properties annotated with #[Url] to automatically store and retrieve table state parameters like reordering status, filters, grouping, search terms, and sorting columns/directions in the URL's query string. This allows the table's state to persist across requests and be shareable via the URL.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_21

LANGUAGE: PHP
CODE:
```
use Livewire\Attributes\Url;

#[Url]
public bool $isTableReordering = false;

/**
 * @var array<string, mixed> | null
 */
#[Url]
public ?array $tableFilters = null;

#[Url]
public ?string $tableGrouping = null;

#[Url]
public ?string $tableGroupingDirection = null;

/**
 * @var ?string
 */
#[Url]
public $tableSearch = '';

#[Url]
public ?string $tableSortColumn = null;

#[Url]
public ?string $tableSortDirection = null;
}
```

----------------------------------------

TITLE: Defining Resource Table Schema (PHP)
DESCRIPTION: This PHP code snippet illustrates the `table()` method in a Filament resource, used to define the structure of the table displayed on the List page. It includes examples of defining columns, filters, record actions, and bulk actions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_11

LANGUAGE: php
CODE:
```
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

public static function table(Table $table): Table
{
    return $table
        ->columns([
            Tables\Columns\TextColumn::make('name'),
            Tables\Columns\TextColumn::make('email'),
            // ...
        ])
        ->filters([
            Tables\Filters\Filter::make('verified')
                ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at')),
            // ...
        ])
        ->actions([
            Tables\Actions\EditAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
            ]),
        ]);
}
```

----------------------------------------

TITLE: Creating a Filament User Account
DESCRIPTION: This Artisan command creates a new user account specifically for Filament, allowing access to the admin panel. It's a quick way to set up an initial administrator after installation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan make:filament-user
```

----------------------------------------

TITLE: Injecting Eloquent Record into Configuration Function - Filament PHP
DESCRIPTION: Explains how to inject the current Eloquent model instance associated with the form into a configuration closure by type-hinting the `$record` parameter with `Illuminate\Database\Eloquent\Model`. This is useful when the form is used for creating or editing records and logic needs to depend on the model's attributes. The parameter is nullable (`?Model`) as the record might not exist (e.g., during a create operation).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_7

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

function (?Model $record) {
    // ...
}
```

----------------------------------------

TITLE: Auto-Hashing Password on Form Submission in Filament PHP
DESCRIPTION: This snippet extends the basic password field to automatically hash the input value when the form state is dehydrated (usually on submission). It uses the `dehydrateStateUsing` method with a closure that applies Laravel's `Hash::make`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_27

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Hash;

TextInput::make('password')
    ->password()
    ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
```

----------------------------------------

TITLE: Applying Unique Validation Ignoring Current Record in Panel Builder (PHP)
DESCRIPTION: This snippet shows how to use `ignoreRecord: true` with the `unique` validation rule specifically within the Filament Panel Builder. This simplifies ignoring the current record during uniqueness checks for update operations.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_50

LANGUAGE: PHP
CODE:
```
Field::make('email')->unique(ignoreRecord: true)
```

----------------------------------------

TITLE: Creating Text Entries with `make()` (Filament PHP)
DESCRIPTION: This snippet illustrates how to create `TextEntry` components using the static `make()` method in Filament. It demonstrates creating an entry for a direct attribute ('title') and accessing a nested attribute through a relationship using dot notation ('author.name'). This method requires a unique name for the entry.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('title')

TextEntry::make('author.name')
```

----------------------------------------

TITLE: Creating Filament Text Columns PHP
DESCRIPTION: Demonstrates how to instantiate `TextColumn` objects using the static `make()` method, referencing model attributes ('title') or relationship attributes using dot notation ('author.name'). Requires the `Filament\Tables\Columns\TextColumn` class.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title');

TextColumn::make('author.name');
```

----------------------------------------

TITLE: Authorizing Panel Access with FilamentUser PHP
DESCRIPTION: Implement the `FilamentUser` contract on your User model to control access to Filament panels in non-local environments. The `canAccessPanel` method determines access based on arbitrary logic, such as checking email domain and verification status.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/08-users.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable implements FilamentUser
{
    // ...

    public function canAccessPanel(Panel $panel): bool
    {
        return str_ends_with($this->email, '@yourdomain.com') && $this->hasVerifiedEmail();
    }
}
```

----------------------------------------

TITLE: Updating or Creating Records by SKU in Filament Importer (PHP)
DESCRIPTION: This code demonstrates how to modify the `resolveRecord()` method to either update an existing record or create a new one. It uses `Product::firstOrNew()` to find a product by its 'sku' from the imported data; if found, it updates it, otherwise, it creates a new product.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_28

LANGUAGE: PHP
CODE:
```
use App\Models\Product;

public function resolveRecord(): ?Product
{
    return Product::firstOrNew([
        'sku' => $this->data['sku'],
    ]);
}
```

----------------------------------------

TITLE: Using HasLabel Enums with Filament Form and Table Components in PHP
DESCRIPTION: This snippet illustrates how to leverage a PHP enum implementing `HasLabel` to automatically populate options for various Filament components. By passing the enum class (e.g., `Status::class`) to the `options()` method of `Select`, `CheckboxList`, `Radio`, `SelectColumn`, and `SelectFilter`, Filament generates key-value pairs where the enum's value is the key and its label is the displayed value, simplifying UI option generation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/07-enums.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Filters\SelectFilter;

Select::make('status')
    ->options(Status::class)

CheckboxList::make('status')
    ->options(Status::class)

Radio::make('status')
    ->options(Status::class)

SelectColumn::make('status')
    ->options(Status::class)

SelectFilter::make('status')
    ->options(Status::class)
```

----------------------------------------

TITLE: Inserting Livewire Component into Filament Infolist - PHP
DESCRIPTION: This snippet demonstrates how to embed a Livewire component directly into a Filament infolist. It uses the Livewire::make() method to instantiate the component, making it available for display within the infolist structure.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/06-advanced.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\Livewire;
use App\Livewire\Foo;

Livewire::make(Foo::class)
```

----------------------------------------

TITLE: Applying Required Validation to Filament Fields (PHP)
DESCRIPTION: This snippet demonstrates how to apply the `required` validation rule to a Filament form field. The field value must not be empty. It leverages Laravel's built-in validation functionality.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_34

LANGUAGE: PHP
CODE:
```
Field::make('name')->required()
```

----------------------------------------

TITLE: Creating a Simple Text Column in Filament Tables (PHP)
DESCRIPTION: This snippet demonstrates the basic usage of TextColumn to display simple text from a database field. It takes the column's name as an argument to retrieve and render the corresponding data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title')
```

----------------------------------------

TITLE: Creating a Basic Text Input Field in Filament
DESCRIPTION: Demonstrates how to create a basic text input field in Filament forms using the static `make()` method. The `name` parameter specifies the unique identifier for the field, which typically corresponds to a property on the Livewire component or a key in an array.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
```

----------------------------------------

TITLE: Initializing Table Filters in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to define filters within a Filament table. Filters are added to the `$table->filters()` method, allowing users to apply constraints to the displayed data. It's a foundational step for enabling filtering functionality.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/01-getting-started.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->filters([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Authorizing Specific Panel Access with FilamentUser PHP
DESCRIPTION: Extend the `canAccessPanel` method to apply different authorization rules based on the specific panel being accessed. This example restricts access to the 'admin' panel based on email domain and verification but allows access to other panels.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/08-users.md#_snippet_1

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable implements FilamentUser
{
    // ...

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin') {
            return str_ends_with($this->email, '@yourdomain.com') && $this->hasVerifiedEmail();
        }

        return true;
    }
}
```

----------------------------------------

TITLE: Customizing Eloquent Query using Method in FilamentPHP
DESCRIPTION: Apply custom constraints or model scopes to the base Eloquent query used by a Filament resource by overriding the static `getEloquentQuery()` method and chaining query builder methods.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_34

LANGUAGE: php
CODE:
```
public static function getEloquentQuery(): Builder
{
    return parent::getEloquentQuery()->where('is_active', true);
}
```

----------------------------------------

TITLE: Creating Resource and Model/Migration/Factory
DESCRIPTION: This command generates a FilamentPHP resource and simultaneously scaffolds the corresponding Eloquent model, database migration, and factory using the specified flags. It streamlines the setup process for new features.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_6

LANGUAGE: bash
CODE:
```
php artisan make:filament-resource Customer --model --migration --factory
```

----------------------------------------

TITLE: Defining Create Option Form for Relationship in Filament Select (PHP)
DESCRIPTION: This Filament PHP code defines a custom form using `createOptionForm()` for a `Select` component. This form, which opens in a modal, allows users to create a new related record (e.g., an author) directly from the select field by providing required `name` and `email` inputs. Upon submission, the newly created record is automatically selected.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_20

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->createOptionForm([
        Forms\Components\TextInput::make('name')
            ->required(),
        Forms\Components\TextInput::make('email')
            ->required()
            ->email(),
    ]),
```

----------------------------------------

TITLE: Authenticating Users in PHPUnit/Pest TestCase
DESCRIPTION: This snippet demonstrates how to ensure a user is authenticated within your test case's `setUp()` method. This is a prerequisite for accessing and testing Filament application pages and components, as they typically require an authenticated user.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_0

LANGUAGE: PHP
CODE:
```
protected function setUp(): void
{
    parent::setUp();

    $this->actingAs(User::factory()->create());
}
```

----------------------------------------

TITLE: Creating a Filament Resource using Artisan Command
DESCRIPTION: This command generates a new Filament resource for the `Patient` Eloquent model, creating necessary files for CRUD operations. It is the initial step to set up a resource in Filament, organizing the codebase for patient management.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_4

LANGUAGE: bash
CODE:
```
php artisan make:filament-resource Patient
```

----------------------------------------

TITLE: Using Functions for Dynamic Field Configuration - Filament PHP
DESCRIPTION: Demonstrates that many Filament form component configuration methods accept closures (functions) as arguments. This allows field properties like display format, options, or required status to be determined dynamically based on logic executed at runtime. Requires the use of `use` statements for the component classes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_3

LANGUAGE: php
CODE:
```
use App\Models\User;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

DatePicker::make('date_of_birth')
    ->displayFormat(function (): string {
        if (auth()->user()->country_id === 'us') {
            return 'm/d/Y';
        }

        return 'd/m/Y';
    });

Select::make('user_id')
    ->options(function (): array {
        return User::all()->pluck('name', 'id')->all();
    });

TextInput::make('middle_name')
    ->required(fn (): bool => auth()->user()->hasMiddleName());
```

----------------------------------------

TITLE: Implementing Various Importer Lifecycle Hooks in Filament PHP
DESCRIPTION: This comprehensive example shows a `ProductImporter` class extending `Filament\Actions\Imports\Importer` and implementing various lifecycle hooks. These hooks, such as `beforeValidate()`, `afterValidate()`, `beforeFill()`, `afterFill()`, `beforeSave()`, `afterSave()`, `beforeCreate()`, `afterCreate()`, `beforeUpdate()`, and `afterUpdate()`, allow developers to inject custom logic at different stages of the import process, from data validation to record saving and creation/update events. Inside these hooks, `$this->data`, `$this->originalData`, `$this->record`, and `$this->options` can be accessed for contextual information.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_59

LANGUAGE: php
CODE:
```
use Filament\Actions\Imports\Importer;

class ProductImporter extends Importer
{
    // ...

    protected function beforeValidate(): void
    {
        // Runs before the CSV data for a row is validated.
    }

    protected function afterValidate(): void
    {
        // Runs after the CSV data for a row is validated.
    }

    protected function beforeFill(): void
    {
        // Runs before the validated CSV data for a row is filled into a model instance.
    }

    protected function afterFill(): void
    {
        // Runs after the validated CSV data for a row is filled into a model instance.
    }

    protected function beforeSave(): void
    {
        // Runs before a record is saved to the database.
    }

    protected function beforeCreate(): void
    {
        // Similar to `beforeSave()`, but only runs when creating a new record.
    }

    protected function beforeUpdate(): void
    {
        // Similar to `beforeSave()`, but only runs when updating an existing record.
    }

    protected function afterSave(): void
    {
        // Runs after a record is saved to the database.
    }
    
    protected function afterCreate(): void
    {
        // Similar to `afterSave()`, but only runs when creating a new record.
    }
    
    protected function afterUpdate(): void
    {
        // Similar to `afterSave()`, but only runs when updating an existing record.
    }
}
```

----------------------------------------

TITLE: Comprehensive Lifecycle Hooks for Filament PHP Edit Pages
DESCRIPTION: This example showcases the full suite of lifecycle hooks available for Filament PHP Edit pages, extending `EditRecord`. These hooks allow developers to inject custom logic at various stages: before/after filling form fields, before/after validating, and before/after saving data to the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_10

LANGUAGE: php
CODE:
```
use Filament\Resources\Pages\EditRecord;

class EditUser extends EditRecord
{
    // ...

    protected function beforeFill(): void
    {
        // Runs before the form fields are populated from the database.
    }

    protected function afterFill(): void
    {
        // Runs after the form fields are populated from the database.
    }

    protected function beforeValidate(): void
    {
        // Runs before the form fields are validated when the form is saved.
    }

    protected function afterValidate(): void
    {
        // Runs after the form fields are validated when the form is saved.
    }

    protected function beforeSave(): void
    {
        // Runs before the form fields are saved to the database.
    }

    protected function afterSave(): void
    {
        // Runs after the form fields are saved to the database.
    }
}
```

----------------------------------------

TITLE: Creating a Basic Text Input in FilamentPHP
DESCRIPTION: This snippet demonstrates how to create a basic text input field using FilamentPHP's `TextInput` component. It initializes a text input named 'name' for general string interaction.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/02-text-input.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
```

----------------------------------------

TITLE: Creating Eloquent Models and Migrations using Artisan
DESCRIPTION: This snippet demonstrates how to use Laravel's Artisan command-line tool to generate new Eloquent models along with their corresponding database migration files. The `-m` flag ensures that a migration file is created for each model, facilitating database schema definition. This is a prerequisite for defining the database structure for the `Owner`, `Patient`, and `Treatment` entities.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:model Owner -m
php artisan make:model Patient -m
php artisan make:model Treatment -m
```

----------------------------------------

TITLE: Creating Edit and Delete Row Actions in Filament
DESCRIPTION: This code illustrates how to create 'edit' and 'delete' actions for table rows. The 'edit' action uses `url()` to navigate to a record's edit page, opening in a new tab. The 'delete' action uses `action()` to perform a record deletion, requiring user confirmation before execution. Both actions can access the current `$record`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use App\Models\Post;
use Filament\Tables\Actions\Action;

Action::make('edit')
    ->url(fn (Post $record): string => route('posts.edit', $record))
    ->openUrlInNewTab();

Action::make('delete')
    ->requiresConfirmation()
    ->action(fn (Post $record) => $record->delete());
```

----------------------------------------

TITLE: Implementing FilamentUser Contract for Panel Access
DESCRIPTION: This PHP snippet demonstrates how to implement the `FilamentUser` contract in a Laravel `User` model. The `canAccessPanel` method defines the logic for determining if a user is authorized to access a specific Filament panel, crucial for production security.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/01-installation.md#_snippet_8

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable implements FilamentUser
{
    // ...

    public function canAccessPanel(Panel $panel): bool
    {
        return str_ends_with($this->email, '@yourdomain.com') && $this->hasVerifiedEmail();
    }
}
```

----------------------------------------

TITLE: Defining Reusable Form Fields in FilamentPHP Resource (PHP)
DESCRIPTION: This PHP snippet demonstrates how to define reusable form fields within a FilamentPHP resource. It extracts common form fields like 'name' and 'slug' into public static methods (`getNameFormField`, `getSlugFormField`) within the `CategoryResource` class. This approach reduces code repetition, allowing these fields to be easily retrieved and used in both the main resource form and wizard steps, promoting maintainability and consistency.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_13

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;

class CategoryResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                static::getNameFormField(),
                static::getSlugFormField(),
                // ...
            ]);
    }
    
    public static function getNameFormField(): Forms\Components\TextInput
    {
        return TextInput::make('name')
            ->required()
            ->live()
            ->afterStateUpdated(fn ($state, callable $set) => $set('slug', Str::slug($state)));
    }
    
    public static function getSlugFormField(): Forms\Components\TextInput
    {
        return TextInput::make('slug')
            ->disabled()
            ->required()
            ->unique(Category::class, 'slug', fn ($record) => $record);
    }
}
```

----------------------------------------

TITLE: Installing Filament Panel Builder with Composer and Artisan
DESCRIPTION: This snippet outlines the commands required to install the Filament Panel Builder into a Laravel project. It uses Composer to add the Filament package and then runs an Artisan command to set up the necessary panel files and register the service provider.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/01-installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require filament/filament:"^3.3" -W

php artisan filament:install --panels
```

----------------------------------------

TITLE: Building Table for Eloquent Relationship (PHP)
DESCRIPTION: This PHP code demonstrates how to build a Filament table based on an Eloquent relationship, specifically a `BelongsToMany` relationship. It uses `relationship()` to define the source and `inverseRelationship()` for actions, allowing the table to operate directly on related records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/11-adding-a-table-to-a-livewire-component.md#_snippet_5

LANGUAGE: php
CODE:
```
use App\Models\Category;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

public Category $category;

public function table(Table $table): Table
{
    return $table
        ->relationship(fn (): BelongsToMany => $this->category->products())
        ->inverseRelationship('categories')
        ->columns([
            TextColumn::make('name'),
        ]);
}
```

----------------------------------------

TITLE: Defining Patient Table Columns with Related Data (PHP)
DESCRIPTION: This snippet configures the table view for patients in FilamentPHP. It defines 'TextColumn' entries for 'name', 'type', 'date_of_birth', and 'owner.name'. The 'owner.name' column demonstrates Filament's dot notation for eager-loading and displaying related model data, showing the owner's name instead of just their ID.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_13

LANGUAGE: php
CODE:
```
use Filament\Tables;
use Filament\Tables\Table;

public static function table(Table $table): Table
{
    return $table
        ->columns([
            Tables\Columns\TextColumn::make('name'),
            Tables\Columns\TextColumn::make('type'),
            Tables\Columns\TextColumn::make('date_of_birth'),
            Tables\Columns\TextColumn::make('owner.name'),
        ]);
}
```

----------------------------------------

TITLE: Implementing Multi-Step Wizard for Create Action in FilamentPHP (PHP)
DESCRIPTION: This snippet demonstrates transforming a `CreateAction` into a multi-step wizard by using the `steps()` method instead of `form()`. It defines three steps ('Name', 'Description', 'Visibility'), each with its own schema of form components like `TextInput`, `MarkdownEditor`, and `Toggle`. This provides a guided user experience for complex creation processes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/01-create.md#_snippet_12

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard\Step;

CreateAction::make()
    ->steps([
        Step::make('Name')
            ->description('Give the category a unique name')
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn ($state, callable $set) => $set('slug', Str::slug($state))),
                TextInput::make('slug')
                    ->disabled()
                    ->required()
                    ->unique(Category::class, 'slug'),
            ])
            ->columns(2),
        Step::make('Description')
            ->description('Add some extra details')
            ->schema([
                MarkdownEditor::make('description'),
            ]),
        Step::make('Visibility')
            ->description('Control who can view it')
            ->schema([
                Toggle::make('is_visible')
                    ->label('Visible to customers.')
                    ->default(true),
            ]),
    ])
```

----------------------------------------

TITLE: Accessing Parameters in Livewire Component Mount Method - PHP
DESCRIPTION: This PHP snippet shows how parameters passed to a Livewire component from a Filament infolist can be received directly in the component's mount() method as arguments. This allows for initial data setup based on the provided parameters.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/06-advanced.md#_snippet_3

LANGUAGE: PHP
CODE:
```
class Foo extends Component
{
    public function mount(string $bar): void
    {
        // ...
    }
}
```

----------------------------------------

TITLE: Installing Filament with Scaffold and Notifications for New Laravel Projects
DESCRIPTION: These commands quickly set up a new Laravel project with Livewire, Alpine.js, and Tailwind CSS, including Filament's scaffolding and notifications. It's crucial to run this only on new projects as it overwrites existing files.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install --scaffold --notifications

npm install

npm run dev
```

----------------------------------------

TITLE: Persisting Current Tab in URL Query String - Filament PHP
DESCRIPTION: This snippet demonstrates how to enable persistence of the active tab in the URL's query string using the `persistTabInQueryString()` method on a Filament Tabs component. This ensures the tab state is maintained when the page is reloaded or shared, using the default 'tab' query key.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/04-tabs.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Tabs;

Tabs::make('Tabs')
    ->tabs([
        Tabs\Tab::make('Tab 1')
            ->schema([
                // ...
            ]),
        Tabs\Tab::make('Tab 2')
            ->schema([
                // ...
            ]),
        Tabs\Tab::make('Tab 3')
            ->schema([
                // ...
            ]),
    ])
    ->persistTabInQueryString()
```

----------------------------------------

TITLE: Manipulating Raw Builder Component State in Filament PHP
DESCRIPTION: This snippet illustrates how to access and modify the entire raw data state of a Filament Builder component. It demonstrates retrieving the current state with `getState()`, adding a new item with a UUID, and then setting the modified state back to the component using `state()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/13-builder.md#_snippet_24

LANGUAGE: PHP
CODE:
```
use Illuminate
\Support\Str;

// Get the raw data for the entire builder
$state = $component->getState();

// Add an item, with a random UUID as the key
$state[Str::uuid()] = [
    'type' => 'contactDetails',
    'data' => [
        'email' => auth()->user()->email,
    ],
];

// Set the new data for the builder
$component->state($state);
```

----------------------------------------

TITLE: Confirmed Field Validation in Filament PHP
DESCRIPTION: Requires a matching confirmation field (e.g., `password_confirmation` for a `password` field). This is commonly used for password or email confirmation inputs to ensure consistency. The rule automatically looks for a field named by appending `_confirmation` to the current field's name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_9

LANGUAGE: php
CODE:
```
Field::make('password')->confirmed()
Field::make('password_confirmation')
```

----------------------------------------

TITLE: Ensuring Field is Filled in FilamentPHP (PHP)
DESCRIPTION: The `filled` rule validates that a field is not empty when it is present in the form submission. This is useful for ensuring that optional fields, if provided, contain a value.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_16

LANGUAGE: PHP
CODE:
```
Field::make('name')->filled()
```

----------------------------------------

TITLE: Adding Unique Keys to Multiple Livewire Components in Filament Infolist - PHP
DESCRIPTION: When rendering multiple instances of the same Livewire component within a Filament infolist, it's crucial to assign a unique key to each using the key() method. This ensures proper Livewire tracking and avoids conflicts.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/06-advanced.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\Livewire;
use App\Livewire\Foo;

Livewire::make(Foo::class)
    ->key('foo-first')

Livewire::make(Foo::class)
    ->key('foo-second')

Livewire::make(Foo::class)
    ->key('foo-third')
```

----------------------------------------

TITLE: Adding Select Filters to Filament Tables (PHP)
DESCRIPTION: This snippet demonstrates how to add a `SelectFilter` to a Filament table, enabling users to filter records based on predefined options. The `SelectFilter::make('type')` is configured with 'cat', 'dog', and 'rabbit' options, providing a dropdown menu for filtering patient types. This enhances user experience by offering a structured way to narrow down table results and requires `Filament\Tables` and `Filament\Tables\Table`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_16

LANGUAGE: PHP
CODE:
```
use Filament\Tables;
use Filament\Tables\Table;

public static function table(Table $table): Table
{
    return $table
        ->columns([
            // ...
        ])
        ->filters([
            Tables\Filters\SelectFilter::make('type')
                ->options([
                    'cat' => 'Cat',
                    'dog' => 'Dog',
                    'rabbit' => 'Rabbit',
                ]),
        ]);
}
```

----------------------------------------

TITLE: Creating a Livewire Application Layout Blade File
DESCRIPTION: This Blade template defines the basic HTML structure for Livewire components, including meta tags, title, and links to application styles and scripts. It integrates Filament's styles and scripts, and includes the Livewire notifications component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/01-installation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">

        <meta name="application-name" content="{{ config('app.name') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        @vite('resources/css/app.css')
    </head>

    <body class="antialiased">
        {{ $slot }}

        @livewire('notifications')

        @filamentScripts
        @vite('resources/js/app.js')
    </body>
</html>
```

----------------------------------------

TITLE: Implementing Custom Search and Labeling for Select Options (PHP)
DESCRIPTION: This example shows how to handle custom search results for a Filament select field, especially useful for large datasets. It uses `getSearchResultsUsing()` to dynamically fetch and filter options from the `User` model based on user input, and `getOptionLabelUsing()` to display the correct label for a selected option when the form initially loads.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_3

LANGUAGE: php
CODE:
```
Select::make('author_id')
    ->searchable()
    ->getSearchResultsUsing(fn (string $search): array => User::where('name', 'like', "%{$search}%")->limit(50)->pluck('name', 'id')->toArray())
    ->getOptionLabelUsing(fn ($value): ?string => User::find($value)?->name),
```

----------------------------------------

TITLE: Registering Clock Widget and Alpine Component in Filament PHP
DESCRIPTION: This PHP snippet defines the `ClockWidgetServiceProvider` for a Filament plugin. It configures the package with views and translations, and in the `packageBooted` method, it registers the `ClockWidget` Livewire component and the `clock-widget` Alpine component using Filament's asset manager. This ensures the widget and its associated JavaScript are available within Filament panels.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/08-plugins/02-build-a-panel-plugin.md#_snippet_3

LANGUAGE: php
CODE:
```
use Filament\Support\Assets\AlpineComponent;
use Filament\Support\Facades\FilamentAsset;
use Livewire\Livewire;
use Spatie\LaravelPackageTools\Package;
use Spatie\LaravelPackageTools\PackageServiceProvider;

class ClockWidgetServiceProvider extends PackageServiceProvider
{
    public static string $name = 'clock-widget';

    public function configurePackage(Package $package): void
    {
        $package->name(static::$name)
            ->hasViews()
            ->hasTranslations();
    }

    public function packageBooted(): void
    {
        Livewire::component('clock-widget', ClockWidget::class);

        // Asset Registration
        FilamentAsset::register(
            assets:[
                 AlpineComponent::make('clock-widget', __DIR__ . '/../resources/dist/clock-widget.js'),
            ],
            package: 'awcodes/clock-widget'
        );
    }
}
```

----------------------------------------

TITLE: Calculating Sum for Filament Table Column (PHP)
DESCRIPTION: This snippet shows how to use the `Sum` summarizer to calculate the total of all values in a specified column within a Filament table. It applies the sum operation to the 'price' column, aggregating all prices in the dataset.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/07-summaries.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextColumn;

TextColumn::make('price')
    ->summarize(Sum::make())
```

----------------------------------------

TITLE: Calculating Value Range for a TextColumn (PHP)
DESCRIPTION: This snippet illustrates how to apply a Range summarizer to a TextColumn in Filament. It calculates and displays the minimum and maximum values for the 'price' column in the dataset, providing insight into the data spread.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/07-summaries.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\Summarizers\Range;
use Filament\Tables\Columns\TextColumn;

TextColumn::make('price')
    ->summarize(Range::make())
```

----------------------------------------

TITLE: Binding Field State Path in Filament Views (Blade)
DESCRIPTION: This Blade snippet illustrates how to bind an input field to a Filament field's 'state path' within its view. By using the `$getStatePath()` function, the input's value is correctly linked to the corresponding public property in the Livewire component, ensuring proper state management for the field.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/20-custom.md#_snippet_5

LANGUAGE: blade
CODE:
```
<input wire:model="{{ $getStatePath() }}" />

<!-- Or -->

<div x-data="{ state: $wire.$entangle('{{ $getStatePath() }}') }">
    <input x-model="state" />
</div>
```

----------------------------------------

TITLE: Installing Tailwind CSS v3 and Plugins
DESCRIPTION: This command installs Tailwind CSS version 3 along with essential plugins (`@tailwindcss/forms`, `@tailwindcss/typography`), PostCSS, PostCSS Nesting, and Autoprefixer as development dependencies, required for Filament's styling.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/01-installation.md#_snippet_3

LANGUAGE: bash
CODE:
```
npm install tailwindcss@3 @tailwindcss/forms @tailwindcss/typography postcss postcss-nesting autoprefixer --save-dev
```

----------------------------------------

TITLE: Adding Validation Rules to Name Text Input
DESCRIPTION: This PHP snippet enhances the 'name' text input field by adding `required()` and `maxLength(255)` validation rules. These rules ensure the field is not empty and its length does not exceed 255 characters, aligning with database constraints and improving data integrity.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_6

LANGUAGE: php
CODE:
```
use Filament\Forms;

Forms\Components\TextInput::make('name')
    ->required()
    ->maxLength(255)
```

----------------------------------------

TITLE: Creating Basic Tabs in Filament Infolists
DESCRIPTION: This snippet demonstrates how to create a fundamental `Tabs` component in Filament Infolists. It defines a set of individual `Tab` instances, each capable of containing its own schema of components, effectively organizing complex infolists into manageable sections.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/04-tabs.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Tabs;

Tabs::make('Tabs')
    ->tabs([
        Tabs\Tab::make('Tab 1')
            ->schema([
                // ...
            ]),
        Tabs\Tab::make('Tab 2')
            ->schema([
                // ...
            ]),
        Tabs\Tab::make('Tab 3')
            ->schema([
                // ...
            ]),
    ])
```

----------------------------------------

TITLE: Applying Global Configuration to Checkbox Components in Filament (PHP)
DESCRIPTION: This code demonstrates how to set global default behavior for a Filament component, specifically `Checkbox`, using the static `configureUsing()` method. This method accepts a closure that modifies the component instance, allowing for consistent styling or behavior across all instances unless explicitly overridden.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_26

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Checkbox;

Checkbox::configureUsing(function (Checkbox $checkbox): void {
    $checkbox->inline(false);
});
```

----------------------------------------

TITLE: Configuring File Upload Storage and Visibility (PHP)
DESCRIPTION: This code shows how to customize the storage location and access visibility for a Filament file upload field. It sets the storage disk to 's3', specifies 'form-attachments' as the directory, and makes the uploaded files 'private', ensuring they are not publicly accessible.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\FileUpload;

FileUpload::make('attachment')
    ->disk('s3')
    ->directory('form-attachments')
    ->visibility('private')
```

----------------------------------------

TITLE: Creating a Basic Filter with Query Scope (PHP)
DESCRIPTION: This snippet shows how to create a simple checkbox filter named 'is_featured'. The `make()` method defines the filter, and the `query()` method accepts a callback that applies the filtering logic to the Eloquent query builder, in this case, filtering records where `is_featured` is true.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/01-getting-started.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

Filter::make('is_featured')
    ->query(fn (Builder $query): Builder => $query->where('is_featured', true))
```

----------------------------------------

TITLE: Enabling Per-Resource Tenancy in Filament (PHP)
DESCRIPTION: This snippet shows how to enable multi-tenancy for an individual Filament resource. By setting the static property `$isScopedToTenant` to `true` on a resource class, that specific resource will be automatically scoped to the current tenant, assuming global tenancy has been disabled.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_37

LANGUAGE: PHP
CODE:
```
protected static bool $isScopedToTenant = true;
```

----------------------------------------

TITLE: Creating Pivot Model for BelongsToMany Relationship in PHP
DESCRIPTION: This PHP snippet defines an `OrderProduct` model that extends `Illuminate\Database\Eloquent\Relations\Pivot`. It sets `$incrementing` to `true` to ensure Filament tracks primary keys and defines inverse `BelongsTo` relationships to `Order` and `Product`, enabling proper data management for the `order_product` pivot table.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class OrderProduct extends Pivot
{
    public $incrementing = true;

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
```

----------------------------------------

TITLE: Create Livewire Application Layout Blade
DESCRIPTION: Creates a basic Blade layout file (`resources/views/components/layouts/app.blade.php`) that includes necessary meta tags, title, styles, and scripts for a Livewire application using Filament.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/01-installation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">

        <meta name="application-name" content="{{ config('app.name') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        @vite('resources/css/app.css')
    </head>

    <body class="antialiased">
        {{ $slot }}

        @filamentScripts
        @vite('resources/js/app.js')
    </body>
</html>
```

----------------------------------------

TITLE: Creating a Basic View Field in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to define a simple 'view' field in Filament using `ViewField::make()`. It links the field to a specified Blade view file, allowing for custom rendering without requiring a dedicated PHP class. The example assumes the existence of `resources/views/filament/forms/components/range-slider.blade.php`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/20-custom.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\ViewField;

ViewField::make('rating')
    ->view('filament.forms.components.range-slider')
```

----------------------------------------

TITLE: Adding Standard Laravel Validation Rules to a Filament TextInput (PHP)
DESCRIPTION: This example shows how to apply a standard Laravel validation rule, such as 'alpha_dash', to a Filament `TextInput` component using the `rules()` method. This ensures the 'slug' field contains only alphanumeric characters, as well as dashes and underscores.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_54

LANGUAGE: PHP
CODE:
```
TextInput::make('slug')->rules(['alpha_dash'])
```

----------------------------------------

TITLE: Basic Eloquent Record Creation with Filament CreateAction (PHP)
DESCRIPTION: This snippet demonstrates the fundamental usage of Filament's `CreateAction` to generate a modal form for creating Eloquent records. It specifies the model to be created and defines the form fields, such as a required text input for the 'title'.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/01-create.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Actions\CreateAction;
use Filament\Forms\Components\TextInput;

CreateAction::make()
    ->model(Post::class)
    ->form([
        TextInput::make('title')
            ->required()
            ->maxLength(255),
        // ...
    ])
```

----------------------------------------

TITLE: Integrating Filament Repeater with Eloquent HasMany Relationship (PHP)
DESCRIPTION: This snippet demonstrates how to link a Filament Repeater component to an Eloquent `HasMany` relationship. The `relationship()` method automatically loads and saves data from the specified relationship (inferred from the field name), simplifying the management of related records within the form. Ensure the form's model is set up for proper relationship retrieval.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Repeater;

Repeater::make('qualifications')
    ->relationship()
    ->schema([
        // ...
    ])
```

----------------------------------------

TITLE: Customizing Data Before Creating Records (PHP)
DESCRIPTION: Explains how to modify form data before it is finally saved to the database by overriding the `mutateFormDataBeforeCreate()` method on the Create page class. This method accepts the `$data` array and returns the modified version, allowing for data manipulation like adding a `user_id`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_0

LANGUAGE: PHP
CODE:
```
protected function mutateFormDataBeforeCreate(array $data): array
{
    $data['user_id'] = auth()->id();

    return $data;
}
```

----------------------------------------

TITLE: Halting Filament PHP Form Saving Based on Condition
DESCRIPTION: This example illustrates how to halt the form saving process in Filament PHP using `$this->halt()` within a `beforeSave()` lifecycle hook. If a condition (e.g., user not subscribed) is met, a warning notification is displayed, and the saving operation is aborted, preventing data persistence.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_12

LANGUAGE: php
CODE:
```
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;

protected function beforeSave(): void
{
    if (! $this->getRecord()->team->subscribed()) {
        Notification::make()
            ->warning()
            ->title('You don\'t have an active subscription!')
            ->body('Choose a plan to continue.')
            ->persistent()
            ->actions([
                Action::make('subscribe')
                    ->button()
                    ->url(route('subscribe'), shouldOpenInNewTab: true),
            ])
            ->send();

        $this->halt();
    }
}
```

----------------------------------------

TITLE: Defining Virtual Column for Full Name in Database Migration (PHP)
DESCRIPTION: This PHP snippet defines a virtual column `full_name` in a database migration. It concatenates the `first_name` and `last_name` fields, separated by a space, to create a single, descriptive label for related records. This is useful for displaying combined names in UI components like select fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_16

LANGUAGE: php
CODE:
```
$table->string('full_name')->virtualAs('concat(first_name, \' \', last_name)');
```

----------------------------------------

TITLE: Installing Tailwind CSS v3 and Plugins
DESCRIPTION: This command installs Tailwind CSS version 3 along with essential plugins (`@tailwindcss/forms`, `@tailwindcss/typography`), PostCSS, PostCSS Nesting, and Autoprefixer as development dependencies. These are required for Filament's styling.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/01-installation.md#_snippet_3

LANGUAGE: bash
CODE:
```
npm install tailwindcss@3 @tailwindcss/forms @tailwindcss/typography postcss postcss-nesting autoprefixer --save-dev
```

----------------------------------------

TITLE: Enabling Global Table Search with Scout (PHP)
DESCRIPTION: This snippet demonstrates how to make an entire Filament table globally searchable by calling the searchable() method on the Table instance. This is an alternative to marking individual columns as searchable, especially useful when Laravel Scout is already controlling which columns are searchable.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_20

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->searchable();
}
```

----------------------------------------

TITLE: Defining SelectColumn Options in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to define a `SelectColumn` in Filament tables, providing a set of predefined options for a database field. It allows users to update the 'status' field directly within the table view without navigating to a new page.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/06-select.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\SelectColumn;

SelectColumn::make('status')
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
```

----------------------------------------

TITLE: Mutating Form Data Before Fill in FilamentPHP Edit Page (PHP)
DESCRIPTION: This method allows you to modify the data array ($data) from a record before it is filled into the form. It's useful for setting default values or manipulating existing data based on specific logic, such as assigning the current authenticated user's ID to a 'user_id' field. The modified array is then returned to be used for form population.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_0

LANGUAGE: PHP
CODE:
```
protected function mutateFormDataBeforeFill(array $data): array
{
    $data['user_id'] = auth()->id();

    return $data;
}
```

----------------------------------------

TITLE: Applying Frontend Validation Rules to Filament Form Fields (PHP)
DESCRIPTION: This snippet demonstrates how to apply frontend validation rules to various Filament form fields using built-in methods like required() and maxLength(). It shows how these rules are chained directly to field definitions, providing immediate user feedback before backend requests are made. This approach leverages Filament's validation capabilities for improved user experience.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/02-getting-started.md#_snippet_5

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

[
    TextInput::make('title')
        ->required()
        ->maxLength(255),
    TextInput::make('slug')
        ->required()
        ->maxLength(255),
    RichEditor::make('content')
        ->columnSpan(2)
        ->maxLength(65535),
    Section::make('Publishing')
        ->description('Settings for publishing this post.')
        ->schema([
            Select::make('status')
                ->options([
                    'draft' => 'Draft',
                    'reviewing' => 'Reviewing',
                    'published' => 'Published',
                ])
                ->required(),
            DateTimePicker::make('published_at'),
        ]),
]
```

----------------------------------------

TITLE: Adding Name Text Input to Filament Resource Form (Initial)
DESCRIPTION: This PHP snippet defines the `form()` method within a Filament resource, adding a basic text input field for the 'name' attribute. It uses `Filament\Forms\Components\TextInput::make('name')` to create the field, allowing users to input the patient's name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_5

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;

public static function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('name'),
        ]);
}
```

----------------------------------------

TITLE: Testing Data Creation via Filament Form (PHP)
DESCRIPTION: This snippet illustrates how to test the creation of new data through a Filament form. It fills the form with mock data, calls the 'create' action, asserts no form errors, and finally verifies that the new record is correctly persisted in the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can create', function () {
    $newData = Post::factory()->make();

    livewire(PostResource\Pages\CreatePost::class)
        ->fillForm([
            'author_id' => $newData->author->getKey(),
            'content' => $newData->content,
            'tags' => $newData->tags,
            'title' => $newData->title,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    $this->assertDatabaseHas(Post::class, [
        'author_id' => $newData->author->getKey(),
        'content' => $newData->content,
        'tags' => json_encode($newData->tags),
        'title' => $newData->title,
    ]);
});
```

----------------------------------------

TITLE: Debouncing Reactivity on TextInput - Filament PHP
DESCRIPTION: Illustrates how to add a debounce delay to a reactive Filament `TextInput` using `live(debounce: 500)`. The form will only re-render if the user stops typing for the specified duration (500ms in this case), preventing excessive network requests while the user is actively typing. Requires the field to be defined within a Filament form schema.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('username')
    ->live(debounce: 500); // Wait 500ms before re-rendering the form.
```

----------------------------------------

TITLE: Installing Filament Form Builder
DESCRIPTION: This command installs the Filament Form Builder, enabling the creation of interactive forms within Livewire components. It includes over 25 out-of-the-box components and is extensible for custom fields and actions, integrating with action modals and table filters.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
composer require filament/forms
```

----------------------------------------

TITLE: Deferring Table Data Loading in Filament Tables (PHP)
DESCRIPTION: This code shows how to defer the loading of table data, making the table load asynchronously. This is particularly useful for tables with large datasets, as it improves initial page load performance by loading the table content in the background.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->deferLoading();
}
```

----------------------------------------

TITLE: Using Filament Input Component with Wrapper - Blade
DESCRIPTION: This snippet demonstrates how to use the Filament input component, which is a wrapper around the native HTML <input> element. It must be enclosed within a `x-filament::input.wrapper` component to provide styling, borders, and support for additional elements like prefixes or suffixes. The `wire:model` attribute is used for Livewire data binding.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-input.md#_snippet_0

LANGUAGE: Blade
CODE:
```
<x-filament::input.wrapper>
    <x-filament::input
        type="text"
        wire:model="name"
    />
</x-filament::input.wrapper>
```

----------------------------------------

TITLE: Defining Resource Record Title Attribute (PHP)
DESCRIPTION: This PHP code snippet shows how to set the static property `$recordTitleAttribute` in a Filament resource class. This property specifies which column on the model should be used to identify the record in the UI, such as for global search.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_7

LANGUAGE: php
CODE:
```
protected static ?string $recordTitleAttribute = 'name';
```

----------------------------------------

TITLE: Opening Filament Modal from Livewire (PHP)
DESCRIPTION: This PHP snippet illustrates how to open a Filament modal from a Livewire component. It dispatches an `open-modal` browser event, passing the `id` of the target modal as a parameter, which triggers the modal to become visible.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-modal.md#_snippet_2

LANGUAGE: php
CODE:
```
$this->dispatch('open-modal', id: 'edit-user');
```

----------------------------------------

TITLE: Making a Column Sortable in Filament PHP
DESCRIPTION: Demonstrates how to make a `TextColumn` sortable by chaining the `sortable()` method. This adds a sort icon to the column header, allowing users to order the table data by this column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/02-getting-started.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title')
    ->sortable()
```

----------------------------------------

TITLE: Conditionally Making FilamentPHP Field Required
DESCRIPTION: Makes a field conditionally required based on another field's value. Uses the `required()` method with a function that injects the `Get` utility to check if the `company_name` field is filled. The `company_name` field uses `live(onBlur: true)` for reactivity.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_20

LANGUAGE: php
CODE:
```
use Filament\Forms\Get;
use Filament\Forms\Components\TextInput;

TextInput::make('company_name')
    ->live(onBlur: true)
    
TextInput::make('vat_number')
    ->required(fn (Get $get): bool => filled($get('company_name')))
```

----------------------------------------

TITLE: Applying Persistent Middleware to Authenticated Filament Panel Routes (PHP)
DESCRIPTION: This code demonstrates how to apply middleware that runs on every request for authenticated routes, including Livewire AJAX requests, by setting the `isPersistent` argument to `true` in the `authMiddleware()` method for a Filament panel.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_21

LANGUAGE: php
CODE:
```
use FilamentPanel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->authMiddleware([
            // ...
        ], isPersistent: true);
}
```

----------------------------------------

TITLE: Using lifecycle hooks (before/after) with Filament DeleteAction (PHP)
DESCRIPTION: Defines closures to execute custom code immediately before and after a record is deleted by the DeleteAction using the `before()` and `after()` methods.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/04-delete.md#_snippet_6

LANGUAGE: php
CODE:
```
DeleteAction::make()
    ->before(function () {
        // ...
    })
    ->after(function () {
        // ...
    });
```

----------------------------------------

TITLE: Sanitizing Rich Editor HTML Output in Blade
DESCRIPTION: This Blade snippet shows how to safely output HTML content from a Rich Editor component, specifically for a record's 'content' attribute. It utilizes Filament's `sanitizeHtml()` helper to prevent Cross-Site Scripting (XSS) vulnerabilities by removing dangerous JavaScript from the raw HTML.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/10-rich-editor.md#_snippet_1

LANGUAGE: blade
CODE:
```
{!! str($record->content)->sanitizeHtml() !!}
```

----------------------------------------

TITLE: Integrating Filament CheckboxList with Eloquent Relationship (PHP)
DESCRIPTION: This snippet shows how to integrate a Filament CheckboxList with an Eloquent `BelongsToMany` relationship. The `relationship()` method automatically loads options from the related model and saves selections to the pivot table upon form submission, using `titleAttribute` for labels.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/06-checkbox-list.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\CheckboxList;

CheckboxList::make('technologies')
    ->relationship(titleAttribute: 'name')
```

----------------------------------------

TITLE: Adding Pivot Attributes to Create Form (PHP)
DESCRIPTION: Shows how to include input fields for pivot table attributes (for BelongsToMany or MorphToMany relationships) within the relation manager's create/edit form schema. Define form components like `TextInput::make()` using the name of the pivot attribute.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('name')->required(),
            Forms\Components\TextInput::make('role')->required(),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Scope Available Options for AssociateAction in Filament
DESCRIPTION: Applies a custom Eloquent query scope to filter the list of records available for selection in the `AssociateAction` modal for HasMany/MorphMany relationships, limiting the options based on specific criteria.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_23

LANGUAGE: php
CODE:
```
use Filament\Tables\Actions\AssociateAction;
use Illuminate\Database\Eloquent\Builder;

AssociateAction::make()
    ->recordSelectOptionsQuery(fn (Builder $query) => $query->whereBelongsTo(auth()->user()))
```

----------------------------------------

TITLE: Defining Basic Infolist Entries in PHP
DESCRIPTION: This snippet demonstrates how to define the basic entries for an infolist using the `schema()` method. It utilizes `TextEntry` components to display 'title', 'slug', and 'content' fields, which are fundamental for rendering read-only data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/02-getting-started.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\TextEntry;

$infolist
    ->schema([
        TextEntry::make('title'),
        TextEntry::make('slug'),
        TextEntry::make('content'),
    ]);
```

----------------------------------------

TITLE: Configuring Tailwind CSS for Filament
DESCRIPTION: This JavaScript configuration file sets up Tailwind CSS for Filament. It imports Filament's preset, which includes its color scheme and required plugins, and defines content paths for scanning Blade and PHP files to generate necessary CSS classes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_4

LANGUAGE: js
CODE:
```
import preset from './vendor/filament/support/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        './app/Filament/**/*.php',
        './resources/views/filament/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ]
}
```

----------------------------------------

TITLE: Generating Form Schemas Automatically from Model Columns with Filament CLI
DESCRIPTION: This Bash command generates a Livewire form component (`Products/CreateProduct`) and automatically infers the form's schema based on the database columns of the associated Eloquent model. The `--generate` flag streamlines development by populating the form with appropriate fields derived directly from the model's structure.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_18

LANGUAGE: bash
CODE:
```
php artisan make:livewire-form Products/CreateProduct --generate
```

----------------------------------------

TITLE: Adding a Suffix Action to a Text Input Field in PHP
DESCRIPTION: This example illustrates how to attach a suffix action to a `TextInput` field in Filament. The action, named 'copyCostToPrice', displays a clipboard icon, requires confirmation, and uses utility injection (`$set`, `$state`) to copy the current field's state ('cost') to another field ('price').
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/06-actions.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;

TextInput::make('cost')
    ->prefix('€')
    ->suffixAction(
        Action::make('copyCostToPrice')
            ->icon('heroicon-m-clipboard')
            ->requiresConfirmation()
            ->action(function (Set $set, $state) {
                $set('price', $state);
            })
    )
```

----------------------------------------

TITLE: Making Password Field Required Conditionally by Operation in Filament PHP
DESCRIPTION: Building upon conditional dehydration, this snippet makes the password field required only when the form operation is 'create'. It injects the `$operation` utility into the `required` method's closure to check the current form mode.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_29

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Hash;

TextInput::make('password')
    ->password()
    ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
    ->dehydrated(fn (?string $state): bool => filled($state))
    ->required(fn (string $operation): bool => $operation === 'create')
```

----------------------------------------

TITLE: Injecting Set Function for Modifying Other Field States - Filament PHP
DESCRIPTION: Shows how to inject the `$set` callable into a form configuration closure by type-hinting the `$set` parameter with `Filament\Forms\Set`. This function allows programmatically setting the state (value) of *other* fields within the same form, triggering reactivity and re-rendering. It takes the field name and the new value as arguments.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Forms\Set;

function (Set $set) {
    $set('title', 'Blog Post'); // Set the `title` field to `Blog Post`.
    //...
}
```

----------------------------------------

TITLE: Injecting Field State into Configuration Function - Filament PHP
DESCRIPTION: Explains how to inject the current value (state) of the form field into a configuration closure by defining a `$state` parameter. This allows the logic within the function to react directly to the field's current value.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_4

LANGUAGE: php
CODE:
```
function ($state) {
    // ...
}
```

----------------------------------------

TITLE: Disabling a Filament Form Field (PHP)
DESCRIPTION: Illustrates how to disable a Filament form field using the `disabled()` method, preventing user interaction and ensuring its value is not submitted with the form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_17

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->disabled()
```

----------------------------------------

TITLE: Rendering a Basic Checkbox Input (Blade)
DESCRIPTION: This snippet demonstrates the basic usage of the Filament checkbox component to render a simple checkbox input. It uses `wire:model` to bind the checkbox's state to an `isAdmin` property, allowing it to toggle a boolean value.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-checkbox.md#_snippet_0

LANGUAGE: Blade
CODE:
```
<label>
    <x-filament::input.checkbox wire:model="isAdmin" />

    <span>
        Is Admin
    </span>
</label>
```

----------------------------------------

TITLE: Sanitizing Markdown Editor Output in Blade
DESCRIPTION: This Blade snippet shows how to safely output content from a Filament Markdown editor in a custom Blade view. It uses Laravel's `str` helper, `markdown()` to convert Markdown to HTML, and Filament's `sanitizeHtml()` helper to prevent Cross-Site Scripting (XSS) vulnerabilities.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/11-markdown-editor.md#_snippet_1

LANGUAGE: Blade
CODE:
```
{!! str($record->content)->markdown()->sanitizeHtml() !!}
```

----------------------------------------

TITLE: Optimizing Laravel Application for Production
DESCRIPTION: This command optimizes the core Laravel application for production by caching configuration files and routes, leading to improved performance. It should be part of the deployment script.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/01-installation.md#_snippet_7

LANGUAGE: bash
CODE:
```
php artisan optimize
```

----------------------------------------

TITLE: Applying Global Scope for Team ID in Eloquent (PHP)
DESCRIPTION: This snippet demonstrates how to apply a global scope to an Eloquent model (`Post`) to automatically filter records by the authenticated user's `team_id`. It ensures that only records associated with the current user's team are retrieved, either directly by `team_id` or through a `team` relationship. This is suitable for simple one-to-many tenancy scenarios.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_0

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Builder;

class Post extends Model
{
    protected static function booted(): void
    {
        static::addGlobalScope('team', function (Builder $query) {
            if (auth()->hasUser()) {
                $query->where('team_id', auth()->user()->team_id);
                // or with a `team` relationship defined:
                $query->whereBelongsTo(auth()->user()->team);
            }
        });
    }
}
```

----------------------------------------

TITLE: Enabling JavaScript Select for Customization (PHP)
DESCRIPTION: This code shows how to disable the native HTML5 select and enable a more customizable JavaScript-based select in Filament. By calling `native(false)`, the component will render a JavaScript-powered select field, offering enhanced UI/UX features beyond the default browser select.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('status')
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
    ->native(false)
```

----------------------------------------

TITLE: Overview of Create Page Lifecycle Hooks (PHP)
DESCRIPTION: Lists and describes various lifecycle hooks available for the Filament Create page, such as `beforeFill`, `afterFill`, `beforeValidate`, `afterValidate`, `beforeCreate`, and `afterCreate`. These hooks allow developers to execute code at specific points within the page's lifecycle.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    // ...

    protected function beforeFill(): void
    {
        // Runs before the form fields are populated with their default values.
    }

    protected function afterFill(): void
    n{
        // Runs after the form fields are populated with their default values.
    }

    protected function beforeValidate(): void
    {
        // Runs before the form fields are validated when the form is submitted.
    }

    protected function afterValidate(): void
    {
        // Runs after the form fields are validated when the form is submitted.
    }

    protected function beforeCreate(): void
    {
        // Runs before the form fields are saved to the database.
    }

    protected function afterCreate(): void
    {
        // Runs after the form fields are saved to the database.
    }
}
```

----------------------------------------

TITLE: Configuring Price Input Field for Treatment Form - PHP
DESCRIPTION: This PHP snippet further enhances the treatment form by adding a `price` text input field. It's configured as `numeric()` for validation and mobile keyboard optimization, and includes a `prefix('€')` for currency display without affecting the stored value. A `maxValue` is also set for data integrity.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_22

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('description')
                ->required()
                ->maxLength(255)
                ->columnSpan('full'),
            Forms\Components\Textarea::make('notes')
                ->maxLength(65535)
                ->columnSpan('full'),
            Forms\Components\TextInput::make('price')
                ->numeric()
                ->prefix('€')
                ->maxValue(42949672.95),
        ]);
}
```

----------------------------------------

TITLE: Mutating Form Data Before Save in FilamentPHP Edit Page (PHP)
DESCRIPTION: This method enables modification of form data ($data) just before it is persisted to the database. It's ideal for tasks like auditing, setting timestamps, or adjusting values based on other form inputs before the final save operation. For instance, it can be used to record the ID of the user who last edited the record.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_1

LANGUAGE: PHP
CODE:
```
protected function mutateFormDataBeforeSave(array $data): array
{
    $data['last_edited_by_id'] = auth()->id();

    return $data;
}
```

----------------------------------------

TITLE: Casting Tags to Array in Eloquent Model (PHP)
DESCRIPTION: Defines an Eloquent model cast for the 'tags' attribute, ensuring that the JSON string stored in the database is automatically converted to a PHP array when accessed and vice-versa. This is crucial for proper interaction with Filament's TagsInput component when storing JSON.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/14-tags-input.md#_snippet_1

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
    protected $casts = [
        'tags' => 'array',
    ];

    // ...
}
```

----------------------------------------

TITLE: Saving Form Data to Eloquent Model (PHP)
DESCRIPTION: This PHP snippet demonstrates how to save the validated and transformed form data to an Eloquent model. It replaces the `dd()` call in the `create()` method, persisting the data instead of just dumping it.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_5

LANGUAGE: php
CODE:
```
use App\Models\Post;

public function create(): void
{
    Post::create($this->form->getState());
}
```

----------------------------------------

TITLE: Using Simple and Cursor Pagination in Laravel PHP
DESCRIPTION: This PHP snippet illustrates the use of `simplePaginate()` and `cursorPaginate()` methods for basic 'previous' and 'next' button pagination. `simplePaginate` is suitable for large datasets where total count is not needed, while `cursorPaginate` offers efficient, offset-free pagination.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-pagination.md#_snippet_2

LANGUAGE: php
CODE:
```
use AppModelsUser;

User::query()->simplePaginate(10);
User::query()->cursorPaginate(10);
```

----------------------------------------

TITLE: Formatting Text Entry as Currency with Custom Locale in FilamentPHP
DESCRIPTION: This example demonstrates how to format a `TextEntry` as currency using a specific locale, such as 'nl' for Dutch, by passing the `locale` argument to the `money()` method. This overrides the application's default locale for currency formatting.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/02-text.md#_snippet_11

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('price')
    ->money('EUR', locale: 'nl')
```

----------------------------------------

TITLE: Finalizing Filament v3 Installation (PHP Artisan)
DESCRIPTION: This command is crucial for completing the Filament v3 installation process after an upgrade or fresh install. It performs necessary setup tasks, such as publishing assets and clearing caches, ensuring the new version functions correctly.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/15-upgrade-guide.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install
```

----------------------------------------

TITLE: Injecting Current Form Operation - Filament PHP
DESCRIPTION: Explains how to inject the current form operation ('create', 'edit', or 'view') into a configuration closure by defining a `$operation` parameter type-hinted as `string`. This is primarily used within panel resources or relation managers to customize form behavior based on the context (creating, editing, or viewing a record).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_10

LANGUAGE: php
CODE:
```
function (string $operation) {
    // ...
}
```

----------------------------------------

TITLE: Creating a Grid Layout Component in Filament PHP
DESCRIPTION: This snippet demonstrates how to create a `Grid` layout component in Filament PHP. It uses the static `make()` method to instantiate a grid with 2 columns and then defines its child components within the `schema()` method. This organizes form fields into a structured layout.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/01-getting-started.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Grid;

Grid::make(2)
    ->schema([
        // ...
    ])
```

----------------------------------------

TITLE: Using Reusable Form Fields in FilamentPHP Wizard (PHP)
DESCRIPTION: This PHP snippet illustrates how to integrate reusable form fields, defined in a FilamentPHP resource, into a wizard's steps. The `CreateCategory` class extends `CreateRecord` and uses the `HasWizard` concern. It retrieves the 'name' and 'slug' fields from `CategoryResource` using their static methods, embedding them within a wizard step, which ensures consistency and reusability across different form contexts.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_14

LANGUAGE: php
CODE:
```
use App\Filament\Resources\CategoryResource;
use Filament\Resources\Pages\CreateRecord;

class CreateCategory extends CreateRecord
{
    use CreateRecord\Concerns\HasWizard;
    
    protected static string $resource = CategoryResource::class;

    protected function getSteps(): array
    {
        return [
            Step::make('Name')
                ->description('Give the category a clear and unique name')
                ->schema([
                    CategoryResource::getNameFormField(),
                    CategoryResource::getSlugFormField(),
                ]),
            // ...
        ];
    }
}
```

----------------------------------------

TITLE: Applying Middleware to Authenticated Filament Panel Routes (PHP)
DESCRIPTION: This example illustrates how to apply an array of middleware classes specifically to all authenticated routes within a Filament panel using the `authMiddleware()` method. This middleware runs on initial page load by default.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_20

LANGUAGE: php
CODE:
```
use FilamentPanel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->authMiddleware([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Setting a Default Value for a Filament Field
DESCRIPTION: Demonstrates how to assign a default value to a Filament form field using the `default()` method. This value is used when the form is loaded without existing data, typically on 'Create' pages in panel resources.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->default('John')
```

----------------------------------------

TITLE: Creating a Basic Select Field in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to create a basic select input field in Filament using the `Select` component. It defines a 'status' field with predefined options for 'draft', 'reviewing', and 'published', allowing users to choose a single value from the list.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('status')
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
```

----------------------------------------

TITLE: Implementing HasTenants Interface for User Model in Filament (PHP)
DESCRIPTION: This snippet illustrates how to implement the `HasTenants` interface on the `App\Models\User` model for Filament's multi-tenancy. It defines a `teams()` relationship, provides the `getTenants()` method to return the user's associated tenants, and implements `canAccessTenant()` for security to verify if a user can access a given tenant, preventing unauthorized data access.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_3

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasTenants;
use Filament\Panel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Collection;

class User extends Authenticatable implements FilamentUser, HasTenants
{
    // ...

    public function teams(): BelongsToMany
    {
        return $this->belongsToMany(Team::class);
    }

    public function getTenants(Panel $panel): Collection
    {
        return $this->teams;
    }

    public function canAccessTenant(Model $tenant): bool
    {
        return $this->teams()->whereKey($tenant)->exists();
    }
}
```

----------------------------------------

TITLE: Using afterStateUpdated Hook for State Changes - Filament PHP
DESCRIPTION: Explains the `afterStateUpdated()` method, which triggers a closure after a field's state is changed by user interaction on the frontend. It demonstrates injecting both the new state (`$state`) and the old state (`$old`) into the closure, allowing logic to compare values or react to the change.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_15

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->afterStateUpdated(function (?string $state, ?string $old) {
        // ...
    });
```

----------------------------------------

TITLE: Reusing Resource Form/Table in FilamentPHP Relation Manager (PHP)
DESCRIPTION: Demonstrates calling the static `form()` and `table()` methods of a FilamentPHP Resource (`PostResource`) from the corresponding methods in a relation manager. This pattern allows sharing the form schema and table configuration defined in the resource, promoting code reuse for related models. Requires access to the resource class and its static `form`/`table` methods.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_50

LANGUAGE: php
CODE:
```
use App\Filament\Resources\Blog\PostResource;
use Filament\Forms\Form;
use Filament\Tables\Table;

public function form(Form $form): Form
{
    return PostResource::form($form);
}

public function table(Table $table): Table
{
    return PostResource::table($table);
}
```

----------------------------------------

TITLE: Adding Soft Deletes to Existing Filament Resource Table (PHP)
DESCRIPTION: This PHP code snippet demonstrates how to modify an existing FilamentPHP resource's `table` method to include soft delete functionality. It adds `TrashedFilter`, `DeleteAction`, `ForceDeleteAction`, `RestoreAction`, and their bulk action counterparts, along with updating `getEloquentQuery` to include soft-deleted records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/06-deleting-records.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

public static function table(Table $table): Table
{
    return $table
        ->columns([
            // ...
        ])
        ->filters([
            Tables\Filters\TrashedFilter::make(),
            // ...
        ])
        ->actions([
            // You may add these actions to your table if you're using a simple
            // resource, or you just want to be able to delete records without
            // leaving the table.
            Tables\Actions\DeleteAction::make(),
            Tables\Actions\ForceDeleteAction::make(),
            Tables\Actions\RestoreAction::make(),
            // ...
        ])
        ->bulkActions([
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                Tables\Actions\ForceDeleteBulkAction::make(),
                Tables\Actions\RestoreBulkAction::make(),
                // ...
            ]),
        ]);
}

public static function getEloquentQuery(): Builder
{
    return parent::getEloquentQuery()
        ->withoutGlobalScopes([
            SoftDeletingScope::class,
        ]);
}
```

----------------------------------------

TITLE: Implementing Custom Closure-Based Validation Rules in Filament (PHP)
DESCRIPTION: This code demonstrates how to define a custom validation rule using a PHP closure for a Filament `TextInput`. The closure receives the attribute name, value, and a `$fail` callback, allowing for inline validation logic and custom error messages based on the field's value.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_56

LANGUAGE: PHP
CODE:
```
use Closure;

TextInput::make('slug')->rules([
    fn (): Closure => function (string $attribute, $value, Closure $fail) {
        if ($value === 'foo') {
            $fail('The :attribute is invalid.');
        }
    },
])
```

----------------------------------------

TITLE: Defining a Basic Lifecycle Hook in Filament PHP
DESCRIPTION: This snippet demonstrates how to define a basic lifecycle hook, `beforeSave()`, within a Filament Edit page class. The code inside this method will execute just before the form data is saved to the database, allowing for pre-save operations.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_9

LANGUAGE: php
CODE:
```
protected function beforeSave(): void
{
    // ...
}
```

----------------------------------------

TITLE: Applying a Custom Class-Based Validation Rule to a Filament TextInput (PHP)
DESCRIPTION: This snippet illustrates how to integrate a custom class-based validation rule, `Uppercase`, with a Filament `TextInput`. By passing an instance of the custom rule class to the `rules()` method, developers can enforce application-specific validation logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_55

LANGUAGE: PHP
CODE:
```
TextInput::make('slug')->rules([new Uppercase()])
```

----------------------------------------

TITLE: Injecting Request and Table into Filament Function (PHP)
DESCRIPTION: This snippet illustrates how to inject Laravel's `Request` object and Filament's `Table` object into a function. This allows access to the current HTTP request data and the table instance within Filament components, leveraging Laravel's dependency injection container.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/01-getting-started.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;
use Illuminate\Http\Request;

function (Request $request, Table $table) {
    // ...
}
```

----------------------------------------

TITLE: Making Columns Sortable in Filament Tables (PHP)
DESCRIPTION: This code illustrates how to enable sorting for a column in a Filament table. Applying the `sortable()` method to the `date_of_birth` `TextColumn` adds a sort icon to its header, allowing users to sort table entries by date of birth. This functionality depends on the `Filament\Tables` and `Filament\Tables\Table` components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_15

LANGUAGE: PHP
CODE:
```
use Filament\Tables;
use Filament\Tables\Table;

public static function table(Table $table): Table
{
    return $table
        ->columns([
            Tables\Columns\TextColumn::make('name')
                ->searchable(),
            Tables\Columns\TextColumn::make('type'),
            Tables\Columns\TextColumn::make('date_of_birth')
                ->sortable(),
            Tables\Columns\TextColumn::make('owner.name')
                ->searchable(),
        ]);
}
```

----------------------------------------

TITLE: Checking Scoped Relationship Existence in Filament Export Column (PHP)
DESCRIPTION: This snippet shows how to check for the existence of related records after applying a custom scope to the relationship query. By passing an array to `exists()`, you can define a callback function to filter the related records before checking for their presence, enabling more precise existence checks.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_22

LANGUAGE: php
CODE:
```
use Filament\Actions\Exports\ExportColumn;
use Illuminate\Database\Eloquent\Builder;

ExportColumn::make('users_exists')->exists([
    'users' => fn (Builder $query) => $query->where('is_active', true),
])
```

----------------------------------------

TITLE: Defining Edit Option Form for Relationship in Filament Select (PHP)
DESCRIPTION: This Filament PHP code defines a custom form using `editOptionForm()` for a `Select` component. This form, which opens in a modal, allows users to edit the currently selected related record (e.g., an author) by providing required `name` and `email` inputs. Upon submission, the changes are saved back to the existing record.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_22

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->editOptionForm([
        Forms\Components\TextInput::make('name')
            ->required(),
        Forms\Components\TextInput::make('email')
            ->required()
            ->email(),
    ]),
```

----------------------------------------

TITLE: Customizing Grid Column Counts per Breakpoint in Filament PHP
DESCRIPTION: This example extends the `Grid` layout to define different column counts for various breakpoints. It configures 2 columns for large screens (`lg`) and 4 columns for extra-extra-large screens (`2xl`), allowing responsive control over the layout. It also shows nesting a `Stack` component within the grid for grouping 'name' and 'job' columns.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/06-layout.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\Layout\Grid;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;

Grid::make([
    'lg' => 2,
    '2xl' => 4,
])
    ->schema([
        Stack::make([
            TextColumn::make('name'),
            TextColumn::make('job'),
        ]),
        TextColumn::make('phone')
            ->icon('heroicon-m-phone'),
        TextColumn::make('email')
            ->icon('heroicon-m-envelope'),
    ])
```

----------------------------------------

TITLE: Asserting Filament Form Validation Errors (PHP)
DESCRIPTION: This example shows how to test form validation using `assertHasFormErrors()` in a Pest test. It fills the form with invalid data (null title), calls the 'create' action, and then asserts that a 'required' validation error exists for the 'title' field. This helper is crucial for verifying that form validation rules are correctly enforced. For multiple forms, a second parameter can specify the form name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/09-testing.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can validate input', function () {
    livewire(CreatePost::class)
        ->fillForm([
            'title' => null,
        ])
        ->call('create')
        ->assertHasFormErrors(['title' => 'required']);
});
```

----------------------------------------

TITLE: Attaching Filament Action Object to Column PHP
DESCRIPTION: Configures a column cell to trigger a full Filament Action object when clicked. This allows for more complex actions like opening confirmation modals or performing actions defined elsewhere. The Action object must have a unique name. Requires `Filament\Tables\Columns\TextColumn` and `Filament\Tables\Actions\Action`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_21

LANGUAGE: php
CODE:
```
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title')
    ->action(
        Action::make('select')
            ->requiresConfirmation()
            ->action(function (Post $record): void {
                $this->dispatch('select-post', post: $record->getKey());
            }),
    );
```

----------------------------------------

TITLE: Implementing a Custom Filament Billing Provider - PHP
DESCRIPTION: This example demonstrates a custom billing provider implementing the `Provider` interface. `getRouteAction()` defines the action for the billing page, here redirecting to an external billing URL. `getSubscribedMiddleware()` specifies a middleware to check subscription status and redirect unsubscribed users.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use App\Http\Middleware\RedirectIfUserNotSubscribed;
use Filament\Billing\Providers\Contracts\Provider;
use Illuminate\Http\RedirectResponse;

class ExampleBillingProvider implements Provider
{
    public function getRouteAction(): string
    {
        return function (): RedirectResponse {
            return redirect('https://billing.example.com');
        };
    }

    public function getSubscribedMiddleware(): string
    {
        return RedirectIfUserNotSubscribed::class;
    }
}
```

----------------------------------------

TITLE: Registering Custom Filament Icons - PHP
DESCRIPTION: This PHP snippet demonstrates how to replace default Filament UI icons using the `FilamentIcon` facade's `register()` method. It accepts an associative array where keys are Filament's internal icon aliases and values are either Blade icon names (e.g., `fas-magnifying-glass`) or Blade views (e.g., `view('icons.chevron-up')`) that render the desired icon.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/03-icons.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Support\Facades\FilamentIcon;

FilamentIcon::register([
    'panels::topbar.global-search.field' => 'fas-magnifying-glass',
    'panels::sidebar.group.collapse-button' => view('icons.chevron-up'),
]);
```

----------------------------------------

TITLE: Configuring Automatic Upgrade Hook in Composer
DESCRIPTION: This JSON snippet illustrates how the `filament:upgrade` command is typically added to the `post-autoload-dump` hook within your `composer.json` file. This configuration ensures that after any `composer update`, the necessary Filament caches are cleared and frontend assets are republished automatically.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/01-installation.md#_snippet_10

LANGUAGE: JSON
CODE:
```
"post-autoload-dump": [
    // ...
    "@php artisan filament:upgrade"
],
```

----------------------------------------

TITLE: Configuring Automatic Filament Upgrade in Composer
DESCRIPTION: This JSON snippet shows how to configure Composer's `post-autoload-dump` hook to automatically run the `php artisan filament:upgrade` command after Composer's autoloader is dumped. This ensures that Laravel caches are cleared and frontend assets are republished after any updates.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_10

LANGUAGE: JSON
CODE:
```
"post-autoload-dump": [
    // ...
    "@php artisan filament:upgrade"
],
```

----------------------------------------

TITLE: Inserting a Livewire Component into a Filament Form
DESCRIPTION: This snippet demonstrates how to embed a Livewire component directly into a Filament form using the `Livewire::make()` component. You pass the fully qualified class name of the Livewire component to this method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_33

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Livewire;
use App\Livewire\Foo;

Livewire::make(Foo::class)
```

----------------------------------------

TITLE: Defining Infolist Schema in a Class Method (Filament PHP)
DESCRIPTION: This snippet demonstrates how to define the schema for a Filament Infolist within a public class method. It uses the `Infolist` class to return a configured infolist instance, where entry components will be placed inside the `schema()` method. This is typically used in standard Filament pages or custom classes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Infolist;

public function infolist(Infolist $infolist): Infolist
{
    return $infolist
        ->schema([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Nesting Navigation Items using Property in FilamentPHP
DESCRIPTION: Group a resource's navigation item as a child under another specific navigation item using the static `$navigationParentItem` property. The `$navigationGroup` must also be specified if the parent item belongs to a group.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_26

LANGUAGE: php
CODE:
```
protected static ?string $navigationParentItem = 'Products';

protected static ?string $navigationGroup = 'Shop';
```

----------------------------------------

TITLE: Restricting File Types in Filament FileUpload (PHP)
DESCRIPTION: This snippet shows how to restrict the types of files that can be uploaded using the `acceptedFileTypes()` method. It accepts an array of MIME types, here demonstrated for PDF files.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_30

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\FileUpload;

FileUpload::make('document')
    ->acceptedFileTypes(['application/pdf'])
```

----------------------------------------

TITLE: Injecting Action Arguments into Action Function - PHP
DESCRIPTION: This snippet shows how to inject the arguments passed to the action into its configuration function. By defining an `$arguments` parameter of type `array`, the function gains access to any parameters provided when the action was called. This enables actions to behave differently based on specific input arguments.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/08-advanced.md#_snippet_2

LANGUAGE: php
CODE:
```
function (array $arguments) {
    // ...
}
```

----------------------------------------

TITLE: Creating a View Component in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to define a view component in Filament using the `Filament\Forms\Components\View` class. This approach allows for custom layouts directly from a Blade file without requiring a separate PHP class, assuming the Blade file exists at `resources/views/filament/forms/components/wizard.blade.php`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/08-custom.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\View;

View::make('filament.forms.components.wizard')
```

----------------------------------------

TITLE: Customizing MorphToSelect Relationship Queries in PHP
DESCRIPTION: This example illustrates how to modify the database query used to retrieve options for each morphed type in a `MorphToSelect` component. The `modifyOptionsQueryUsing()` method allows applying custom Eloquent query constraints, such as filtering records based on team ownership.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_25

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\MorphToSelect;
use Illuminate\Database\Eloquent\Builder;

MorphToSelect::make('commentable')
    ->types([
        MorphToSelect\Type::make(Product::class)
            ->titleAttribute('name')
            ->modifyOptionsQueryUsing(fn (Builder $query) => $query->whereBelongsTo($this->team)),
        MorphToSelect\Type::make(Post::class)
            ->titleAttribute('title')
            ->modifyOptionsQueryUsing(fn (Builder $query) => $query->whereBelongsTo($this->team)),
    ])
```

----------------------------------------

TITLE: Chaining Filament Actions in PHP
DESCRIPTION: This PHP example demonstrates how to chain two Filament actions together using `replaceMountedAction()`. The `editAction` processes data and then programmatically opens the `publishAction`, passing along the original arguments. This allows for sequential execution of actions, where the second action depends on the completion of the first.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/06-adding-an-action-to-a-livewire-component.md#_snippet_12

LANGUAGE: php
CODE:
```
use App\Models\Post;
use Filament\Actions\Action;

public function editAction(): Action
{
    return Action::make('edit')
        ->form([
            // ...
        ])
        // ...
        ->action(function (array $arguments) {
            $post = Post::find($arguments['post']);

            // ...

            $this->replaceMountedAction('publish', $arguments);
        });
}

public function publishAction(): Action
{
    return Action::make('publish')
        ->requiresConfirmation()
        // ...
        ->action(function (array $arguments) {
            $post = Post::find($arguments['post']);

            $post->publish();
        });
}
```

----------------------------------------

TITLE: Conditionally Requiring a Filament Field (PHP)
DESCRIPTION: This snippet shows how to use the `requiredIf` validation rule. The field value is required only if another specified field has one of the given values. This is useful for conditional form logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_35

LANGUAGE: PHP
CODE:
```
Field::make('name')->requiredIf('field', 'value')
```

----------------------------------------

TITLE: Formatting Dates and Times in Filament Text Columns (PHP)
DESCRIPTION: This code shows how to format a column's state as a date and time using the `dateTime()` method. This method leverages PHP date formatting tokens to present timestamps in a readable format.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('created_at')
    ->dateTime()
```

----------------------------------------

TITLE: Hiding Form Component Conditionally (PHP)
DESCRIPTION: This PHP code snippet shows how to use the `hiddenOn()` method on a form component (e.g., `TextInput`) to hide it dynamically based on the current page or operation context within the resource (e.g., hiding the password field on the 'edit' page).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_9

LANGUAGE: php
CODE:
```
use Livewire\Component;

Forms\Components\TextInput::make('password')
    ->password()
    ->required()
    ->hiddenOn('edit');
```

----------------------------------------

TITLE: Adding Extra HTML Attributes to Filament Component in PHP
DESCRIPTION: This snippet illustrates how to add custom HTML attributes to a Filament component using the `extraAttributes()` method. It accepts an associative array where keys are attribute names and values are their corresponding values. This allows for custom styling or behavior by merging attributes onto the component's outer DOM element.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/01-getting-started.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Section;

Section::make()
    ->extraAttributes(['class' => 'custom-section-style'])
```

----------------------------------------

TITLE: Defining Multiple Blocks with Filament Builder in PHP
DESCRIPTION: This snippet demonstrates how to define a Filament Builder component with multiple distinct blocks: 'heading', 'paragraph', and 'image'. Each block has its own schema of form components, allowing for flexible content creation. It's designed to store a JSON array of these varied content blocks in a database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/13-builder.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Builder;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;

Builder::make('content')
    ->blocks([
        Builder\Block::make('heading')
            ->schema([
                TextInput::make('content')
                    ->label('Heading')
                    ->required(),
                Select::make('level')
                    ->options([
                        'h1' => 'Heading 1',
                        'h2' => 'Heading 2',
                        'h3' => 'Heading 3',
                        'h4' => 'Heading 4',
                        'h5' => 'Heading 5',
                        'h6' => 'Heading 6',
                    ])
                    ->required(),
            ])
            ->columns(2),
        Builder\Block::make('paragraph')
            ->schema([
                Textarea::make('content')
                    ->label('Paragraph')
                    ->required(),
            ]),
        Builder\Block::make('image')
            ->schema([
                FileUpload::make('url')
                    ->label('Image')
                    ->image()
                    ->required(),
                TextInput::make('alt')
                    ->label('Alt text')
                    ->required(),
            ]),
    ])
```

----------------------------------------

TITLE: Validating Number of Items in FilamentPHP Repeater - PHP
DESCRIPTION: This snippet demonstrates how to apply minimum and maximum item validation to a FilamentPHP Repeater component. The `minItems()` and `maxItems()` methods are used to restrict the number of entries a user can add to the repeater, ensuring data integrity.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_27

LANGUAGE: PHP
CODE:
```
use FilamentFormsComponentsRepeater;

Repeater::make('members')
    ->schema([
        // ...
    ])
    ->minItems(2)
    ->maxItems(5)
```

----------------------------------------

TITLE: Adding Custom Item Actions to Filament Builder Items in PHP
DESCRIPTION: This example shows how to extend the functionality of individual builder items by adding custom action buttons using `extraItemActions()`. It includes an example of sending an email based on item data, demonstrating how to retrieve validated item state using `getItemState()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/13-builder.md#_snippet_23

LANGUAGE: PHP
CODE:
```
use FilamentFormsComponentsActionsAction;
use FilamentFormsComponentsBuilder;
use FilamentFormsComponentsTextInput;
use IlluminateSupportFacadesMail;

Builder::make('content')
    ->blocks([
        Builder\Block::make('contactDetails')
            ->schema([
                TextInput::make('email')
                    ->label('Email address')
                    ->email()
                    ->required(),
                // ...
            ]),
        // ...
    ])
    ->extraItemActions([
        Action::make('sendEmail')
            ->icon('heroicon-m-square-2-stack')
            ->action(function (array $arguments, Builder $component): void {
                $itemData = $component->getItemState($arguments['item']);
                
                Mail::to($itemData['email'])
                    ->send(
                        // ...
                    );
            }),
    ])
```

----------------------------------------

TITLE: Require Table Builder Package with Composer Bash
DESCRIPTION: Installs the Filament Table Builder package using Composer. This is the primary command to add the package dependency to your project.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/01-installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require filament/tables:"^3.3" -W
```

----------------------------------------

TITLE: Enabling Reactivity on Select Field - Filament PHP
DESCRIPTION: Demonstrates how to make a Filament `Select` form field reactive using the `live()` method. When the user changes the selected option, the entire form will re-render, allowing other form elements to dynamically update based on the new value. Requires the field to be defined within a Filament form schema.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('status')
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
    ->live();
```

----------------------------------------

TITLE: Optimizing Filament for Production
DESCRIPTION: This command optimizes Filament for production environments by caching components and Blade icons, significantly improving performance. It's a shorthand for `filament:cache-components` and `icons:cache`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/01-installation.md#_snippet_2

LANGUAGE: bash
CODE:
```
php artisan filament:optimize
```

----------------------------------------

TITLE: Saving Form Relationships After Model Creation in Filament PHP
DESCRIPTION: This snippet illustrates how to save relationships to a newly created model instance after a form submission. After creating the `Post` record, the new `$post` instance is passed to `$this->form->model()`, and `saveRelationships()` is called to persist any related data managed by the form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use App\Models\Post;

public function create(): void
{
    $post = Post::create($this->form->getState());
    
    // Save the relationships from the form to the post after it is created.
    $this->form->model($post)->saveRelationships();
}
```

----------------------------------------

TITLE: Accessing Page Filter Data in Filament Widgets (PHP)
DESCRIPTION: This code snippet illustrates how widgets can access the raw data from the dashboard's filter form. By using the `InteractsWithPageFilters` trait, widgets can utilize the `$this->filters` property to retrieve filter values, enabling dynamic data querying based on user selections, such as filtering blog posts by date range.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/05-dashboard.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use App\Models\BlogPost;
use Carbon\CarbonImmutable;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Database\Eloquent\Builder;

class BlogPostsOverview extends StatsOverviewWidget
{
    use InteractsWithPageFilters;

    public function getStats(): array
    {
        $startDate = $this->filters['startDate'] ?? null;
        $endDate = $this->filters['endDate'] ?? null;

        return [
            StatsOverviewWidget\Stat::make(
                label: 'Total posts',
                value: BlogPost::query()
                    ->when($startDate, fn (Builder $query) => $query->whereDate('created_at', '>=', $startDate))
                    ->when($endDate, fn (Builder $query) => $query->whereDate('created_at', '<=', $endDate))
                    ->count(),
            ),
            // ...
        ];
    }
}
```

----------------------------------------

TITLE: Manipulating Raw Repeater Data in FilamentPHP
DESCRIPTION: This snippet demonstrates how to get and set the raw data for an entire FilamentPHP repeater component. It uses `getState()` to retrieve the current data, adds a new item with a UUID key, and then updates the repeater's state using `state()`. This is useful for programmatic additions, removals, or modifications of repeater items.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_35

LANGUAGE: PHP
CODE:
```
use IlluminateSupportStr;

// Get the raw data for the entire repeater
$state = $component->getState();

// Add an item, with a random UUID as the key
$state[Str::uuid()] = [
    'email' => auth()->user()->email,
];

// Set the new data for the repeater
$component->state($state);
```

----------------------------------------

TITLE: Defining Resource Pages in FilamentPHP (PHP)
DESCRIPTION: This static method on a FilamentPHP resource class is responsible for registering the pages associated with the resource and their respective routes. By returning an array excluding a specific page class (like a 'create' page), that page is effectively removed from the resource's available pages, preventing users from accessing its functionality.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_40

LANGUAGE: php
CODE:
```
public static function getPages(): array
{
    return [
        'index' => Pages\ListCustomers::route('/'),
        'edit' => Pages\EditCustomer::route('/{record}/edit'),
    ];
}
```

----------------------------------------

TITLE: Manually Upgrading Filament Assets and Caches
DESCRIPTION: This Bash snippet provides the commands for manually upgrading Filament. First, `composer update` fetches the latest package versions. Subsequently, `php artisan filament:upgrade` is executed to clear Laravel caches and republish frontend assets, which is essential for a complete and clean upgrade.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/01-installation.md#_snippet_11

LANGUAGE: Bash
CODE:
```
composer update

php artisan filament:upgrade
```

----------------------------------------

TITLE: Defining Filter Tabs for Record Listing in PHP
DESCRIPTION: This snippet demonstrates how to add filter tabs to a Filament list page by implementing the `getTabs()` method. Each `Tab` object can scope the Eloquent query using `modifyQueryUsing()`, allowing for predefined filtering conditions. The array keys serve as unique identifiers for the tabs.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/02-listing-records.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

public function getTabs(): array
{
    return [
        'all' => Tab::make(),
        'active' => Tab::make()
            ->modifyQueryUsing(fn (Builder $query) => $query->where('active', true)),
        'inactive' => Tab::make()
            ->modifyQueryUsing(fn (Builder $query) => $query->where('active', false)),
    ];
}
```

----------------------------------------

TITLE: Replacing Page Header with Custom View in FilamentPHP (PHP)
DESCRIPTION: Replaces the default page header (heading, subheading, actions) with a custom Blade view by implementing the `getHeader()` method. This method returns an `Illuminate\Contracts\View\View` instance, allowing for complete control over the header's appearance. Requires `Illuminate\Contracts\View\View`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/04-pages.md#_snippet_22

LANGUAGE: PHP
CODE:
```
use Illuminate\Contracts\View\View;

public function getHeader(): ?View
{
    return view('filament.settings.custom-header');
}
```

----------------------------------------

TITLE: Creating a Basic Text Entry in FilamentPHP
DESCRIPTION: This snippet demonstrates how to create a simple text entry in FilamentPHP infolists. It uses the `TextEntry::make()` method to display the value of the 'title' attribute.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/02-text.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('title')
```

----------------------------------------

TITLE: Requiring a Filament Field When All Others Are Absent (PHP)
DESCRIPTION: This snippet shows how to use the `requiredWithoutAll` validation rule. The field value is required only when all the other specified fields are empty. This is for cases where a field is needed if no alternatives are provided.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_41

LANGUAGE: PHP
CODE:
```
Field::make('name')->requiredWithoutAll('field,another_field')
```

----------------------------------------

TITLE: Publish Filament Configuration Bash
DESCRIPTION: Publishes the Filament package configuration file (`config/filament.php`) to your application's config directory, allowing customization (optional step).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/01-installation.md#_snippet_9

LANGUAGE: bash
CODE:
```
php artisan vendor:publish --tag=filament-config
```

----------------------------------------

TITLE: Halting Create Action Process in FilamentPHP (PHP)
DESCRIPTION: This snippet demonstrates how to halt the `CreateAction` process using `$action->halt()` within a `before()` lifecycle hook. It checks for an active subscription and, if not found, sends a persistent warning notification before halting the action, preventing record creation. It requires `Filament\Notifications\Notification` and `Filament\Notifications\Actions\Action`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/01-create.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use App\Models\Post;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;

CreateAction::make()
    ->before(function (CreateAction $action, Post $record) {
        if (! $record->team->subscribed()) {
            Notification::make()
                ->warning()
                ->title('You don\'t have an active subscription!')
                ->body('Choose a plan to continue.')
                ->persistent()
                ->actions([
                    Action::make('subscribe')
                        ->button()
                        ->url(route('subscribe'), shouldOpenInNewTab: true),
                ])
                ->send();
        
            $action->halt();
        }
    })
```

----------------------------------------

TITLE: Adding Extra HTML Attributes to Filament Field (PHP)
DESCRIPTION: Demonstrates how to add custom HTML attributes to the outer DOM element of a Filament form field using the `extraAttributes()` method, useful for general styling, accessibility, or custom data attributes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->extraAttributes(['title' => 'Text input'])
```

----------------------------------------

TITLE: Generating Relation Manager with Soft Deletes (Bash)
DESCRIPTION: Provides the Artisan command to generate a Filament Relation Manager with built-in support for handling soft-deleted records. The `--soft-deletes` flag adds functionality for restoring, force deleting, and filtering trashed records in the relation manager table.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_7

LANGUAGE: bash
CODE:
```
php artisan make:filament-relation-manager CategoryResource posts title --soft-deletes
```

----------------------------------------

TITLE: Defining Repeater Component Schema in Filament PHP
DESCRIPTION: This snippet illustrates how the component schema for a Filament Repeater is defined within the `schema()` method. It shows a basic example of adding a TextInput field for a 'name' within the repeater's structure, indicating that more fields can be added.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;

Repeater::make('members')
    ->schema([
        TextInput::make('name')->required(),
        // ...
    ])
```

----------------------------------------

TITLE: Asserting Infolist Action Enabled/Disabled State in PHP
DESCRIPTION: This snippet demonstrates how to assert whether a Filament Infolist action is enabled or disabled using Pest's `assertInfolistActionEnabled()` and `assertInfolistActionDisabled()` methods. It checks that the 'send' action is disabled while the 'print' action is enabled for a customer on an invoice editing page, reflecting conditional availability.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/08-testing.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can only print a customer for a sent invoice', function () {
    $invoice = Invoice::factory()->create();

    livewire(EditInvoice::class, [
        'invoice' => $invoice,
    ])
        ->assertInfolistActionDisabled('customer', 'send')
        ->assertInfolistActionEnabled('customer', 'print');
});
```

----------------------------------------

TITLE: Defining Table Filters in Filament PHP
DESCRIPTION: Shows how to add filters to a Filament table using the `filters()` method. It includes examples of a simple boolean filter with a query closure and a select filter with predefined options, allowing users to narrow down table results.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/02-getting-started.md#_snippet_4

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

public function table(Table $table): Table
{
    return $table
        ->columns([
            // ...
        ])
        ->filters([
            Filter::make('is_featured')
                ->query(fn (Builder $query) => $query->where('is_featured', true)),
            SelectFilter::make('status')
                ->options([
                    'draft' => 'Draft',
                    'reviewing' => 'Reviewing',
                    'published' => 'Published',
                ]),
        ]);
}
```

----------------------------------------

TITLE: Saving a Specific Form Component Independently in Filament PHP
DESCRIPTION: This snippet demonstrates how to save a specific `Section` component of a Filament form independently using a footer action. The `saveFormComponentOnly()` method is called on the `EditRecord` Livewire instance, allowing a subset of the form data to be persisted without saving the entire form. A notification confirms the successful save.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_11

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

Section::make('Rate limiting')
    ->schema([
        // ...
    ])
    ->footerActions([
        fn (string $operation): Action => Action::make('save')
            ->action(function (Section $component, EditRecord $livewire) {
                $livewire->saveFormComponentOnly($component);
                
                Notification::make()
                    ->title('Rate limiting saved')
                    ->body('The rate limiting settings have been saved successfully.')
                    ->success()
                    ->send();
            })
            ->visible($operation === 'edit'),
    ])
```

----------------------------------------

TITLE: Validating Field Existence in FilamentPHP (PHP)
DESCRIPTION: The `exists` rule ensures that a field's value corresponds to an existing record in the database. It can be configured to search the form's model by default, or a custom table/model and column can be specified. Advanced customization is possible using a closure to modify the underlying Laravel `Exists` rule.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_15

LANGUAGE: PHP
CODE:
```
Field::make('invitation')->exists()
```

LANGUAGE: PHP
CODE:
```
use App\Models\Invitation;

Field::make('invitation')->exists(table: Invitation::class)
```

LANGUAGE: PHP
CODE:
```
Field::make('invitation')->exists(column: 'id')
```

LANGUAGE: PHP
CODE:
```
use Illuminate\Validation\Rules\Exists;

Field::make('invitation')
    ->exists(modifyRuleUsing: function (Exists $rule) {
        return $rule->where('is_active', 1);
    })
```

----------------------------------------

TITLE: Conditionally Disabling a Button in FilamentPHP (PHP)
DESCRIPTION: This example demonstrates how to conditionally disable a button based on a boolean condition, such as user permissions, by passing the result of the authorization check to the `disabled()` method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/03-trigger-button.md#_snippet_14

LANGUAGE: PHP
CODE:
```
Action::make('delete')
    ->disabled(! auth()->user()->can('delete', $this->post))
```

----------------------------------------

TITLE: Using Section Layout Component in PHP Infolists
DESCRIPTION: This snippet demonstrates the use of the `Section` layout component to group related entries within an infolist. It allows adding a heading ("Media") and a description, and can contain its own schema of nested entries, improving organization and readability.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/02-getting-started.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;

[
    TextEntry::make('title'),
    TextEntry::make('slug'),
    TextEntry::make('content')
        ->columnSpan(2)
        ->markdown(),
    Section::make('Media')
        ->description('Images used in the page layout.')
        ->schema([
            // ...
        ]),
]
```

----------------------------------------

TITLE: Installing Filament for New Laravel Projects
DESCRIPTION: These commands quickly set up Filament, Livewire, Alpine.js, and Tailwind CSS in a new Laravel project. The `--scaffold` flag generates necessary files, and `--infolists` specifically installs the Infolist Builder. `npm install` and `npm run dev` handle frontend dependencies and compilation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install --scaffold --infolists

npm install

npm run dev
```

----------------------------------------

TITLE: Using Filament DeleteAction for a single record (PHP)
DESCRIPTION: Demonstrates the basic usage of the Filament DeleteAction to delete a single Eloquent record. The `record()` method is used to specify the record to be deleted.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/04-delete.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Actions\DeleteAction;

DeleteAction::make()
    ->record($this->post);
```

----------------------------------------

TITLE: Implementing Lifecycle Hooks for Filament Toggle Column (PHP)
DESCRIPTION: This example shows how to use `beforeStateUpdated` and `afterStateUpdated` lifecycle hooks with a Filament Toggle Column. These hooks allow executing custom logic before and after the toggle's state is saved to the database, providing opportunities for validation or side effects.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/07-toggle.md#_snippet_1

LANGUAGE: php
CODE:
```
ToggleColumn::make()
    ->beforeStateUpdated(function ($record, $state) {
        // Runs before the state is saved to the database.
    })
    ->afterStateUpdated(function ($record, $state) {
        // Runs after the state is saved to the database.
    })
```

----------------------------------------

TITLE: Accessing the Eloquent Record in Filament Views (Blade)
DESCRIPTION: This Blade snippet demonstrates how to access the current Eloquent record associated with the form directly within a Filament field's view. By using the `$getRecord()` function, developers can retrieve the model instance and access its properties, such as `name`, for display or conditional logic within the custom field's view.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/20-custom.md#_snippet_7

LANGUAGE: blade
CODE:
```
<div>
    {{ $getRecord()->name }}
</div>
```

----------------------------------------

TITLE: Dynamically Generating Placeholder Content in Filament Forms (PHP)
DESCRIPTION: This snippet shows how to dynamically generate placeholder content by passing a closure to the `content()` method. It utilizes the `Filament\Forms\Get` object to access values from other form fields (e.g., 'cost' and 'quantity') and calculate a formatted total, providing real-time updates based on user input.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/08-placeholder.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Get;

Placeholder::make('total')
    ->content(function (Get $get): string {
        return '€' . number_format($get('cost') * $get('quantity'), 2);
    })
```

----------------------------------------

TITLE: Creating Clock Widget Blade View with Alpine.js
DESCRIPTION: This Blade template defines the visual structure of the clock widget. It uses Filament's `x-filament-widgets::widget` and `x-filament::section` components for consistent styling. Crucially, it incorporates an asynchronous Alpine.js component (`x-load`, `x-data="clockWidget()"`) to display the current time, dynamically loading the JavaScript source. It also includes translatable text for the title and description.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/08-plugins/02-build-a-panel-plugin.md#_snippet_5

LANGUAGE: blade
CODE:
```
<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            {{ __('clock-widget::clock-widget.title') }}
        </x-slot>

        <div
            x-load
            x-load-src="{{ \Filament\Support\Facades\FilamentAsset::getAlpineComponentSrc('clock-widget', 'awcodes/clock-widget') }}"
            x-data="clockWidget()"
            class="text-center"
        >
            <p>{{ __('clock-widget::clock-widget.description') }}</p>
            <p class="text-xl" x-text="time"></p>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
```

----------------------------------------

TITLE: Basic Filament Cluster Class Definition
DESCRIPTION: This PHP snippet shows the basic structure of a Filament cluster class. It extends `Filament\Clusters\Cluster` and includes a default `$navigationIcon` property, which can be customized to define the icon for the cluster's main navigation item. Other navigation properties are also available for further customization.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/10-clusters.md#_snippet_2

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Filament\Clusters;

use Filament\Clusters\Cluster;

class Settings extends Cluster
{
    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
}
```

----------------------------------------

TITLE: Displaying Private Images in Filament PHP Column
DESCRIPTION: Configures an image column in FilamentPHP to display private images by generating temporary URLs. This is necessary for files not accessible via the default public URL and requires the column to have 'private' visibility.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/04-image.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\ImageColumn;

ImageColumn::make('header_image')
    ->visibility('private')
```

----------------------------------------

TITLE: Enable Multiple Record Association with AssociateAction in Filament
DESCRIPTION: Allows the user to select and associate multiple related records simultaneously within the `AssociateAction` modal for HasMany/MorphMany relationships, streamlining the process.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_25

LANGUAGE: php
CODE:
```
use Filament\Tables\Actions\AssociateAction;

AssociateAction::make()
    ->multiple()
```

----------------------------------------

TITLE: Casting Multiple File Uploads in Eloquent (PHP)
DESCRIPTION: This Eloquent model snippet illustrates how to cast a database column, such as 'attachments', to an array. This is crucial when the `FileUpload` component is configured for multiple uploads, as it ensures that the stored JSON string of file URLs is automatically converted to a PHP array upon retrieval.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    protected $casts = [
        'attachments' => 'array',
    ];

    // ...
}
```

----------------------------------------

TITLE: Conditionally Hiding Livewire Component Based on Record Existence in Filament PHP
DESCRIPTION: This snippet shows how to hide an embedded Livewire component in a Filament form when the form is creating a new record (i.e., the `$record` is `null`). It uses the `hidden()` method with a closure checking the `$record` state.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_40

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Livewire;
use Illuminate\Database\Eloquent\Model;

Livewire::make(Foo::class)
    ->hidden(fn (?Model $record): bool => $record === null)
```

----------------------------------------

TITLE: Making Filament ViewRecord Page Translatable
DESCRIPTION: This PHP code demonstrates how to make a Filament `ViewRecord` page translatable. It requires applying the `ViewRecord\Concerns\Translatable` trait and including the `Actions\LocaleSwitcher::make()` action in the `getHeaderActions()` method, enabling locale switching when viewing records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-translatable-plugin/README.md#_snippet_7

LANGUAGE: php
CODE:
```
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBlogPost extends ViewRecord
{
    use ViewRecord\Concerns\Translatable;
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            // ...
        ];
    }
    
    // ...
}
```

----------------------------------------

TITLE: Sending Database Notifications with Filament Fluent API (PHP)
DESCRIPTION: This PHP snippet shows how to send a database notification using Filament's fluent API. It demonstrates creating a `Notification` instance, setting its title, and then sending it to a specific recipient's database using `sendToDatabase()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/03-database-notifications.md#_snippet_5

LANGUAGE: php
CODE:
```
use Filament\Notifications\Notification;

$recipient = auth()->user();

Notification::make()
    ->title('Saved successfully')
    ->sendToDatabase($recipient);
```

----------------------------------------

TITLE: Handling Extra Modal Footer Action Arguments in FilamentPHP (PHP)
DESCRIPTION: This snippet demonstrates how to pass and access arguments from custom modal footer actions within the main action's `action()` closure. Arguments, such as `['another' => true]`, can be used as flags to control the action's behavior based on user interaction.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_22

LANGUAGE: PHP
CODE:
```
Action::make('create')
    ->form([
        // ...
    ])
    // ...
    ->extraModalFooterActions(fn (Action $action): array => [
        $action->makeModalSubmitAction('createAnother', arguments: ['another' => true]),
    ])
    ->action(function (array $data, array $arguments): void {
        // Create

        if ($arguments['another'] ?? false) {
            // Reset the form and don't close the modal
        }
    })
```

----------------------------------------

TITLE: Conditionally Hiding Navigation Item in Filament PHP
DESCRIPTION: Demonstrates how to make a navigation item conditionally visible or hidden based on a boolean condition, typically using user permissions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/06-navigation.md#_snippet_20

LANGUAGE: php
CODE:
```
use Filament\Navigation\NavigationItem;

NavigationItem::make('Analytics')
    ->visible(fn(): bool => auth()->user()->can('view-analytics'))
```

LANGUAGE: php
CODE:
```
use Filament\Navigation\NavigationItem;

NavigationItem::make('Analytics')
    ->hidden(fn(): bool => ! auth()->user()->can('view-analytics'))
```

----------------------------------------

TITLE: FilamentPHP Dynamic Fields Based on Select
DESCRIPTION: Renders a different set of fields dynamically based on the value of a select field (`type`). A layout component (`Grid`) uses a `schema()` function that injects `Get` to check the `type` value and returns different field arrays (employee or freelancer). The `afterStateUpdated` on the `type` select triggers a `fill()` on the dynamic container to initialize the new fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_25

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;

Select::make('type')
    ->options([
        'employee' => 'Employee',
        'freelancer' => 'Freelancer',
    ])
    ->live()
    ->afterStateUpdated(fn (Select $component) => $component
        ->getContainer()
        ->getComponent('dynamicTypeFields')
        ->getChildComponentContainer()
        ->fill())
    
Grid::make(2)
    ->schema(fn (Get $get): array => match ($get('type')) {
        'employee' => [
            TextInput::make('employee_number')
                ->required(),
            FileUpload::make('badge')
                ->image()
                ->required(),
        ],
        'freelancer' => [
            TextInput::make('hourly_rate')
                ->numeric()
                ->required()
                ->prefix('€'),
            FileUpload::make('contract')
                ->required(),
        ],
        default => [],
    })
    ->key('dynamicTypeFields')
```

----------------------------------------

TITLE: Configuring Global Filament Table Defaults (PHP)
DESCRIPTION: This code demonstrates how to set global default configurations for all Filament tables by calling the static configureUsing() method on the Table class within a service provider's boot() method. This allows setting common properties like filter layout and pagination options once, which will then apply to every table created in the application.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_25

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;

Table::configureUsing(function (Table $table): void {
    $table
        ->filtersLayout(FiltersLayout::AboveContentCollapsible)
        ->paginationPageOptions([10, 25, 50]);
});
```

----------------------------------------

TITLE: Registering Navigation Items in Filament Panel PHP
DESCRIPTION: Registers custom navigation items within a Filament panel configuration. Items can be external URLs, internal pages, or resources, configured with labels, icons, groups, sorting, and conditional active states.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/06-navigation.md#_snippet_19

LANGUAGE: php
CODE:
```
use Filament\Navigation\NavigationItem;
use Filament\Pages\Dashboard;
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->navigationItems([
            NavigationItem::make('Analytics')
                ->url('https://filament.pirsch.io', shouldOpenInNewTab: true)
                ->icon('heroicon-o-presentation-chart-line')
                ->group('Reports')
                ->sort(3),
            NavigationItem::make('dashboard')
                ->label(fn (): string => __('filament-panels::pages/dashboard.title'))
                ->url(fn (): string => Dashboard::getUrl())
                ->isActiveWhen(fn () => request()->routeIs('filament.admin.pages.dashboard')),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Applying Min/Max Length Validation to Textarea in Filament
DESCRIPTION: This snippet shows how to enforce minimum and maximum character limits on a Filament Textarea. The `minLength()` and `maxLength()` methods add both client-side (frontend) and server-side (backend) validation, ensuring the input adheres to specified length constraints.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/15-textarea.md#_snippet_4

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Textarea;

Textarea::make('description')
    ->minLength(2)
    ->maxLength(1024)
```

----------------------------------------

TITLE: Creating Basic Action Groups in FilamentPHP
DESCRIPTION: This snippet demonstrates how to create a basic `ActionGroup` in FilamentPHP, allowing multiple `Action` objects to be grouped into a single dropdown menu. It shows a simple group containing 'view', 'edit', and 'delete' actions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/05-grouping-actions.md#_snippet_0

LANGUAGE: PHP
CODE:
```
ActionGroup::make([
    Action::make('view'),
    Action::make('edit'),
    Action::make('delete'),
])
```

----------------------------------------

TITLE: Injecting State Parameter in Column Closure - Filament PHP
DESCRIPTION: Shows the basic syntax for defining a function that accepts the `$state` parameter, allowing access to the current value of the column within the closure.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/12-advanced.md#_snippet_1

LANGUAGE: php
CODE:
```
function ($state) {
    // ...
}
```

----------------------------------------

TITLE: Sending Broadcast Notification via Fluent API (PHP)
DESCRIPTION: This snippet demonstrates sending a real-time broadcast notification using Filament's fluent API. It creates a new notification instance, sets its title, and broadcasts it to a specified recipient, typically a logged-in user. Requires Laravel Echo and a server-side websocket integration.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/04-broadcast-notifications.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Notifications\Notification;

$recipient = auth()->user();

Notification::make()
    ->title('Saved successfully')
    ->broadcast($recipient);
```

----------------------------------------

TITLE: Resolving FilamentPHP Relationship Import by Email (PHP)
DESCRIPTION: Customizes the resolution of a `BelongsTo` relationship import to find the related record using a specific column (e.g., 'email') instead of the primary key. The `resolveUsing` parameter specifies the column name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_17

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Imports\ImportColumn;

ImportColumn::make('author')
    ->relationship(resolveUsing: 'email')
```

----------------------------------------

TITLE: Customizing Filament Table Filters Form Schema in PHP
DESCRIPTION: This snippet demonstrates how to customize the layout of filters within a Filament table using the `filtersFormSchema()` method. It shows how to group filters into a `Section` component, apply column layouts, and insert individual filters into the schema using their names as keys from the provided `$filters` array.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/06-layout.md#_snippet_8

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Section;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->filters([
            Filter::make('is_featured'),
            Filter::make('published_at'),
            Filter::make('author'),
        ])
        ->filtersFormColumns(2)
        ->filtersFormSchema(fn (array $filters): array => [
            Section::make('Visibility')
                ->description('These filters affect the visibility of the records in the table.')
                ->schema([
                    $filters['is_featured'],
                    $filters['published_at'],
                ])
                    ->columns(2)
                ->columnSpanFull(),
            $filters['author'],
        ]);
}
```

----------------------------------------

TITLE: Add Pivot Attributes to Edit Form in Filament Relation Manager
DESCRIPTION: Configures the form schema for editing a related record within a Filament Relation Manager to include pivot attributes. This allows editing attributes directly on the pivot table via the relation manager's edit action.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_10

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('name')->required(),
            Forms\Components\TextInput::make('role')->required(),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Adding Collapsible Custom HTML to Filament Table Rows - PHP
DESCRIPTION: This PHP snippet shows how to integrate custom HTML content into Filament table rows using the `View` component. It defines a `Split` layout for primary columns and a `View` component that points to a Blade file (`users.table.collapsible-row-content`) and is configured to be collapsible, allowing for expandable row details.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/06-layout.md#_snippet_14

LANGUAGE: php
CODE:
```
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\View;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;

[
    Split::make([
        ImageColumn::make('avatar')
            ->circular(),
        TextColumn::make('name')
            ->weight(FontWeight::Bold)
            ->searchable()
            ->sortable(),
    ]),
    View::make('users.table.collapsible-row-content')
        ->collapsible(),
]
```

----------------------------------------

TITLE: Creating Livewire Application Layout
DESCRIPTION: This Blade template defines the main layout for Livewire components in `resources/views/components/layouts/app.blade.php`. It includes essential HTML structure, meta tags, CSRF token, viewport settings, a `x-cloak` style for Alpine.js, and directives to inject Filament styles/scripts and Vite-compiled assets.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/01-installation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">

        <meta name="application-name" content="{{ config('app.name') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        @vite('resources/css/app.css')
    </head>

    <body class="antialiased">
        {{ $slot }}

        @filamentScripts
        @vite('resources/js/app.js')
    </body>
</html>
```

----------------------------------------

TITLE: Customizing Default Filament UI Colors (PHP)
DESCRIPTION: This snippet demonstrates how to customize Filament's six default UI colors (danger, gray, info, primary, success, warning) using the FilamentColor::register() method. It utilizes predefined Color constants from the Tailwind CSS color palette. This method should be called from a service provider's boot() method or middleware.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/04-colors.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentColor;

FilamentColor::register([
    'danger' => Color::Red,
    'gray' => Color::Zinc,
    'info' => Color::Blue,
    'primary' => Color::Amber,
    'success' => Color::Green,
    'warning' => Color::Amber,
]);
```

----------------------------------------

TITLE: Using HasDescription Enum with Filament Form Fields in PHP
DESCRIPTION: This PHP snippet demonstrates how to integrate an enum implementing `HasDescription` (e.g., `Status` enum) with Filament's `Radio` and `CheckboxList` form fields. By passing the enum class to the `options()` method, Filament automatically populates the field options and their corresponding descriptions, streamlining form creation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/07-enums.md#_snippet_5

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Radio;

Radio::make('status')
    ->options(Status::class);

CheckboxList::make('status')
    ->options(Status::class);
```

----------------------------------------

TITLE: Defining Infolist Schema in a Static Method (Filament PHP Resource)
DESCRIPTION: This snippet shows how to define the infolist schema when working within a Filament panel builder resource. Unlike a standard class method, the `infolist()` method must be declared as `static` to be properly recognized and utilized by the resource. It configures the infolist instance with its schema.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Infolist;

public static function infolist(Infolist $infolist): Infolist
{
    return $infolist
        ->schema([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Configuring PostCSS with Tailwind CSS and Autoprefixer
DESCRIPTION: This JavaScript configuration file for PostCSS registers `tailwindcss/nesting`, `tailwindcss`, and `autoprefixer` as plugins. This setup is essential for processing CSS with nesting syntax and adding vendor prefixes automatically.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/01-installation.md#_snippet_6

LANGUAGE: javascript
CODE:
```
export default {
    plugins: {
        'tailwindcss/nesting': 'postcss-nesting',
        tailwindcss: {},
        autoprefixer: {}
    }
}
```

----------------------------------------

TITLE: Conditionally Hiding FilamentPHP Field (hidden)
DESCRIPTION: Hides a field based on the value of another field. Uses the `hidden()` method with a function that injects the `Get` utility to access the state of the `is_company` checkbox. The `live()` method on `is_company` ensures the form rerenders when its value changes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_18

LANGUAGE: php
CODE:
```
use Filament\Forms\Get;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\TextInput;

Checkbox::make('is_company')
    ->live()

TextInput::make('company_name')
    ->hidden(fn (Get $get): bool => ! $get('is_company'))
```

----------------------------------------

TITLE: Defining Eloquent Relationships Between Owner, Patient, and Treatment Models
DESCRIPTION: This PHP snippet defines the Eloquent relationships between the `Owner`, `Patient`, and `Treatment` models. It establishes a one-to-many relationship where an `Owner` `hasMany` `Patient`s, a `Patient` `belongsTo` an `Owner` and `hasMany` `Treatment`s, and a `Treatment` `belongsTo` a `Patient`. These relationships are crucial for navigating and querying related data within the application.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_3

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Owner extends Model
{
    public function patients(): HasMany
    {
        return $this->hasMany(Patient::class);
    }
}

class Patient extends Model
{
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class);
    }

    public function treatments(): HasMany
    {
        return $this->hasMany(Treatment::class);
    }
}

class Treatment extends Model
{
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }
}
```

----------------------------------------

TITLE: Customizing Relationship Query in Filament Select (PHP)
DESCRIPTION: This snippet illustrates how to customize the Eloquent query used to retrieve options for a Filament Select field. The `modifyQueryUsing` parameter accepts a closure that receives the query builder, allowing for advanced filtering, such as including soft-deleted records with `withTrashed()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

Select::make('author_id')
    ->relationship(
        name: 'author',
        titleAttribute: 'name',
        modifyQueryUsing: fn (Builder $query) => $query->withTrashed(),
    )
```

----------------------------------------

TITLE: Manually Upgrading Filament
DESCRIPTION: These commands provide the steps for manually upgrading Filament. First, `composer update` fetches the latest package versions, and then `php artisan filament:upgrade` clears all Laravel caches and republishes frontend assets to ensure compatibility.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/01-installation.md#_snippet_11

LANGUAGE: bash
CODE:
```
composer update

php artisan filament:upgrade
```

----------------------------------------

TITLE: Configuring Tailwind CSS for Filament
DESCRIPTION: This JavaScript configuration for `tailwind.config.js` imports and applies Filament's Tailwind preset, which includes its color scheme and required plugins. It also specifies content paths for Tailwind to scan, ensuring all Blade and PHP files are processed for JIT compilation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/01-installation.md#_snippet_4

LANGUAGE: javascript
CODE:
```
import preset from './vendor/filament/support/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        './app/Filament/**/*.php',
        './resources/views/filament/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
}
```

----------------------------------------

TITLE: Rendering the Filament Field Wrapper (Blade)
DESCRIPTION: This Blade snippet shows how to render the built-in Filament 'field wrapper' component. This component is crucial for maintaining design consistency, as it automatically renders the field's label, validation errors, and any surrounding text. It is highly encouraged to use this wrapper for custom fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/20-custom.md#_snippet_6

LANGUAGE: blade
CODE:
```
<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <!-- Field -->
</x-dynamic-component>
```

----------------------------------------

TITLE: Generate Responsive Images on Upload - PHP
DESCRIPTION: Configures the Spatie Media Library file upload component to automatically generate responsive image variants upon file upload. Requires Spatie's responsive image setup.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-media-library-plugin/README.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

SpatieMediaLibraryFileUpload::make('attachments')
    ->multiple()
    ->responsiveImages()
```

----------------------------------------

TITLE: Conditionally Hiding/Showing Filament Infolist Entries (PHP)
DESCRIPTION: This snippet demonstrates how to control the visibility of an entry based on a condition using either the `hidden()` or `visible()` methods. This is useful for displaying information only when relevant to the user's permissions or data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_16

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('role')
    ->hidden(! auth()->user()->isAdmin())
// or
TextEntry::make('role')
    ->visible(auth()->user()->isAdmin())
```

----------------------------------------

TITLE: Defining Custom Filament Infolist Entry Class (PHP)
DESCRIPTION: This PHP snippet illustrates the basic structure of a custom Filament Infolist entry class. It extends `Filament\Infolists\Components\Entry` and defines the `$view` protected property to link to its dedicated Blade template, establishing the visual representation of the entry.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/08-custom.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Entry;

class StatusSwitcher extends Entry
{
    protected string $view = 'filament.infolists.entries.status-switcher';
}
```

----------------------------------------

TITLE: Applying Unique Validation Ignoring a Specific Model (PHP)
DESCRIPTION: This snippet illustrates how to use the `ignorable` parameter with the `unique` validation rule. It allows a specific model instance to be ignored during the uniqueness check, which is crucial for update forms where the current record's value should not trigger a validation error.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_49

LANGUAGE: PHP
CODE:
```
Field::make('email')->unique(ignorable: $ignoredUser)
```

----------------------------------------

TITLE: Globally Configuring Section Component - PHP
DESCRIPTION: This code demonstrates how to set global default settings for a specific Filament Infolist layout component type, using the `Section` component as an example. The static `configureUsing()` method accepts a closure that modifies the component instance, allowing you to set properties like the default number of columns (`columns(2)`). This configuration should typically be placed within a service provider's `boot()` method. Requires importing the `Section` class from `Filament\Infolists\Components`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/01-getting-started.md#_snippet_3

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Section;

Section::configureUsing(function (Section $section): void {
    $section
        ->columns(2);
});
```

----------------------------------------

TITLE: Enhancing Owner Select Field with Search and Preload (PHP)
DESCRIPTION: This snippet refines the 'owner_id' select field within a Filament form. It makes the field 'required', enables 'searchable()' functionality for filtering options, and uses 'preload()' to load the initial 50 owners, improving user experience for long lists.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_11

LANGUAGE: php
CODE:
```
use Filament\Forms;

Forms\Components\Select::make('owner_id')
    ->relationship('owner', 'name')
    ->searchable()
    ->preload()
    ->required()
```

----------------------------------------

TITLE: Configuring Patient Form with Owner Relationship Select (PHP)
DESCRIPTION: This snippet defines the form schema for creating a new patient in FilamentPHP. It includes fields for 'name', 'type', 'date_of_birth', and 'owner_id'. The 'owner_id' field uses the 'relationship()' method to populate a select dropdown with owners from the related 'Owner' model, displaying their 'name'.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_10

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;

public static function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),
            Forms\Components\Select::make('type')
                ->options([
                    'cat' => 'Cat',
                    'dog' => 'Dog',
                    'rabbit' => 'Rabbit',
                ])
                ->required(),
            Forms\Components\DatePicker::make('date_of_birth')
                ->required()
                ->maxDate(now()),
            Forms\Components\Select::make('owner_id')
                ->relationship('owner', 'name')
                ->required(),
        ]);
}
```

----------------------------------------

TITLE: Customizing CheckboxList Relationship Labels with Callback (PHP)
DESCRIPTION: This snippet demonstrates using `getOptionLabelFromRecordUsing()` to dynamically generate labels for options in a Filament CheckboxList based on the Eloquent model. It allows for complex label formatting, such as concatenating first and last names, and can be combined with query modifications.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/06-checkbox-list.md#_snippet_15

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\CheckboxList;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

CheckboxList::make('authors')
    ->relationship(
        modifyQueryUsing: fn (Builder $query) => $query->orderBy('first_name')->orderBy('last_name'),
    )
    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->first_name} {$record->last_name}")
```

----------------------------------------

TITLE: Enabling Reordering for Filament Repeater Relationship Items (PHP)
DESCRIPTION: This snippet enables reordering functionality for items within a Filament Repeater component that is linked to an Eloquent relationship. The `orderColumn('sort')` method specifies 'sort' as the column on the related model that will store the order of records, allowing users to visually reorder items in the UI and persist the new order to the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_15

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Repeater;

Repeater::make('qualifications')
    ->relationship()
    ->schema([
        // ...
    ])
    ->orderColumn('sort')
```

----------------------------------------

TITLE: Generating Filament Resource with Soft Deletes (Bash)
DESCRIPTION: This command generates a new FilamentPHP resource for the `Customer` model, pre-configured with soft delete capabilities. It automatically sets up the necessary table filters, actions, and bulk actions for restoring, force deleting, and filtering trashed records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/06-deleting-records.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:filament-resource Customer --soft-deletes
```

----------------------------------------

TITLE: Testing Form Input Validation in Filament (PHP)
DESCRIPTION: This example demonstrates how to test form input validation in Filament. It attempts to submit a form with invalid data (e.g., a null 'title' for a required field) and then uses `assertHasFormErrors()` to confirm that the expected validation error is present.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_6

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can validate input', function () {
    livewire(PostResource\Pages\CreatePost::class)
        ->fillForm([
            'title' => null,
        ])
        ->call('create')
        ->assertHasFormErrors(['title' => 'required']);
});
```

----------------------------------------

TITLE: Configuring a Multi-Select Field in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to create a multi-select input field in Filament using the `multiple()` method. It allows users to select several predefined 'technologies' from a list, which will be returned as an array of values.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_4

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('technologies')
    ->multiple()
    ->options([
        'tailwind' => 'Tailwind CSS',
        'alpine' => 'Alpine.js',
        'laravel' => 'Laravel',
        'livewire' => 'Laravel Livewire',
    ])
```

----------------------------------------

TITLE: Customizing Record Creation Process with Filament CreateAction (PHP)
DESCRIPTION: This code demonstrates how to override the default record creation logic using the `using()` method. It allows developers to define a custom function that receives the form data and the model class name, providing full control over how the Eloquent model is instantiated and saved.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/01-create.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

CreateAction::make()
    ->using(function (array $data, string $model): Model {
        return $model::create($data);
    })
```

----------------------------------------

TITLE: Adding Soft Delete Actions to Filament Edit Page Header (PHP)
DESCRIPTION: This PHP code snippet shows how to add `DeleteAction`, `ForceDeleteAction`, and `RestoreAction` to the `getHeaderActions` array of a FilamentPHP resource's Edit page. This allows users to perform these actions directly from the edit form's header.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/06-deleting-records.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Actions;

protected function getHeaderActions(): array
{
    return [
        Actions\DeleteAction::make(),
        Actions\ForceDeleteAction::make(),
        Actions\RestoreAction::make(),
        // ...
    ];
}
```

----------------------------------------

TITLE: Applying Custom Formatting to FilamentPHP TextEntry State (PHP)
DESCRIPTION: This snippet shows how to apply custom formatting to the `TextEntry`'s state using the `formatStateUsing()` callback. This allows for dynamic manipulation of the displayed value, such as localization.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/02-text.md#_snippet_25

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('status')
    ->formatStateUsing(fn (string $state): string => __("statuses.{$state}"))
```

----------------------------------------

TITLE: Defining a Standalone Form Component Action in PHP
DESCRIPTION: This snippet demonstrates how to define a standalone action for a Filament form component. It uses `Action::make()` with a unique name, configures an icon, sets a danger color, requires user confirmation, and defines a callback function to execute a `ResetStars` action.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/06-actions.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use App\Actions\ResetStars;
use Filament\Forms\Components\Actions\Action;

Action::make('resetStars')
    ->icon('heroicon-m-x-mark')
    ->color('danger')
    ->requiresConfirmation()
    ->action(function (ResetStars $resetStars) {
        $resetStars();
    })
```

----------------------------------------

TITLE: Integrating BelongsTo Relationship with Filament Select (PHP)
DESCRIPTION: This code configures a Filament Select field to automatically retrieve options from a `BelongsTo` Eloquent relationship. The `relationship()` method specifies the relationship name (`author`) and the `titleAttribute` (`name`) to use for displaying options.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
```

----------------------------------------

TITLE: Conditionally Hiding Fields in Filament Forms (PHP)
DESCRIPTION: This snippet demonstrates how to dynamically hide or show a form field based on the value of another field. The `hidden()` method accepts a closure that uses the `$get` utility to retrieve the `status` field's value. The `status` field is set to `live()` to ensure the form schema reloads on change, enabling real-time visibility adjustments.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/02-getting-started.md#_snippet_6

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Get;

[
    Select::make('status')
        ->options([
            'draft' => 'Draft',
            'reviewing' => 'Reviewing',
            'published' => 'Published'
        ])
        ->required()
        ->live(),
    DateTimePicker::make('published_at')
        ->hidden(fn (Get $get) => $get('status') !== 'published')
]
```

----------------------------------------

TITLE: Injecting Livewire Component Instance - Filament PHP
DESCRIPTION: Details how to inject the parent Livewire component instance into a form configuration closure by type-hinting the `$livewire` parameter with `Livewire\Component`. This allows interaction with the Livewire component's state and methods from within the form field's logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_6

LANGUAGE: php
CODE:
```
use Livewire\Component as Livewire;

function (Livewire $livewire) {
    // ...
}
```

----------------------------------------

TITLE: Controlling Page Access (PHP)
DESCRIPTION: Overrides the `canAccess()` method in a Filament Page class to implement authorization logic, preventing unauthorized users from seeing the page in the navigation menu or accessing it directly. This example checks if the authenticated user has permission to manage settings.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/04-pages.md#_snippet_1

LANGUAGE: php
CODE:
```
public static function canAccess(): bool
{
    return auth()->user()->canManageSettings();
}
```

----------------------------------------

TITLE: Applying Regex Validation to Filament Fields (PHP)
DESCRIPTION: This snippet demonstrates how to apply a regular expression validation rule to a Filament field. The field's value must match the given regex pattern. This is useful for enforcing specific input formats like email or phone numbers.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_42

LANGUAGE: PHP
CODE:
```
Field::make('email')->regex('/^.+@.+$/i')
```

----------------------------------------

TITLE: Conditionally Showing Actions in FilamentPHP (PHP)
DESCRIPTION: This snippet demonstrates how to make an action visible only if the authenticated user possesses the 'update' permission for the associated 'post' model, utilizing the `visible()` method for authorization.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/03-trigger-button.md#_snippet_11

LANGUAGE: PHP
CODE:
```
Action::make('edit')
    ->url(fn (): string => route('posts.edit', ['post' => $this->post]))
    ->visible(auth()->user()->can('update', $this->post))
```

----------------------------------------

TITLE: Accessing Owner Record in FilamentPHP Static Form Method (PHP)
DESCRIPTION: Illustrates how to access the owner record within the static `form()` method of a FilamentPHP relation manager by using a callback function for a component's option. The callback receives the `$livewire` instance, allowing access to `$livewire->getOwnerRecord()`. Useful for dynamically populating form fields based on the parent record. Requires using a callback closure for component configuration.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_35

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;

public function form(Form $form):
{
    return $form
        ->schema([
            Forms\Components\Select::make('store_id')
                ->options(function (RelationManager $livewire): array {
                    return $livewire->getOwnerRecord()->stores()
                        ->pluck('name', 'id')
                        ->toArray();
                }),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Sending Basic Notifications in PHP
DESCRIPTION: This snippet demonstrates how to send a basic success notification from a Livewire component in PHP using Filament's `Notification` facade. It initializes a notification, sets a title, marks it as a success type, and dispatches it.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/02-sending-notifications.md#_snippet_0

LANGUAGE: php
CODE:
```
<?php

namespace App\Livewire;

use Filament\Notifications\Notification;
use Livewire\Component;

class EditPost extends Component
{
    public function save(): void
    {
        // ...

        Notification::make()
            ->title('Saved successfully')
            ->success()
            ->send();
    }
}
```

----------------------------------------

TITLE: Generating an Eloquent Record Edit Form with Filament CLI
DESCRIPTION: This Bash command generates a Livewire form component (`Products/EditProduct`) specifically for editing an existing Eloquent record. The `--edit` flag ensures the form automatically pre-fills with the record's data and handles saving changes back to the model upon submission, facilitating record updates.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_17

LANGUAGE: bash
CODE:
```
php artisan make:livewire-form Products/EditProduct --edit
```

----------------------------------------

TITLE: Setting a Custom Label for a Filament Field
DESCRIPTION: Illustrates how to set a custom label for a Filament form field using the `label()` method. This is particularly useful for localization, allowing the label to be a translation string retrieved via Laravel's `__` helper.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->label(__('fields.name'))
```

----------------------------------------

TITLE: Applying Unique Validation to Filament Fields (PHP)
DESCRIPTION: This snippet demonstrates the basic `unique` validation rule. The field value must not already exist in the database. By default, it searches the form's model and uses the field name as the column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_46

LANGUAGE: PHP
CODE:
```
Field::make('email')->unique()
```

----------------------------------------

TITLE: Caching Filament Components
DESCRIPTION: This command caches Filament components (resources, pages, widgets, etc.) to improve performance by reducing file scanning. It's recommended for production but should be avoided during active development.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/01-installation.md#_snippet_4

LANGUAGE: bash
CODE:
```
php artisan filament:cache-components
```

----------------------------------------

TITLE: Configuring Vite for Livewire Component Refreshing
DESCRIPTION: This Vite configuration (`vite.config.js`) extends the default Laravel Vite plugin to include `app/Livewire/**` in the `refreshPaths`. This ensures that the browser automatically refreshes when changes are detected in Livewire components, improving the development experience.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/01-installation.md#_snippet_7

LANGUAGE: javascript
CODE:
```
import { defineConfig } from 'vite'
import laravel, { refreshPaths } from 'laravel-vite-plugin'

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: [
                ...refreshPaths,
                'app/Livewire/**'
            ]
        })
    ]
})
```

----------------------------------------

TITLE: Accessing Owner Record in FilamentPHP Relation Manager (PHP)
DESCRIPTION: Shows how to retrieve the owner (parent) Eloquent record within a non-static method of a FilamentPHP relation manager using the `$this->getOwnerRecord()` method. This provides access to the parent model instance the relation manager is attached to. Requires being inside a relation manager instance method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_34

LANGUAGE: php
CODE:
```
$this->getOwnerRecord()
```

----------------------------------------

TITLE: Basic Block Definition in Filament Builder (PHP)
DESCRIPTION: This snippet illustrates the fundamental structure for defining blocks within the Filament Builder component. It shows that blocks are `Builder\Block` objects, requiring a unique name and a component schema, which can include various form fields like `TextInput`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/13-builder.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Builder;
use Filament\Forms\Components\TextInput;

Builder::make('content')
    ->blocks([
        Builder\Block::make('heading')
            ->schema([
                TextInput::make('content')->required(),
                // ...
            ]),
        // ...
    ])
```

----------------------------------------

TITLE: Using EditAction for Eloquent Records in Filament
DESCRIPTION: This snippet demonstrates the basic usage of `EditAction` to edit an Eloquent record. It configures the action to use a `TextInput` for the 'title' field, making it required and limiting its length.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/02-edit.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Actions\EditAction;
use Filament\Forms\Components\TextInput;

EditAction::make()
    ->record($this->post)
    ->form([
        TextInput::make('title')
            ->required()
            ->maxLength(255),
        // ...
    ])
```

----------------------------------------

TITLE: Conditionally Disabling a Filament Form Field (PHP)
DESCRIPTION: Shows how to conditionally disable a Filament form field by passing a boolean value to the `disabled()` method, allowing dynamic control over field interactivity based on application logic or user permissions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Toggle;

Toggle::make('is_admin')
    ->disabled(! auth()->user()->isAdmin())
```

----------------------------------------

TITLE: Accessing Related Data in Filament Table Column PHP
DESCRIPTION: Illustrates how to display data from a relationship in a table column using dot notation (`relationship.attribute`). Filament automatically eager-loads the relationship and displays the specified attribute.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/02-getting-started.md#_snippet_3

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('author.name')
```

----------------------------------------

TITLE: Integrating BelongsToMany Relationship with Filament Select (PHP)
DESCRIPTION: This snippet shows how to use a Filament Select field with a `BelongsToMany` relationship. The `multiple()` method enables multi-selection, and `relationship()` loads options from the pivot table, saving selections back upon form submission. If `name` is omitted, the field name is used as the relationship name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Select;

Select::make('technologies')
    ->multiple()
    ->relationship(titleAttribute: 'name')
```

----------------------------------------

TITLE: Saving Form Data to Individual Properties in Filament PHP
DESCRIPTION: This example demonstrates how to configure a Filament form to save data directly to public properties of the Livewire component, rather than a single `statePath`. By omitting `statePath()` and declaring public properties like `$title` and `$content`, form fields will automatically bind to these properties.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Form;

public ?string $title = null;

public ?string $content = null;

public function form(Form $form): Form
{
    return $form
        ->schema([
            TextInput::make('title')
                ->required(),
            MarkdownEditor::make('content'),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Accessing Entry State in Filament Infolist View (Blade)
DESCRIPTION: This Blade snippet demonstrates how to retrieve and display the current state (value) of a Filament Infolist entry within its custom Blade view. The `$getState()` helper function provides direct access to the entry's processed value, enabling dynamic content based on the entry's data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/08-custom.md#_snippet_3

LANGUAGE: blade
CODE:
```
<div>
    {{ $getState() }}
</div>
```

----------------------------------------

TITLE: Implementing Filament Infolist/Form Traits in Livewire (PHP)
DESCRIPTION: This PHP code demonstrates how to implement the `HasForms` and `HasInfolists` interfaces and use their corresponding `InteractsWithForms` and `InteractsWithInfolists` traits in a Livewire component. These are essential for enabling Filament's form and infolist functionalities within the component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/07-adding-an-infolist-to-a-livewire-component.md#_snippet_3

LANGUAGE: php
CODE:
```
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Livewire\Component;

class ViewProduct extends Component implements HasForms, HasInfolists
{
    use InteractsWithInfolists;
    use InteractsWithForms;

    // ...
}
```

----------------------------------------

TITLE: Enabling SPA Mode in Filament Panel (PHP)
DESCRIPTION: Activates Single-Page Application (SPA) mode for a Filament panel, leveraging Livewire's `wire:navigate` feature. This enhances user experience by reducing page load delays and providing a loading bar for longer requests. Requires `Filament\Panel`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->spa();
}
```

----------------------------------------

TITLE: Displaying Relative Dates in Filament Text Columns (PHP)
DESCRIPTION: This snippet demonstrates using the `since()` method to format a column's state into a human-readable relative time, such as '2 days ago'. It utilizes Carbon's `diffForHumans()` functionality for this purpose.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('created_at')
    ->since()
```

----------------------------------------

TITLE: Defining a Responsive Split Layout in Filament Forms (PHP)
DESCRIPTION: This PHP snippet demonstrates how to use the `Split` component in Filament Forms to create a responsive layout. It arranges two `Section` components side-by-side, where the first section dynamically grows to fill available space, while the second section maintains a fixed width, creating a sidebar effect. The `from('md')` method ensures the split layout is applied only on medium-sized devices and larger, stacking sections vertically on smaller screens.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/07-split.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Section;\nuse Filament\Forms\Components\Split;\nuse Filament\Forms\Components\Textarea;\nuse Filament\Forms\Components\TextInput;\nuse Filament\Forms\Components\Toggle;\n\nSplit::make([\n    Section::make([\n        TextInput::make('title'),\n        Textarea::make('content'),\n    ]),\n    Section::make([\n        Toggle::make('is_published'),\n        Toggle::make('is_featured'),\n    ])->grow(false),\n])->from('md')
```

----------------------------------------

TITLE: Defining Table Columns in Filament PHP
DESCRIPTION: Demonstrates how to define columns for a Filament table using the `columns()` method. It shows adding text columns (`TextColumn`) and an icon column (`IconColumn`) with a boolean transformation, defining how data from the record is displayed.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/02-getting-started.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->columns([
            TextColumn::make('title'),
            TextColumn::make('slug'),
            IconColumn::make('is_featured')
                ->boolean(),
        ]);
}
```

----------------------------------------

TITLE: Installing Filament with Scaffolding for New Laravel Projects
DESCRIPTION: These commands quickly set up Filament, Livewire, Alpine.js, and Tailwind CSS in a new Laravel project. `filament:install --scaffold --widgets` installs core Filament components and widgets, while `npm install` and `npm run dev` handle frontend dependencies and asset compilation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install --scaffold --widgets

npm install

npm run dev
```

----------------------------------------

TITLE: Configuring Predefined Panel Colors in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to customize the default color palette of a Filament panel by assigning predefined `Filament\Support\Colors\Color` constants to specific color roles like 'danger', 'primary', and 'success'. This method allows for quick and consistent theme adjustments across the application.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/12-themes.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Panel;
use Filament\Support\Colors\Color;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->colors([
            'danger' => Color::Rose,
            'gray' => Color::Gray,
            'info' => Color::Blue,
            'primary' => Color::Indigo,
            'success' => Color::Emerald,
            'warning' => Color::Orange,
        ]);
}
```

----------------------------------------

TITLE: Sending Email with Form Input in Filament PHP Action
DESCRIPTION: This action collects email subject and body from the user through a modal form using `TextInput` and `RichEditor` components. Upon submission, the `action()` callback uses Laravel's Mail facade to send an email to the client with the collected data, demonstrating how actions can integrate with forms.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/02-overview.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Mail;

Action::make('sendEmail')
    ->form([
        TextInput::make('subject')->required(),
        RichEditor::make('body')->required(),
    ])
    ->action(function (array $data) {
        Mail::to($this->client)
            ->send(new GenericEmail(
                subject: $data['subject'],
                body: $data['body'],
            ));
    })
```

----------------------------------------

TITLE: Defining a Distributable Filament Panel with PanelProvider (PHP)
DESCRIPTION: This PHP snippet illustrates how to define a complete Filament panel within a plugin for distribution. The `BlogPanelProvider` extends `Filament\PanelProvider`, acting as a standard Laravel service provider. It configures the panel's ID, path, resources, pages, widgets, and middleware, allowing users to install the plugin and get a pre-built part of their application.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/13-plugins.md#_snippet_8

LANGUAGE: PHP
CODE:
```
<?php

namespace DanHarrin\FilamentBlog;

use Filament\Panel;
use Filament\PanelProvider;

class BlogPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('blog')
            ->path('blog')
            ->resources([
                // ...
            ])
            ->pages([
                // ...
            ])
            ->widgets([
                // ...
            ])
            ->middleware([
                // ...
            ])
            ->authMiddleware([
                // ...
            ]);
    }
}
```

----------------------------------------

TITLE: Scoping Filament Select Component to Current Tenant (PHP)
DESCRIPTION: This example illustrates how to manually scope a Filament `Select` form component to the current tenant. Since form components and filters do not automatically scope their data, the `modifyQueryUsing` method is used to apply a `whereBelongsTo` clause, ensuring only records associated with the active tenant are displayed as options.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_38

LANGUAGE: PHP
CODE:
```
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

Select::make('author_id')
    ->relationship(
        name: 'author',
        titleAttribute: 'name',
        modifyQueryUsing: fn (Builder $query) => $query->whereBelongsTo(Filament::getTenant()),
    );
```

----------------------------------------

TITLE: Defining Relation Manager Form and Table (PHP)
DESCRIPTION: Shows the basic structure of a Filament Relation Manager class, illustrating the `form()` method to define the schema for creating or editing related records and the `table()` method to define the columns for listing related records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('title')->required(),
            // ...
        ]);
}

public function table(Table $table): Table
{
    return $table
        ->columns([
            Tables\Columns\TextColumn::make('title'),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Implementing Tenant-Aware Global Scope in PHP
DESCRIPTION: This PHP middleware class, `ApplyTenantScopes`, defines a global Eloquent scope for the `Author` model. Inside the `handle` method, it automatically filters `Author` queries to only include records belonging to the currently authenticated tenant, retrieved via `Filament::getTenant()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_40

LANGUAGE: php
CODE:
```
use App\Models\Author;
use Closure;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ApplyTenantScopes
{
    public function handle(Request $request, Closure $next)
    {
        Author::addGlobalScope(
            fn (Builder $query) => $query->whereBelongsTo(Filament::getTenant()),
        );

        return $next($request);
    }
}
```

----------------------------------------

TITLE: Adding Validation Rules to Import Columns (PHP)
DESCRIPTION: This snippet illustrates how to apply validation rules to an import column using the `rules()` method. These rules, such as 'required' and 'max:32', ensure that data from the CSV meets specified criteria before being saved to the database. Rows failing validation are compiled into a 'failed rows' CSV.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Imports\ImportColumn;

ImportColumn::make('sku')
    ->rules(['required', 'max:32'])
```

----------------------------------------

TITLE: Increasing Max File Size in php.ini
DESCRIPTION: This INI configuration snippet increases the maximum allowed size for POST data and uploaded files to 120 megabytes. This is crucial for handling large file uploads and preventing HTTP 422 errors.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_34

LANGUAGE: INI
CODE:
```
post_max_size = 120M
upload_max_filesize = 120M
```

----------------------------------------

TITLE: Using dehydrateStateUsing Hook for State Transformation - Filament PHP
DESCRIPTION: Shows how to use the `dehydrateStateUsing()` method to transform a field's state before it is retrieved by `getState()` or saved automatically. The closure receives the field's current state and should return the transformed state, like capitalizing the name in this example.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_16

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->required()
    ->dehydrateStateUsing(fn (string $state): string => ucwords($state));
```

----------------------------------------

TITLE: Injecting Table Instance in Column Closure - Filament PHP
DESCRIPTION: Illustrates how to access the current `Table` configuration object instance by defining a function parameter named `$table`, allowing interaction with the overall table configuration.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/12-advanced.md#_snippet_5

LANGUAGE: php
CODE:
```
use Filament\Tables\Table;

function (Table $table) {
    // ...
}
```

----------------------------------------

TITLE: FilamentPHP Dependant Select Options (Static)
DESCRIPTION: Dynamically updates the options of a select field based on the value of another select field. The `sub_category` field's `options()` method uses a function injecting `Get` to access the `category` field's value, returning a static array of options based on a PHP `match` statement.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_23

LANGUAGE: php
CODE:
```
use Filament\Forms\Get;
use Filament\Forms\Components\Select;

Select::make('category')
    ->options([
        'web' => 'Web development',
        'mobile' => 'Mobile development',
        'design' => 'Design',
    ])
    ->live()

Select::make('sub_category')
    ->options(fn (Get $get): array => match ($get('category')) {
        'web' => [
            'frontend_web' => 'Frontend development',
            'backend_web' => 'Backend development',
        ],
        'mobile' => [
            'ios_mobile' => 'iOS development',
            'android_mobile' => 'Android development',
        ],
        'design' => [
            'app_design' => 'Panel design',
            'marketing_website_design' => 'Marketing website design',
        ],
        default => [],
    })
```

----------------------------------------

TITLE: Using Built-in TrashedFilter for Soft Deletes in Filament (PHP)
DESCRIPTION: This snippet demonstrates the usage of Filament's built-in 'TrashedFilter', a specialized ternary filter for handling soft-deleted records. It simplifies filtering between trashed, non-trashed, and all records without custom query logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/03-ternary.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Filters\TrashedFilter;

TrashedFilter::make()
```

----------------------------------------

TITLE: Initializing Form with Existing Data (PHP)
DESCRIPTION: This PHP snippet shows how to pre-fill a Filament form with existing data, typically when editing a record. It passes the model's attributes to the `$this->form->fill()` method in the `mount()` lifecycle hook.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_6

LANGUAGE: php
CODE:
```
use App\Models\Post;

public function mount(Post $post): void
{
    $this->form->fill($post->attributesToArray());
}
```

----------------------------------------

TITLE: Conditionally Displaying Table Columns Responsively (Filament PHP)
DESCRIPTION: This snippet demonstrates how to control the visibility of a table column based on the responsive breakpoint of the browser. Using `visibleFrom('md')` ensures the 'slug' column is only shown on medium-sized devices and larger, improving mobile responsiveness.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/06-layout.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('slug')
    ->visibleFrom('md')
```

----------------------------------------

TITLE: Manually Upgrading Filament
DESCRIPTION: These commands manually update Filament: `composer update` fetches the latest package versions, and `php artisan filament:upgrade` clears Laravel caches and republishes frontend assets, completing the upgrade process.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/01-installation.md#_snippet_11

LANGUAGE: bash
CODE:
```
composer update

php artisan filament:upgrade
```

----------------------------------------

TITLE: Implementing Collapsible Content with Panel in Filament Tables (PHP)
DESCRIPTION: This code shows how to create collapsible content within Filament tables using the `Panel` component. It combines a `Split` layout for primary content (avatar, name) with a `Panel` containing a `Stack` of additional details (phone, email). The `collapsible()` method makes the panel expandable/collapsible, useful for hiding less critical data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/06-layout.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;

[
    Split::make([
        ImageColumn::make('avatar')
            ->circular(),
        TextColumn::make('name')
            ->weight(FontWeight::Bold)
            ->searchable()
            ->sortable(),
    ]),
    Panel::make([
        Stack::make([
            TextColumn::make('phone')
                ->icon('heroicon-m-phone'),
            TextColumn::make('email')
                ->icon('heroicon-m-envelope'),
        ]),
    ])->collapsible(),
]
```

----------------------------------------

TITLE: Importing BelongsTo Relationship in FilamentPHP (PHP)
DESCRIPTION: Configures an import column to map a CSV column (e.g., 'author') to a `BelongsTo` relationship in the database. The CSV should contain the primary keys (usually IDs) of the related records. Filament automatically adds validation to ensure the relationship is not empty if required.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_16

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Imports\ImportColumn;

ImportColumn::make('author')
    ->relationship()
```

----------------------------------------

TITLE: Creating a Basic Select Filter (PHP)
DESCRIPTION: This snippet demonstrates how to create a basic select filter in Filament using the `SelectFilter` class. It defines a filter for the 'status' column with predefined options like 'Draft', 'Reviewing', and 'Published', allowing users to select a single status to filter table records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/02-select.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Filters\SelectFilter;

SelectFilter::make('status')
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
```

----------------------------------------

TITLE: Customizing Import File Validation Rules in FilamentPHP (PHP)
DESCRIPTION: This snippet shows how to apply custom Laravel validation rules to the imported file itself using the fileRules() method on an ImportAction. It allows specifying constraints like maximum file size or allowed file types (e.g., CSV, TXT), ensuring only valid files are processed.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_57

LANGUAGE: PHP
CODE:
```
use Illuminate\Validation\Rules\File;

ImportAction::make()
    ->importer(ProductImporter::class)
    ->fileRules([
        'max:1024',
        // or
        File::types(['csv', 'txt'])->max(1024),
    ]),
```

----------------------------------------

TITLE: Modifying Base Eloquent Query for Filament Table Filters (PHP)
DESCRIPTION: This example shows how to modify the base Eloquent query directly for a filter, bypassing the default scoped `where()` clause. It uses `baseQuery()` with a closure to apply changes like removing global scopes, which is necessary for operations that require direct query manipulation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/01-getting-started.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\TernaryFilter;

TernaryFilter::make('trashed')
    // ...
    ->baseQuery(fn (Builder $query) => $query->withoutGlobalScopes([
        SoftDeletingScope::class,
    ]))
```

----------------------------------------

TITLE: Customizing Tenant Name with HasName Contract - FilamentPHP
DESCRIPTION: This code shows how to customize the display name of a tenant within the Filament application. By implementing the `HasName` contract on your tenant model and defining the `getFilamentName()` method, you can return a custom string that will be used to represent the tenant's name in the UI, potentially combining multiple attributes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_27

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Filament\Models\Contracts\HasName;
use Illuminate\Database\Eloquent\Model;

class Team extends Model implements HasName
{
    // ...

    public function getFilamentName(): string
    {
        return "{$this->name} {$this->subscription_plan}";
    }
}
```

----------------------------------------

TITLE: Configuring Image Storage Disk for Filament Image Entry (PHP)
DESCRIPTION: This snippet shows how to specify a custom storage disk for the `ImageEntry` component using the `disk()` method. By default, the `public` disk is used, but this example sets it to `s3` for retrieving images from an Amazon S3 bucket.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/04-image.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\ImageEntry;

ImageEntry::make('header_image')
    ->disk('s3')
```

----------------------------------------

TITLE: Rendering HTML Content in Filament TextColumn (PHP)
DESCRIPTION: This snippet demonstrates how to render HTML content directly within a Filament TextColumn. The `html()` method is used, which also sanitizes the HTML by default to prevent XSS vulnerabilities.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_25

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('description')
    ->html()
```

----------------------------------------

TITLE: Conditionally Hiding Actions in FilamentPHP (PHP)
DESCRIPTION: This example illustrates how to hide an action if the authenticated user lacks the 'update' permission for the 'post' model, achieving conditional visibility through the `hidden()` method with inverted logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/03-trigger-button.md#_snippet_12

LANGUAGE: PHP
CODE:
```
Action::make('edit')
    ->url(fn (): string => route('posts.edit', ['post' => $this->post]))
    ->hidden(! auth()->user()->can('update', $this->post))
```

----------------------------------------

TITLE: Displaying Related Data in Filament Export Column (PHP)
DESCRIPTION: This snippet illustrates how to access and display data from related Eloquent models using "dot notation" within an export column. By specifying `relationship.column_name`, Filament automatically fetches the related attribute for export.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_18

LANGUAGE: php
CODE:
```
use Filament\Actions\Exports\ExportColumn;

ExportColumn::make('author.name')
```

----------------------------------------

TITLE: Customizing Unique Validation Rule with Closure (PHP)
DESCRIPTION: This snippet demonstrates how to customize the `unique` validation rule using a closure passed to `modifyRuleUsing`. This allows adding complex conditions, such as `where` clauses, to the uniqueness check for more granular control.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_51

LANGUAGE: PHP
CODE:
```
use Illuminate\Validation\Rules\Unique;

Field::make('email')
    ->unique(modifyRuleUsing: function (Unique $rule) {
        return $rule->where('is_active', 1);
    })
```

----------------------------------------

TITLE: Creating Responsive Grid Layout with Section and TextEntry (PHP)
DESCRIPTION: This snippet demonstrates how to create a responsive grid layout within a Filament infolist section. It uses `Section::make()->columns()` to define varying column counts for different breakpoints and `TextEntry::make()->columnSpan()` to control how many columns a text entry spans, ensuring the layout adapts dynamically to screen sizes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/02-grid.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;

Section::make()
    ->columns([
        'sm' => 3,
        'xl' => 6,
        '2xl' => 8,
    ])
    ->schema([
        TextEntry::make('name')
            ->columnSpan([
                'sm' => 2,
                'xl' => 3,
                '2xl' => 4,
            ]),
        // ...
    ])
```

----------------------------------------

TITLE: Conditionally Dehydrating Hashed Password Field in Filament PHP
DESCRIPTION: This snippet refines the auto-hashing to prevent overwriting an existing password if the field is left empty during an edit operation. It uses the `dehydrated` method with a closure that checks if the field's state is filled using Laravel's `filled()` helper.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_28

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Hash;

TextInput::make('password')
    ->password()
    ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
    ->dehydrated(fn (?string $state): bool => filled($state))
```

----------------------------------------

TITLE: Installing Filament Actions Package
DESCRIPTION: This command installs the Filament Actions package, which enables the creation of versatile buttons that open modals. These modals are built on the Form Builder and can be used for various tasks like confirming destructive actions, editing records, or importing data, without requiring HTML or JavaScript.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/README.md#_snippet_4

LANGUAGE: bash
CODE:
```
composer require filament/actions
```

----------------------------------------

TITLE: Accessing Current Record in Livewire Component from Filament Infolist - PHP
DESCRIPTION: This snippet demonstrates how to access the current Eloquent record associated with the Filament infolist within the embedded Livewire component. The record can be received as a parameter in the mount() method or as a public $record property.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/06-advanced.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

class Foo extends Component
{
    public function mount(Model $record): void
    {
        // ...
    }

    // or

    public Model $record;
}
```

----------------------------------------

TITLE: Installing Filament Table Builder
DESCRIPTION: This command installs the Filament Table Builder, used for crafting optimized and interactive datatables for any situation. It can be dropped into a Livewire component and is fully extensible for custom columns, filters, and actions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
composer require filament/tables
```

----------------------------------------

TITLE: Grouping Navigation Items PHP FilamentPHP
DESCRIPTION: Group navigation items under a common label in FilamentPHP by setting the `$navigationGroup` static property to the desired group name string.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/06-navigation.md#_snippet_9

LANGUAGE: php
CODE:
```
protected static ?string $navigationGroup = 'Settings';
```

----------------------------------------

TITLE: Customizing New Option Creation Logic in Filament Select (PHP)
DESCRIPTION: This Filament PHP code customizes the record creation process for a `Select` field's `createOptionForm` using the `createOptionUsing()` method. It provides a callback function to handle the data from the form, allowing for custom logic like associating the new record with the authenticated user's team, and must return the primary key of the newly created record.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_21

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->createOptionForm([
       // ...
    ])
    ->createOptionUsing(function (array $data): int {
        return auth()->user()->team->members()->create($data)->getKey();
    }),
```

----------------------------------------

TITLE: Creating a Blade Layout for Livewire Components
DESCRIPTION: This Blade template defines a basic HTML layout for Livewire components, including meta tags, application name, CSRF token, viewport settings, and placeholders for Filament styles and scripts, as well as Vite assets.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/01-installation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">

        <meta name="application-name" content="{{ config('app.name') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        @vite('resources/css/app.css')
    </head>

    <body class="antialiased">
        {{ $slot }}

        @filamentScripts
        @vite('resources/js/app.js')
    </body>
</html>
```

----------------------------------------

TITLE: Moving Create Form Action to FilamentPHP Page Header (PHP)
DESCRIPTION: This PHP snippet demonstrates how to relocate the default 'Create' form action button to the header of a FilamentPHP page. By overriding the `getHeaderActions()` method, it retrieves the `getCreateFormAction()` and explicitly associates it with the form by passing `formId('form')`. This allows for customizing the placement of standard form submission buttons.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_18

LANGUAGE: php
CODE:
```
protected function getHeaderActions(): array
{
    return [
        $this->getCreateFormAction()
            ->formId('form'),
    ];
}
```

----------------------------------------

TITLE: Nesting Fields within a Filament Form Section (PHP)
DESCRIPTION: This example illustrates how to nest specific form fields, such as Select and DateTimePicker, within a Section component's schema() method in Filament. It demonstrates structuring related fields under a common heading and description provided by the Section component. This improves form organization and user experience.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/02-getting-started.md#_snippet_4

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;

Section::make('Publishing')
    ->description('Settings for publishing this post.')
    ->schema([
        Select::make('status')
            ->options([
                'draft' => 'Draft',
                'reviewing' => 'Reviewing',
                'published' => 'Published',
            ]),
        DateTimePicker::make('published_at'),
    ])
```

----------------------------------------

TITLE: Customizing Validation Error Messages for Filament Fields (PHP)
DESCRIPTION: This snippet illustrates how to override default Laravel validation error messages for specific rules on a Filament form field. The `validationMessages()` method allows developers to provide custom messages, such as for the 'unique' rule, enhancing user feedback.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_59

LANGUAGE: PHP
CODE:
```
use FilamentFormsComponentsTextInput;

TextInput::make('email')
    ->unique(// ...)
    ->validationMessages([
        'unique' => 'The :attribute has already been registered.',
    ])
```

----------------------------------------

TITLE: Using Built-in Data Casting Methods for Import Columns (PHP)
DESCRIPTION: This snippet showcases Filament's built-in casting methods for import columns, simplifying data type conversion. `numeric()` casts to a float (with optional decimal places), `integer()` casts to an integer, and `boolean()` casts to a boolean, ensuring data is in the correct format for validation and database storage.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Imports\ImportColumn;

ImportColumn::make('price')
    ->numeric() // Casts the state to a float.

ImportColumn::make('price')
    ->numeric(decimalPlaces: 2) // Casts the state to a float, and rounds it to 2 decimal places.

ImportColumn::make('quantity')
    ->integer() // Casts the state to an integer.

ImportColumn::make('is_visible')
    ->boolean() // Casts the state to a boolean.
```

----------------------------------------

TITLE: Calculating Export Column State in Filament
DESCRIPTION: This PHP code illustrates how to use the `state()` method with a callback function on an `ExportColumn`. This allows for dynamically calculating the value of an exported column based on the record's attributes, rather than directly reading a database field.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_12

LANGUAGE: php
CODE:
```
use App\Models\Order;
use Filament\Actions\Exports\ExportColumn;

ExportColumn::make('amount_including_vat')
    ->state(function (Order $record): float {
        return $record->amount * (1 + $record->vat_rate);
    })
```

----------------------------------------

TITLE: Enabling Multi-Step Wizard for Filament Create Record Page
DESCRIPTION: This PHP snippet shows how to transform a Filament record creation page into a multi-step wizard. It involves extending `CreateRecord` and using the `CreateRecord\Concerns\HasWizard` trait, along with defining the `getSteps()` method to structure the wizard.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_10

LANGUAGE: php
CODE:
```
use App\Filament\Resources\CategoryResource;
use Filament\Resources\Pages\CreateRecord;

class CreateCategory extends CreateRecord
{
    use CreateRecord\Concerns\HasWizard;
    
    protected static string $resource = CategoryResource::class;

    protected function getSteps(): array
    {
        return [
            // ...
        ];
    }
}
```

----------------------------------------

TITLE: Using afterStateHydrated Hook for State Modification - Filament PHP
DESCRIPTION: Shows how to use the `afterStateHydrated()` method to execute custom logic immediately after a field's state has been filled (hydrated), typically from a database record. The example demonstrates modifying the state (capitalizing the name) using the injected component instance (`$component`). The closure receives the component instance and the initial state.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_13

LANGUAGE: php
CODE:
```
use Closure;
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->required()
    ->afterStateHydrated(function (TextInput $component, string $state) {
        $component->state(ucwords($state));
    });
```

----------------------------------------

TITLE: Populating Filament Chart Data with Laravel Trend - PHP
DESCRIPTION: This PHP method, `getData()`, retrieves and formats treatment data for a Filament chart. It uses the `Flowframe\Trend` package to count `Treatment` records per month over the past year, mapping the aggregated values and dates to the `datasets` and `labels` arrays required by Chart.js.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_32

LANGUAGE: php
CODE:
```
use App\Models\Treatment;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

protected function getData(): array
{
    $data = Trend::model(Treatment::class)
        ->between(
            start: now()->subYear(),
            end: now(),
        )
        ->perMonth()
        ->count();

    return [
        'datasets' => [
            [
                'label' => 'Treatments',
                'data' => $data->map(fn (TrendValue $value) => $value->aggregate),
            ],
        ],
        'labels' => $data->map(fn (TrendValue $value) => $value->date),
    ];
}
```

----------------------------------------

TITLE: Confirming Repeater Delete Action with Modal in FilamentPHP
DESCRIPTION: This snippet demonstrates how to add a confirmation modal to a repeater's delete action in FilamentPHP. By calling `requiresConfirmation()` on the action object, a modal will appear before the action is executed, allowing for user confirmation. This enhances user experience by preventing accidental deletions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_33

LANGUAGE: PHP
CODE:
```
use FilamentFormsComponentsActionsAction;
use FilamentFormsComponentsRepeater;

Repeater::make('members')
    ->schema([
        // ...
    ])
    ->deleteAction(
        fn (Action $action) => $action->requiresConfirmation(),
    )
```

----------------------------------------

TITLE: Adding Extra HTML Attributes to Filament Stats - PHP
DESCRIPTION: This code demonstrates using `extraAttributes()` to add custom HTML attributes to a stat, such as CSS classes or Livewire directives. This allows for advanced styling or interactive behavior directly on the stat element.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/02-stats-overview.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Filament\Widgets\StatsOverviewWidget\Stat;

protected function getStats(): array
{
    return [
        Stat::make('Processed', '192.1k')
            ->color('success')
            ->extraAttributes([
                'class' => 'cursor-pointer',
                'wire:click' => "\$dispatch('setStatusFilter', { filter: 'processed' })",
            ]),
        // ...
    ];
}
```

----------------------------------------

TITLE: Dynamic Redirect After Creation with Filament CreateAction (PHP)
DESCRIPTION: This example illustrates how to create a dynamic redirect URL after a record is created, leveraging the newly created Eloquent model (`$record`). This allows for redirects to specific pages related to the created record, such as an edit page.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/01-create.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

CreateAction::make()
    ->successRedirectUrl(fn (Model $record): string => route('posts.edit', [
        'post' => $record,
    ]))
```

----------------------------------------

TITLE: Limiting the number of options in Filament Select
DESCRIPTION: The `optionsLimit()` method controls the maximum number of options displayed in a searchable select or multi-select. The default limit is 50, and increasing it significantly may cause performance degradation due to high in-browser memory usage.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_38

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->searchable()
    ->optionsLimit(20)
```

----------------------------------------

TITLE: Custom Casting CSV Data for Import Columns (PHP)
DESCRIPTION: This snippet demonstrates custom data casting for an import column using `castStateUsing()`. It shows how to convert a string state (e.g., 'price') into a float, removing non-numeric characters and rounding to two decimal places, which is essential for correct validation and storage. The function receives the string state and returns the casted value.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_12

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Imports\ImportColumn;

ImportColumn::make('price')
    ->castStateUsing(function (string $state): ?float {
        if (blank($state)) {
            return null;
        }
        
        $state = preg_replace('/[^0-9.]/', '', $state);
        $state = floatval($state);
    
        return round($state, precision: 2);
    })
```

----------------------------------------

TITLE: Globally Configuring Phone Number Regex for TextInput in Filament PHP
DESCRIPTION: This snippet demonstrates how to apply a custom phone number validation regex globally to all `TextInput` components. By using `TextInput::configureUsing()` within a service provider, a consistent `telRegex()` can be set across the entire application, simplifying validation management.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/02-text-input.md#_snippet_21

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::configureUsing(function (TextInput $component): void {
    $component->telRegex('/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\.\/0-9]*$/');
});
```

----------------------------------------

TITLE: Creating an Icon Column with Dynamic Icons (PHP)
DESCRIPTION: This snippet demonstrates how to create an icon column in Filament tables. It uses a closure to dynamically determine the icon based on the column's `$state` value, mapping different status strings to specific Heroicons. The `$record` variable is also available within the closure for accessing the full Eloquent record.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/03-icon.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\IconColumn;

IconColumn::make('status')
    ->icon(fn (string $state): string => match ($state) {
        'draft' => 'heroicon-o-pencil',
        'reviewing' => 'heroicon-o-clock',
        'published' => 'heroicon-o-check-circle',
    })
```

----------------------------------------

TITLE: Implementing Filament DeleteAction and DeleteBulkAction in a Table (PHP)
DESCRIPTION: Shows how to add the DeleteAction to table actions for individual row deletion and the DeleteBulkAction within a BulkActionGroup for deleting multiple selected rows in a Filament table.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/04-delete.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->actions([
            DeleteAction::make(),
            // ...
        ])
        ->bulkActions([
            BulkActionGroup::make([
                DeleteBulkAction::make(),
                // ...
            ]),
        ]);
}
```

----------------------------------------

TITLE: Generating Chart Data from Eloquent Model with Laravel-Trend
DESCRIPTION: This PHP snippet demonstrates how to dynamically generate chart data from an Eloquent model (BlogPost) using the flowframe/laravel-trend package. It queries data for a specific period, aggregates it by month, and then maps the results into the datasets and labels format required by Filament charts.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/03-charts.md#_snippet_4

LANGUAGE: php
CODE:
```
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

protected function getData(): array
{
    $data = Trend::model(BlogPost::class)
        ->between(
            start: now()->startOfYear(),
            end: now()->endOfYear(),
        )
        ->perMonth()
        ->count();

    return [
        'datasets' => [
            [
                'label' => 'Blog posts',
                'data' => $data->map(fn (TrendValue $value) => $value->aggregate)
            ]
        ],
        'labels' => $data->map(fn (TrendValue $value) => $value->date)
    ];
}
```

----------------------------------------

TITLE: Rendering Filament Form in Livewire View (Blade)
DESCRIPTION: This Blade snippet shows how to render the Filament form within the Livewire component's view. It uses `wire:submit` to bind the form submission to the `create` method and includes a placeholder for Filament action modals.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_4

LANGUAGE: blade
CODE:
```
<div>
    <form wire:submit="create">
        {{ $this->form }}
        
        <button type="submit">
            Submit
        </button>
    </form>
    
    <x-filament-actions::modals />
</div>
```

----------------------------------------

TITLE: Creating Relationship-Based Select Filters (PHP)
DESCRIPTION: This code demonstrates creating a select filter that automatically populates options from a related model. It sets up a filter for 'author' by leveraging the 'author' relationship and displaying the 'name' column from the related model, allowing filtering based on associated records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/02-select.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Filters\SelectFilter;

SelectFilter::make('author')
    ->relationship('author', 'name')
```

----------------------------------------

TITLE: Asserting Filament Form Data (PHP)
DESCRIPTION: This example illustrates how to assert that a Filament form's state matches expected data using `assertFormSet()` in a Pest test. After filling the form with a title, it verifies that the 'slug' field was automatically generated and set correctly based on the title. This helper is crucial for ensuring data transformation or default values are applied as expected. For multiple forms, a second parameter can specify the form name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/09-testing.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Illuminate\Support\Str;
use function Pest\Livewire\livewire;

it('can automatically generate a slug from the title', function () {
    $title = fake()->sentence();

    livewire(CreatePost::class)
        ->fillForm([
            'title' => $title,
        ])
        ->assertFormSet([
            'slug' => Str::slug($title),
        ]);
});
```

----------------------------------------

TITLE: Casting Toggle Button Multiple Selections to Array in Eloquent (PHP)
DESCRIPTION: Provides an example of how to cast a model property to an `array` in an Eloquent model. This is crucial for correctly storing multiple selected values from a `ToggleButtons` component that uses the `multiple()` method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/18-toggle-buttons.md#_snippet_7

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

class App extends Model
{
    protected $casts = [
        'technologies' => 'array',
    ];

    // ...
}
```

----------------------------------------

TITLE: Adding View Pages to Resource Sub-navigation in Filament
DESCRIPTION: This PHP method registers a view page within a Filament resource's sub-navigation, making it accessible through the sub-navigation menu. It integrates custom view pages seamlessly into the resource's navigation structure.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_12

LANGUAGE: php
CODE:
```
use App\Filament\Resources\CustomerResource\Pages;
use Filament\Resources\Pages\Page;

public static function getRecordSubNavigation(Page $page): array
{
    return $page->generateNavigationItems([
        // ...
        Pages\ViewCustomerContact::class,
    ]);
}
```

----------------------------------------

TITLE: Validating ColorPicker Values with Regex in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to apply Laravel's validation rules, specifically regex, to the Filament ColorPicker component. It provides examples for validating HEX, HSL, RGB, and RGBA color formats, ensuring that user inputs conform to the expected color string patterns.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/17-color-picker.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\ColorPicker;

ColorPicker::make('hex_color')
    ->regex('/^#([a-f0-9]{6}|[a-f0-9]{3})\b$/')

ColorPicker::make('hsl_color')
    ->hsl()
    ->regex('/^hsl\(\s*(\d+)\s*,\s*(\d*(?:\.\d+)?%)\s*,\s*(\d*(?:\.\d+)?%)\)$/')

ColorPicker::make('rgb_color')
    ->rgb()
    ->regex('/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/')

ColorPicker::make('rgba_color')
    ->rgba()
    ->regex('/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*(\d*(?:\.\d+)?)\)$/')
```

----------------------------------------

TITLE: Enabling Individual Filament Column Search PHP
DESCRIPTION: Adds a separate search input field directly under the column header for individual column searching by setting the `isIndividual` parameter of the `searchable()` method to `true`. Requires `Filament\Tables\Columns\TextColumn`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_14

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('name')
    ->searchable(isIndividual: true);
```

----------------------------------------

TITLE: Testing Filament Table Record Visibility (PHP)
DESCRIPTION: Shows how to test which records are displayed in a Filament table using `assertCanSeeTableRecords()`, `assertCanNotSeeTableRecords()`, and `assertCountTableRecords()`. It includes creating test data with both visible and trashed records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/12-testing.md#_snippet_1

LANGUAGE: php
CODE:
```
use function Pest\Livewire\livewire;

it('cannot display trashed posts by default', function () {
    $posts = Post::factory()->count(4)->create();
    $trashedPosts = Post::factory()->trashed()->count(6)->create();

    livewire(PostResource\Pages\ListPosts::class)
        ->assertCanSeeTableRecords($posts)
        ->assertCanNotSeeTableRecords($trashedPosts)
        ->assertCountTableRecords(4);
});
```

----------------------------------------

TITLE: Using SelectConstraint in Filament Query Builder PHP
DESCRIPTION: Shows how to create a SelectConstraint for a direct column (`status`) and how to use it to filter a column (`department`) on a related model (`creator`) via the `relationship` method, defining the available options for selection.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/04-query-builder.md#_snippet_7

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\QueryBuilder\Constraints\SelectConstraint;

SelectConstraint::make('status') // Filter the `status` column
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
    
SelectConstraint::make('creatorStatus')
    ->relationship(name: 'creator', titleAttribute: 'department') // Filter the `department` column on the `creator` relationship
    ->options([
        'sales' => 'Sales',
        'marketing' => 'Marketing',
        'engineering' => 'Engineering',
        'purchasing' => 'Purchasing',
    ])
```

----------------------------------------

TITLE: Customizing Authentication Route Slugs PHP
DESCRIPTION: Modify the URL segments for Filament's built-in authentication routes using dedicated methods like `loginRouteSlug`, `registrationRouteSlug`, `passwordResetRoutePrefix`, etc., in the panel configuration.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/08-users.md#_snippet_11

LANGUAGE: php
CODE:
```
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->loginRouteSlug('login')
        ->registrationRouteSlug('register')
        ->passwordResetRoutePrefix('password-reset')
        ->passwordResetRequestRouteSlug('request')
        ->passwordResetRouteSlug('reset')
        ->emailVerificationRoutePrefix('email-verification')
        ->emailVerificationPromptRouteSlug('prompt')
        ->emailVerificationRouteSlug('verify');
}
```

----------------------------------------

TITLE: Deleting a Record with Confirmation in Filament PHP
DESCRIPTION: This snippet shows how to create a Filament action to delete a record. It uses `requiresConfirmation()` to prompt the user before execution and defines the deletion logic within the `action()` callback, which deletes the associated client record.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/02-overview.md#_snippet_0

LANGUAGE: PHP
CODE:
```
Action::make('delete')
    ->requiresConfirmation()
    ->action(fn () => $this->client->delete())
```

----------------------------------------

TITLE: Enabling Inline Owner Creation in Patient Form (PHP)
DESCRIPTION: This snippet extends the 'owner_id' select field to allow users to create new owners without leaving the current page. It utilizes 'createOptionForm()' to embed a modal form with 'TextInput' fields for the owner's 'name', 'email', and 'phone', including validation and custom labels.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_12

LANGUAGE: php
CODE:
```
use Filament\Forms;

Forms\Components\Select::make('owner_id')
    ->relationship('owner', 'name')
    ->searchable()
    ->preload()
    ->createOptionForm([
        Forms\Components\TextInput::make('name')
            ->required()
            ->maxLength(255),
        Forms\Components\TextInput::make('email')
            ->label('Email address')
            ->email()
            ->required()
            ->maxLength(255),
        Forms\Components\TextInput::make('phone')
            ->label('Phone number')
            ->tel()
            ->required(),
    ])
    ->required()
```

----------------------------------------

TITLE: Casting Multi-Select Values to Array in Eloquent (PHP)
DESCRIPTION: When using a multi-select field in Filament, the selected options are returned as JSON. This Eloquent model snippet shows how to properly cast the `technologies` attribute to an `array`, ensuring that the data is automatically serialized and deserialized correctly when interacting with the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_5

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

class App extends Model
{
    protected $casts = [
        'technologies' => 'array',
    ];

    // ...
}
```

----------------------------------------

TITLE: Marking TextInput as Required and Hiding Asterisk in Filament (PHP)
DESCRIPTION: This example shows how to make a `TextInput` field required using the `required()` method, which adds validation. Additionally, it demonstrates how to hide the default asterisk indicator for required fields by passing `false` to the `markAsRequired()` method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_24

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->required() // Adds validation to ensure the field is required
    ->markAsRequired(false) // Removes the asterisk
```

----------------------------------------

TITLE: Grouping Multiple Table Actions in Filament PHP
DESCRIPTION: This snippet demonstrates how to group multiple prebuilt table actions (View, Edit, Delete) into a single dropdown using `ActionGroup::make()` within a Filament `Table` definition. This approach simplifies the user interface by consolidating related actions under one clickable element.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->actions([
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ]),
            // ...
        ]);
}
```

----------------------------------------

TITLE: Rendering Custom View Columns in Filament Tables (PHP)
DESCRIPTION: This snippet demonstrates how to use the `ViewColumn` class in Filament to render a custom Blade view for a table cell. It specifies the path to the Blade file that will be used to display the column's content, allowing for highly customized cell rendering.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/10-custom.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\ViewColumn;

ViewColumn::make('status')->view('filament.tables.columns.status-switcher')
```

----------------------------------------

TITLE: Initializing a Basic Textarea Field in Filament
DESCRIPTION: This snippet demonstrates the basic initialization of a Textarea component in Filament. It creates a multi-line text input field named 'description' for form interaction.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/15-textarea.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Textarea;

Textarea::make('description')
```

----------------------------------------

TITLE: Overriding Export Query in Filament Exporter Class (PHP)
DESCRIPTION: This code demonstrates how to globally modify the export query for all actions that use a particular exporter class. By overriding the static `modifyQuery()` method, you can apply consistent query modifications, including eager loading relationships, across all exports using this exporter.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_29

LANGUAGE: PHP
CODE:
```
use IlluminateDatabaseEloquentBuilder;
use IlluminateDatabaseEloquentRelationsMorphTo;

public static function modifyQuery(Builder $query): Builder
{
    return $query->with([
        'purchasable' => fn (MorphTo $morphTo) => $morphTo->morphWith([
            ProductPurchase::class => ['product'],
            ServicePurchase::class => ['service'],
            Subscription::class => ['plan'],
        ]),
    ]);
}
```

----------------------------------------

TITLE: Returning Raw JavaScript for Chart.js Options (Filament PHP)
DESCRIPTION: This PHP method demonstrates returning raw JavaScript for Chart.js options using Filament's RawJs object. This is particularly useful for implementing JavaScript callback functions, such as custom tick formatting for chart scales, which cannot be expressed directly in a PHP array.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/03-charts.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use Filament\Support\RawJs;

protected function getOptions(): RawJs
{
    return RawJs::make(<<<JS
        {
            scales: {
                y: {
                    ticks: {
                        callback: (value) => '€' + value,
                    },
                },
            },
        }
    JS);
}
```

----------------------------------------

TITLE: Configuring Tailwind CSS for Filament
DESCRIPTION: This JavaScript configuration file sets up Tailwind CSS to use Filament's preset, which includes its color scheme and required plugins. It also defines content paths for Tailwind to scan for classes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/01-installation.md#_snippet_4

LANGUAGE: javascript
CODE:
```
import preset from './vendor/filament/support/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        './app/Filament/**/*.php',
        './resources/views/filament/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
}
```

----------------------------------------

TITLE: Binding Livewire Properties to Input Fields (Blade)
DESCRIPTION: This Blade snippet demonstrates two common ways to bind a Livewire component's public property (e.g., `$name`) to an HTML input field. It shows binding directly with `wire:model` and also by entangling with an Alpine.js property using `$wire.$entangle()`, which is foundational to how Filament fields manage their state.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/20-custom.md#_snippet_4

LANGUAGE: blade
CODE:
```
<input wire:model="name" />

<!-- Or -->

<div x-data="{ state: $wire.$entangle('name') }">
    <input x-model="state" />
</div>
```

----------------------------------------

TITLE: Registering Additional Tailwind Colors (PHP)
DESCRIPTION: This snippet shows how to register extra colors beyond the default six, such as 'indigo', using the FilamentColor::register() method and a Color constant. Once registered, these additional colors can be used throughout Filament UI elements, similar to the primary or danger colors.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/04-colors.md#_snippet_4

LANGUAGE: php
CODE:
```
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentColor;

FilamentColor::register([
    'indigo' => Color::Indigo,
]);
```

----------------------------------------

TITLE: Customizing Initial Relation Manager Form and Table - PHP
DESCRIPTION: This PHP code shows the initial structure of the `form()` and `table()` methods within the `TreatmentsRelationManager`. It defines a basic form with a `description` text input and a table column for the `description`, prepopulated by the Artisan command. This serves as a starting point for further customization of the relation manager's UI.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_19

LANGUAGE: php
CODE:
```
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\TextInput::make('description')
                ->required()
                ->maxLength(255),
        ]);
}

public function table(Table $table): Table
{
    return $table
        ->columns([
            Tables\Columns\TextColumn::make('description'),
        ]);
}
```

----------------------------------------

TITLE: Resizing and Cropping Images Before Upload in Filament (PHP)
DESCRIPTION: This snippet shows how to perform client-side image resizing and cropping using Filepond's capabilities, without a separate editor. It uses `imageResizeMode()`, `imageCropAspectRatio()`, `imageResizeTargetWidth()`, and `imageResizeTargetHeight()` to define the pre-upload transformation rules.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_16

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\FileUpload;

FileUpload::make('image')
    ->image()
    ->imageResizeMode('cover')
    ->imageCropAspectRatio('16:9')
    ->imageResizeTargetWidth('1920')
    ->imageResizeTargetHeight('1080')
```

----------------------------------------

TITLE: Dynamically Setting SelectFilter Options (PHP)
DESCRIPTION: This snippet shows how to dynamically populate options for a `SelectFilter` using a closure. The closure queries the `Author` model to retrieve names and IDs, providing a flexible way to manage filter options.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/01-getting-started.md#_snippet_13

LANGUAGE: PHP
CODE:
```
use App\Models\Author;
use Filament\Tables\Filters\SelectFilter;

SelectFilter::make('author')
    ->options(fn (): array => Author::query()->pluck('name', 'id')->all())
```

----------------------------------------

TITLE: Saving Form Data to a HasOne/BelongsTo/MorphOne Relationship Using Fieldset in Filament PHP
DESCRIPTION: This snippet demonstrates how to use a Filament layout component, specifically `Fieldset`, to group fields and automatically load/save their data to a related Eloquent model. The `relationship()` method specifies the relation name, and the nested fields in `schema` correspond to columns on the related model.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_30

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;

Fieldset::make('Metadata')
    ->relationship('metadata')
    ->schema([
        TextInput::make('title'),
        Textarea::make('description'),
        FileUpload::make('image'),
    ])
```

----------------------------------------

TITLE: Attaching URL Callback to Filament Column PHP
DESCRIPTION: Configures a column cell to act as a clickable link that navigates to a URL. The URL is generated dynamically for each record using a callback function provided to the `url()` method. Requires `Filament\Tables\Columns\TextColumn`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_22

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title')
    ->url(fn (Post $record): string => route('posts.edit', ['post' => $record]));
```

----------------------------------------

TITLE: Conditionally Saving Data to a Relationship in Filament PHP
DESCRIPTION: This snippet illustrates how to make saving data to a relationship optional based on the state of the related form fields. The `relationship()` method accepts a `condition` closure that receives the related model's state and returns a boolean indicating whether the relationship should be saved.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_32

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Group;
use Filament\Forms\Components\TextInput;

Group::make()
    ->relationship(
        'customer',
        condition: fn (?array $state): bool => filled($state['name']),
    )
    ->schema([
        TextInput::make('name')
            ->label('Customer'),
        TextInput::make('email')
            ->label('Email address')
            ->email()
            ->requiredWith('name'),
    ])
```

----------------------------------------

TITLE: Initializing Date/Time Pickers in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to initialize various date and time picker components in Filament forms. It shows the basic usage of `DateTimePicker`, `DatePicker`, and `TimePicker` to create fields for 'published_at', 'date_of_birth', and 'alarm_at' respectively.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/08-date-time-picker.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\TimePicker;

DateTimePicker::make('published_at');
DatePicker::make('date_of_birth');
TimePicker::make('alarm_at');
```

----------------------------------------

TITLE: Using Lifecycle Hooks for Wizard Steps in PHP
DESCRIPTION: This code illustrates the use of `afterValidation()` and `beforeValidation()` methods to execute custom logic before and after a wizard step's validation process. These hooks are useful for performing actions or data manipulations at specific points in the step's lifecycle.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/05-wizard.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Wizard;

Wizard\Step::make('Order')
    ->afterValidation(function () {
        // ...
    })
    ->beforeValidation(function () {
        // ...
    })
    ->schema([
        // ...
    ])
```

----------------------------------------

TITLE: Creating a Custom Filament Page using Artisan
DESCRIPTION: This command generates the necessary files for a new custom Filament page. It specifies the page name (`SortUsers`), associates it with a resource (`UserResource`), and defines its type as `custom`. This sets up the basic structure for a new page class and its corresponding view.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/10-custom-pages.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:filament-page SortUsers --resource=UserResource --type=custom
```

----------------------------------------

TITLE: Interacting with a Resource Record on a Custom Page
DESCRIPTION: This PHP code illustrates the use of the `InteractsWithRecord` trait in a custom Filament page class (`ManageUser`). The `mount()` method resolves the record from the URL parameter, making it accessible via `$this->record` or `$this->getRecord()` for pages that need to operate on a specific resource entry.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/10-custom-pages.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;

class ManageUser extends Page
{
    use InteractsWithRecord;
    
    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
    }

    // ...
}
```

----------------------------------------

TITLE: Injecting Modal Form Data into Action Function - PHP
DESCRIPTION: This snippet illustrates how to inject the current modal form data into an action's configuration function. By defining a `$data` parameter of type `array`, the function receives the submitted data from the modal. It's important to note that `$data` will be empty if the modal has not yet been submitted.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/08-advanced.md#_snippet_1

LANGUAGE: php
CODE:
```
function (array $data) {
    // ...
}
```

----------------------------------------

TITLE: Before Date Validation in Filament PHP
DESCRIPTION: Ensures the field's date value occurs before a specified date or the value of another field. This is useful for setting chronological constraints, such as a start date being before an end date. The rule can accept a specific date string or the name of another field for comparison.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_7

LANGUAGE: php
CODE:
```
Field::make('start_date')->before('first day of next month')
```

LANGUAGE: php
CODE:
```
Field::make('start_date')->before('end_date')
Field::make('end_date')
```

----------------------------------------

TITLE: Defining Relationship Constraint with Selectable Operator in Filament
DESCRIPTION: This snippet demonstrates how to create a relationship constraint for the 'creator' relationship. It uses the `selectable()` method with `IsRelatedToOperator` to allow filtering by related records. Key methods like `titleAttribute()` define the display field, `searchable()` enables searching, and `multiple()` allows selecting multiple related records for filtering.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/04-query-builder.md#_snippet_10

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\QueryBuilder\Constraints\RelationshipConstraint;
use Filament\Tables\Filters\QueryBuilder\Constraints\RelationshipConstraint\Operators\IsRelatedToOperator;

RelationshipConstraint::make('creator') // Filter the `creator` relationship
    ->selectable(
        IsRelatedToOperator::make()
            ->titleAttribute('name')
            ->searchable()
            ->multiple(),
    );
```

----------------------------------------

TITLE: Scoping Relationship Query in Filament Constraint
DESCRIPTION: This snippet demonstrates how to scope the query used to filter related records within a constraint's `relationship()` method. The `modifyQueryUsing` closure receives the Eloquent `Builder` for the related model, allowing you to add conditions, such as only considering 'creator' records where `is_admin` is true.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/04-query-builder.md#_snippet_15

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\QueryBuilder\Constraints\TextConstraint;
use Illuminate\Database\Eloquent\Builder;

TextConstraint::make('adminCreatorName')
    ->relationship(
        name: 'creator',
        titleAttribute: 'name',
        modifyQueryUsing: fn (Builder $query) => $query->where('is_admin', true),
    );
```

----------------------------------------

TITLE: Asserting Form Component Action Visibility in PHP
DESCRIPTION: This test illustrates how to assert the visibility or hidden state of form component actions using `assertFormComponentActionHidden()` and `assertFormComponentActionVisible()`. It checks that the 'send' action is hidden and the 'print' action is visible for a specific customer ID on an invoice editing form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/09-testing.md#_snippet_29

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can only print customers', function () {
    $invoice = Invoice::factory()->create();

    livewire(EditInvoice::class, [
        'invoice' => $invoice,
    ])
        ->assertFormComponentActionHidden('customer_id', 'send')
        ->assertFormComponentActionVisible('customer_id', 'print');
});
```

----------------------------------------

TITLE: Defining Infolist for Record View (PHP)
DESCRIPTION: This PHP method defines an infolist for a Filament resource, replacing the default disabled form view. It specifies the structure and content of the infolist using TextEntry components to display 'name', 'email', and 'notes' fields, with 'notes' spanning the full column width. This provides a read-only, structured display of record data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Infolists;
use Filament\Infolists\Infolist;

public static function infolist(Infolist $infolist): Infolist
{
    return $infolist
        ->schema([
            Infolists\Components\TextEntry::make('name'),
            Infolists\Components\TextEntry::make('email'),
            Infolists\Components\TextEntry::make('notes')
                ->columnSpanFull(),
        ]);
}
```

----------------------------------------

TITLE: Registering View Page in Resource (PHP)
DESCRIPTION: This PHP method registers the newly created 'ViewUser' page within the Filament resource's page collection. It maps the 'view' key to the `ViewUser` page and defines its route as `/{record}`, making the view page accessible for individual records. This is a crucial step to integrate the new page into the resource's navigation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_3

LANGUAGE: php
CODE:
```
public static function getPages(): array
{
    return [
        'index' => Pages\ListUsers::route('/'),
        'create' => Pages\CreateUser::route('/create'),
        'view' => Pages\ViewUser::route('/{record}'),
        'edit' => Pages\EditUser::route('/{record}/edit'),
    ];
}
```

----------------------------------------

TITLE: Requiring Filament Infolists Package with Composer
DESCRIPTION: This command adds the Filament Infolist Builder package to your project's dependencies using Composer. The `-W` flag resolves conflicts by updating other packages if necessary.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/01-installation.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require filament/infolists:"^3.3" -W
```

----------------------------------------

TITLE: Adding Tailwind CSS Layers to Application Stylesheet
DESCRIPTION: These directives are added to `resources/css/app.css` to include Tailwind's base styles, components, utilities, and variants, ensuring that all Tailwind CSS features are properly integrated and applied in the application.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_5

LANGUAGE: css
CODE:
```
@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;
```

----------------------------------------

TITLE: Implementing Patient Type Statistics in Filament Widget
DESCRIPTION: This PHP code defines the `getStats()` method within the `PatientTypeOverview` widget. It returns an array of `Stat` instances, each representing a count of patients filtered by their `type` (cat, dog, rabbit), providing a quick overview of patient demographics on the dashboard.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_29

LANGUAGE: php
CODE:
```
<?php

namespace App\Filament\Widgets;

use App\Models\Patient;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class PatientTypeOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Cats', Patient::query()->where('type', 'cat')->count()),
            Stat::make('Dogs', Patient::query()->where('type', 'dog')->count()),
            Stat::make('Rabbits', Patient::query()->where('type', 'rabbit')->count()),
        ];
    }
}
```

----------------------------------------

TITLE: Asserting Filament Form Field Existence (PHP)
DESCRIPTION: This snippet demonstrates how to assert the existence of a specific field within a Filament form using `assertFormFieldExists()`. It checks if the 'title' field is present in the form of the `CreatePost` component. This helper is valuable for verifying the structural integrity of a form and ensuring all expected fields are rendered. For multiple forms, a second parameter can specify the form name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/09-testing.md#_snippet_6

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('has a title field', function () {
    livewire(CreatePost::class)
        ->assertFormFieldExists('title');
});
```

----------------------------------------

TITLE: Creating Custom Forms in Filament Action Modals (PHP)
DESCRIPTION: This code illustrates how to embed a custom form within a Filament action's modal to collect additional user input. It uses a `Select` component from the Form Builder to choose an author. The collected form data is passed as an `$data` array to the `action()` closure, allowing the record to be updated based on user input.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use AppModelsPost;
use AppModelsUser;
use FilamentFormsComponentsSelect;

Action::make('updateAuthor')
    ->form([
        Select::make('authorId')
            ->label('Author')
            ->options(User::query()->pluck('name', 'id'))
            ->required(),
    ])
    ->action(function (array $data, Post $record): void {
        $record->author()->associate($data['authorId']);
        $record->save();
    })
```

----------------------------------------

TITLE: Conditionally Hiding a Filament Form Field (PHP)
DESCRIPTION: Shows how to conditionally hide a Filament form field by passing a boolean value to the `hidden()` method, enabling dynamic visibility based on application logic or user roles.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_21

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->hidden(! auth()->user()->isAdmin())
```

----------------------------------------

TITLE: Enum Value Validation in Filament PHP
DESCRIPTION: Ensures that the field's value corresponds to a valid case within a specified PHP Enum class. This rule is crucial for maintaining data integrity when working with predefined sets of values. It requires the fully qualified class name of the Enum as a parameter.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_14

LANGUAGE: php
CODE:
```
Field::make('status')->enum(MyStatus::class)
```

----------------------------------------

TITLE: Grouping Form Fields with Fieldset Component (Blade)
DESCRIPTION: This snippet demonstrates how to use the `<x-filament::fieldset>` Blade component to group multiple form fields under an optional label. The label is defined within a `<x-slot name="label">` tag. This component helps organize complex forms by visually segmenting related inputs, improving user experience and form clarity.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-fieldset.md#_snippet_0

LANGUAGE: Blade
CODE:
```
<x-filament::fieldset>
    <x-slot name="label">
        Address
    </x-slot>
    
    {{-- Form fields --}}
</x-filament::fieldset>
```

----------------------------------------

TITLE: Registering an Import Policy in Laravel AuthServiceProvider
DESCRIPTION: This snippet demonstrates how to register a custom `ImportPolicy` for the `Filament\Actions\Imports\Models\Import` model within Laravel's `AuthServiceProvider`. By mapping `Import::class` to `ImportPolicy::class` in the `$policies` array, you enable custom authorization logic for import-related actions, overriding the default behavior for accessing failure CSV files.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_60

LANGUAGE: php
CODE:
```
use App\Policies\ImportPolicy;
use Filament\Actions\Imports\Models\Import;

protected $policies = [
    Import::class => ImportPolicy::class,
];
```

----------------------------------------

TITLE: Enabling JavaScript DatePicker in Filament (PHP)
DESCRIPTION: This snippet shows how to enable the more customizable JavaScript-based date picker in Filament by calling `native(false)` on a `DatePicker` component. This overrides the default native HTML5 date picker, providing enhanced UI options.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/08-date-time-picker.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\DatePicker;

DatePicker::make('date_of_birth')
    ->native(false);
```

----------------------------------------

TITLE: Using Advanced Navigation Builder Groups in Filament PHP
DESCRIPTION: Uses the advanced `navigation()` builder to define navigation groups and their contained items, providing granular control over the navigation structure and grouping.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/06-navigation.md#_snippet_27

LANGUAGE: php
CODE:
```
use App\Filament\Pages\HomePageSettings;
use App\Filament\Resources\CategoryResource;
use App\Filament\Resources\PageResource;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->navigation(function (NavigationBuilder $builder): NavigationBuilder {
            return $builder->groups([
                NavigationGroup::make('Website')
                    ->items([
                        ...PageResource::getNavigationItems(),
                        ...CategoryResource::getNavigationItems(),
                        ...HomePageSettings::getNavigationItems(),
                    ]),
            ]);
        });
}
```

----------------------------------------

TITLE: Generating Livewire Table Component (Bash)
DESCRIPTION: This Artisan command generates a new Livewire component pre-configured with a Filament table. It prompts for a model name (e.g., `Product`) and sets up the basic structure, accelerating the development of table-based Livewire components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/11-adding-a-table-to-a-livewire-component.md#_snippet_6

LANGUAGE: bash
CODE:
```
php artisan make:livewire-table Products/ListProducts
```

----------------------------------------

TITLE: Requiring a Filament Field When Others Are Absent (PHP)
DESCRIPTION: This snippet demonstrates the `requiredWithout` validation rule. The field value is required only when any of the other specified fields are empty. This is useful for alternative input scenarios.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_40

LANGUAGE: PHP
CODE:
```
Field::make('name')->requiredWithout('field,another_field')
```

----------------------------------------

TITLE: Enabling Unsaved Changes Alerts in Filament Panel (PHP)
DESCRIPTION: Activates a feature that alerts users if they attempt to navigate away from a page with unsaved changes. This applies to Create and Edit resource pages, as well as open action modals, preventing accidental data loss. Requires `Filament\Panel`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->unsavedChangesAlerts();
}
```

----------------------------------------

TITLE: Creating a Livewire Application Layout Blade File
DESCRIPTION: This Blade file (`resources/views/components/layouts/app.blade.php`) defines the base HTML layout for Livewire components. It includes meta tags, a title, a basic `x-cloak` style for Alpine.js, and integrates Filament's styles and scripts using `@filamentStyles` and `@filamentScripts` directives, along with Vite asset compilation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/01-installation.md#_snippet_8

LANGUAGE: blade
CODE:
```
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">

        <meta name="application-name" content="{{ config('app.name') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <style>
            [x-cloak] {
                display: none !important;
            }
        </style>

        @filamentStyles
        @vite('resources/css/app.css')
    </head>

    <body class="antialiased">
        {{ $slot }}

        @filamentScripts
        @vite('resources/js/app.js')
    </body>
</html>
```

----------------------------------------

TITLE: Injecting Laravel Dependencies and Filament Utilities - Filament PHP
DESCRIPTION: Illustrates that you can inject dependencies from Laravel's service container (like `Illuminate\Http\Request`) alongside Filament's utility parameters (`$set`, etc.) into a form configuration closure. This allows leveraging Laravel services directly within dynamic form logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_12

LANGUAGE: php
CODE:
```
use Filament\Forms\Set;
use Illuminate\Http\Request;

function (Request $request, Set $set) {
    // ...
}
```

----------------------------------------

TITLE: Automatically Generating Slugs in Filament Forms (PHP)
DESCRIPTION: This example illustrates how to automatically update one form field's value based on changes in another. The `afterStateUpdated()` method on the `title` field executes a closure whenever the title changes. This closure uses the `$set` utility to update the `slug` field with a slugified version of the `$state` (the current title). The `title` field is set to `live()` to trigger this update.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/02-getting-started.md#_snippet_7

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Illuminate\Support\Str;

[
    TextInput::make('title')
        ->required()
        ->maxLength(255)
        ->live()
        ->afterStateUpdated(function (Set $set, $state) {
            $set('slug', Str::slug($state));
        }),
    TextInput::make('slug')
        ->required()
        ->maxLength(255)
]
```

----------------------------------------

TITLE: Using Lifecycle Hooks in Filament EditAction
DESCRIPTION: This snippet illustrates the various lifecycle hooks available in `EditAction` for executing custom code at different stages. It includes hooks for before/after form filling, before/after form validation, and before/after saving to the database, allowing for fine-grained control over the action's flow.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/02-edit.md#_snippet_10

LANGUAGE: php
CODE:
```
EditAction::make()
    ->beforeFormFilled(function () {
        // Runs before the form fields are populated from the database.
    })
    ->afterFormFilled(function () {
        // Runs after the form fields are populated from the database.
    })
    ->beforeFormValidated(function () {
        // Runs before the form fields are validated when the form is saved.
    })
    ->afterFormValidated(function () {
        // Runs after the form fields are validated when the form is saved.
    })
    ->before(function () {
        // Runs before the form fields are saved to the database.
    })
    ->after(function () {
        // Runs after the form fields are saved to the database.
    })
```

----------------------------------------

TITLE: Publishing Filament Stubs using Artisan Command
DESCRIPTION: This command publishes Filament's default template files, known as 'stubs', to the `stubs/filament` directory within your application. This allows developers to customize the generated code for various Filament components, such as resources, pages, or forms, to match specific project requirements. It's a prerequisite for advanced customization of Filament's scaffolding output.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-stubs.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan vendor:publish --tag=filament-stubs
```

----------------------------------------

TITLE: Customizing Record Update Process in FilamentPHP Edit Page (PHP)
DESCRIPTION: This method provides control over how the Eloquent model ($record) is updated with the form data ($data). By overriding handleRecordUpdate(), developers can implement custom saving logic, interact with other services, or perform additional operations beyond a simple $record->update($data). It requires the Illuminate\Database\Eloquent\Model dependency.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

protected function handleRecordUpdate(Model $record, array $data): Model
{
    $record->update($data);

    return $record;
}
```

----------------------------------------

TITLE: Modifying Table Eloquent Query Filament PHP
DESCRIPTION: This snippet illustrates how to apply custom Eloquent query constraints to the relation manager's table data. It uses the `modifyQueryUsing()` method on the table instance, passing a closure that receives the query builder.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_56

LANGUAGE: php
CODE:
```
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

public function table(Table $table): Table
{
    return $table
        ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', true))
        ->columns([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Defining a Component Action in Filament Infolists (PHP)
DESCRIPTION: This snippet demonstrates how to define a standalone action for a Filament infolist component. It creates an action named 'resetStars' with a specific icon and color, requiring user confirmation before executing a `ResetStars` action. This action is typically attached to an infolist component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/05-actions.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use App\Actions\ResetStars;
use Filament\Infolists\Components\Actions\Action;

Action::make('resetStars')
    ->icon('heroicon-m-x-mark')
    ->color('danger')
    ->requiresConfirmation()
    ->action(function (ResetStars $resetStars) {
        $resetStars();
    })
```

----------------------------------------

TITLE: Setting Color for Filament Actions (PHP)
DESCRIPTION: Applies a predefined color to a Filament action trigger, indicating its significance (e.g., 'danger' for destructive actions). Available colors include `danger`, `gray`, `info`, `primary`, `success`, or `warning`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/03-trigger-button.md#_snippet_7

LANGUAGE: PHP
CODE:
```
Action::make('delete')
    ->color('danger')
```

----------------------------------------

TITLE: Resetting Form Data After Submission in FilamentPHP
DESCRIPTION: This PHP method demonstrates how to create a new comment using the form's current state and then reset the form. Calling `$this->form->fill()` reinitializes the form, effectively clearing its input fields and returning it to its default state, which is useful for post-submission cleanup.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_14

LANGUAGE: php
CODE:
```
use App\Models\Comment;

public function createComment(): void
{
    Comment::create($this->form->getState());

    // Reinitialize the form to clear its data.
    $this->form->fill();
}
```

----------------------------------------

TITLE: Creating a Basic File Upload Field (PHP)
DESCRIPTION: This snippet demonstrates how to create a basic file upload field in Filament using the `FileUpload` component. It initializes a field named 'attachment' for single file uploads, leveraging the underlying Filepond library for frontend functionality.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\FileUpload;

FileUpload::make('attachment')
```

----------------------------------------

TITLE: Applying Min/Max Length Validation to TextInput in Filament PHP
DESCRIPTION: This snippet shows how to enforce minimum and maximum length constraints on a `TextInput` field using `minLength()` and `maxLength()`. These methods add both frontend and backend validation, ensuring the input value falls within the specified character count range.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/02-text-input.md#_snippet_17

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('name')
    ->minLength(2)
    ->maxLength(255)
```

----------------------------------------

TITLE: Registering a Custom Page Route in Filament
DESCRIPTION: This PHP snippet demonstrates how to register a custom page, `SortUsers`, to a specific URL path (`/sort`) within the static `getPages()` method of a Filament resource. The order of registration is crucial, as Laravel's router matches wildcard segments before hard-coded ones.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/10-custom-pages.md#_snippet_1

LANGUAGE: php
CODE:
```
public static function getPages(): array
{
    return [
        // ...
        'sort' => Pages\SortUsers::route('/sort'),
    ];
}
```

----------------------------------------

TITLE: Accessing the Current Eloquent Record in Livewire Mount Method
DESCRIPTION: This snippet demonstrates how the current Eloquent record associated with the Filament form can be accessed within the `mount()` method of an embedded Livewire component. The record is passed as a parameter, which can be nullable.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_38

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

class Foo extends Component
{
    public function mount(?Model $record = null): void
    {
        // ...
    }
}
```

----------------------------------------

TITLE: Adding Header Actions to a Page (PHP)
DESCRIPTION: Defines an array of actions to be displayed in the page header by overriding the `getHeaderActions()` method. This example includes an 'edit' action that navigates to a URL and a 'delete' action that requires confirmation before deleting a post.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/04-pages.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Actions\Action;

protected function getHeaderActions(): array
{
    return [
        Action::make('edit')
            ->url(route('posts.edit', ['post' => $this->post])),
        Action::make('delete')
            ->requiresConfirmation()
            ->action(fn () => $this->post->delete()),
    ];
}
```

----------------------------------------

TITLE: Injecting Multiple Utilities into Action Function - PHP
DESCRIPTION: This snippet demonstrates the ability to inject multiple utilities simultaneously into an action's configuration function. By defining both `$arguments` and `$livewire` parameters, the function receives access to both the action's passed arguments and the Livewire component instance. Parameters can be combined in any order due to dynamic injection.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/08-advanced.md#_snippet_5

LANGUAGE: php
CODE:
```
use Livewire\Component;

function (array $arguments, Component $livewire) {
    // ...
}
```

----------------------------------------

TITLE: Implementing View Authorization for Import Policy in PHP
DESCRIPTION: This snippet provides an example implementation of the `view()` method within an `ImportPolicy` class. This method is responsible for authorizing access to the failure CSV file generated by Filament imports. It checks if the `User` attempting to view the file is the same user who initiated the `Import`, returning `true` for authorized access and `false` otherwise. This logic must be explicitly added if a custom policy is defined to retain the default authorization behavior.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_61

LANGUAGE: php
CODE:
```
use App\Models\User;
use Filament\Actions\Imports\Models\Import;

public function view(User $user, Import $import): bool
{
    return $import->user()->is($user);
}
```

----------------------------------------

TITLE: Updating Filament Configuration File - Bash
DESCRIPTION: These commands publish the new combined Filament configuration file (`filament.php`) and remove the old, package-specific `tables.php` config file, which is no longer used in v3.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/13-upgrade-guide.md#_snippet_4

LANGUAGE: Bash
CODE:
```
php artisan vendor:publish --tag=filament-config --force
rm config/tables.php
```

----------------------------------------

TITLE: Restricting File Size in Filament FileUpload (PHP)
DESCRIPTION: This snippet demonstrates how to restrict the size of uploaded files in kilobytes using `minSize()` and `maxSize()`. Here, it sets a minimum size of 512KB and a maximum size of 1024KB for the attachment.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_33

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\FileUpload;

FileUpload::make('attachment')
    ->minSize(512)
    ->maxSize(1024)
```

----------------------------------------

TITLE: Creating Basic Tabs in Filament Forms
DESCRIPTION: This snippet demonstrates how to create a basic set of tabs in Filament forms using the `Tabs::make()` method. Each tab is defined with `Tabs\Tab::make()` and can contain its own schema of form components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/04-tabs.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Tabs;

Tabs::make('Tabs')
    ->tabs([
        Tabs\Tab::make('Tab 1')
            ->schema([
                // ...
            ]),
        Tabs\Tab::make('Tab 2')
            ->schema([
                // ...
            ]),
        Tabs\Tab::make('Tab 3')
            ->schema([
                // ...
            ])
    ]);
```

----------------------------------------

TITLE: Creating a Basic Filament Wizard in PHP
DESCRIPTION: This snippet demonstrates how to initialize a multi-step form wizard using `Wizard::make()` in Filament. Each step is defined with `Wizard\Step::make()` and contains its own schema for form fields, allowing for a structured and validated progression through the form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/05-wizard.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Wizard;

Wizard::make([
    Wizard\Step::make('Order')
        ->schema([
            // ...
        ]),
    Wizard\Step::make('Delivery')
        ->schema([
            // ...
        ]),
    Wizard\Step::make('Billing')
        ->schema([
            // ...
        ])
])
```

----------------------------------------

TITLE: Creating a Basic Image Column Filament PHP
DESCRIPTION: Creates a basic image column in a FilamentPHP table, displaying the image located at the path specified by the 'avatar' database field relative to the default storage disk ('public'). Requires the ImageColumn class.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/04-image.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\ImageColumn;

ImageColumn::make('avatar')
```

----------------------------------------

TITLE: Halting Filament Record Creation with `beforeCreate` Hook
DESCRIPTION: This PHP snippet demonstrates how to halt the record creation process in Filament by calling `$this->halt()` within the `beforeCreate()` lifecycle hook. It checks if the authenticated user's team is subscribed and, if not, sends a persistent warning notification with a 'subscribe' action before stopping the creation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;

protected function beforeCreate(): void
{
    if (! auth()->user()->team->subscribed()) {
        Notification::make()
            ->warning()
            ->title('You don\'t have an active subscription!')
            ->body('Choose a plan to continue.')
            ->persistent()
            ->actions([
                Action::make('subscribe')
                    ->button()
                    ->url(route('subscribe'), shouldOpenInNewTab: true),
            ])
            ->send();
    
        $this->halt();
    }
}
```

----------------------------------------

TITLE: Adding HTML or Markdown Hints to Filament TextInput (PHP)
DESCRIPTION: Demonstrates how to use the `hint()` method with `Illuminate\Support\HtmlString` for raw HTML, `inlineMarkdown()` for markdown, or a Blade `view()` for more complex hint content, allowing rich formatting and interactive elements.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/01-getting-started.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

TextInput::make('password')
    ->hint(new HtmlString('<a href="/forgotten-password">Forgotten your password?</a>'))

TextInput::make('password')
    ->hint(str('[Forgotten your password?](/forgotten-password)')->inlineMarkdown()->toHtmlString())

TextInput::make('password')
    ->hint(view('forgotten-password-hint'))
```

----------------------------------------

TITLE: Testing Table Action Validation Errors - Pest/PHP
DESCRIPTION: Explains how to assert validation errors for a table action using `assertHasTableActionErrors()`. This example checks for a required error on the 'title' field when editing a post with null data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/12-testing.md#_snippet_28

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can validate edited post data', function () {
    $post = Post::factory()->create();

    livewire(PostResource\Pages\ListPosts::class)
        ->callTableAction(EditAction::class, $post, data: [
            'title' => null,
        ])
        ->assertHasTableActionErrors(['title' => ['required']]);
});
```

----------------------------------------

TITLE: Making Filament ManageRecords Page Translatable
DESCRIPTION: This PHP snippet shows how to enable translation for a Filament `ManageRecords` page, typically used for simple resources. It involves using the `ManageRecords\Concerns\Translatable` trait and adding the `Actions\LocaleSwitcher::make()` action to the `getHeaderActions()` array, providing locale switching for managing records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-translatable-plugin/README.md#_snippet_8

LANGUAGE: php
CODE:
```
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageBlogPosts extends ListRecords
{
    use ManageRecords\Concerns\Translatable;
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            // ...
        ];
    }
    
    // ...
}
```

----------------------------------------

TITLE: Creating Laravel Notifications Database Table (Bash)
DESCRIPTION: This snippet provides the `php artisan` commands to create the necessary `notifications` database table for Laravel's notification system. It includes commands for both Laravel 11+ and Laravel 10, and notes important considerations for PostgreSQL's `data` column and UUID usage for the `notifiable` column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/03-database-notifications.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Laravel 11 and higher
php artisan make:notifications-table
```

LANGUAGE: bash
CODE:
```
# Laravel 10
php artisan notifications:table
```

----------------------------------------

TITLE: Calculating Entry State in Filament Infolists (PHP)
DESCRIPTION: This example shows how to dynamically calculate the state (value) of an entry using a callback function passed to the `state()` method. This is useful when the entry's value is derived from other record properties rather than directly from a database column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_17

LANGUAGE: PHP
CODE:
```
Infolists\Components\TextEntry::make('amount_including_vat')
    ->state(function (Model $record): float {
        return $record->amount * (1 + $record->vat_rate);
    })
```

----------------------------------------

TITLE: Adding Confirmation to Filament Actions (PHP)
DESCRIPTION: This snippet demonstrates how to add a confirmation modal to a Filament action using the `requiresConfirmation()` method. It's typically used for destructive operations like deleting records, ensuring user intent before execution. The action closure defines the operation to be performed upon confirmation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use AppModelsPost;

Action::make('delete')
    ->action(fn (Post $record) => $record->delete())
    ->requiresConfirmation()
```

----------------------------------------

TITLE: Executing Code on Modal Open in FilamentPHP (PHP)
DESCRIPTION: This code demonstrates how to execute custom logic when a modal opens by passing a closure to the `mountUsing()` method. If overriding Filament's default form initialization, it's crucial to call `$form->fill()` to ensure the form is correctly populated.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_18

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Form;

Action::make('create')
    ->mountUsing(function (Form $form) {
        $form->fill();

        // ...
    })
```

----------------------------------------

TITLE: Defining Filament Table Columns PHP
DESCRIPTION: Shows the basic structure of the `table` method within a Filament component or resource where an array of column objects should be defined using the `$table->columns()` call.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->columns([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Creating a Basic Filament Checkbox (PHP)
DESCRIPTION: This snippet demonstrates how to create a basic checkbox component in Filament forms. It uses the `Checkbox::make()` method to define a checkbox named 'is_admin', which typically corresponds to a boolean field in a database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/04-checkbox.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Checkbox;

Checkbox::make('is_admin')
```

----------------------------------------

TITLE: Creating a Basic Filament Toggle Component (PHP)
DESCRIPTION: This snippet demonstrates the fundamental way to instantiate a Toggle component in Filament forms. It creates a toggle named 'is_admin' which is typically used to represent a boolean state.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/05-toggle.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Toggle;

Toggle::make('is_admin')
```

----------------------------------------

TITLE: Customizing Record Creation Process (PHP)
DESCRIPTION: Describes how to customize the actual record creation process by overriding the `handleRecordCreation()` method on the Create page class. This method accepts the `$data` array and is responsible for creating and returning the Eloquent Model instance.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

protected function handleRecordCreation(array $data): Model
{
    return static::getModel()::create($data);
}
```

----------------------------------------

TITLE: Implementing Basic Pagination in Livewire PHP
DESCRIPTION: This PHP snippet demonstrates how to fetch paginated data using `User::query()->paginate(10)` within a Livewire component's `render` method. It prepares data for display in a Blade view, showing 10 items per page.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-pagination.md#_snippet_0

LANGUAGE: php
CODE:
```
use AppModelsUser;
use IlluminateContractsViewView;
use LivewireComponent;

class ListUsers extends Component
{
    // ...
    
    public function render(): View
    {
        return view('livewire.list-users', [
            'users' => User::query()->paginate(10),
        ]);
    }
}
```

----------------------------------------

TITLE: Asserting Filament Table Action Existence (PHP)
DESCRIPTION: This snippet shows how to use Pest and Livewire to test if specific table actions (`assertTableActionExists`, `assertTableActionDoesNotExist`) and bulk actions (`assertTableBulkActionExists`, `assertTableBulkActionDoesNotExist`) are present or absent on a Filament resource list page. It checks for 'publish' and 'unpublish' actions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/12-testing.md#_snippet_31

LANGUAGE: php
CODE:
```
use function Pest\Livewire\livewire;

it('can publish but not unpublish posts', function () {
    livewire(PostResource\Pages\ListPosts::class)
        ->assertTableActionExists('publish')
        ->assertTableActionDoesNotExist('unpublish')
        ->assertTableBulkActionExists('publish')
        ->assertTableBulkActionDoesNotExist('unpublish');
});
```

----------------------------------------

TITLE: Displaying Related Data Filament Table Column PHP
DESCRIPTION: Creates a TextColumn to display a field from a related Eloquent model using dot notation. The column name follows the structure 'relationship.field' (e.g., 'author.name'). This requires an Eloquent relationship named 'author' to be defined on the model.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/11-relationships.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('author.name')
```

----------------------------------------

TITLE: Testing Form Saving in Filament PHP
DESCRIPTION: This snippet demonstrates how to test the saving functionality of a Filament form. It fills the form with new data, calls the 'save' action, asserts no form errors, and then verifies that the record in the database has been updated with the new data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can save', function () {
    $post = Post::factory()->create();
    $newData = Post::factory()->make();

    livewire(PostResource\Pages\EditPost::class, [
        'record' => $post->getRouteKey(),
    ])
        ->fillForm([
            'author_id' => $newData->author->getKey(),
            'content' => $newData->content,
            'tags' => $newData->tags,
            'title' => $newData->title,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($post->refresh())
        ->author_id->toBe($newData->author->getKey())
        ->content->toBe($newData->content)
        ->tags->toBe($newData->tags)
        ->title->toBe($newData->title);
});
```

----------------------------------------

TITLE: Defining Import Columns in a Filament Importer (PHP)
DESCRIPTION: This PHP snippet demonstrates how to override the `getColumns()` method within an importer class to explicitly define the importable columns. It shows how to set required mappings, custom labels, and validation rules for each column using `ImportColumn` objects.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_7

LANGUAGE: php
CODE:
```
use Filament\Actions\Imports\ImportColumn;

public static function getColumns(): array
{
    return [
        ImportColumn::make('name')
            ->requiredMapping()
            ->rules(['required', 'max:255']),
        ImportColumn::make('sku')
            ->label('SKU')
            ->requiredMapping()
            ->rules(['required', 'max:32']),
        ImportColumn::make('price')
            ->numeric()
            ->rules(['numeric', 'min:0'])
    ];
}
```

----------------------------------------

TITLE: Defining Database Migrations for Owner, Patient, and Treatment
DESCRIPTION: This PHP snippet provides the schema definitions for the `owners`, `patients`, and `treatments` tables. It includes primary keys, various data types (string, date, text, unsignedInteger), and foreign key constraints (`owner_id` and `patient_id`) with `cascadeOnDelete` for referential integrity. These migrations are essential for setting up the database structure required by the Filament application.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/02-getting-started.md#_snippet_1

LANGUAGE: php
CODE:
```
// create_owners_table
Schema::create('owners', function (Blueprint $table) {
    $table->id();
    $table->string('email');
    $table->string('name');
    $table->string('phone');
    $table->timestamps();
});

// create_patients_table
Schema::create('patients', function (Blueprint $table) {
    $table->id();
    $table->date('date_of_birth');
    $table->string('name');
    $table->foreignId('owner_id')->constrained('owners')->cascadeOnDelete();
    $table->string('type');
    $table->timestamps();
});

// create_treatments_table
Schema::create('treatments', function (Blueprint $table) {
    $table->id();
    $table->string('description');
    $table->text('notes')->nullable();
    $table->foreignId('patient_id')->constrained('patients')->cascadeOnDelete();
    $table->unsignedInteger('price')->nullable();
    $table->timestamps();
});
```

----------------------------------------

TITLE: Configuring PostCSS for Tailwind CSS and Nesting
DESCRIPTION: This JavaScript configuration file for PostCSS registers `tailwindcss/nesting`, `tailwindcss`, and `autoprefixer` as plugins. This setup enables CSS nesting syntax and automatically adds vendor prefixes to CSS rules, enhancing compatibility and maintainability.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_6

LANGUAGE: js
CODE:
```
export default {
    plugins: {
        'tailwindcss/nesting': 'postcss-nesting',
        tailwindcss: {},
        autoprefixer: {}
    }
}
```

----------------------------------------

TITLE: Configuring Vite for Livewire Component Refresh
DESCRIPTION: This Vite configuration updates the `refreshPaths` to include `app/Livewire/**`, ensuring that the browser automatically refreshes when Livewire components are modified. This improves the development workflow by providing instant feedback on changes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_7

LANGUAGE: js
CODE:
```
import { defineConfig } from 'vite'
import laravel, { refreshPaths } from 'laravel-vite-plugin'

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: [
                ...refreshPaths,
                'app/Livewire/**'
            ]
        })
    ]
})
```

----------------------------------------

TITLE: Adding ExportBulkAction to Filament Table Bulk Actions
DESCRIPTION: This PHP snippet illustrates how to add `Filament\Tables\Actions\ExportBulkAction` to a Filament table's bulk actions. This enables users to select multiple rows and then export only those chosen records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_5

LANGUAGE: php
CODE:
```
use App\Filament\Exports\ProductExporter;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->bulkActions([
            ExportBulkAction::make()
                ->exporter(ProductExporter::class)
        ]);
}
```

----------------------------------------

TITLE: Persisting Collapsed State with Custom ID for Filament Infolist Section (PHP)
DESCRIPTION: This example shows how to explicitly set a unique ID for a Filament Infolist `Section` using the `id()` method when persisting its collapsed state. This is crucial to prevent ID conflicts, especially when sections lack headings or multiple sections share the same heading, ensuring independent state persistence.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/05-section.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\Section;

Section::make('Cart')
    ->description('The items you have selected for purchase')
    ->schema([
        // ...
    ])
    ->collapsible()
    ->persistCollapsed()
    ->id('order-cart')
```

----------------------------------------

TITLE: Implementing `beforeCreate` Lifecycle Hook (PHP)
DESCRIPTION: Provides an example of using the `beforeCreate()` lifecycle hook, which allows executing custom code just before the form data is saved to the database. This method is defined on the Create page class.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_7

LANGUAGE: PHP
CODE:
```
protected function beforeCreate(): void
{
    // ...
}
```

----------------------------------------

TITLE: Saving Form Fields to Relationship (PHP)
DESCRIPTION: Demonstrates how to use the `relationship()` method on a layout component (like Fieldset) to automatically save all nested form fields within its schema to the specified Eloquent relationship instead of the owner model. This is commonly used for one-to-one or morph-one relationships.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;

Fieldset::make('Metadata')
    ->relationship('metadata')
    ->schema([
        TextInput::make('title'),
        Textarea::make('description'),
        FileUpload::make('image'),
    ])
```

----------------------------------------

TITLE: Scope Available Options for AttachAction in Filament
DESCRIPTION: Applies a custom Eloquent query scope to filter the list of records available for selection in the `AttachAction` modal, limiting the options based on specific criteria.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_15

LANGUAGE: php
CODE:
```
use Filament\Tables\Actions\AttachAction;
use Illuminate\Database\Eloquent\Builder;

AttachAction::make()
    ->recordSelectOptionsQuery(fn (Builder $query) => $query->whereBelongsTo(auth()->user()))
```

----------------------------------------

TITLE: Configuring Page Options in Filament Pagination Blade
DESCRIPTION: This Blade snippet extends the Filament pagination component to allow users to select the number of items per page. The `page-options` attribute provides available choices, and `current-page-option-property` binds the selection to the `perPage` Livewire property.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-pagination.md#_snippet_4

LANGUAGE: blade
CODE:
```
<x-filament::pagination
    :paginator="$users"
    :page-options="[5, 10, 20, 50, 100, 'all']"
    :current-page-option-property="perPage"
/>
```

----------------------------------------

TITLE: Adding Spatie Translatable Plugin to Filament Panel
DESCRIPTION: This PHP snippet demonstrates how to register the `Filament\SpatieLaravelTranslatablePlugin` with a Filament panel by calling the `plugin()` method. This makes the plugin's functionalities available throughout the panel.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-translatable-plugin/README.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\SpatieLaravelTranslatablePlugin;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->plugin(SpatieLaravelTranslatablePlugin::make());
}
```

----------------------------------------

TITLE: Setting Global Search Title Attribute (FilamentPHP)
DESCRIPTION: Configures the static property `$recordTitleAttribute` on a Filament resource to specify which model attribute should be used as the title for global search results. Setting this property automatically enables global search for the resource.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/08-global-search.md#_snippet_0

LANGUAGE: php
CODE:
```
protected static ?string $recordTitleAttribute = 'title';
```

----------------------------------------

TITLE: Installing Filament Form Builder Assets in Existing Projects
DESCRIPTION: This Artisan command installs the necessary frontend assets for the Filament Form Builder into an existing Laravel application, integrating it with your current project setup.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_2

LANGUAGE: bash
CODE:
```
php artisan filament:install --forms
```

----------------------------------------

TITLE: Testing Delete Action in Filament PHP
DESCRIPTION: This snippet demonstrates how to test the `DeleteAction` in Filament. It creates a record, calls the `DeleteAction` on the Livewire component, and then asserts that the model no longer exists in the database, confirming successful deletion.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Actions\DeleteAction;
use function Pest\Livewire\livewire;

it('can delete', function () {
    $post = Post::factory()->create();

    livewire(PostResource\Pages\EditPost::class, [
        'record' => $post->getRouteKey(),
    ])
        ->callAction(DeleteAction::class);

    $this->assertModelMissing($post);
});
```

----------------------------------------

TITLE: Setting Min/Max Files for Filament FileUpload
DESCRIPTION: This PHP code demonstrates how to configure the Filament `FileUpload` component to allow multiple file uploads, specifying a minimum of 2 and a maximum of 5 files. This provides granular control over the quantity of files a user can attach.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/09-file-upload.md#_snippet_37

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\FileUpload;

FileUpload::make('attachments')
    ->multiple()
    ->minFiles(2)
    ->maxFiles(5)
```

----------------------------------------

TITLE: Making Filament EditRecord Page Translatable
DESCRIPTION: This PHP example shows how to enable translation for a Filament `EditRecord` page. It involves using the `EditRecord\Concerns\Translatable` trait and adding the `Actions\LocaleSwitcher::make()` action to the `getHeaderActions()` array, allowing locale switching when editing records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-translatable-plugin/README.md#_snippet_6

LANGUAGE: php
CODE:
```
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBlogPost extends EditRecord
{
    use EditRecord\Concerns\Translatable;
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            // ...
        ];
    }
    
    // ...
}
```

----------------------------------------

TITLE: Customizing Eloquent Grouping Query in FilamentPHP Tables
DESCRIPTION: This snippet shows how to customize the Eloquent query's grouping behavior. By using the `groupQueryUsing()` method on a `Group` object, developers can provide a custom closure to define how the underlying database query performs its grouping operation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/08-grouping.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Grouping\Group;
use Illuminate\Database\Eloquent\Builder;

public function table(Table $table): Table
{
    return $table
        ->groups([
            Group::make('status')
                ->groupQueryUsing(fn (Builder $query) => $query->groupBy('status')),
        ]);
}
```

----------------------------------------

TITLE: Customizing Relationship Option Labels with Callback in Filament Select (PHP)
DESCRIPTION: This Filament PHP code demonstrates how to dynamically generate option labels for a `Select` field using the `getOptionLabelFromRecordUsing()` method. It also includes `modifyQueryUsing()` to order the related records by first and last name, providing a flexible way to format displayed options based on the Eloquent model.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_18

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

Select::make('author_id')
    ->relationship(
        name: 'author',
        modifyQueryUsing: fn (Builder $query) => $query->orderBy('first_name')->orderBy('last_name'),
    )
    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->first_name} {$record->last_name}")
    ->searchable(['first_name', 'last_name'])
```

----------------------------------------

TITLE: Registering a Render Hook with a View File (PHP)
DESCRIPTION: This snippet illustrates how to register a Filament render hook to inject content from a dedicated Blade view file. It utilizes `FilamentView::registerRenderHook` with `PanelsRenderHook::BODY_START` and a callback that returns an `Illuminate\Contracts\View\View` instance from `view('impersonation-banner')`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/06-render-hooks.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Illuminate\Contracts\View\View;

FilamentView::registerRenderHook(
    PanelsRenderHook::BODY_START,
    fn (): View => view('impersonation-banner'),
);
```

----------------------------------------

TITLE: Accessing Record Data in Custom Blade View for Filament Table - Blade
DESCRIPTION: This Blade snippet provides an example of the content for a custom view file (`collapsible-row-content.blade.php`) used within a Filament table. It demonstrates how to access the current table record's data (e.g., `email`) using the `$getRecord()` helper function to display dynamic information within custom HTML.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/06-layout.md#_snippet_15

LANGUAGE: blade
CODE:
```
<p class="px-4 py-3 bg-gray-100 rounded-lg">
    <span class="font-medium">
        Email address:
    </span>

    <span>
        {{ $getRecord()->email }}
    </span>
</p>
```

----------------------------------------

TITLE: Customizing Ternary Filter Query Logic in Filament (PHP)
DESCRIPTION: This snippet shows how to define custom query logic for each state (true, false, blank) of a ternary filter using the 'queries()' method. This allows for flexible filtering based on specific database conditions, such as 'whereNotNull' or 'whereNull'.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/03-ternary.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\TernaryFilter;

TernaryFilter::make('email_verified_at')
    ->label('Email verification')
    ->placeholder('All users')
    ->trueLabel('Verified users')
    ->falseLabel('Not verified users')
    ->queries(
        true: fn (Builder $query) => $query->whereNotNull('email_verified_at'),
        false: fn (Builder $query) => $query->whereNull('email_verified_at'),
        blank: fn (Builder $query) => $query, // In this example, we do not want to filter the query when it is blank.
    )
```

----------------------------------------

TITLE: Applying Custom Formatting to Filament TextColumn (PHP)
DESCRIPTION: This snippet demonstrates how to apply custom formatting to the state of a Filament TextColumn using a callback function. The `formatStateUsing()` method accepts a closure that receives the column's state and optionally the record, allowing for dynamic transformations.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_29

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('status')
    ->formatStateUsing(fn (string $state): string => __("statuses.{$state}"))
```

----------------------------------------

TITLE: Refreshing Form Data After Action (PHP)
DESCRIPTION: Demonstrates how to refresh specific form data attributes after an action is performed on an Edit or View resource page. The `refreshFormData()` method is called with an array of attribute names (e.g., 'status') to update the form's display with the latest data from the model.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/04-pages.md#_snippet_5

LANGUAGE: php
CODE:
```
use App\Models\Post;
use Filament\Actions\Action;

Action::make('approve')
    ->action(function (Post $record) {
        $record->approve();

        $this->refreshFormData([
            'status',
        ]);
    })
```

----------------------------------------

TITLE: Generating Page URLs for Specific Panels in Filament (PHP)
DESCRIPTION: This snippet shows how to generate a URL for a Filament page that resides in a different panel. By passing the `panel` argument with the target panel's ID to `getUrl()`, the method constructs a URL specific to that panel, enabling cross-panel navigation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/04-pages.md#_snippet_27

LANGUAGE: PHP
CODE:
```
use App\Filament\Pages\Settings;

Settings::getUrl(panel: 'marketing');
```

----------------------------------------

TITLE: Importing PanelsRenderHook Class in PHP
DESCRIPTION: This PHP snippet imports the `PanelsRenderHook` class from the `Filament\View` namespace. This class provides constants that define various points within the Filament Panel Builder's UI where custom content can be rendered, allowing for extensive customization of the panel's appearance and functionality.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/06-render-hooks.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use Filament\View\PanelsRenderHook;
```

----------------------------------------

TITLE: Generating Relation Page Artisan Bash
DESCRIPTION: This bash command uses the Artisan CLI to generate a new Filament page specifically designed to manage related records. It requires specifying the page class name, the associated resource, and the page type (`ManageRelatedRecords`).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_63

LANGUAGE: bash
CODE:
```
php artisan make:filament-page ManageCustomerAddresses --resource=CustomerResource --type=ManageRelatedRecords
```

----------------------------------------

TITLE: Mutating Form Data Before Fill (PHP)
DESCRIPTION: This PHP method allows modification of record data before it is used to populate a disabled form on the View page. It takes the `$data` array as input, modifies it (e.g., setting `user_id` to the authenticated user's ID), and returns the altered array. This is useful for pre-processing or adding contextual data to the form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_5

LANGUAGE: php
CODE:
```
protected function mutateFormDataBeforeFill(array $data): array
{
    $data['user_id'] = auth()->id();

    return $data;
}
```

----------------------------------------

TITLE: Generate Filament Relation Manager with Attach Actions via Artisan
DESCRIPTION: Uses the Filament Artisan command to generate a Relation Manager class and automatically include the necessary AttachAction, DetachAction, and DetachBulkAction in the generated code.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_11

LANGUAGE: bash
CODE:
```
php artisan make:filament-relation-manager CategoryResource posts title --attach
```

----------------------------------------

TITLE: Configuring Filament Panel Path (PHP)
DESCRIPTION: This PHP snippet demonstrates how to set a custom URL path for a Filament panel. By calling the `path()` method on the panel configuration object, you can define where the panel will be accessible in your application's routes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_1

LANGUAGE: php
CODE:
```
use FilamentPanel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->path('app');
}
```

----------------------------------------

TITLE: Conditionally Disabling Bulk Actions for Specific Rows
DESCRIPTION: Shows how to control which records can be selected for bulk actions using `checkIfRecordIsSelectableUsing()`. This method accepts a callback to determine if a given record is selectable, based on its properties like `status`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

public function table(Table $table): Table
{
    return $table
        ->bulkActions([
            // ...
        ])
        ->checkIfRecordIsSelectableUsing(
            fn (Model $record): bool => $record->status === Status::Enabled,
        );
}
```

----------------------------------------

TITLE: Setting User Avatar URL with HasAvatar PHP
DESCRIPTION: Implement the `HasAvatar` contract on your User model to specify how Filament should retrieve a user's avatar URL. The `getFilamentAvatarUrl` method should return the URL string or `null` to fall back to the default ui-avatars.com service.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/08-users.md#_snippet_2

LANGUAGE: php
CODE:
```
<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable implements FilamentUser, HasAvatar
{
    // ...

    public function getFilamentAvatarUrl(): ?string
    {
        return $this->avatar_url;
    }
}
```

----------------------------------------

TITLE: Overriding Default View for a Filament Page
DESCRIPTION: This PHP property allows developers to specify a custom Blade view file for a Filament page, enabling complete control over the page's rendering. It points to a custom Blade template located within the application's `resources/views` directory.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_13

LANGUAGE: php
CODE:
```
protected static string $view = 'filament.resources.users.pages.view-user';
```

----------------------------------------

TITLE: Global Validation Error Notification in Filament Panel Builder (PHP)
DESCRIPTION: This snippet shows how to configure global validation error notifications across all pages in the Filament Panel Builder. By setting `Page::$reportValidationErrorUsing` in the `AppServiceProvider`, a consistent notification can be displayed for all validation exceptions, centralizing error feedback.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_61

LANGUAGE: PHP
CODE:
```
use FilamentNotificationsNotification;
use FilamentPagesPage;
use IlluminateValidationValidationException;

Page::$reportValidationErrorUsing = function (ValidationException $exception) {
    Notification::make()
        ->title($exception->getMessage())
        ->danger()
        ->send();
};
```

----------------------------------------

TITLE: Defining Action with Closure in Filament v3.x (PHP)
DESCRIPTION: This PHP snippet demonstrates the recommended way to define actions in Filament v3.x when using forms. Instead of a string, a `Closure` (e.g., `Closure::fromCallable()`) should be used to ensure the action correctly opens a modal and executes its logic upon submission.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/10-upgrade-guide.md#_snippet_5

LANGUAGE: php
CODE:
```
Action::make('import_data')
    ->action(Closure::fromCallable([$this, 'importData']))
```

----------------------------------------

TITLE: Identifying Tenant by Subdomain in FilamentPHP (PHP)
DESCRIPTION: This snippet demonstrates how to configure FilamentPHP to identify tenants using subdomains (e.g., `team1.example.com`). It involves setting the `tenant()` method with a `slugAttribute` and using `tenantDomain('{tenant:slug}.example.com')` to map the tenant's slug to a subdomain.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_33

LANGUAGE: php
CODE:
```
use App\Models\Team;
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->tenant(Team::class, slugAttribute: 'slug')
        ->tenantDomain('{tenant:slug}.example.com');
}
```

----------------------------------------

TITLE: Defining a Filament Action Method in Livewire (PHP)
DESCRIPTION: This PHP method defines a `delete` action within the `ManagePost` Livewire component. It uses `Action::make()` to create a confirmable action that deletes a post. The method name must match the action name or be suffixed with `Action`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/06-adding-an-action-to-a-livewire-component.md#_snippet_4

LANGUAGE: php
CODE:
```
use App\Models\Post;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;

class ManagePost extends Component implements HasForms, HasActions
{
    use InteractsWithActions;
    use InteractsWithForms;

    public Post $post;

    public function deleteAction(): Action
    {
        return Action::make('delete')
            ->requiresConfirmation()
            ->action(fn () => $this->post->delete());
    }
    
    // This method name also works, since the action name is `delete`:
    // public function delete(): Action
    
    // This method name does not work, since the action name is `delete`, not `deletePost`:
    // public function deletePost(): Action

    // ...
}
```

----------------------------------------

TITLE: Formatting Text Entry as Currency with Value Division in FilamentPHP
DESCRIPTION: This snippet illustrates how to format a `TextEntry` as currency while dividing the original value by a specified number using the `divideBy` argument. This is particularly useful when monetary values are stored in a smaller unit (e.g., cents) in the database, converting them to the correct currency unit before display.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/02-text.md#_snippet_10

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('price')
    ->money('EUR', divideBy: 100)
```

----------------------------------------

TITLE: Conditionally Showing Summaries with visible() in FilamentPHP
DESCRIPTION: This snippet provides an alternative to `hidden()`, using the `visible()` method to conditionally display a column summary. Similar to `hidden()`, it accepts a boolean or a callback function with access to the Eloquent query builder for dynamic visibility.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/07-summaries.md#_snippet_23

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\Summarizers\Summarizer;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;

TextColumn::make('sku')
    ->summarize(Summarizer::make()
        ->visible(fn (Builder $query): bool => $query->exists()))
```

----------------------------------------

TITLE: Generating a Custom Infolist Layout Class - Bash
DESCRIPTION: This command-line snippet shows how to use the Artisan command `make:infolist-layout` to generate a new custom Infolist layout component class and its corresponding Blade view file. This is the recommended approach for creating reusable custom components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/07-custom.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan make:infolist-layout Box
```

----------------------------------------

TITLE: Implementing View Page Lifecycle Hooks (PHP)
DESCRIPTION: This PHP class snippet demonstrates how to implement lifecycle hooks (`beforeFill` and `afterFill`) within a Filament `ViewRecord` page. These methods allow developers to execute custom code at specific points during the page's lifecycle, such as before or after form fields are populated, providing opportunities for data manipulation or side effects. Note that these hooks are not active when an infolist is used.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_6

LANGUAGE: php
CODE:
```
use Filament\Resources\Pages\ViewRecord;

class ViewUser extends ViewRecord
{
    // ...

    protected function beforeFill(): void
    {
        // Runs before the disabled form fields are populated from the database. Not run on pages using an infolist.
    }

    protected function afterFill(): void
    {
        // Runs after the disabled form fields are populated from the database. Not run on pages using an infolist.
    }
}
```

----------------------------------------

TITLE: Creating a Basic Fieldset in Filament PHP
DESCRIPTION: This snippet demonstrates how to create a basic Fieldset component in Filament PHP. It groups related entries under a specified label, providing a visual border and a default two-column grid for its schema. It's used for organizing form or infolist fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/03-fieldset.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Fieldset;

Fieldset::make('Label')
    ->schema([
        // ...
    ])
```

----------------------------------------

TITLE: Formatting Currency in Filament Text Columns (PHP)
DESCRIPTION: This snippet shows how to format monetary values in a TextColumn using the `money()` method. It requires specifying the currency code (e.g., 'EUR') to ensure proper currency symbol and formatting.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('price')
    ->money('EUR')
```

----------------------------------------

TITLE: Formatting Text Entry as Date/Time in FilamentPHP
DESCRIPTION: This snippet demonstrates how to format a `TextEntry` value as a date and time using the `dateTime()` method. This is useful for displaying timestamp fields like 'created_at' in a human-readable format based on PHP date formatting tokens.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/02-text.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('created_at')
    ->dateTime()
```

----------------------------------------

TITLE: Eager Loading Relationships for Global Search (FilamentPHP)
DESCRIPTION: Overrides the `getGlobalSearchEloquentQuery` method on a Filament resource to modify the base Eloquent query used for global search. This is typically used to eager-load relationships required by search result details or for general query optimization.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/08-global-search.md#_snippet_4

LANGUAGE: php
CODE:
```
public static function getGlobalSearchEloquentQuery(): Builder
{
    return parent::getGlobalSearchEloquentQuery()->with(['author', 'category']);
}
```

----------------------------------------

TITLE: Attaching Imported Record in FilamentPHP Importer (PHP)
DESCRIPTION: Demonstrates accessing options via `$this->options` and using the `afterSave()` hook in a FilamentPHP importer. It shows how to attach the imported record (`$this->record`) to a many-to-many relationship (`categories`) using `syncWithoutDetaching` and the provided `categoryId` option. Requires an `Importer` class and passed options.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_33

LANGUAGE: php
CODE:
```
protected function afterSave(): void
{
    $this->record->categories()->syncWithoutDetaching([$this->options['categoryId']]);
}
```

----------------------------------------

TITLE: Customizing the Saving Process in Filament EditAction
DESCRIPTION: This snippet shows how to override the default saving mechanism using the `using()` method. It allows for custom logic to update the Eloquent model, here demonstrating a direct `update()` call on the record with the provided data.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/02-edit.md#_snippet_4

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

EditAction::make()
    ->using(function (Model $record, array $data): Model {
        $record->update($data);

        return $record;
    })
```

----------------------------------------

TITLE: Casting Key-Value Data in Eloquent Model (PHP)
DESCRIPTION: Configures an Eloquent model to cast the 'meta' attribute as an array. This is crucial when storing data from the Filament KeyValue field, ensuring that the one-dimensional JSON object is properly serialized and deserialized by Laravel's ORM.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/16-key-value.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
    protected $casts = [
        'meta' => 'array',
    ];

    // ...
}
```

----------------------------------------

TITLE: Passing Data to Custom Modal Content in Filament (PHP)
DESCRIPTION: This example illustrates how to pass dynamic data, such as a record, to a custom Blade view rendered within a Filament action's modal. By returning the view from a function, data can be injected into the view's context for rendering.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use Illuminate\Contracts\View\View;

Action::make('advance')
    ->action(fn (Contract $record) => $record->advance())
    ->modalContent(fn (Contract $record): View => view(
        'filament.pages.actions.advance',
        ['record' => $record],
    ))
```

----------------------------------------

TITLE: Enabling Database Transactions Globally in Filament Panel (PHP)
DESCRIPTION: This snippet demonstrates how to enable database transactions for all operations within a Filament panel by calling the `databaseTransactions()` method on the panel instance. This ensures data consistency across all panel operations.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/09-configuration.md#_snippet_12

LANGUAGE: php
CODE:
```
use FilamentPanel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->databaseTransactions();
}
```

----------------------------------------

TITLE: Adding Custom Form Actions in Filament Edit Page (PHP)
DESCRIPTION: Illustrates how to extend the default form actions in a FilamentPHP edit page by adding a 'close' action that triggers a custom saveAndClose() method. This allows for custom behavior upon form submission.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;

class EditUser extends EditRecord
{
    // ...

    protected function getFormActions(): array
    {
        return [
            ...parent::getFormActions(),
            Action::make('close')->action('saveAndClose'),
        ];
    }

    public function saveAndClose(): void
    {
        // ...
    }
}
```

----------------------------------------

TITLE: Installing Filament in New Laravel Projects
DESCRIPTION: These commands facilitate a quick start with Filament in a new Laravel project by installing Livewire, Alpine.js, and Tailwind CSS, scaffolding necessary files, and compiling frontend assets. This should only be run in new projects as it may overwrite existing files.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/01-installation.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan filament:install --scaffold --actions

npm install

npm run dev
```

----------------------------------------

TITLE: Pre-filling Filament Action Modal Forms (PHP)
DESCRIPTION: This snippet demonstrates how to pre-populate a Filament action's modal form with existing record data using the `fillForm()` method. The `fillForm()` closure receives the current record and returns an associative array mapping form field names to their initial values, enhancing user experience by providing relevant defaults.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use AppModelsPost;
use AppModelsUser;
use FilamentFormsComponentsSelect;
use FilamentFormsForm;

Action::make('updateAuthor')
    ->fillForm(fn (Post $record): array => [
        'authorId' => $record->author->id,
    ])
    ->form([
        Select::make('authorId')
            ->label('Author')
            ->options(User::query()->pluck('name', 'id'))
            ->required(),
    ])
    ->action(function (array $data, Post $record): void {
        $record->author()->associate($data['authorId']);
        $record->save();
    })
```

----------------------------------------

TITLE: Generating Filament v3 Admin Panel Provider (PHP Artisan)
DESCRIPTION: This command creates a new `AdminPanelProvider.php` file, which is the central configuration point for Filament v3 panels. It replaces the old `config/filament.php` file, offering a cleaner API and support for multiple panels.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/15-upgrade-guide.md#_snippet_4

LANGUAGE: bash
CODE:
```
php artisan filament:install --panels
```

----------------------------------------

TITLE: Conditionally Showing FilamentPHP Relation Manager (PHP)
DESCRIPTION: Shows how to implement the static `canViewForRecord()` method in a FilamentPHP relation manager. This method receives the `$ownerRecord` and `$pageClass` and should return a boolean determining if the relation manager should be visible for that specific record and page. Useful for dynamic visibility based on parent record attributes. Requires the `Model` class.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_37

LANGUAGE: php
CODE:
```
use Illuminate\Database\Eloquent\Model;

public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
{
    return $ownerRecord->status === Status::Draft;
}
```

----------------------------------------

TITLE: Displaying Text as a Colored Badge in Filament Tables (PHP)
DESCRIPTION: This example shows how to render text as a visually distinct 'badge' using the `badge()` method. It further illustrates dynamic coloring of the badge based on the column's state (e.g., 'draft', 'published') using a closure with the `color()` method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/02-text.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('status')
    ->badge()
    ->color(fn (string $state): string => match ($state) {
        'draft' => 'gray',
        'reviewing' => 'warning',
        'published' => 'success',
        'rejected' => 'danger',
    })
```

----------------------------------------

TITLE: Testing Filament Resource Create Page Rendering (PHP)
DESCRIPTION: This code snippet ensures that the Create page for a Filament resource renders successfully. It sends an HTTP GET request to the page's creation URL and asserts a successful response, confirming the page's accessibility and initial rendering capabilities.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_4

LANGUAGE: PHP
CODE:
```
it('can render page', function () {
    $this->get(PostResource::getUrl('create'))->assertSuccessful();
});
```

----------------------------------------

TITLE: Asserting Pre-filled Filament Action Data in Pest
DESCRIPTION: This snippet illustrates how to verify if a Filament action's form is pre-filled with expected data using `assertActionDataSet()`. It mounts the 'send' action, asserts the default recipient email, then calls the mounted action and confirms successful execution and invoice update.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/09-testing.md#_snippet_6

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can send invoices to the primary contact by default', function () {
    $invoice = Invoice::factory()->create();
    $recipientEmail = $invoice->company->primaryContact->email;

    livewire(EditInvoice::class, [
        'invoice' => $invoice,
    ])
        ->mountAction('send')
        ->assertActionDataSet([
            'email' => $recipientEmail,
        ])
        ->callMountedAction()
        ->assertHasNoActionErrors();

    expect($invoice->refresh())
        ->isSent()->toBeTrue()
        ->recipient_email->toBe($recipientEmail);
});
```

----------------------------------------

TITLE: Testing Filament Table Sorting (PHP)
DESCRIPTION: Details how to test table sorting using `sortTable()` with optional direction and `assertCanSeeTableRecords()` with the `inOrder` parameter set to `true` to verify the record order after sorting. Requires test data and the table component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/12-testing.md#_snippet_4

LANGUAGE: php
CODE:
```
use function Pest\Livewire\livewire;

it('can sort posts by title', function () {
    $posts = Post::factory()->count(10)->create();

    livewire(PostResource\Pages\ListPosts::class)
        ->sortTable('title')
        ->assertCanSeeTableRecords($posts->sortBy('title'), inOrder: true)
        ->sortTable('title', 'desc')
        ->assertCanSeeTableRecords($posts->sortByDesc('title'), inOrder: true);
});
```

----------------------------------------

TITLE: Making Filament Column Searchable PHP
DESCRIPTION: Enables searching functionality for a column using the main global search input field at the top of the table by calling the `searchable()` method. Requires `Filament\Tables\Columns\TextColumn`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_10

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('name')
    ->searchable();
```

----------------------------------------

TITLE: Generating a Custom Form Field Class (Bash)
DESCRIPTION: This command-line snippet shows how to use the Artisan command `make:form-field` to generate a new custom form field class and its corresponding Blade view file. This is the initial step for creating reusable custom fields that can be shared across projects or released as plugins.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/20-custom.md#_snippet_2

LANGUAGE: bash
CODE:
```
php artisan make:form-field RangeSlider
```

----------------------------------------

TITLE: Implementing Filament Action and Form Traits in Livewire (PHP)
DESCRIPTION: This PHP class demonstrates the required traits (`InteractsWithActions`, `InteractsWithForms`) and interfaces (`HasActions`, `HasForms`) for a Livewire component to interact with Filament actions and forms. These are essential for enabling action functionality within the component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/06-adding-an-action-to-a-livewire-component.md#_snippet_3

LANGUAGE: php
CODE:
```
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;

class ManagePost extends Component implements HasForms, HasActions
{
    use InteractsWithActions;
    use InteractsWithForms;

    // ...
}
```

----------------------------------------

TITLE: Customizing Tenant Ownership Relationship (Panel) - FilamentPHP
DESCRIPTION: This snippet demonstrates how to globally customize the name of the Eloquent ownership relationship for all resources associated with a tenant. By passing the `ownershipRelationship` argument to the `tenant()` method in the panel configuration, you can specify a custom relationship name (e.g., 'owner') that Filament will use to link resources to their respective tenants.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_23

LANGUAGE: php
CODE:
```
use App\Models\Team;
use Filament\Panel;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->tenant(Team::class, ownershipRelationship: 'owner');
}
```

----------------------------------------

TITLE: Filling Filament Form Data (PHP)
DESCRIPTION: This snippet demonstrates how to fill a Filament form with data using the `fillForm()` helper in a Pest Livewire test. It initializes a Livewire component (`CreatePost::class`) and populates its form with an array of key-value pairs, such as a 'title'. This method is essential for preparing form state for subsequent assertions or actions. For components with multiple forms, a second parameter can specify the form name.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/09-testing.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

livewire(CreatePost::class)
    ->fillForm([
        'title' => fake()->sentence(),
        // ...
    ]);
```

----------------------------------------

TITLE: Adding Text Prefixes and Suffixes to Input in FilamentPHP
DESCRIPTION: This snippet demonstrates how to add static text before and after a text input using the `prefix()` and `suffix()` methods. This is useful for displaying units, protocols, or domain extensions.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/02-text-input.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('domain')
    ->prefix('https://')
    ->suffix('.com')
```

----------------------------------------

TITLE: Generating Livewire Component (Bash)
DESCRIPTION: This command uses Artisan to generate a new Livewire component named `CreatePost`. This is the initial step for setting up a Livewire component that will host a Filament form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:livewire CreatePost
```

----------------------------------------

TITLE: Configure Tailwind CSS with Filament Preset JavaScript
DESCRIPTION: Sets up the Tailwind configuration file (`tailwind.config.js`) to use Filament's preset, including its color scheme and required plugins. It also configures content paths for scanning classes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/01-installation.md#_snippet_4

LANGUAGE: javascript
CODE:
```
import preset from './vendor/filament/support/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        './app/Filament/**/*.php',
        './resources/views/filament/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
}
```

----------------------------------------

TITLE: Defining Custom Route Path for Filament Dashboard (PHP)
DESCRIPTION: This snippet illustrates how to define a custom URL path for a Filament dashboard by overriding the static `$routePath` property. Setting this property ensures that the dashboard is accessible at the specified segment (e.g., `/finance`) instead of the default root path (`/`). This is crucial when creating multiple dashboards.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/05-dashboard.md#_snippet_14

LANGUAGE: php
CODE:
```
protected static string $routePath = 'finance';
```

----------------------------------------

TITLE: Adding Section Layout Component to Filament Form Schema (PHP)
DESCRIPTION: This snippet demonstrates how to integrate a Section layout component into a Filament form schema. It shows the basic structure of a form array including TextInput, RichEditor, and a Section component, which provides a heading and description for nested fields. The Section component uses its schema() method to contain other form elements.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/02-getting-started.md#_snippet_3

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;

[
    TextInput::make('title'),
    TextInput::make('slug'),
    RichEditor::make('content')
        ->columnSpan(2),
    Section::make('Publishing')
        ->description('Settings for publishing this post.')
        ->schema([
            // ...
        ]),
]
```

----------------------------------------

TITLE: Install Tailwind CSS Dependencies with NPM Bash
DESCRIPTION: Installs Tailwind CSS version 3 and required plugins (@tailwindcss/forms, @tailwindcss/typography) along with PostCSS and Autoprefixer as development dependencies.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/01-installation.md#_snippet_3

LANGUAGE: bash
CODE:
```
npm install tailwindcss@3 @tailwindcss/forms @tailwindcss/typography postcss postcss-nesting autoprefixer --save-dev
```

----------------------------------------

TITLE: Setting Filament Column Label PHP
DESCRIPTION: Customizes the display label for the column header in the table by using the `label()` method on the column instance, overriding the default label generated from the column name. Requires `Filament\Tables\Columns\TextColumn`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/01-getting-started.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title')
    ->label('Post title');
```

----------------------------------------

TITLE: Integrating Laravel Scout for Table Search (PHP)
DESCRIPTION: This method overrides the default table search behavior to integrate Laravel Scout. It applies column-specific searches first, then, if a global search term is present, it filters the query using a whereIn() clause based on the IDs returned by Scout's search. This approach leverages Scout's internal whereIn() usage, ensuring no performance penalty.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_19

LANGUAGE: PHP
CODE:
```
use App\Models\Post;
use Illuminate\Database\Eloquent\Builder;

protected function applySearchToTableQuery(Builder $query): Builder
{
    $this->applyColumnSearchesToTableQuery($query);
    
    if (filled($search = $this->getTableSearch())) {
        $query->whereIn('id', Post::search($search)->keys());
    }

    return $query;
}
```

----------------------------------------

TITLE: Removing Model Attributes from JavaScript in Filament PHP
DESCRIPTION: This PHP method, `mutateFormDataBeforeFill`, is designed to modify the form data array before it is used to fill a form in Filament's Edit or View pages. It demonstrates how to remove specific attributes, such as `is_admin`, from the data, preventing them from being exposed to JavaScript in the frontend. This is useful for securing sensitive information not required by the form.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/11-security.md#_snippet_0

LANGUAGE: PHP
CODE:
```
protected function mutateFormDataBeforeFill(array $data): array
{
    unset($data['is_admin']);

    return $data;
}
```

----------------------------------------

TITLE: Using DateConstraint in Filament Query Builder PHP
DESCRIPTION: Illustrates how to apply a DateConstraint to filter a direct date column (`created_at`) and how to use it to filter a date column (`created_at`) on a related model (`creator`) via the `relationship` method.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/04-query-builder.md#_snippet_6

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\QueryBuilder\Constraints\DateConstraint;

DateConstraint::make('created_at') // Filter the `created_at` column

DateConstraint::make('creatorCreatedAt')
    ->relationship(name: 'creator', titleAttribute: 'created_at') // Filter the `created_at` column on the `creator` relationship
```

----------------------------------------

TITLE: Defining Responsive Grid Layout with Grid Component (PHP)
DESCRIPTION: This example illustrates the use of Filament's dedicated `Grid` component to define a responsive column configuration. Instead of using the `columns()` method on other layout components, `Grid::make()` directly accepts an array of breakpoints and column counts, providing an explicit and clean syntax for structuring forms or infolists with a grid.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/02-grid.md#_snippet_1

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\Grid;

Grid::make([
    'default' => 1,
    'sm' => 2,
    'md' => 3,
    'lg' => 4,
    'xl' => 6,
    '2xl' => 8,
])
    ->schema([
        // ...
    ])
```

----------------------------------------

TITLE: Formatting Export Column State in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to apply custom formatting to an export column's value using a callback function. The `formatStateUsing()` method accepts the current state (`$state`) and optionally the Eloquent record (`$record`) to transform the output. This is useful for translating status codes or applying specific display logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_13

LANGUAGE: php
CODE:
```
use Filament\Actions\Exports\ExportColumn;

ExportColumn::make('status')
    ->formatStateUsing(fn (string $state): string => __("statuses.{$state}"))
```

----------------------------------------

TITLE: Customizing Eloquent Relationship Query for CheckboxList (PHP)
DESCRIPTION: This snippet illustrates how to customize the database query used to retrieve options for a Filament CheckboxList's Eloquent relationship. The `modifyQueryUsing` parameter accepts a closure, allowing advanced filtering or ordering, such as including soft-deleted records with `withTrashed()`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/06-checkbox-list.md#_snippet_12

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\CheckboxList;
use Illuminate\Database\Eloquent\Builder;

CheckboxList::make('technologies')
    ->relationship(
        titleAttribute: 'name',
        modifyQueryUsing: fn (Builder $query) => $query->withTrashed(),
    )
```

----------------------------------------

TITLE: Accessing Eloquent Record in Blade (Blade)
DESCRIPTION: This Blade snippet demonstrates how to access the associated Eloquent record within a Filament component's view. The `$getRecord()` function retrieves the current Eloquent model instance, allowing you to display or interact with its properties, such as `$getRecord()->name`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/04-layout/08-custom.md#_snippet_4

LANGUAGE: blade
CODE:
```
<div>
    {{ $getRecord()->name }}
</div>
```

----------------------------------------

TITLE: Passing Properties to Relation Manager Filament PHP
DESCRIPTION: This PHP snippet demonstrates how to pass an array of data (interpreted as Livewire properties) to a relation manager instance when registering it in the resource's `getRelations()` method. This is done using the static `make()` method on the relation manager class.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/07-relation-managers.md#_snippet_66

LANGUAGE: php
CODE:
```
use App\Filament\Resources\Blog\PostResource\RelationManagers\CommentsRelationManager;

public static function getRelations(): array
{
    return [
        CommentsRelationManager::make([
            'status' => 'approved',
        ]),
    ];
}
```

----------------------------------------

TITLE: Registering JavaScript Assets in Filament Service Provider
DESCRIPTION: This PHP snippet illustrates how to register a compiled JavaScript asset with Filament using the `FilamentAsset::register()` method, typically within the `boot()` method of a service provider like `AppServiceProvider`. It uses `Js::make()` to create an asset instance with a unique handle and `Vite::asset()` to resolve the correct path to the compiled JavaScript file, ensuring it's loaded when Filament renders relevant components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/widgets/docs/03-charts.md#_snippet_19

LANGUAGE: php
CODE:
```
use Filament\Support\Assets\Js;
use Filament\Support\Facades\FilamentAsset;
use Illuminate\Support\Facades\Vite;

FilamentAsset::register([
    Js::make('chart-js-plugins', Vite::asset('resources/js/filament-chart-js-plugins.js'))->module()
]);
```

----------------------------------------

TITLE: Injecting Livewire Component Instance into Action Function - PHP
DESCRIPTION: This snippet demonstrates how to inject the current Livewire component instance into an action's configuration function. By type-hinting the `$livewire` parameter as `Livewire\Component`, the function can interact with the Livewire component that owns the action, allowing access to its properties and methods.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/08-advanced.md#_snippet_3

LANGUAGE: php
CODE:
```
use Livewire\Component;

function (Component $livewire) {
    // ...
}
```

----------------------------------------

TITLE: Creating a Custom Widget in Filament CLI
DESCRIPTION: This Artisan command generates the necessary files for a new custom widget in Filament. It creates both the widget class in the `/Widgets` directory and its corresponding view file in the `/widgets` views directory, providing a starting point for custom dashboard components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/05-dashboard.md#_snippet_7

LANGUAGE: bash
CODE:
```
php artisan make:filament-widget BlogPostsOverview
```

----------------------------------------

TITLE: Accessing Page Table Query in Filament Widget (PHP)
DESCRIPTION: This PHP snippet illustrates how to obtain the Eloquent query builder instance from the associated page's table within a Filament widget. The `getPageTableQuery()` method allows for performing database operations, such as counting records, based on the table's current state.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/09-widgets.md#_snippet_6

LANGUAGE: php
CODE:
```
use Filament\Widgets\StatsOverviewWidget\Stat;

Stat::make('Total Products', $this->getPageTableQuery()->count()),
```

----------------------------------------

TITLE: Enabling Basic Record Reordering in Filament Tables (PHP)
DESCRIPTION: This snippet demonstrates how to enable drag-and-drop reordering for records in a Filament table. It uses the `reorderable()` method, specifying 'sort' as the column to store the record order. If mass assignment protection is enabled, the 'sort' attribute must be added to the model's `$fillable` array.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->reorderable('sort');
}
```

----------------------------------------

TITLE: Allowing Multiple Selections in Filament SelectConstraint PHP
DESCRIPTION: Shows how to configure a SelectConstraint using the `multiple()` method to allow users to select more than one option. When multiple options are selected, the filter will match records where the column value matches any of the selected options.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/04-query-builder.md#_snippet_9

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\QueryBuilder\Constraints\SelectConstraint;

SelectConstraint::make('status')
    ->multiple()
    ->options([
        'draft' => 'Draft',
        'reviewing' => 'Reviewing',
        'published' => 'Published',
    ])
```

----------------------------------------

TITLE: Generating a Model-Bound Form Component with Filament CLI
DESCRIPTION: This Bash command generates a Livewire form component (`Products/CreateProduct`) that is automatically configured to interact with an Eloquent model. This type of form simplifies data saving and ensures form fields are properly set up to access the model's attributes, streamlining the creation of new records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/08-adding-a-form-to-a-livewire-component.md#_snippet_16

LANGUAGE: bash
CODE:
```
php artisan make:livewire-form Products/CreateProduct
```

----------------------------------------

TITLE: Persisting Table Filters in Session (PHP)
DESCRIPTION: This snippet demonstrates how to make table filters persist across user sessions. By calling `persistFiltersInSession()` on the table instance, the selected filter states are saved, ensuring that users return to the same filtered view even after navigating away and returning.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/01-getting-started.md#_snippet_7

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->filters([
            // ...
        ])
        ->persistFiltersInSession();
}
```

----------------------------------------

TITLE: Accessing Selected Records with Row Actions in Filament
DESCRIPTION: This snippet illustrates how a row action can access other selected records in the table. It uses `selectable()` to enable row selection and `accessSelectedRecords()` on the action to pass the `$selectedRecords` collection to the action's callback, allowing the current row's data to be copied to all selected records.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

public function table(Table $table): Table
{
    return $table
        ->selectable()
        ->actions([
            Action::make('copyToSelected')
                ->accessSelectedRecords()
                ->action(function (Model $record, Collection $selectedRecords) {
                    $selectedRecords->each(
                        fn (Model $selectedRecord) => $selectedRecord->update([
                            'is_active' => $record->is_active,
                        ]),
                    );
                }),
        ]);
}
```

----------------------------------------

TITLE: Aggregating Relationship Average in Filament Export Column (PHP)
DESCRIPTION: This snippet demonstrates how to calculate the average of a specific field across all related records and display it in an export column. The `avg()` method takes the relationship name and the field to average, following Laravel's `_avg_field` naming convention for the column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/09-export.md#_snippet_23

LANGUAGE: php
CODE:
```
use Filament\Actions\Exports\ExportColumn;

ExportColumn::make('users_avg_age')->avg('users', 'age')
```

----------------------------------------

TITLE: Basic Custom Filament Page View (Blade)
DESCRIPTION: This Blade snippet provides a basic example of a custom view for a Filament page. It utilizes Filament's `x-filament-panels::page` and `x-filament-panels::form` components to render the form and its actions, enabling custom layout and content within the Filament panel structure.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/03-creating-records.md#_snippet_21

LANGUAGE: Blade
CODE:
```
<x-filament-panels::page>
    <x-filament-panels::form wire:submit="create">
        {{ $this->form }}

        <x-filament-panels::form.actions
            :actions="$this->getCachedFormActions()"
            :full-width="$this->hasFullWidthFormActions()"
        />
    </x-filament-panels::form>
</x-filament-panels::page>
```

----------------------------------------

TITLE: Opening Filament Modal from Alpine.js (JavaScript)
DESCRIPTION: This Alpine.js snippet demonstrates how to open a Filament modal using a JavaScript `dispatch` event. It sends an `open-modal` event with an object containing the modal's `id`, allowing client-side control over modal visibility.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-modal.md#_snippet_3

LANGUAGE: alpine.js
CODE:
```
$dispatch('open-modal', { id: 'edit-user' })
```

----------------------------------------

TITLE: Conditionally Enabling Record Reordering in Filament Tables (PHP)
DESCRIPTION: This snippet illustrates how to conditionally enable record reordering based on a boolean condition. The `reorderable()` method accepts a second parameter, which is a boolean expression (e.g., `auth()->user()->isAdmin()`) that determines whether reordering is active.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->reorderable('sort', auth()->user()->isAdmin());
}
```

----------------------------------------

TITLE: Saving Pivot Data for Multiple Relationships in Filament Select (PHP)
DESCRIPTION: This Filament PHP code configures a `Select` component for a `multiple()` relationship. It uses the `pivotData()` method to specify additional data (`is_primary` set to `true`) that should be saved to the pivot table when records are attached, enabling the storage of extra attributes for many-to-many relationships.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_19

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\Select;

Select::make('primaryTechnologies')
    ->relationship(name: 'technologies', titleAttribute: 'name')
    ->multiple()
    ->pivotData([
        'is_primary' => true,
    ])
```

----------------------------------------

TITLE: Nesting Actions for Extra Modal Footer Actions in FilamentPHP (PHP)
DESCRIPTION: This snippet shows how to add an independent action, like a 'Delete' button, to the footer of an existing modal (e.g., 'edit' modal). This nested action opens its own confirmation modal without triggering the parent action. It requires the `Action` class from FilamentPHP.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_23

LANGUAGE: php
CODE:
```
Action::make('edit')
    // ...
    ->extraModalFooterActions([
        Action::make('delete')
            ->requiresConfirmation()
            ->action(function () {
                // ...
            }),
    ])
```

----------------------------------------

TITLE: Alpha Dash Characters Validation in Filament PHP
DESCRIPTION: Validates that the field's value contains only alphanumeric characters, dashes, and underscores. This is commonly used for slugs, usernames, or other identifiers that allow these specific special characters.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_4

LANGUAGE: php
CODE:
```
Field::make('name')->alphaDash()
```

----------------------------------------

TITLE: Asserting Filament Table Action Visibility (PHP)
DESCRIPTION: This snippet illustrates how to verify the visibility of table actions (`assertTableActionVisible`, `assertTableActionHidden`) and bulk actions (`assertTableBulkActionVisible`, `assertTableBulkActionHidden`) in Filament using Pest and Livewire. It tests visibility for a specific `$post` record and for bulk actions generally.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/12-testing.md#_snippet_34

LANGUAGE: php
CODE:
```
use function Pest\Livewire\livewire;

it('can not publish, but can delete posts', function () {
    $post = Post::factory()->create();

    livewire(PostResource\Pages\ListPosts::class)
        ->assertTableActionHidden('publish', $post)
        ->assertTableActionVisible('delete', $post)
        ->assertTableBulkActionHidden('publish')
        ->assertTableBulkActionVisible('delete');
});
```

----------------------------------------

TITLE: Modifying Form Data Before Saving with Filament CreateAction (PHP)
DESCRIPTION: This snippet shows how to use the `mutateFormDataUsing()` method to intercept and modify the form data array before it is saved to the database. This is useful for injecting additional data, like a `user_id` from the authenticated user, or performing other data transformations.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/01-create.md#_snippet_2

LANGUAGE: PHP
CODE:
```
CreateAction::make()
    ->mutateFormDataUsing(function (array $data): array {
        $data['user_id'] = auth()->id();

        return $data;
    })
```

----------------------------------------

TITLE: Creating Grid Layout Component - PHP
DESCRIPTION: This snippet demonstrates how to instantiate a `Grid` layout component in a Filament Infolist schema. It uses the `make()` static method to specify the number of columns (2 in this case) and the `schema()` method to define the array of components that will be placed within the grid. Requires importing the `Grid` class from `Filament\Infolists\Components`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/01-getting-started.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Grid;

Grid::make(2)
    ->schema([
        // ...
    ])
```

----------------------------------------

TITLE: Basic Custom Filament Page Blade View (Blade)
DESCRIPTION: Provides a basic example of a custom Blade view for a FilamentPHP page, demonstrating the use of Filament's Livewire components for forms, actions, and relation managers. This serves as a starting point for custom page layouts.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/04-editing-records.md#_snippet_22

LANGUAGE: Blade
CODE:
```
<x-filament-panels::page>
    <x-filament-panels::form wire:submit="save">
        {{ $this->form }}

        <x-filament-panels::form.actions
            :actions="$this->getCachedFormActions()"
            :full-width="$this->hasFullWidthFormActions()"
        />
    </x-filament-panels::form>

    @if (count($relationManagers = $this->getRelationManagers()))
        <x-filament-panels::resources.relation-managers
            :active-manager="$this->activeRelationManager"
            :managers="$relationManagers"
            :owner-record="$record"
            :page-class="static::class"
        />
    @endif
</x-filament-panels::page>
```

----------------------------------------

TITLE: Accessing Eloquent Record in Blade View - Blade
DESCRIPTION: This Blade snippet shows how to access the current Eloquent record associated with the Infolist directly within a custom layout's Blade view. The `$getRecord()` function provides access to the underlying model data, enabling dynamic content display.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/07-custom.md#_snippet_4

LANGUAGE: blade
CODE:
```
<div>
    {{ $getRecord()->name }}
</div>
```

----------------------------------------

TITLE: Counting Scoped Relationship Records Filament Table Column PHP
DESCRIPTION: Creates a TextColumn that displays a scoped count of related records. The counts() method accepts an array mapping the relationship name to a closure that modifies the Eloquent query before counting. The column name must still follow the Laravel 'relationship_count' convention.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/03-columns/11-relationships.md#_snippet_2

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;

TextColumn::make('users_count')->counts([
    'users' => fn (Builder $query) => $query->where('is_active', true),
])
```

----------------------------------------

TITLE: Basic Structure for Custom List Page Blade View
DESCRIPTION: This example provides a minimal structure for a custom Blade view intended for a Filament list page. It shows how to render the table component (`$this->table`) within the standard Filament page layout component (`<x-filament-panels::page>`).
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/02-listing-records.md#_snippet_10

LANGUAGE: Blade
CODE:
```
<x-filament-panels::page>
    {{ $this->table }}
</x-filament-panels::page>
```

----------------------------------------

TITLE: Creating a Basic Checkbox List in Filament
DESCRIPTION: This snippet demonstrates how to create a basic checkbox list component in Filament forms, allowing users to select multiple predefined options. The options are defined as an associative array where keys are values and values are display labels.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/06-checkbox-list.md#_snippet_0

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\CheckboxList;

CheckboxList::make('technologies')
    ->options([
        'tailwind' => 'Tailwind CSS',
        'alpine' => 'Alpine.js',
        'laravel' => 'Laravel',
        'livewire' => 'Laravel Livewire',
    ])
```

----------------------------------------

TITLE: Generating URL for Resource Page with Parameters in FilamentPHP
DESCRIPTION: Generate the URL for a Filament resource page that requires parameters (like 'edit' with a 'record' ID) by passing an array of parameters as the second argument to the static `getUrl()` method. The record can be an Eloquent model or its ID.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/01-getting-started.md#_snippet_30

LANGUAGE: php
CODE:
```
use App\Filament\Resources\CustomerResource;

CustomerResource::getUrl('edit', ['record' => $customer]); // /admin/customers/edit/1
```

----------------------------------------

TITLE: Removing Filament Upgrade Package (Composer)
DESCRIPTION: After successfully upgrading the application to Filament v3, this Composer command removes the `filament/upgrade` package. This package is no longer needed once the migration process is complete, helping to keep the project dependencies clean.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/07-upgrade-guide.md#_snippet_2

LANGUAGE: bash
CODE:
```
composer remove filament/upgrade
```

----------------------------------------

TITLE: Publish Spatie Media Library Migrations - Bash
DESCRIPTION: Publishes the necessary database migration files from Spatie's Media Library package. This is a prerequisite step before running migrations to create the media table.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-media-library-plugin/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-migrations"
```

----------------------------------------

TITLE: Preventing Field Dehydration - Filament PHP
DESCRIPTION: Illustrates how to prevent a field's state from being included when calling `getState()` or when the form automatically saves data, using the `dehydrated(false)` method. This is useful for fields like password confirmations that are needed for validation but shouldn't be saved to the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_17

LANGUAGE: php
CODE:
```
use Filament\Forms\Components\TextInput;

TextInput::make('password_confirmation')
    ->password()
    ->dehydrated(false);
```

----------------------------------------

TITLE: Disabling Global Tenancy for Filament Resources (PHP)
DESCRIPTION: This snippet demonstrates how to globally disable automatic multi-tenancy scoping for all Filament resources. By calling `Resource::scopeToTenant(false)` in a service provider's `boot()` method or a middleware, developers can opt-in to tenancy on a per-resource basis instead of opting out.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_36

LANGUAGE: PHP
CODE:
```
use Filament\Resources\Resource;

Resource::scopeToTenant(false);
```

----------------------------------------

TITLE: Implementing Dynamic Input Masks with RawJs in Filament PHP
DESCRIPTION: This example shows how to create a dynamic input mask using a JavaScript expression wrapped in `RawJs::make()`. The mask changes based on the input value, allowing for flexible formatting like different credit card number patterns. The JavaScript expression determines the mask dynamically.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/02-text-input.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\TextInput;
use Filament\Support\RawJs;

TextInput::make('cardNumber')
    ->mask(RawJs::make(<<<'JS'
        $input.startsWith('34') || $input.startsWith('37') ? '9999 999999 99999' : '9999 9999 9999 9999'
    JS))
```

----------------------------------------

TITLE: Making a Column Searchable in Filament PHP
DESCRIPTION: Shows how to make a `TextColumn` searchable by chaining the `searchable()` method. This adds a search input to the table that filters rows based on the value in this column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/02-getting-started.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\TextColumn;

TextColumn::make('title')
    ->searchable()
```

----------------------------------------

TITLE: Customizing action objects in Filament Select
DESCRIPTION: Filament Select components use action objects for button customization. Methods like `createOptionAction()`, `editOptionAction()`, and `manageOptionActions()` allow passing a function to customize actions, providing access to the `$action` object for further configuration, such as modal width.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_40

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->createOptionAction(
        fn (Action $action) => $action->modalWidth('3xl'),
    )
```

----------------------------------------

TITLE: Setting Team ID on Record Creation using Eloquent Observer (PHP)
DESCRIPTION: This snippet shows an Eloquent observer (`PostObserver`) that automatically sets the `team_id` for a `Post` model when it is being created. It assigns the authenticated user's `team_id` to the post, either directly or by associating it with the user's team relationship. This ensures new records are correctly linked to the current user's tenant.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_1

LANGUAGE: php
CODE:
```
class PostObserver
{
    public function creating(Post $post): void
    {
        if (auth()->hasUser()) {
            $post->team_id = auth()->user()->team_id;
            // or with a `team` relationship defined:
            $post->team()->associate(auth()->user()->team);
        }
    }
}
```

----------------------------------------

TITLE: Injecting Eloquent Record into Filament Table Action Closure
DESCRIPTION: This snippet shows how to access the current Eloquent record associated with a table row within an action's closure by defining a `$record` parameter of type `Illuminate\Database\Eloquent\Model`. This allows actions to operate directly on the specific data of the row, enabling record-specific logic.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_21

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Model;

function (Model $record) {
    // ...
}
```

----------------------------------------

TITLE: Defining HasMany Relationship for BelongsToMany Pivot in PHP
DESCRIPTION: This PHP snippet defines a `HasMany` relationship named `orderProducts` on an `Order` model. This is crucial for integrating Filament's Repeater with a `BelongsToMany` relationship by treating the pivot table as a separate model (`OrderProduct`), allowing the Repeater to manage individual pivot rows.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/12-repeater.md#_snippet_17

LANGUAGE: PHP
CODE:
```
use Illuminate\Database\Eloquent\Relations\HasMany;

public function orderProducts(): HasMany
{
    return $this->hasMany(OrderProduct::class);
}
```

----------------------------------------

TITLE: Asserting Action Color in FilamentPHP with Pest
DESCRIPTION: This example demonstrates how to verify the color of an action button using `assertActionHasColor()` and `assertActionDoesNotHaveColor()`. It ensures that actions, such as 'delete', display the 'danger' color, while others, like 'print', do not.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/09-testing.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('actions display proper colors', function () {
    $invoice = Invoice::factory()->create();

    livewire(EditInvoice::class, [
        'invoice' => $invoice,
    ])
        ->assertActionHasColor('delete', 'danger')
        ->assertActionDoesNotHaveColor('print', 'danger');
});
```

----------------------------------------

TITLE: Setting Dynamic Entry URL with Callback (Filament PHP)
DESCRIPTION: This snippet illustrates how to make an infolist entry clickable, opening a dynamic URL. The `url()` method accepts a callback function, which receives the current `$record` as a parameter, allowing the URL to be generated dynamically based on the record's data, such as routing to an edit page.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::make('title')
    ->url(fn (Post $record): string => route('posts.edit', ['post' => $record]))
```

----------------------------------------

TITLE: Registering Panel Render Hook for Multiple Pages in PHP
DESCRIPTION: This snippet demonstrates registering a render hook to display a `warning-banner` at the `PAGE_START` position for multiple specific pages. By providing an array of page classes to the `scopes` parameter, the hook is activated on both the `CreateUser` and `EditUser` pages of the `UserResource`.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/06-render-hooks.md#_snippet_6

LANGUAGE: PHP
CODE:
```
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;

FilamentView::registerRenderHook(
    PanelsRenderHook::PAGE_START,
    fn (): View => view('warning-banner'),
    scopes: [
        \App\Filament\Resources\UserResource\Pages\CreateUser::class,
        \App\Filament\Resources\UserResource\Pages\EditUser::class,
    ],
);
```

----------------------------------------

TITLE: Generating Livewire Component (Bash)
DESCRIPTION: This command generates a new Livewire component named `ViewProduct`, creating the necessary class file and view file. It's the first step in setting up a Livewire component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/07-adding-an-infolist-to-a-livewire-component.md#_snippet_0

LANGUAGE: bash
CODE:
```
php artisan make:livewire ViewProduct
```

----------------------------------------

TITLE: Configuring Bulk Actions in Filament Table
DESCRIPTION: Demonstrates the basic structure for defining bulk actions within a Filament table. The `bulkActions()` method is used to register actions that operate on selected table rows.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/05-actions.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->bulkActions([
            // ...
        ]);
}
```

----------------------------------------

TITLE: Initializing Filament Markdown Editor in PHP
DESCRIPTION: This snippet demonstrates the basic initialization of the Filament MarkdownEditor component. It creates a markdown editor field named 'content' for use within Filament forms.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/11-markdown-editor.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\MarkdownEditor;

MarkdownEditor::make('content')
```

----------------------------------------

TITLE: Using NumberConstraint in Filament Query Builder PHP
DESCRIPTION: Shows how to create a NumberConstraint for a direct numeric column (`stock`) and how to use it to filter a numeric column (`item_count`) on a related model (`orders`) using the `relationship` method, which also enables aggregation options for users.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/04-query-builder.md#_snippet_4

LANGUAGE: php
CODE:
```
use Filament\Tables\Filters\QueryBuilder\Constraints\NumberConstraint;

NumberConstraint::make('stock') // Filter the `stock` column

NumberConstraint::make('ordersItemCount')
    ->relationship(name: 'orders', titleAttribute: 'item_count') // Filter the `item_count` column on the `orders` relationship
```

----------------------------------------

TITLE: Implementing Simple Pagination in FilamentPHP Tables (PHP)
DESCRIPTION: This snippet demonstrates how to implement simple pagination for a FilamentPHP table by overriding the `paginateTableQuery()` method. It uses `simplePaginate()` to fetch records, adjusting the count based on the selected "records per page" option, including an 'all' option. This method should be placed in the Livewire component, typically `Pages/List.php` for resources.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/10-advanced.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;

protected function paginateTableQuery(Builder $query): Paginator
{
    return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
}
```

----------------------------------------

TITLE: Defining Basic Filament Tabs (Blade)
DESCRIPTION: Demonstrates the fundamental structure for creating a set of tabs using the `x-filament::tabs` component and nested `x-filament::tabs.item` components. The `label` attribute provides an accessible label for the tab group.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/support/docs/09-blade-components/02-tabs.md#_snippet_0

LANGUAGE: Blade
CODE:
```
<x-filament::tabs label="Content tabs">
    <x-filament::tabs.item>
        Tab 1
    </x-filament::tabs.item>

    <x-filament::tabs.item>
        Tab 2
    </x-filament::tabs.item>

    <x-filament::tabs.item>
        Tab 3
    </x-filament::tabs.item>
</x-filament::tabs>
```

----------------------------------------

TITLE: Requiring Tenant Subscription for Filament Resources/Pages - PHP
DESCRIPTION: This method, when implemented in a Filament resource or page class, determines if a tenant subscription is required to access it. Returning `true` enforces the subscription, while returning `false` (when `requiresTenantSubscription()` is enabled globally) allows access as an exception.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/11-tenancy.md#_snippet_12

LANGUAGE: PHP
CODE:
```
public static function isTenantSubscriptionRequired(Panel $panel): bool
{
    return true;
}
```

----------------------------------------

TITLE: Enabling User-Selectable Row Groupings in Filament Tables (PHP)
DESCRIPTION: This snippet shows how to allow users to select from multiple grouping options in a Filament table. By passing an array of attribute names to the `groups()` method, a dropdown or similar UI element will be rendered, enabling users to dynamically change the table's grouping.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/08-grouping.md#_snippet_1

LANGUAGE: php
CODE:
```
use Filament\Tables\Table;

public function table(Table $table): Table
{
    return $table
        ->groups([
            'status',
            'category',
        ]);
}
```

----------------------------------------

TITLE: Customizing Entire Restore Success Notification - PHP
DESCRIPTION: This snippet shows how to completely customize the success notification using the `successNotification()` method. It allows building a custom `Notification` object with a specific title, body, and type, providing full control over the user feedback.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/07-restore.md#_snippet_4

LANGUAGE: PHP
CODE:
```
use Filament\Notifications\Notification;

RestoreAction::make()
    ->successNotification(
       Notification::make()
            ->success()
            ->title('User restored')
            ->body('The user has been restored successfully.'),
    )
```

----------------------------------------

TITLE: Customizing Global Search Result URL (FilamentPHP)
DESCRIPTION: Overrides the `getGlobalSearchResultUrl` method on a Filament resource to specify the target URL when a user clicks on a global search result for that resource. By default, it links to the Edit or View page.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/08-global-search.md#_snippet_5

LANGUAGE: php
CODE:
```
public static function getGlobalSearchResultUrl(Model $record): string
{
    return UserResource::getUrl('edit', ['record' => $record]);
}
```

----------------------------------------

TITLE: Defining Globally Searchable Attributes (FilamentPHP)
DESCRIPTION: Overrides the `getGloballySearchableAttributes` method on a Filament resource to specify an array of model attributes and relationships (using dot notation) that the global search functionality should search across for this resource.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/08-global-search.md#_snippet_2

LANGUAGE: php
CODE:
```
public static function getGloballySearchableAttributes(): array
{
    return ['title', 'slug', 'author.name', 'category.name'];
}
```

----------------------------------------

TITLE: Publishing Filament Configuration File
DESCRIPTION: This optional Artisan command publishes the default configuration file for the Filament package to your application's `config` directory. This allows for customization of Filament's settings.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/01-installation.md#_snippet_9

LANGUAGE: bash
CODE:
```
php artisan vendor:publish --tag=filament-config
```

----------------------------------------

TITLE: Creating a Basic Ternary Filter in Filament (PHP)
DESCRIPTION: This snippet demonstrates how to create a basic ternary filter for a boolean-like column, such as 'is_admin', in Filament tables. It allows users to filter records based on 'true', 'false', or all states of the column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/04-filters/03-ternary.md#_snippet_0

LANGUAGE: PHP
CODE:
```
use Filament\Tables\Filters\TernaryFilter;

TernaryFilter::make('is_admin')
```

----------------------------------------

TITLE: Setting First Day of Week Semantically for JavaScript DateTimePicker in Filament (PHP)
DESCRIPTION: This code demonstrates using semantic helper methods like `weekStartsOnMonday()` and `weekStartsOnSunday()` to configure the first day of the week for a JavaScript-enabled `DateTimePicker`. These methods provide a more readable way to set the calendar's start day for the 'published_at' field.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/08-date-time-picker.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\DateTimePicker;

DateTimePicker::make('published_at')
    ->native(false)
    ->weekStartsOnMonday();

DateTimePicker::make('published_at')
    ->native(false)
    ->weekStartsOnSunday();
```

----------------------------------------

TITLE: Making Notifications Persistent in PHP
DESCRIPTION: This snippet demonstrates how to make a Filament notification persistent in PHP using the `persistent()` method. A persistent notification will not automatically close and requires manual user dismissal.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/notifications/docs/02-sending-notifications.md#_snippet_13

LANGUAGE: php
CODE:
```
use Filament\Notifications\Notification;

Notification::make()
    ->title('Saved successfully')
    ->success()
    ->persistent()
    ->send();
```

----------------------------------------

TITLE: Creating Custom Summaries with using() in FilamentPHP
DESCRIPTION: This snippet demonstrates how to define a custom summary calculation for a table column using the `using()` method. The callback function receives the database query builder instance, allowing for complex calculations, and should return the value to be displayed as the summary.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/tables/docs/07-summaries.md#_snippet_21

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\Summarizers\Summarizer;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Query\Builder;

TextColumn::make('name')
    ->summarize(Summarizer::make()
        ->label('First last name')
        ->using(fn (Builder $query): string => $query->min('last_name')))
```

----------------------------------------

TITLE: Configuring Global TextEntry Word Limit in Filament PHP
DESCRIPTION: This snippet demonstrates how to set a global default word limit for all `TextEntry` components in Filament using the static `configureUsing()` method. This method should be called inside a service provider's `boot()` method, passing a Closure to modify the entries. Here, it sets the limit to 10 words for all `TextEntry` instances.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/03-entries/01-getting-started.md#_snippet_22

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\TextEntry;

TextEntry::configureUsing(function (TextEntry $entry): void {
    $entry
        ->words(10);
});
```

----------------------------------------

TITLE: Searching Multiple Columns in Filament Select Relationship (PHP)
DESCRIPTION: This code extends a searchable Filament Select field to allow searching across multiple columns of a related model. By passing an array of column names (`['name', 'email']`) to the `searchable()` method, the search functionality will query all specified attributes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_11

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Select;

Select::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->searchable(['name', 'email'])
```

----------------------------------------

TITLE: Publishing Filament Actions Migrations and Running Migrations (Artisan)
DESCRIPTION: These Artisan commands publish Filament-specific migrations required for the `ImportAction` and then run all pending database migrations. This step ensures that the necessary tables for Filament's import feature are created in the database.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/07-prebuilt-actions/08-import.md#_snippet_1

LANGUAGE: bash
CODE:
```
# All apps
php artisan vendor:publish --tag=filament-actions-migrations
php artisan migrate
```

----------------------------------------

TITLE: Asserting Existing Data in Filament Edit Form (PHP)
DESCRIPTION: This code demonstrates how to test if a Filament edit form is correctly pre-filled with existing record data. It creates a post, loads the edit page for that post, and then uses `assertFormSet()` to verify that the form fields match the original record's attributes.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can retrieve data', function () {
    $post = Post::factory()->create();

    livewire(PostResource\Pages\EditPost::class, [
        'record' => $post->getRouteKey(),
    ])
        ->assertFormSet([
            'author_id' => $post->author->getKey(),
            'content' => $post->content,
            'tags' => $post->tags,
            'title' => $post->title,
        ]);
});
```

----------------------------------------

TITLE: Configuring PostCSS for Tailwind CSS and Autoprefixer
DESCRIPTION: This JavaScript configuration for `postcss.config.js` registers PostCSS plugins. It sets up `tailwindcss/nesting` for CSS nesting, `tailwindcss` itself, and `autoprefixer` to automatically add vendor prefixes to CSS rules, ensuring broader browser compatibility.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/01-installation.md#_snippet_6

LANGUAGE: javascript
CODE:
```
export default {
    plugins: {
        'tailwindcss/nesting': 'postcss-nesting',
        tailwindcss: {},
        autoprefixer: {},
    },
}
```

----------------------------------------

TITLE: Defining Custom Modal Content with Blade View in Filament (PHP)
DESCRIPTION: This snippet shows how to render custom content inside a Filament action's modal by providing a Blade view to the `modalContent()` method. This allows for highly flexible and custom UI within the modal, separate from standard form fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/actions/docs/04-modals.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use App\Models\Post;

Action::make('advance')
    ->action(fn (Post $record) => $record->advance())
    ->modalContent(view('filament.pages.actions.advance'))
```

----------------------------------------

TITLE: Requiring a Filament Field When Others Are Present (PHP)
DESCRIPTION: This snippet shows how to use the `requiredWith` validation rule. The field value is required only if any of the other specified fields are not empty. This is useful for interdependent fields.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/05-validation.md#_snippet_38

LANGUAGE: PHP
CODE:
```
Field::make('name')->requiredWith('field,another_field')
```

----------------------------------------

TITLE: Customizing Relationship Query with Search in Filament Select (PHP)
DESCRIPTION: This example shows how to customize the relationship query for a Filament Select field while also accessing the current search input. By injecting `$search` into the `modifyQueryUsing` closure, you can implement custom search logic, such as a 'like' query on the 'name' column.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/03-fields/03-select.md#_snippet_15

LANGUAGE: PHP
CODE:
```
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

Select::make('author_id')
    ->relationship(
        name: 'author',
        titleAttribute: 'name',
        modifyQueryUsing: fn (Builder $query, string $search) => $query->where('name', 'like', "%{$search}%"),
    )
```

----------------------------------------

TITLE: Injecting Get Function for Accessing Other Field States - Filament PHP
DESCRIPTION: Demonstrates how to inject the `$get` callable into a form configuration closure by type-hinting the `$get` parameter with `Filament\Forms\Get`. This function allows retrieving the current state (value) of *other* fields within the same form by providing their name as an argument.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/forms/docs/07-advanced.md#_snippet_8

LANGUAGE: php
CODE:
```
use Filament\Forms\Get;

function (Get $get) {
    $email = $get('email'); // Store the value of the `email` field in the `$email` variable.
    //...
}
```

----------------------------------------

TITLE: Testing Form Validation in Filament PHP
DESCRIPTION: This snippet illustrates how to test form validation rules in Filament. It attempts to save a form with invalid data (e.g., 'title' as null) and then asserts that specific form errors are present, ensuring validation rules are enforced.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_10

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can validate input', function () {
    $post = Post::factory()->create();

    livewire(PostResource\Pages\EditPost::class, [
        'record' => $post->getRouteKey(),
    ])
        ->fillForm([
            'title' => null,
        ])
        ->call('save')
        ->assertHasFormErrors(['title' => 'required']);
});
```

----------------------------------------

TITLE: Asserting Table Records on Filament List Page (PHP)
DESCRIPTION: This example demonstrates how to use Livewire testing helpers to assert that specific records are visible within a Filament resource's list page table. It creates multiple `Post` records and then verifies their presence using `assertCanSeeTableRecords()` on the Livewire component.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/14-testing.md#_snippet_3

LANGUAGE: PHP
CODE:
```
use function Pest\Livewire\livewire;

it('can list posts', function () {
    $posts = Post::factory()->count(10)->create();

    livewire(PostResource\Pages\ListPosts::class)
        ->assertCanSeeTableRecords($posts);
});
```

----------------------------------------

TITLE: Defining a Custom Infolist Layout Component Class - PHP
DESCRIPTION: This PHP snippet illustrates the structure of a custom Filament Infolist component class generated by the Artisan command. It extends `Filament\Infolists\Components\Component`, defines the path to its associated Blade view, and includes a static `make` method for instantiation.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/infolists/docs/04-layout/07-custom.md#_snippet_2

LANGUAGE: php
CODE:
```
use Filament\Infolists\Components\Component;

class Box extends Component
{
    protected string $view = 'filament.infolists.components.box';

    public static function make(): static
    {
        return app(static::class);
    }
}
```

----------------------------------------

TITLE: Basic Structure of a Custom Filament View Page
DESCRIPTION: This Blade template provides a basic structure for a custom Filament view page, conditionally rendering an infolist or form and including relation managers. It serves as a starting point for highly customized page layouts, leveraging Filament's components.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/03-resources/05-viewing-records.md#_snippet_14

LANGUAGE: blade
CODE:
```
<x-filament-panels::page>
    @if ($this->hasInfolist())
        {{ $this->infolist }}
    @else
        {{ $this->form }}
    @endif

    @if (count($relationManagers = $this->getRelationManagers()))
        <x-filament-panels::resources.relation-managers
            :active-manager="$this->activeRelationManager"
            :managers="$relationManagers"
            :owner-record="$record"
            :page-class="static::class"
        />
    @endif
</x-filament-panels::page>
```

----------------------------------------

TITLE: Basic Spatie Media Library Image Column - PHP
DESCRIPTION: Registers a basic Spatie Media Library image column for displaying associated media in a Filament table. It will display media from the default collection.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/spatie-laravel-media-library-plugin/README.md#_snippet_13

LANGUAGE: php
CODE:
```
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;

SpatieMediaLibraryImageColumn::make('avatar')
```

----------------------------------------

TITLE: Conditionally Hiding Widgets in Filament PHP
DESCRIPTION: This snippet demonstrates how to control the visibility of a widget based on custom logic. By overriding the static `canView()` method, you can return a boolean value to determine whether the widget should be displayed to the current user.
SOURCE: https://github.com/filamentphp/filament/blob/3.x/packages/panels/docs/05-dashboard.md#_snippet_5

LANGUAGE: php
CODE:
```
public static function canView(): bool
{
    return auth()->user()->isAdmin();
}
```
