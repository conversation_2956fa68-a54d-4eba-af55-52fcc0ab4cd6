# Missing Arabic Localization Report

## Overview
This report identifies hardcoded English text strings in the Laravel Contractly application that should be translated using <PERSON><PERSON>'s native `__()` translation function for complete Arabic-first localization.

## Critical Issues Found

### ❌ **1. ContractResource - Hardcoded Strings**

#### **File**: `app/Filament/Resources/ContractResource.php`

**Line 54**: Hardcoded payment number prefix
```php
// ISSUE: Hardcoded 'PAY-' prefix
return 'PAY-' . str_pad("$nextId", 6, '0', STR_PAD_LEFT);

// SHOULD BE:
return __('filament-resources/contract.prefixes.payment') . str_pad("$nextId", 6, '0', STR_PAD_LEFT);
```

**Line 77**: Hardcoded currency prefix
```php
// ISSUE: Hardcoded 'SAR' prefix
->prefix('SAR'),

// SHOULD BE:
->prefix(__('filament-resources/contract.currency.sar')),
```

**Lines 147, 179**: Hardcoded status values in SQL queries
```php
// ISSUE: Hardcoded status strings in database queries
AND visits.status IN (\'scheduled\', \'in_progress\', \'completed\')

// SHOULD BE: Use constants or configuration
AND visits.status IN (?, ?, ?)
// With parameters: [Visit::STATUS_SCHEDULED, Visit::STATUS_IN_PROGRESS, Visit::STATUS_COMPLETED]
```

**Line 225**: Hardcoded status assignment
```php
// ISSUE: Hardcoded status value
$visit->status = 'scheduled';

// SHOULD BE:
$visit->status = Visit::STATUS_SCHEDULED;
```

### ❌ **2. PaymentResource - Hardcoded Strings**

#### **File**: `app/Filament/Resources/PaymentResource.php`

**Line 50**: Hardcoded payment number prefix
```php
// ISSUE: Hardcoded 'PAY-' prefix
return 'PAY-' . str_pad("$nextId", 6, '0', STR_PAD_LEFT);

// SHOULD BE:
return __('filament-resources/payment.prefixes.payment') . str_pad("$nextId", 6, '0', STR_PAD_LEFT);
```

**Line 73**: Hardcoded currency prefix
```php
// ISSUE: Hardcoded 'SAR' prefix
->prefix('SAR'),

// SHOULD BE:
->prefix(__('filament-resources/payment.currency.sar')),
```

**Line 221**: Hardcoded status assignment
```php
// ISSUE: Hardcoded status value
'status' => 'paid',

// SHOULD BE:
'status' => MaintenanceRequest::STATUS_PAID,
```

### ❌ **3. MaintenanceRequestResource - Hardcoded Strings**

#### **File**: `app/Filament/Resources/MaintenanceRequestResource.php`

**Line 308**: Hardcoded currency prefix
```php
// ISSUE: Hardcoded 'SAR' prefix
->prefix('SAR')

// SHOULD BE:
->prefix(__('filament-resources/maintenance-request.currency.sar'))
```

**Line 319**: Hardcoded field access
```php
// ISSUE: Direct field update without translation context
$record->update(['price' => $data['price']]);

// SHOULD BE: Add validation message translation
```

**Line 332**: Hardcoded payment number format
```php
// ISSUE: Hardcoded payment number format
$payment->payment_number = 'PAY-' . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);

// SHOULD BE:
$payment->payment_number = __('filament-resources/payment.prefixes.payment') . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);
```

**Line 379**: Hardcoded contract number format
```php
// ISSUE: Hardcoded contract number format
return 'CNT-' . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);

// SHOULD BE:
return __('filament-resources/contract.prefixes.contract') . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);
```

### ❌ **4. DocumentTemplateController - Hardcoded Arabic Messages**

#### **File**: `app/Http/Controllers/Admin/DocumentTemplateController.php`

**Line 26**: Hardcoded Arabic error message
```php
// ISSUE: Hardcoded Arabic text
abort(404, 'نوع القالب غير صالح');

// SHOULD BE:
abort(404, __('document-template.errors.invalid_template_type'));
```

**Line 67**: Hardcoded Arabic error message
```php
// ISSUE: Hardcoded Arabic text
'message' => 'نوع القالب غير صالح',

// SHOULD BE:
'message' => __('document-template.errors.invalid_template_type'),
```

**Line 79**: Hardcoded Arabic validation message
```php
// ISSUE: Hardcoded Arabic text
'message' => 'بيانات غير صالحة',

// SHOULD BE:
'message' => __('document-template.errors.invalid_data'),
```

**Line 178**: Hardcoded Arabic error message
```php
// ISSUE: Hardcoded Arabic text
abort(404, 'نوع القالب غير صالح');

// SHOULD BE:
abort(404, __('document-template.errors.invalid_template_type'));
```

**Line 186**: Hardcoded Arabic error message
```php
// ISSUE: Hardcoded Arabic text
'message' => 'فشل تحميل معاينة القالب. تأكد من صحة إعدادات DocKing وتمت مزامنة القالب.',

// SHOULD BE:
'message' => __('document-template.errors.preview_failed'),
```

**Line 234**: Hardcoded Arabic error message
```php
// ISSUE: Hardcoded Arabic text
'message' => 'فشل إنشاء قالب مؤقت للمعاينة',

// SHOULD BE:
'message' => __('document-template.errors.temp_template_failed'),
```

### ❌ **5. Notification Classes - Hardcoded Arabic Messages**

#### **File**: `app/Notifications/OTPNotification.php`

**Line 18**: Hardcoded Arabic message
```php
// ISSUE: Hardcoded Arabic text
public static $defaultMessage = 'رمز التحقق: {otp}' . "\r\n" . 'للدخول لمنصة {family_name}';

// SHOULD BE:
public static $defaultMessage = null; // Load from translation file
public static function getDefaultMessage() {
    return __('notifications.otp.default_message');
}
```

**Lines 24-27**: Hardcoded Arabic parameter descriptions
```php
// ISSUE: Hardcoded Arabic text
public static $parameters = [
    '{otp}' => 'رمز التأكيد otp',
    '{domain}' => 'المنصة',
    '{family_name}' => 'اسم العائلة',
];

// SHOULD BE:
public static function getParameters() {
    return [
        '{otp}' => __('notifications.otp.parameters.otp'),
        '{domain}' => __('notifications.otp.parameters.domain'),
        '{family_name}' => __('notifications.otp.parameters.family_name'),
    ];
}
```

## Missing Translation Keys Required

### **1. Contract Resource Translations**
Add to `lang/ar/filament-resources/contract.php`:
```php
'prefixes' => [
    'payment' => 'دفع-',
    'contract' => 'عقد-',
],
'currency' => [
    'sar' => 'ريال',
],
```

### **2. Payment Resource Translations**
Add to `lang/ar/filament-resources/payment.php`:
```php
'prefixes' => [
    'payment' => 'دفع-',
],
'currency' => [
    'sar' => 'ريال',
],
```

### **3. Maintenance Request Translations**
Add to `lang/ar/filament-resources/maintenance-request.php`:
```php
'currency' => [
    'sar' => 'ريال',
],
```

### **4. Document Template Translations**
Create `lang/ar/document-template.php`:
```php
<?php
return [
    'errors' => [
        'invalid_template_type' => 'نوع القالب غير صالح',
        'invalid_data' => 'بيانات غير صالحة',
        'preview_failed' => 'فشل تحميل معاينة القالب. تأكد من صحة إعدادات DocKing وتمت مزامنة القالب.',
        'temp_template_failed' => 'فشل إنشاء قالب مؤقت للمعاينة',
    ],
];
```

### **5. Notification Translations**
Create `lang/ar/notifications.php`:
```php
<?php
return [
    'otp' => [
        'default_message' => 'رمز التحقق: {otp}' . "\r\n" . 'للدخول لمنصة {family_name}',
        'parameters' => [
            'otp' => 'رمز التأكيد otp',
            'domain' => 'المنصة',
            'family_name' => 'اسم العائلة',
        ],
    ],
];
```

## Recommendations

### **1. Use Constants for Status Values**
Replace hardcoded status strings with model constants:
```php
// In Visit model
const STATUS_SCHEDULED = 'scheduled';
const STATUS_IN_PROGRESS = 'in_progress';
const STATUS_COMPLETED = 'completed';
const STATUS_CANCELLED = 'cancelled';
```

### **2. Create Configuration for Prefixes**
Move hardcoded prefixes to configuration:
```php
// config/app.php
'prefixes' => [
    'payment' => env('PAYMENT_PREFIX', 'PAY-'),
    'contract' => env('CONTRACT_PREFIX', 'CNT-'),
],
```

### **3. Implement Translation Helper**
Create a helper for consistent translation usage:
```php
// app/Helpers/TranslationHelper.php
class TranslationHelper {
    public static function currency($amount, $currency = 'SAR') {
        return $amount . ' ' . __('currency.' . strtolower($currency));
    }
}
```

### **4. Add Validation Message Translations**
Ensure all validation messages use Laravel's validation translation system.

### **5. Review All Notification Classes**
Audit all notification classes for hardcoded Arabic text and move to translation files.

## Priority Actions

1. **High Priority**: Fix hardcoded Arabic messages in controllers and notifications
2. **Medium Priority**: Replace hardcoded prefixes and currency symbols
3. **Low Priority**: Move status constants to model definitions

This comprehensive localization will ensure complete Arabic-first user experience while maintaining Laravel best practices.
