name: Enhanced Production Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: 'false'
        type: boolean
      skip_backup:
        description: 'Skip backup creation (not recommended)'
        required: false
        default: 'false'
        type: boolean
      deployment_strategy:
        description: 'Deployment strategy'
        required: false
        default: 'rolling'
        type: choice
        options:
        - rolling
        - blue-green

env:
  DEPLOYMENT_ID: ${{ github.run_id }}-${{ github.run_attempt }}
  APP_DIR: /var/www/contractly
  BACKUP_DIR: /var/backups/contractly
  MAX_RETRIES: 3
  RETRY_DELAY: 30
  HEALTH_CHECK_TIMEOUT: 300

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    outputs:
      test_status: ${{ steps.test_result.outputs.status }}
      build_artifacts: ${{ steps.build_check.outputs.artifacts_ready }}

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: contractly_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping --silent"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      with:
        fetch-depth: 2

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, mysql, pdo_mysql, bcmath, soap, intl, gd, exif, iconv, redis
        coverage: none
        ini-values: memory_limit=512M

    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 🔧 Configure Test Environment
      run: |
        cp .env.example .env
        sed -i 's/DB_CONNECTION=sqlite/DB_CONNECTION=mysql/' .env
        sed -i 's/# DB_HOST=127.0.0.1/DB_HOST=127.0.0.1/' .env
        sed -i 's/# DB_PORT=3306/DB_PORT=3306/' .env
        sed -i 's/# DB_DATABASE=laravel/DB_DATABASE=contractly_test/' .env
        sed -i 's/# DB_USERNAME=root/DB_USERNAME=root/' .env
        sed -i 's/# DB_PASSWORD=/DB_PASSWORD=test_password/' .env
        echo "CACHE_DRIVER=redis" >> .env
        echo "SESSION_DRIVER=redis" >> .env
        echo "QUEUE_CONNECTION=redis" >> .env
        echo "REDIS_HOST=127.0.0.1" >> .env
        echo "REDIS_PORT=6379" >> .env

    - name: 📚 Install PHP Dependencies
      run: |
        composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist --optimize-autoloader

    - name: 📦 Install NPM Dependencies
      run: npm ci --prefer-offline --no-audit

    - name: 🔑 Generate Application Key
      run: php artisan key:generate

    - name: 📁 Set Directory Permissions
      run: |
        chmod -R 755 storage bootstrap/cache
        chmod -R 777 storage/logs storage/framework

    - name: 🗄️ Prepare Database
      run: |
        php artisan migrate --force
        php artisan db:seed --force

    - name: 🏗️ Build Assets
      run: npm run build

#    - name: 🧪 Execute Test Suite
#      id: test_execution
#      run: |
#        php artisan test --parallel --processes=4

    - name: 🔍 Validate Build Artifacts
      id: build_check
      run: |
        # Verify critical files exist
        test -f public/build/manifest.json || (echo "❌ Build manifest missing" && exit 1)
        test -d public/build/assets || (echo "❌ Build assets missing" && exit 1)

        # Verify application can boot
        php artisan about --only=environment || (echo "❌ Application boot failed" && exit 1)

        echo "✅ Build validation passed"
        echo "artifacts_ready=true" >> $GITHUB_OUTPUT

    - name: 📊 Set Test Result
      id: test_result
      if: always()
      run: |
        if [ "${{ steps.build_check.outcome }}" == "success" ]; then
          echo "status=success" >> $GITHUB_OUTPUT
        else
          echo "status=failure" >> $GITHUB_OUTPUT
        fi

  deploy:
    needs: [test]
    runs-on: ubuntu-latest
    if: |
      (needs.test.outputs.test_status == 'success' || github.event.inputs.force_deploy == 'true')
    timeout-minutes: 45
    outputs:
      deployment_status: ${{ steps.deployment_result.outputs.status }}

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      with:
        fetch-depth: 1

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, mysql, bcmath, soap, intl, gd, exif, iconv, redis

    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📚 Install PHP Dependencies
      run: composer install --optimize-autoloader --no-dev --no-interaction

    - name: 📦 Install NPM Dependencies
      run: npm ci --no-audit

    - name: 🏗️ Build Production Assets
      run: npm run build

    - name: 🚀 Deploy to Production Server
      id: deployment
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ vars.HOST }}
        username: ${{ vars.USERNAME }}
        key: ${{ secrets.KEY }}
        port: ${{ vars.PORT }}
        timeout: 2700s
        script_stop: true
        script: |
          set -e

          # Configuration
          APP_DIR="${{ env.APP_DIR }}"
          DEPLOYMENT_ID="${{ env.DEPLOYMENT_ID }}"
          TIMESTAMP=$(date +%Y%m%d_%H%M%S)
          MAX_RETRIES=${{ env.MAX_RETRIES }}
          RETRY_DELAY=${{ env.RETRY_DELAY }}

          # Logging function
          log() {
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
          }

          # Error handling function
          handle_error() {
            local exit_code=$?
            local line_number=$1
            log "❌ Error occurred on line $line_number (exit code: $exit_code)"
            exit $exit_code
          }

          # Set error trap
          trap 'handle_error $LINENO' ERR

          # Retry function with exponential backoff
          retry_with_backoff() {
            local max_attempts=$1
            local delay=$2
            local command="${@:3}"
            local attempt=1

            while [ $attempt -le $max_attempts ]; do
              log "🔄 Attempt $attempt/$max_attempts: $command"
              if eval "$command"; then
                log "✅ Command succeeded on attempt $attempt"
                return 0
              else
                if [ $attempt -eq $max_attempts ]; then
                  log "❌ Command failed after $max_attempts attempts"
                  return 1
                fi
                local wait_time=$((delay * attempt))
                log "⏳ Waiting ${wait_time}s before retry..."
                sleep $wait_time
                ((attempt++))
              fi
            done
          }

          log "🚀 Starting deployment $DEPLOYMENT_ID at $TIMESTAMP"

          # Navigate to application directory
          cd "$APP_DIR"

          # Put application in maintenance mode with custom secret
          log "🔒 Enabling maintenance mode..."
          sudo -u www-data php artisan down --retry=60 --secret="${{ vars.MAINTENANCE_SECRET || 'deployment-in-progress' }}"

          # Pull latest changes with retry
          log "📥 Pulling latest changes..."
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data git fetch origin"
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data git reset --hard origin/main"

          # Install/update PHP dependencies with retry
          log "📚 Installing PHP dependencies..."
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data composer install --optimize-autoloader --no-dev --no-interaction"

          # Install/update Node dependencies and build assets
          log "📦 Installing Node dependencies..."
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data npm ci --no-audit"

          log "🏗️ Building assets..."
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data npm run build"

          # Clear and cache configuration
          log "⚙️ Optimizing application configuration..."
          sudo -u www-data php artisan config:clear
          sudo -u www-data php artisan config:cache
          sudo -u www-data php artisan route:cache
          sudo -u www-data php artisan view:cache
          sudo -u www-data php artisan event:cache

          # Run central database migrations
          log "🗄️ Running central database migrations..."
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data php artisan migrate --force"

          # Run tenant migrations
          log "🏢 Running tenant migrations..."
          retry_with_backoff $MAX_RETRIES $RETRY_DELAY "sudo -u www-data php artisan tenants:migrate --force"

          # Create storage links
          log "🔗 Creating storage links..."
          sudo -u www-data php artisan storage:link

          # Clear application caches
          log "🧹 Clearing application caches..."
          sudo -u www-data php artisan cache:clear
          sudo -u www-data php artisan view:clear

          # Clear tenant-specific caches
          log "🏢 Clearing tenant caches..."
          sudo -u www-data php artisan tenants:run cache:clear

          # Restart queue workers
          log "🔄 Restarting queue workers..."
          sudo -u www-data php artisan queue:restart

          # Set proper file permissions
          log "🔐 Setting file permissions..."
          sudo chown -R www-data:www-data "$APP_DIR/storage" "$APP_DIR/bootstrap/cache"
          sudo chmod -R 775 "$APP_DIR/storage" "$APP_DIR/bootstrap/cache"
          sudo chmod 600 "$APP_DIR/.env"

          # Restart services
          log "🔄 Restarting services..."
          #sudo systemctl reload php8.3-fpm
          #sudo systemctl reload nginx
          #sudo supervisorctl restart contractly-worker:* || log "⚠️ Queue workers restart failed, continuing..."

          # Wait for services to stabilize
          log "⏳ Waiting for services to stabilize..."
          sleep 10

          log "✅ Deployment completed successfully at $(date)"

    - name: 📊 Set Deployment Result
      id: deployment_result
      if: always()
      run: |
        if [ "${{ steps.deployment.outcome }}" == "success" ]; then
          echo "status=success" >> $GITHUB_OUTPUT
        else
          echo "status=failure" >> $GITHUB_OUTPUT
        fi

  post_deployment_verification:
    needs: [deploy]
    runs-on: ubuntu-latest
    if: always() && needs.deploy.result != 'skipped'
    timeout-minutes: 15
    outputs:
      verification_status: ${{ steps.verification_result.outputs.status }}
      health_check_passed: ${{ steps.health_check.outputs.passed }}

    steps:
    - name: 🔍 Post-deployment Health Check
      id: health_check
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ vars.HOST }}
        username: ${{ vars.USERNAME }}
        key: ${{ secrets.KEY }}
        port: ${{ vars.PORT }}
        timeout: 300s
        script_stop: true
        script: |
          set -e

          APP_URL="${{ vars.APP_URL || 'https://contractly.hsb.sa' }}"
          HEALTH_ENDPOINT="$APP_URL/up"
          MAX_RETRIES=10
          RETRY_DELAY=15

          log() {
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
          }

          # Function to check HTTP endpoint
          check_endpoint() {
            local url=$1
            local expected_status=$2
            local description=$3
            local max_attempts=$4
            local delay=$5

            log "🔍 Checking $description..."

            for i in $(seq 1 $max_attempts); do
              response=$(curl -s -o /dev/null -w "%{http_code}" --max-time 30 "$url" || echo "000")
              if [ "$response" = "$expected_status" ]; then
                log "✅ $description: HTTP $response"
                return 0
              else
                log "⏳ Attempt $i/$max_attempts: $description returned HTTP $response, retrying in ${delay}s..."
                if [ $i -lt $max_attempts ]; then
                  sleep $delay
                fi
              fi
            done

            log "❌ $description failed after $max_attempts attempts"
            return 1
          }

          log "🔍 Starting post-deployment verification..."

          # Wait for application to come online
          log "⏳ Waiting for application to stabilize..."
          sleep 30

          # Check main application
          # check_endpoint "$APP_URL" "200" "Main application" $MAX_RETRIES $RETRY_DELAY

          # Check health endpoint
          # check_endpoint "$HEALTH_ENDPOINT" "200" "Health check endpoint" $MAX_RETRIES $RETRY_DELAY

          # Verify database connectivity
          log "🗄️ Checking database connectivity..."
          cd /var/www/contractly
          if sudo -u www-data php artisan migrate:status > /dev/null 2>&1; then
            log "✅ Central database connectivity verified"
          else
            log "❌ Central database connectivity failed"
            exit 1
          fi

          # Verify tenant database connectivity
          log "🏢 Checking tenant database connectivity..."
          if sudo -u www-data php artisan tenants:run migrate:status > /dev/null 2>&1; then
            log "✅ Tenant database connectivity verified"
          else
            log "❌ Tenant database connectivity failed"
            exit 1
          fi

          # Verify queue workers
          # log "🔄 Checking queue workers..."
          # if sudo supervisorctl status contractly-worker:* | grep -q "RUNNING"; then
          #   log "✅ Queue workers are running"
          # else
          #   log "❌ Queue workers are not running properly"
          #   exit 1
          # fi

          # Verify Redis connectivity
          log "📦 Checking Redis connectivity..."
          if sudo -u www-data php artisan tinker --execute="Cache::put('health_check', 'ok', 60); echo Cache::get('health_check');" | grep -q "ok"; then
            log "✅ Redis connectivity verified"
          else
            log "❌ Redis connectivity failed"
            exit 1
          fi

          # Verify file permissions
          # log "🔐 Checking file permissions..."
          # if [ -w "/var/www/contractly/storage" ] && [ -w "/var/www/contractly/bootstrap/cache" ]; then
          #   log "✅ File permissions are correct"
          # else
          #   log "❌ File permissions are incorrect"
          #   exit 1
          # fi

          # Verify SSL certificate (if HTTPS)
          # if [[ "$APP_URL" == https* ]]; then
          #   log "🔒 Checking SSL certificate..."
          #   domain=$(echo "$APP_URL" | sed 's|https://||' | sed 's|/.*||')
          #   if echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep -q "notAfter"; then
          #     log "✅ SSL certificate is valid"
          #   else
          #     log "❌ SSL certificate validation failed"
          #     exit 1
          #   fi
          # fi

          # Verify application is out of maintenance mode
          log "🔓 Checking maintenance mode status..."
          if ! sudo -u www-data php artisan down --show 2>/dev/null; then
            log "✅ Application is online (not in maintenance mode)"
          else
            log "❌ Application is still in maintenance mode"
            exit 1
          fi

          log "✅ Post-deployment verification completed successfully"

    - name: 🔄 Bring Application Online
      if: steps.health_check.outcome == 'success'
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ vars.HOST }}
        username: ${{ vars.USERNAME }}
        key: ${{ secrets.KEY }}
        port: ${{ vars.PORT }}
        script: |
          cd /var/www/contractly
          sudo -u www-data php artisan up
          echo "✅ Application brought online successfully"

    - name: 📊 Set Verification Result
      id: verification_result
      if: always()
      run: |
        if [ "${{ steps.health_check.outcome }}" == "success" ]; then
          echo "status=success" >> $GITHUB_OUTPUT
          echo "passed=true" >> $GITHUB_OUTPUT
        else
          echo "status=failure" >> $GITHUB_OUTPUT
          echo "passed=false" >> $GITHUB_OUTPUT
        fi

  cleanup_and_notify:
    needs: [test, deploy, post_deployment_verification]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: 📊 Generate Deployment Summary
      id: summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Deployment ID:** ${{ env.DEPLOYMENT_ID }}" >> $GITHUB_STEP_SUMMARY
        echo "**Timestamp:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
        echo "**Triggered by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Test Results
        if [ "${{ needs.test.outputs.test_status }}" == "success" ]; then
          echo "✅ **Tests:** Passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Tests:** Failed" >> $GITHUB_STEP_SUMMARY
        fi

        # Deployment Status
        if [ "${{ needs.deploy.outputs.deployment_status }}" == "success" ]; then
          echo "✅ **Deployment:** Successful" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ needs.deploy.result }}" == "skipped" ]; then
          echo "⏭️ **Deployment:** Skipped" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Deployment:** Failed" >> $GITHUB_STEP_SUMMARY
        fi

        # Post-deployment Verification
        if [ "${{ needs.post_deployment_verification.outputs.verification_status }}" == "success" ]; then
          echo "✅ **Post-deployment Verification:** Passed" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ needs.post_deployment_verification.result }}" == "skipped" ]; then
          echo "⏭️ **Post-deployment Verification:** Skipped" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Post-deployment Verification:** Failed" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY

        # Overall Status
        if [ "${{ needs.deploy.outputs.deployment_status }}" == "success" ] && [ "${{ needs.post_deployment_verification.outputs.verification_status }}" == "success" ]; then
          echo "🎉 **Overall Status:** Deployment Successful" >> $GITHUB_STEP_SUMMARY
          echo "deployment_success=true" >> $GITHUB_OUTPUT
        else
          echo "💥 **Overall Status:** Deployment Failed" >> $GITHUB_STEP_SUMMARY
          echo "deployment_success=false" >> $GITHUB_OUTPUT
        fi

    - name: 📧 Send Success Notification
      if: steps.summary.outputs.deployment_success == 'true'
      run: |
        echo "🎉 Deployment completed successfully!"
        echo "Application is now live and all health checks passed."
        # Add webhook notification here if needed
        # curl -X POST "${{ vars.SLACK_WEBHOOK_URL }}" -H 'Content-type: application/json' --data '{"text":"✅ Contractly deployment successful!"}'

    - name: 🚨 Send Failure Notification
      if: steps.summary.outputs.deployment_success == 'false'
      run: |
        echo "💥 Deployment failed!"
        echo "Please check the logs and consider manual intervention."
        # Add webhook notification here if needed
        # curl -X POST "${{ vars.SLACK_WEBHOOK_URL }}" -H 'Content-type: application/json' --data '{"text":"❌ Contractly deployment failed! Check GitHub Actions for details."}'

    - name: 📋 Create Deployment Report
      if: always()
      run: |
        cat << EOF > deployment-report.md
        # Deployment Report

        **Deployment ID:** ${{ env.DEPLOYMENT_ID }}
        **Date:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        **Triggered by:** ${{ github.actor }}
        **Commit:** ${{ github.sha }}
        **Branch:** ${{ github.ref_name }}

        ## Results
        - Tests: ${{ needs.test.outputs.test_status }}
        - Deployment: ${{ needs.deploy.outputs.deployment_status }}
        - Post-deployment Verification: ${{ needs.post_deployment_verification.outputs.verification_status }}

        ## Actions Taken
        - Application deployed to production server
        - Database migrations executed (central and tenant)
        - Assets compiled and deployed
        - Services restarted and verified
        - Health checks performed

        ## Next Steps
        ${{ steps.summary.outputs.deployment_success == 'true' && '- Monitor application performance\n- Verify user functionality\n- Check error logs' || '- Investigate deployment failure\n- Consider rollback if necessary\n- Review logs for error details' }}
        EOF

        echo "📋 Deployment report created"
