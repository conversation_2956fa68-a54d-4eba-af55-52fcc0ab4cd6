<?php

return [
    'auth' => [
        'title' => 'Technician Login',
        'heading' => 'Login to Technician Panel',
        'login_label' => 'Email or Phone Number',
        'login_placeholder' => 'Enter email or phone number',
        'password_label' => 'Password',
        'password_placeholder' => 'Enter password',
        'remember_label' => 'Remember me',
        'submit_label' => 'Login',
        'failed' => 'Invalid login credentials.',
        'unauthorized' => 'You are not authorized to access the technician panel.',
        'logout' => 'Logout',
    ],

    'navigation' => [
        'dashboard' => 'Dashboard',
        'groups' => [
            'maintenance' => 'Maintenance Management',
            'reports' => 'Reports & Documentation',
        ],
        'items' => [
            'maintenance_requests' => 'Maintenance Requests',
            'my_reports' => 'My Reports',
        ],
    ],

    'dashboard' => [
        'title' => 'Technician Dashboard',
        'heading' => 'Technician Dashboard',
        'subheading' => 'Hello :name, here\'s a summary of your assigned maintenance requests',
    ],

    'resources' => [
        'maintenance_request' => [
            'label' => 'Maintenance Request',
            'plural' => 'Maintenance Requests',

            'sections' => [
                'request_overview' => 'Request Overview',
                'request_details' => 'Request Details',
                'work_update' => 'Work Update',
                'client_info' => 'Client Information',
                'contract_info' => 'Contract Information',
                'timeline' => 'Timeline',
            ],

            'fields' => [
                'request_number' => 'Request Number',
                'title' => 'Title',
                'description' => 'Description',
                'status' => 'Status',
                'client_name' => 'Client Name',
                'client_phone' => 'Client Phone',
                'client_email' => 'Client Email',
                'client_address' => 'Client Address',
                'contract_type' => 'Contract Type',
                'price' => 'Price',
                'visits_included' => 'Visits Included',
                'work_report' => 'Work Report',
                'created_at' => 'Created At',
                'updated_at' => 'Updated At',
                'completed_at' => 'Completed At',
            ],

            'columns' => [
                'request_number' => 'Request Number',
                'title' => 'Title',
                'client' => 'Client',
                'phone' => 'Phone',
                'status' => 'Status',
                'created_at' => 'Created At',
            ],

            'status_options' => [
                'new' => 'New Request',
                'pending' => 'Under Review',
                'assigned' => 'Assigned',
                'in_progress' => 'In Progress',
                'completed' => 'Completed',
                'canceled' => 'Canceled',
            ],

            'filters' => [
                'status' => 'Status',
            ],

            'actions' => [
                'view' => 'View',
                'edit' => 'Edit',
                'submit_report' => 'Submit Report',
                'view_report' => 'View Report',
                'create_report' => 'Create New Report',
                'view_reports' => 'View Reports',
                'start_work' => 'Start Work',
                'complete_work' => 'Complete Work',
                'call_client' => 'Call Client',
            ],

            'placeholders' => [
                'work_report' => 'Write a detailed report about the work completed, issues encountered, and solutions applied...',
                'no_notes' => 'No notes available',
            ],

            'helpers' => [
                'work_report' => 'Write a comprehensive report about the completed work to help the team with follow-up',
            ],

            'pages' => [
                'list' => [
                    'title' => 'Assigned Maintenance Requests',
                    'heading' => 'Assigned Maintenance Requests',
                ],
                'view' => [
                    'title' => 'View Maintenance Request :number',
                    'heading' => 'Maintenance Request :number',
                ],
                'edit' => [
                    'title' => 'Edit Maintenance Request :number',
                    'heading' => 'Edit Maintenance Request :number',
                ],
            ],

            'notifications' => [
                'updated' => 'Maintenance request updated successfully',
                'updated_body' => 'Changes to the maintenance request have been saved.',
                'status_updated' => 'Request status updated',
                'status_updated_body' => 'Maintenance request status has been updated successfully.',
                'access_denied' => 'Access denied',
                'access_denied_body' => 'You cannot access this request.',
            ],

            'relation_managers' => [
                'documents' => [
                    'title' => 'Documents',

                    'sections' => [
                        'document_details' => 'Document Details',
                        'file_upload' => 'File Upload',
                    ],

                    'fields' => [
                        'title' => 'Document Title',
                        'file' => 'File',
                        'description' => 'Description',
                    ],

                    'columns' => [
                        'title' => 'Title',
                        'file_type' => 'File Type',
                        'file_size' => 'File Size',
                        'uploaded_by' => 'Uploaded By',
                        'uploaded_at' => 'Upload Date',
                    ],

                    'placeholders' => [
                        'title' => 'Enter document title',
                        'description' => 'Add document description...',
                        'system' => 'System',
                    ],

                    'helpers' => [
                        'file_upload' => 'Maximum file size: 10 MB. Supported types: Images, PDF.',
                    ],

                    'filters' => [
                        'file_type' => 'File Type',
                        'my_uploads' => 'My Files Only',
                    ],

                    'actions' => [
                        'upload' => 'Upload Document',
                        'download' => 'Download',
                        'view' => 'View',
                        'edit' => 'Edit',
                        'delete' => 'Delete',
                        'delete_selected' => 'Delete Selected',
                    ],

                    'empty' => [
                        'heading' => 'No Documents',
                        'description' => 'No documents have been uploaded for this request yet.',
                    ],
                ],
            ],

            'validation' => [
                'status_required' => 'Status field is required.',
                'notes_max' => 'Notes must not exceed :max characters.',
            ],

            'messages' => [
                'no_requests_assigned' => 'No maintenance requests are currently assigned to you.',
                'request_completed' => 'Maintenance request completed successfully.',
                'loading' => 'Loading...',
                'save_changes' => 'Save Changes',
                'cancel' => 'Cancel',
                'confirm' => 'Confirm',
                'no_requests' => 'No maintenance requests assigned to you currently',
                'work_started' => 'Work started successfully',
                'work_completed' => 'Work completed successfully',
                'status_updated' => 'Request status updated',
                'report_required' => 'A report must be submitted before completing work',
            ],
        ],

        'report' => [
            'label' => 'Technical Report',
            'plural' => 'Technical Reports',

            'sections' => [
                'basic_info' => 'Basic Information',
                'work_details' => 'Work Details',
                'checklist' => 'Checklist',
                'attachments' => 'Attachments',
                'summary' => 'Report Summary',
            ],

            'fields' => [
                'maintenance_request' => 'Maintenance Request',
                'time_spent' => 'Time Spent',
                'time_spent_suffix' => 'minutes',
                'status' => 'Report Status',
                'work_summary' => 'Work Summary',
                'findings' => 'Findings and Observations',
                'recommendations' => 'Recommendations',
                'issues_found' => 'Issues Found',
                'parts_used' => 'Parts Used',
                'attachments' => 'Attachments',
                'drawing_area' => 'Drawing Area',
                'submitted_at' => 'Submitted At',
            ],

            'tabs' => [
                'file_uploads' => 'File Uploads',
                'drawing_canvas' => 'Drawing & Illustration',
            ],

            'placeholders' => [
                'work_summary' => 'Write a comprehensive summary of the work completed...',
                'findings' => 'Mention important findings and observations discovered during work...',
                'recommendations' => 'Provide recommendations for future maintenance or required improvements...',
                'issues_found' => 'List any problems or failures discovered...',
                'parts_used' => 'List parts or materials used in the maintenance...',
            ],

            'helpers' => [
                'time_spent' => 'Enter time spent in minutes',
                'attachments' => 'You can upload images and documents (max 10 MB per file, 10 files maximum)',
                'attachments_unified' => 'You can upload files or create illustrations (max 10 MB per file, 15 files maximum)',
                'attachments_enhanced' => 'File uploads and attachments - you can upload images, documents, and illustrations. Drag files here or click to select. You can reorder files by drag and drop. Maximum: 20 files, 10 MB per file.',
                'work_summary' => 'Summary should be clear and detailed',
                'multiple_reports_allowed' => 'You can create multiple reports for the same maintenance request',
            ],

            'status_options' => [
                'draft' => 'Draft',
                'submitted' => 'Submitted',
                'approved' => 'Approved',
                'rejected' => 'Rejected',
            ],

            'checklist' => [
                'fields' => [
                    'category' => 'Category',
                    'description' => 'Description',
                    'status' => 'Status',
                    'notes' => 'Notes',
                    'is_required' => 'Required',
                ],

                'categories' => [
                    'safety' => 'Safety & Security',
                    'electrical' => 'Electrical Systems',
                    'mechanical' => 'Mechanical Systems',
                    'cleaning' => 'Cleaning & Maintenance',
                    'testing' => 'Testing & Inspections',
                    'documentation' => 'Documentation',
                    'general' => 'General',
                ],

                'status_options' => [
                    'not_checked' => 'Not Checked',
                    'passed' => 'Passed',
                    'failed' => 'Failed',
                    'not_applicable' => 'Not Applicable',
                    'needs_attention' => 'Needs Attention',
                ],

                'actions' => [
                    'add_item' => 'Add Item',
                    'remove_item' => 'Remove Item',
                    'move_up' => 'Move Up',
                    'move_down' => 'Move Down',
                ],

                'placeholders' => [
                    'notes' => 'Add additional notes about this item...',
                ],

                'loading_items' => 'Loading checklist items...',
                'no_items_for_category' => 'No items in this category',
                'no_items' => 'No items in checklist',
                'loading' => 'Loading checklist...',
            ],

            'actions' => [
                'create' => 'Create New Report',
                'view' => 'View Report',
                'edit' => 'Edit Report',
                'submit' => 'Submit Report',
                'delete' => 'Delete Report',
                'download' => 'Download Report',
                'print' => 'Print Report',
                'submit_modal_heading' => 'Confirm Report Submission',
                'submit_modal_description' => 'Are you sure you want to submit this report? You will not be able to edit it after submission.',
            ],

            'columns' => [
                'request_number' => 'Request Number',
                'client' => 'Client',
                'report_number' => 'Report Number',
                'status' => 'Status',
                'time_spent' => 'Time Spent',
                'attachments' => 'Attachments',
                'files' => 'file',
                'images' => 'image',
                'documents' => 'document',
                'drawings' => 'drawing',
                'submitted_at' => 'Submitted At',
                'created_at' => 'Created At',
            ],

            'filters' => [
                'status' => 'Report Status',
                'maintenance_request' => 'Maintenance Request',
                'submitted_today' => 'Submitted Today',
                'pending_submission' => 'Pending Submission',
                'with_attachments' => 'With Attachments',
            ],

            'messages' => [
                'created_successfully' => 'Report created successfully',
                'created_with_checklist' => 'Default checklist has been added',
                'updated_successfully' => 'Report updated successfully',
                'submitted_successfully' => 'Report submitted successfully',
                'submit_failed' => 'Failed to submit report',
                'deleted_successfully' => 'Report deleted successfully',
                'validation_required_fields' => 'Please fill all required fields',
                'no_reports' => 'No reports yet',
            ],

            'validation' => [
                'required_checklist_item' => 'This item is required and must be marked as completed',
                'required_checklist_items_title' => 'Required items incomplete',
                'required_checklist_items_message' => 'Please complete all required checklist items before proceeding',
                'attachments_must_be_array' => 'Attachments must be in proper list format',
                'invalid_file_at_position' => 'File at position :position is invalid',
            ],

            'drawing' => [
                'instructions' => 'Use the drawing area below to create diagrams, illustrations, or handwritten notes. You can use mouse or touch to draw.',
                'instructions_unified' => 'Use the drawing area to create diagrams or illustrations. When saved, the drawing will automatically be added to the attachments list in the "File Uploads" tab.',

                'tools' => [
                    'pen' => 'Pen',
                    'eraser' => 'Eraser',
                ],

                'stroke_width' => 'Stroke Width',
                'color' => 'Color',

                'actions' => [
                    'clear' => 'Clear All',
                    'save' => 'Save Drawing',
                    'save_to_attachments' => 'Save to Attachments',
                    'saving' => 'Saving...',
                    'view_in_uploads' => 'View in Attachments',
                ],

                'status' => [
                    'has_content' => 'Contains drawing',
                    'empty' => 'Drawing area is empty',
                    'saved_to_attachments' => 'Saved to attachments',
                ],

                'messages' => [
                    'saved' => 'Drawing saved',
                    'saved_description' => 'Drawing has been saved successfully and will be attached with the report',
                    'saved_to_attachments' => 'Drawing added to attachments',
                    'saved_to_attachments_description' => 'Drawing has been saved and automatically added to the attachments list. You can see it in the "File Uploads" tab.',
                    'no_content' => 'No content to save',
                    'no_content_description' => 'Please draw something first before trying to save',
                    'save_error' => 'Error saving drawing',
                    'save_error_description' => 'An error occurred while saving the drawing. Please try again.',
                    'clear_after_save' => 'Clear the canvas after saving?',
                    'file_already_exists' => 'File already exists',
                    'file_already_exists_description' => 'This drawing already exists in the attachments list.',
                ],
            ],

            'pages' => [
                'list' => [
                    'title' => 'My Technical Reports',
                    'heading' => 'Technical Reports',
                ],
                'create' => [
                    'title' => 'Create New Technical Report',
                    'heading' => 'New Technical Report',
                ],
                'view' => [
                    'title' => 'View Technical Report',
                    'heading' => 'Report Details',
                ],
                'edit' => [
                    'title' => 'Edit Technical Report',
                    'heading' => 'Edit Report',
                ],
            ],
        ],
    ],

    'dashboard' => [
        'title' => 'Technician Dashboard',
        'welcome' => 'Welcome',
    ],

    'widgets' => [
        'stats' => [
            'total_assigned' => 'Total Assigned',
            'total_assigned_desc' => 'All assigned requests',
            'new_requests' => 'New Requests',
            'new_requests_desc' => 'Waiting to start',
            'in_progress' => 'In Progress',
            'in_progress_desc' => 'Currently working on',
            'completed' => 'Completed',
            'completed_desc' => 'Successfully completed',
            'completed_this_month' => 'Completed This Month',
            'completed_this_month_desc' => 'Current month achievements',
        ],

        'assigned_requests' => [
            'heading' => 'Active Requests',
            'description' => 'Assigned maintenance requests that need attention',

            'columns' => [
                'request_number' => 'Request Number',
                'title' => 'Title',
                'client' => 'Client',
                'phone' => 'Phone',
                'status' => 'Status',
                'created_at' => 'Since',
            ],

            'actions' => [
                'view' => 'View',
                'edit' => 'Edit',
                'call_client' => 'Call Client',
                'email_client' => 'Email Client',
            ],

            'empty' => [
                'heading' => 'No active requests',
                'description' => 'No maintenance requests are currently assigned to you.',
            ],
        ],
    ],

    'common' => [
        'search' => 'Search',
        'filter' => 'Filter',
        'reset' => 'Reset',
        'export' => 'Export',
        'refresh' => 'Refresh',
        'loading' => 'Loading...',
        'no_data' => 'No data available',
        'select_option' => 'Select an option',
        'required_field' => 'This field is required',
        'optional_field' => 'Optional',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'confirm' => 'Confirm',
        'delete' => 'Delete',
        'edit' => 'Edit',
        'view' => 'View',
        'back' => 'Back',
        'next' => 'Next',
        'previous' => 'Previous',
        'close' => 'Close',
        'submit' => 'Submit',
        'update' => 'Update',
        'create' => 'Create',
        'actions' => 'Actions',
        'status' => 'Status',
        'date' => 'Date',
        'time' => 'Time',
        'notes' => 'Notes',
        'description' => 'Description',
        'title' => 'Title',
        'name' => 'Name',
        'phone' => 'Phone',
        'email' => 'Email',
        'address' => 'Address',
    ],

    'tooltips' => [
        'view_request' => 'View request details',
        'edit_request' => 'Edit request',
        'call_client' => 'Call client',
        'email_client' => 'Send email to client',
        'refresh_data' => 'Refresh data',
        'filter_requests' => 'Filter requests',
        'search_requests' => 'Search requests',
    ],
];