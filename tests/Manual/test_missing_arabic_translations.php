<?php

/**
 * Manual test script for missing Arabic translations in maintenance-request
 * 
 * This script tests the specific translation keys that are showing as raw keys
 * Run with: php tests/Manual/test_missing_arabic_translations.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\App;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Missing Arabic Translations Audit ===\n\n";

// Define the missing translation keys from the screenshot
$missingKeys = [
    // Actions
    'filament-resources/maintenance-request.actions.print_request',
    'filament-resources/maintenance-request.actions.contact_support',
    'filament-resources/maintenance-request.actions.cancel_request',
    
    // Cards
    'filament-resources/maintenance-request.cards.status',
    'filament-resources/maintenance-request.cards.request_number',
    'filament-resources/maintenance-request.cards.contract_type',
    'filament-resources/maintenance-request.cards.creation_date',
    
    // Placeholders
    'filament-resources/maintenance-request.placeholders.no_title',
    'filament-resources/maintenance-request.placeholders.no_notes',
    'filament-resources/maintenance-request.placeholders.not_assigned',
    'filament-resources/maintenance-request.placeholders.not_priced',
    'filament-resources/maintenance-request.placeholders.no_notes_available',
    
    // Fields
    'filament-resources/maintenance-request.fields.client_email',
    'filament-resources/maintenance-request.fields.contract_number',
    'filament-resources/maintenance-request.fields.contract_type',
    
    // Units
    'filament-resources/maintenance-request.units.months',
    
    // Sections
    'filament-resources/maintenance-request.sections.progress_timeline',
    
    // Timeline
    'filament-resources/maintenance-request.timeline.request_created',
    'filament-resources/maintenance-request.timeline.under_review',
    'filament-resources/maintenance-request.timeline.awaiting_review',
    'filament-resources/maintenance-request.timeline.awaiting_assignment',
    'filament-resources/maintenance-request.timeline.awaiting_work_start',
    'filament-resources/maintenance-request.timeline.contract_creation',
    'filament-resources/maintenance-request.timeline.processing_completion',
    'filament-resources/maintenance-request.timeline.awaiting_final_procedures',
    
    // Tabs
    'filament-resources/maintenance-request.tabs.payments',
];

echo "Testing " . count($missingKeys) . " missing translation keys in Arabic...\n\n";

// Set Arabic locale
App::setLocale('ar');

$missingCount = 0;
$foundCount = 0;

foreach ($missingKeys as $key) {
    $translation = __($key);
    
    // Check if translation exists (not returning the key itself)
    if ($translation === $key) {
        $missingCount++;
        echo "❌ Missing: {$key}\n";
    } else {
        $foundCount++;
        echo "✅ Found: {$key} = \"{$translation}\"\n";
    }
}

echo "\n=== SUMMARY ===\n";
echo "Found: {$foundCount}\n";
echo "Missing: {$missingCount}\n";
echo "Total: " . count($missingKeys) . "\n";
echo "Coverage: " . round(($foundCount / count($missingKeys)) * 100, 1) . "%\n";

if ($missingCount > 0) {
    echo "\n❌ {$missingCount} translations need to be added to Arabic filament-resources/maintenance-request.php\n";
} else {
    echo "\n✅ All translations are complete!\n";
}

echo "\n=== MISSING KEYS TO ADD ===\n";
foreach ($missingKeys as $key) {
    $translation = __($key);
    if ($translation === $key) {
        // Extract the section and key name for easier organization
        $parts = explode('.', str_replace('filament-resources/maintenance-request.', '', $key));
        echo "'{$parts[0]}' => [\n";
        if (count($parts) > 1) {
            echo "    '{$parts[1]}' => 'ARABIC_TRANSLATION_NEEDED',\n";
        }
        echo "],\n";
    }
}
