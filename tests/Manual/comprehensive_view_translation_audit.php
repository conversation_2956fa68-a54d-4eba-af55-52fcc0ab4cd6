<?php

/**
 * Comprehensive Translation Audit for ViewMaintenanceRequest Page
 * 
 * This script identifies all translation keys used in both the PHP class and Blade view
 * Run with: php tests/Manual/comprehensive_view_translation_audit.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\App;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== COMPREHENSIVE VIEWMAINTENANCEREQUEST TRANSLATION AUDIT ===\n\n";

// Set Arabic locale
App::setLocale('ar');

// Define all translation keys used in ViewMaintenanceRequest.php
$phpClassKeys = [
    // Page titles and headings
    'filament-resources/maintenance-request.pages.view.title',
    'filament-resources/maintenance-request.pages.view.heading',
    
    // Cards section
    'filament-resources/maintenance-request.cards.status',
    'filament-resources/maintenance-request.cards.request_number',
    'filament-resources/maintenance-request.cards.contract_type',
    'filament-resources/maintenance-request.cards.creation_date',
    
    // Messages
    'filament-resources/maintenance-request.messages.request_number_copied',
    'filament-resources/maintenance-request.messages.phone_copied',
    'filament-resources/maintenance-request.messages.email_copied',
    'filament-resources/maintenance-request.messages.contract_number_copied',
    'filament-resources/maintenance-request.messages.payment_instructions',
    
    // Sections
    'filament-resources/maintenance-request.sections.request_details',
    'filament-resources/maintenance-request.sections.client_contract',
    'filament-resources/maintenance-request.sections.assignment_financial',
    'filament-resources/maintenance-request.sections.progress_timeline',
    'filament-resources/maintenance-request.sections.additional_info',
    'filament-resources/maintenance-request.sections.contract_details',
    
    // Fields
    'filament-resources/maintenance-request.fields.request_number',
    'filament-resources/maintenance-request.fields.title',
    'filament-resources/maintenance-request.fields.created_at',
    'filament-resources/maintenance-request.fields.status',
    'filament-resources/maintenance-request.fields.visits_included',
    'filament-resources/maintenance-request.fields.notes',
    'filament-resources/maintenance-request.fields.client_id',
    'filament-resources/maintenance-request.fields.client_phone',
    'filament-resources/maintenance-request.fields.client_email',
    'filament-resources/maintenance-request.fields.contract_number',
    'filament-resources/maintenance-request.fields.contract_type',
    'filament-resources/maintenance-request.fields.contract_status',
    'filament-resources/maintenance-request.fields.assigned_technician',
    'filament-resources/maintenance-request.fields.price',
    'filament-resources/maintenance-request.fields.payment_status',
    'filament-resources/maintenance-request.fields.contract_period',
    'filament-resources/maintenance-request.fields.contract_start_date',
    'filament-resources/maintenance-request.fields.contract_end_date',
    'filament-resources/maintenance-request.fields.contract_type_description',
    'filament-resources/maintenance-request.fields.contract_benefits',
    'filament-resources/maintenance-request.fields.total_amount_required',
    'filament-resources/maintenance-request.fields.amount_paid',
    'filament-resources/maintenance-request.fields.amount_remaining',
    'filament-resources/maintenance-request.fields.payment_instructions',
    'filament-resources/maintenance-request.fields.start_date',
    'filament-resources/maintenance-request.fields.end_date',
    'filament-resources/maintenance-request.fields.contract_terms',
    
    // Placeholders
    'filament-resources/maintenance-request.placeholders.no_title',
    'filament-resources/maintenance-request.placeholders.no_notes',
    'filament-resources/maintenance-request.placeholders.not_specified',
    'filament-resources/maintenance-request.placeholders.no_contract',
    'filament-resources/maintenance-request.placeholders.not_assigned',
    'filament-resources/maintenance-request.placeholders.not_priced',
    'filament-resources/maintenance-request.placeholders.no_payments',
    'filament-resources/maintenance-request.placeholders.not_set',
    'filament-resources/maintenance-request.placeholders.no_notes_available',
    'filament-resources/maintenance-request.placeholders.no_description_available',
    'filament-resources/maintenance-request.placeholders.no_benefits_specified',
    'filament-resources/maintenance-request.placeholders.not_set_yet',
    'filament-resources/maintenance-request.placeholders.no_contract_created',
    'filament-resources/maintenance-request.placeholders.no_terms_set',
    
    // Units
    'filament-resources/maintenance-request.units.months',
    'filament-resources/maintenance-request.units.riyal',
    
    // Timeline
    'filament-resources/maintenance-request.timeline.request_created',
    'filament-resources/maintenance-request.timeline.under_review',
    'filament-resources/maintenance-request.timeline.review_completed',
    'filament-resources/maintenance-request.timeline.awaiting_review',
    'filament-resources/maintenance-request.timeline.technician_assigned',
    'filament-resources/maintenance-request.timeline.awaiting_assignment',
    'filament-resources/maintenance-request.timeline.work_in_progress',
    'filament-resources/maintenance-request.timeline.awaiting_work_start',
    'filament-resources/maintenance-request.timeline.contract_created',
    'filament-resources/maintenance-request.timeline.contract_number',
    'filament-resources/maintenance-request.timeline.awaiting_contract_creation',
    'filament-resources/maintenance-request.timeline.contract_creation',
    'filament-resources/maintenance-request.timeline.processing_completed',
    'filament-resources/maintenance-request.timeline.processing_completion',
    'filament-resources/maintenance-request.timeline.awaiting_final_procedures',
    
    // Tabs
    'filament-resources/maintenance-request.tabs.notes_details',
    'filament-resources/maintenance-request.tabs.payments',
    'filament-resources/maintenance-request.tabs.linked_contract',
    
    // Actions
    'filament-resources/maintenance-request.actions.print_request',
    'filament-resources/maintenance-request.actions.download_pdf',
    'filament-resources/maintenance-request.actions.close',
    'filament-resources/maintenance-request.actions.download_contract',
    'filament-resources/maintenance-request.actions.contact_support',
    'filament-resources/maintenance-request.actions.cancel_request',
    
    // Tooltips
    'filament-resources/maintenance-request.tooltips.print_request',
    'filament-resources/maintenance-request.tooltips.download_contract',
    'filament-resources/maintenance-request.tooltips.contact_support',
    'filament-resources/maintenance-request.tooltips.cancel_request',
    
    // Support
    'filament-resources/maintenance-request.support.inquiry_type_label',
    'filament-resources/maintenance-request.support.inquiry_types.status',
    'filament-resources/maintenance-request.support.inquiry_types.payment',
    'filament-resources/maintenance-request.support.inquiry_types.contract',
    'filament-resources/maintenance-request.support.inquiry_types.technical',
    'filament-resources/maintenance-request.support.inquiry_types.other',
    'filament-resources/maintenance-request.support.inquiry_type_placeholder',
    'filament-resources/maintenance-request.support.message_label',
    'filament-resources/maintenance-request.support.message_placeholder',
    'filament-resources/maintenance-request.support.message_helper',
    
    // Cancel
    'filament-resources/maintenance-request.cancel.modal_heading',
    'filament-resources/maintenance-request.cancel.modal_description',
    'filament-resources/maintenance-request.cancel.submit_label',
    'filament-resources/maintenance-request.cancel.cancel_label',
    
    // Notifications
    'filament-resources/maintenance-request.notifications.pdf_loaded',
    'filament-resources/maintenance-request.notifications.pdf_loaded_body',
    'filament-resources/maintenance-request.notifications.pdf_generated',
    'filament-resources/maintenance-request.notifications.pdf_generated_body',
    'filament-resources/maintenance-request.notifications.pdf_generated_temp',
    'filament-resources/maintenance-request.notifications.pdf_storage_failed',
    'filament-resources/maintenance-request.notifications.pdf_generation_failed',
    'filament-resources/maintenance-request.notifications.pdf_generation_failed_body',
    'filament-resources/maintenance-request.notifications.support_sent_title',
    'filament-resources/maintenance-request.notifications.support_sent_body',
    'filament-resources/maintenance-request.notifications.support_failed_title',
    'filament-resources/maintenance-request.notifications.support_failed_body',
    'filament-resources/maintenance-request.notifications.cancel_success_title',
    'filament-resources/maintenance-request.notifications.cancel_success_body',
    'filament-resources/maintenance-request.notifications.cancel_failed_title',
    'filament-resources/maintenance-request.notifications.cancel_failed_body',
    
    // Aria labels
    'filament-resources/maintenance-request.aria.progress_timeline',
    'filament-resources/maintenance-request.aria.notes',
    'filament-resources/maintenance-request.aria.contract_type_description',
    'filament-resources/maintenance-request.aria.contract_benefits',
    'filament-resources/maintenance-request.aria.notes_details_tab',
    'filament-resources/maintenance-request.aria.total_amount_required',
    'filament-resources/maintenance-request.aria.amount_paid',
    'filament-resources/maintenance-request.aria.amount_remaining',
    'filament-resources/maintenance-request.aria.payment_instructions',
    'filament-resources/maintenance-request.aria.payments_tab',
    'filament-resources/maintenance-request.aria.contract_number',
    'filament-resources/maintenance-request.aria.contract_start_date',
    'filament-resources/maintenance-request.aria.contract_end_date',
    'filament-resources/maintenance-request.aria.contract_status',
    'filament-resources/maintenance-request.aria.contract_terms',
    'filament-resources/maintenance-request.aria.linked_contract_tab',
    'filament-resources/maintenance-request.aria.generate_pdf',
    'filament-resources/maintenance-request.aria.download_contract',
    'filament-resources/maintenance-request.aria.contact_support',
];

// Define all translation keys used in the Blade view
$bladeViewKeys = [
    // Status options
    'client/resources/maintenance_request.status_options.new',
    'client/resources/maintenance_request.status_options.pending',
    'client/resources/maintenance_request.status_options.assigned',
    'client/resources/maintenance_request.status_options.in_progress',
    'client/resources/maintenance_request.status_options.completed',
    'client/resources/maintenance_request.status_options.canceled',
    
    // View labels
    'client/resources/maintenance_request.view.created_on',
    'client/resources/maintenance_request.view.progress_tracker',
    
    // Progress steps
    'client/resources/maintenance_request.view.progress.request_created',
    'client/resources/maintenance_request.view.progress.review',
    'client/resources/maintenance_request.view.progress.review_completed',
    'client/resources/maintenance_request.view.progress.under_review',
    'client/resources/maintenance_request.view.progress.waiting',
    'client/resources/maintenance_request.view.progress.technician_assignment',
    'client/resources/maintenance_request.view.progress.technician_assigned',
    'client/resources/maintenance_request.view.progress.assigning_technician',
    'client/resources/maintenance_request.view.progress.work_execution',
    'client/resources/maintenance_request.view.progress.work_completed',
    'client/resources/maintenance_request.view.progress.work_in_progress',
    'client/resources/maintenance_request.view.progress.work_starting_soon',
    'client/resources/maintenance_request.view.progress.completion',
    'client/resources/maintenance_request.view.progress.completed',
    
    // Help section
    'client/resources/maintenance_request.view.help.title',
    'client/resources/maintenance_request.view.help.useful_info',
    'client/resources/maintenance_request.view.help.processing_time_label',
    'client/resources/maintenance_request.view.help.processing_time_text',
    'client/resources/maintenance_request.view.help.technician_assignment_label',
    'client/resources/maintenance_request.view.help.technician_assignment_text',
    'client/resources/maintenance_request.view.help.work_execution_label',
    'client/resources/maintenance_request.view.help.work_execution_text',
    'client/resources/maintenance_request.view.help.communication_label',
    'client/resources/maintenance_request.view.help.communication_text',
    'client/resources/maintenance_request.view.help.contract_label',
    'client/resources/maintenance_request.view.help.contract_text',
    'client/resources/maintenance_request.view.help.working_hours',
];

echo "Testing PHP Class Translation Keys (" . count($phpClassKeys) . " keys):\n";
echo "=" . str_repeat("=", 60) . "\n";

$phpMissing = [];
$phpFound = [];

foreach ($phpClassKeys as $key) {
    $translation = __($key);
    if ($translation === $key) {
        $phpMissing[] = $key;
        echo "❌ Missing: {$key}\n";
    } else {
        $phpFound[] = $key;
        echo "✅ Found: {$key}\n";
    }
}

echo "\nTesting Blade View Translation Keys (" . count($bladeViewKeys) . " keys):\n";
echo "=" . str_repeat("=", 60) . "\n";

$bladeMissing = [];
$bladeFound = [];

foreach ($bladeViewKeys as $key) {
    $translation = __($key);
    if ($translation === $key) {
        $bladeMissing[] = $key;
        echo "❌ Missing: {$key}\n";
    } else {
        $bladeFound[] = $key;
        echo "✅ Found: {$key}\n";
    }
}

// Summary
echo "\n" . str_repeat("=", 80) . "\n";
echo "COMPREHENSIVE AUDIT SUMMARY\n";
echo str_repeat("=", 80) . "\n";

echo "\nPHP Class (ViewMaintenanceRequest.php):\n";
echo "  Found: " . count($phpFound) . "/" . count($phpClassKeys) . " (" . round((count($phpFound) / count($phpClassKeys)) * 100, 1) . "%)\n";
echo "  Missing: " . count($phpMissing) . "\n";

echo "\nBlade View (view-maintenance-request.blade.php):\n";
echo "  Found: " . count($bladeFound) . "/" . count($bladeViewKeys) . " (" . round((count($bladeFound) / count($bladeViewKeys)) * 100, 1) . "%)\n";
echo "  Missing: " . count($bladeMissing) . "\n";

$totalKeys = count($phpClassKeys) + count($bladeViewKeys);
$totalFound = count($phpFound) + count($bladeFound);
$totalMissing = count($phpMissing) + count($bladeMissing);

echo "\nOVERALL TOTALS:\n";
echo "  Total Keys: {$totalKeys}\n";
echo "  Found: {$totalFound} (" . round(($totalFound / $totalKeys) * 100, 1) . "%)\n";
echo "  Missing: {$totalMissing}\n";

if ($totalMissing > 0) {
    echo "\n❌ CRITICAL ISSUES IDENTIFIED:\n";
    echo "  - {$totalMissing} translation keys are missing\n";
    echo "  - This causes raw translation keys to display in the browser\n";
    echo "  - Frontend caching may also be preventing updated translations from loading\n";
} else {
    echo "\n✅ ALL TRANSLATIONS FOUND!\n";
    echo "  - Translation files are complete\n";
    echo "  - Issue is likely frontend caching\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "RECOMMENDATIONS:\n";
echo str_repeat("=", 80) . "\n";

if ($totalMissing > 0) {
    echo "1. Add missing translations to appropriate files\n";
    echo "2. Clear all caches after adding translations\n";
    echo "3. Test in browser with hard refresh\n";
} else {
    echo "1. Clear browser cache (Ctrl+Shift+R)\n";
    echo "2. Clear Laravel caches (php artisan optimize:clear)\n";
    echo "3. Restart web server\n";
    echo "4. Check for JavaScript console errors\n";
}

echo "\n=== AUDIT COMPLETE ===\n";
