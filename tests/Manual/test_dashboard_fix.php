<?php

/**
 * Manual test script for client dashboard database fix
 * 
 * This script tests the dashboard methods to ensure the "non_existent_id" error is fixed
 * Run with: php tests/Manual/test_dashboard_fix.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use App\Models\Visit;
use App\Models\Contract;
use App\Models\MaintenanceRequest;
use App\Models\Client;
use Illuminate\Foundation\Application;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Client Dashboard Database Fix Test ===\n\n";

// Test 1: Check Visit model contract() method
echo "1. Testing Visit model contract() method...\n";

try {
    $visit = new Visit();
    
    // Test polymorphic contract relationship
    $visit->visitable_type = 'App\Models\Contract';
    $visit->visitable_id = 1;
    
    $contractRelation = $visit->contract();
    echo "✅ Contract relationship method works for contract visits\n";
    
    // Test maintenance request relationship
    $visit->visitable_type = 'App\Models\MaintenanceRequest';
    $visit->visitable_id = 1;
    $visit->maintenance_request_id = 1;
    
    $contractRelation = $visit->contract();
    echo "✅ Contract relationship method works for maintenance request visits\n";
    
    // Test fallback case
    $visit->visitable_type = null;
    $visit->visitable_id = null;
    $visit->maintenance_request_id = null;
    
    $contractRelation = $visit->contract();
    echo "✅ Contract relationship method works for fallback case\n";
    
} catch (Exception $e) {
    echo "❌ Visit model error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check SQL query generation
echo "2. Testing SQL query generation...\n";

try {
    // Test the query that was causing the error
    $query = Visit::where(function($query) {
        $clientId = 1; // Test client ID
        
        // Visits directly associated with contracts
        $query->where(function($subQuery) use ($clientId) {
            $subQuery->where('visitable_type', 'App\Models\Contract')
                     ->whereHas('visitable', function($contractQuery) use ($clientId) {
                         $contractQuery->whereHas('maintenanceRequest', function($mrQuery) use ($clientId) {
                             $mrQuery->where('client_id', $clientId);
                         });
                     });
        })
        // OR visits associated with maintenance requests
        ->orWhere(function($subQuery) use ($clientId) {
            $subQuery->where('visitable_type', 'App\Models\MaintenanceRequest')
                     ->whereHas('visitable', function($mrQuery) use ($clientId) {
                         $mrQuery->where('client_id', $clientId);
                     });
        });
    })
    ->whereIn('status', ['scheduled', 'in_progress'])
    ->where('scheduled_at', '>=', now());
    
    $sql = $query->toSql();
    echo "Generated SQL: " . $sql . "\n";
    
    // Check if the SQL contains the problematic "non_existent_id"
    if (strpos($sql, 'non_existent_id') !== false) {
        echo "❌ SQL still contains 'non_existent_id'\n";
    } else {
        echo "✅ SQL does not contain 'non_existent_id'\n";
    }
    
} catch (Exception $e) {
    echo "❌ SQL generation error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test dashboard methods (without database execution)
echo "3. Testing dashboard methods structure...\n";

try {
    // Check if the dashboard class exists and methods are callable
    $dashboardClass = new ReflectionClass('App\Filament\Client\Pages\Dashboard');
    
    if ($dashboardClass->hasMethod('getUpcomingVisitsCount')) {
        echo "✅ getUpcomingVisitsCount method exists\n";
    } else {
        echo "❌ getUpcomingVisitsCount method missing\n";
    }
    
    if ($dashboardClass->hasMethod('getUpcomingVisits')) {
        echo "✅ getUpcomingVisits method exists\n";
    } else {
        echo "❌ getUpcomingVisits method missing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Dashboard class error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check relationship definitions
echo "4. Testing model relationships...\n";

try {
    // Test Visit relationships
    $visit = new Visit();
    
    if (method_exists($visit, 'visitable')) {
        echo "✅ Visit has visitable() polymorphic relationship\n";
    } else {
        echo "❌ Visit missing visitable() relationship\n";
    }
    
    if (method_exists($visit, 'contract')) {
        echo "✅ Visit has contract() relationship\n";
    } else {
        echo "❌ Visit missing contract() relationship\n";
    }
    
    if (method_exists($visit, 'maintenanceRequest')) {
        echo "✅ Visit has maintenanceRequest() relationship\n";
    } else {
        echo "❌ Visit missing maintenanceRequest() relationship\n";
    }
    
    // Test Contract relationships
    $contract = new Contract();
    
    if (method_exists($contract, 'visits')) {
        echo "✅ Contract has visits() polymorphic relationship\n";
    } else {
        echo "❌ Contract missing visits() relationship\n";
    }
    
    if (method_exists($contract, 'maintenanceRequest')) {
        echo "✅ Contract has maintenanceRequest() relationship\n";
    } else {
        echo "❌ Contract missing maintenanceRequest() relationship\n";
    }
    
    // Test MaintenanceRequest relationships
    $maintenanceRequest = new MaintenanceRequest();
    
    if (method_exists($maintenanceRequest, 'visits')) {
        echo "✅ MaintenanceRequest has visits() polymorphic relationship\n";
    } else {
        echo "❌ MaintenanceRequest missing visits() relationship\n";
    }
    
} catch (Exception $e) {
    echo "❌ Relationship test error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";

echo "\nSummary:\n";
echo "- Fixed Visit model contract() method to avoid 'non_existent_id' error\n";
echo "- Updated dashboard queries to use polymorphic relationships properly\n";
echo "- Maintained backward compatibility for existing code\n";
echo "- All relationship methods are properly defined\n";

echo "\nTo test in browser:\n";
echo "1. Start your development server: php artisan serve\n";
echo "2. Login as a client\n";
echo "3. Navigate to the dashboard\n";
echo "4. Check that no database errors occur\n";
