<?php

/**
 * Manual test script for MaintenanceRequest method call fix
 * 
 * This script tests that the "Call to undefined method App\Models\MaintenanceRequest::maintenanceRequest()" error is fixed
 * Run with: php tests/Manual/test_maintenance_request_method_fix.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use App\Models\Visit;
use App\Models\Contract;
use App\Models\MaintenanceRequest;
use App\Models\Client;
use Illuminate\Foundation\Application;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== MaintenanceRequest Method Call Fix Test ===\n\n";

// Test 1: Test Visit model attribute accessors
echo "1. Testing Visit model attribute accessors...\n";

try {
    $visit = new Visit();
    
    // Test contract-based visit
    $visit->visitable_type = 'App\Models\Contract';
    $visit->visitable_id = 1;
    
    // This should not cause an error
    $contractAttribute = $visit->getContractAttribute();
    echo "✅ getContractAttribute() works for contract visits\n";
    
    // Test maintenance request-based visit
    $visit->visitable_type = 'App\Models\MaintenanceRequest';
    $visit->visitable_id = 1;
    
    // This should not cause an error or infinite recursion
    $maintenanceRequestAttribute = $visit->getMaintenanceRequestAttribute();
    echo "✅ getMaintenanceRequestAttribute() works for maintenance request visits\n";
    
    // Test fallback case
    $visit->visitable_type = null;
    $visit->visitable_id = null;
    $visit->maintenance_request_id = 1;
    
    // This should not cause recursion
    $fallbackAttribute = $visit->getMaintenanceRequestAttribute();
    echo "✅ getMaintenanceRequestAttribute() works for fallback case\n";
    
} catch (Exception $e) {
    echo "❌ Visit model attribute error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Test Dashboard methods without database calls
echo "2. Testing Dashboard method structure...\n";

try {
    // Test the dashboard mapping logic structure
    $testVisit = new stdClass();
    $testVisit->visitable_type = 'App\Models\Contract';
    $testVisit->visitable = new stdClass();
    $testVisit->visitable->maintenanceRequest = new stdClass();
    $testVisit->visitable->maintenanceRequest->request_number = 'TEST-001';
    $testVisit->scheduled_at = new DateTime();
    $testVisit->technician = null;
    $testVisit->status = 'scheduled';
    
    // Test the mapping logic (simulated)
    $requestNumber = '';
    if ($testVisit->visitable_type === 'App\Models\Contract' && $testVisit->visitable) {
        if ($testVisit->visitable instanceof stdClass) { // Simulating Contract instance
            $requestNumber = $testVisit->visitable->maintenanceRequest->request_number ?? 'N/A';
        }
    }
    
    if ($requestNumber === 'TEST-001') {
        echo "✅ Dashboard mapping logic works correctly\n";
    } else {
        echo "❌ Dashboard mapping logic failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Dashboard mapping error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test relationship method calls
echo "3. Testing relationship method calls...\n";

try {
    // Test Visit relationships
    $visit = new Visit();
    
    // Test that these methods exist and return relationship builders
    $visitableRelation = $visit->visitable();
    echo "✅ Visit visitable() relationship method works\n";
    
    $contractRelation = $visit->contract();
    echo "✅ Visit contract() relationship method works\n";
    
    $maintenanceRequestRelation = $visit->maintenanceRequest();
    echo "✅ Visit maintenanceRequest() relationship method works\n";
    
    $technicianRelation = $visit->technician();
    echo "✅ Visit technician() relationship method works\n";
    
    // Test Contract relationships
    $contract = new Contract();
    
    $contractMaintenanceRequestRelation = $contract->maintenanceRequest();
    echo "✅ Contract maintenanceRequest() relationship method works\n";
    
    $contractVisitsRelation = $contract->visits();
    echo "✅ Contract visits() relationship method works\n";
    
    // Test MaintenanceRequest relationships
    $maintenanceRequest = new MaintenanceRequest();
    
    $mrVisitsRelation = $maintenanceRequest->visits();
    echo "✅ MaintenanceRequest visits() relationship method works\n";
    
    $mrContractRelation = $maintenanceRequest->contract();
    echo "✅ MaintenanceRequest contract() relationship method works\n";
    
} catch (Exception $e) {
    echo "❌ Relationship method error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Test polymorphic relationship logic
echo "4. Testing polymorphic relationship logic...\n";

try {
    $visit = new Visit();
    
    // Test contract visit logic
    $visit->visitable_type = 'App\Models\Contract';
    $visit->visitable_id = 1;
    
    $contractRelationship = $visit->contract();
    if ($contractRelationship instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
        echo "✅ Contract visit returns proper BelongsTo relationship\n";
    } else {
        echo "❌ Contract visit relationship type incorrect\n";
    }
    
    // Test maintenance request visit logic
    $visit->visitable_type = 'App\Models\MaintenanceRequest';
    $visit->visitable_id = 1;
    $visit->maintenance_request_id = 1;
    
    $contractRelationship = $visit->contract();
    if ($contractRelationship instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
        echo "✅ Maintenance request visit returns proper BelongsTo relationship\n";
    } else {
        echo "❌ Maintenance request visit relationship type incorrect\n";
    }
    
    // Test fallback case
    $visit->visitable_type = null;
    $visit->visitable_id = null;
    $visit->maintenance_request_id = null;
    
    $contractRelationship = $visit->contract();
    if ($contractRelationship instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
        echo "✅ Fallback case returns proper BelongsTo relationship\n";
    } else {
        echo "❌ Fallback case relationship type incorrect\n";
    }
    
} catch (Exception $e) {
    echo "❌ Polymorphic relationship error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Test that MaintenanceRequest doesn't have maintenanceRequest method
echo "5. Testing MaintenanceRequest model methods...\n";

try {
    $maintenanceRequest = new MaintenanceRequest();
    
    // Check that maintenanceRequest method doesn't exist on MaintenanceRequest model
    if (!method_exists($maintenanceRequest, 'maintenanceRequest')) {
        echo "✅ MaintenanceRequest model correctly does not have maintenanceRequest() method\n";
    } else {
        echo "❌ MaintenanceRequest model incorrectly has maintenanceRequest() method\n";
    }
    
    // Check that it has the correct relationships
    if (method_exists($maintenanceRequest, 'contract')) {
        echo "✅ MaintenanceRequest has contract() relationship\n";
    } else {
        echo "❌ MaintenanceRequest missing contract() relationship\n";
    }
    
    if (method_exists($maintenanceRequest, 'visits')) {
        echo "✅ MaintenanceRequest has visits() relationship\n";
    } else {
        echo "❌ MaintenanceRequest missing visits() relationship\n";
    }
    
    if (method_exists($maintenanceRequest, 'client')) {
        echo "✅ MaintenanceRequest has client() relationship\n";
    } else {
        echo "❌ MaintenanceRequest missing client() relationship\n";
    }
    
} catch (Exception $e) {
    echo "❌ MaintenanceRequest model test error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";

echo "\nSummary of fixes:\n";
echo "- Fixed Dashboard.php to use proper instanceof checks and error handling\n";
echo "- Fixed Visit model getMaintenanceRequestAttribute() to avoid recursion\n";
echo "- Added proper error handling in dashboard mapping logic\n";
echo "- Ensured MaintenanceRequest model doesn't have recursive method calls\n";

echo "\nTo test in browser:\n";
echo "1. Start your development server: php artisan serve\n";
echo "2. Login as a client\n";
echo "3. Navigate to the dashboard\n";
echo "4. Check that no 'Call to undefined method' errors occur\n";
echo "5. Try the client registration process\n";
