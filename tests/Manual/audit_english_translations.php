<?php

/**
 * Comprehensive English Translation Audit for ViewMaintenanceRequest Page
 * 
 * This script identifies missing English translations by comparing ViewMaintenanceRequest.php
 * translation keys against the English translation file
 * Run with: php tests/Manual/audit_english_translations.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\App;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ENGLISH TRANSLATION AUDIT FOR VIEWMAINTENANCEREQUEST ===\n\n";

// Set English locale
App::setLocale('en');

// Define all translation keys used in ViewMaintenanceRequest.php
$viewMaintenanceRequestKeys = [
    // Page titles and headings
    'filament-resources/maintenance-request.pages.view.title',
    'filament-resources/maintenance-request.pages.view.heading',
    
    // Card components
    'filament-resources/maintenance-request.cards.status',
    'filament-resources/maintenance-request.cards.request_number',
    'filament-resources/maintenance-request.cards.contract_type',
    'filament-resources/maintenance-request.cards.creation_date',
    
    // Messages and notifications
    'filament-resources/maintenance-request.messages.request_number_copied',
    'filament-resources/maintenance-request.messages.phone_copied',
    'filament-resources/maintenance-request.messages.email_copied',
    'filament-resources/maintenance-request.messages.contract_number_copied',
    'filament-resources/maintenance-request.messages.payment_instructions',
    
    // Sections
    'filament-resources/maintenance-request.sections.request_details',
    'filament-resources/maintenance-request.sections.client_contract',
    'filament-resources/maintenance-request.sections.assignment_financial',
    'filament-resources/maintenance-request.sections.progress_timeline',
    'filament-resources/maintenance-request.sections.additional_info',
    'filament-resources/maintenance-request.sections.contract_details',
    
    // Form fields
    'filament-resources/maintenance-request.fields.request_number',
    'filament-resources/maintenance-request.fields.title',
    'filament-resources/maintenance-request.fields.created_at',
    'filament-resources/maintenance-request.fields.status',
    'filament-resources/maintenance-request.fields.visits_included',
    'filament-resources/maintenance-request.fields.notes',
    'filament-resources/maintenance-request.fields.client_id',
    'filament-resources/maintenance-request.fields.client_phone',
    'filament-resources/maintenance-request.fields.client_email',
    'filament-resources/maintenance-request.fields.contract_number',
    'filament-resources/maintenance-request.fields.contract_type',
    'filament-resources/maintenance-request.fields.contract_status',
    'filament-resources/maintenance-request.fields.assigned_technician',
    'filament-resources/maintenance-request.fields.price',
    'filament-resources/maintenance-request.fields.payment_status',
    'filament-resources/maintenance-request.fields.contract_period',
    'filament-resources/maintenance-request.fields.contract_start_date',
    'filament-resources/maintenance-request.fields.contract_end_date',
    'filament-resources/maintenance-request.fields.contract_type_description',
    'filament-resources/maintenance-request.fields.contract_benefits',
    'filament-resources/maintenance-request.fields.total_amount_required',
    'filament-resources/maintenance-request.fields.amount_paid',
    'filament-resources/maintenance-request.fields.amount_remaining',
    'filament-resources/maintenance-request.fields.payment_instructions',
    'filament-resources/maintenance-request.fields.start_date',
    'filament-resources/maintenance-request.fields.end_date',
    'filament-resources/maintenance-request.fields.contract_terms',
    
    // Placeholders
    'filament-resources/maintenance-request.placeholders.no_title',
    'filament-resources/maintenance-request.placeholders.no_notes',
    'filament-resources/maintenance-request.placeholders.not_specified',
    'filament-resources/maintenance-request.placeholders.no_contract',
    'filament-resources/maintenance-request.placeholders.not_assigned',
    'filament-resources/maintenance-request.placeholders.not_priced',
    'filament-resources/maintenance-request.placeholders.no_payments',
    'filament-resources/maintenance-request.placeholders.not_set',
    'filament-resources/maintenance-request.placeholders.no_notes_available',
    'filament-resources/maintenance-request.placeholders.no_description_available',
    'filament-resources/maintenance-request.placeholders.no_benefits_specified',
    'filament-resources/maintenance-request.placeholders.not_set_yet',
    'filament-resources/maintenance-request.placeholders.no_contract_created',
    'filament-resources/maintenance-request.placeholders.no_terms_set',
    
    // Units
    'filament-resources/maintenance-request.units.months',
    'filament-resources/maintenance-request.units.riyal',
    
    // Timeline
    'filament-resources/maintenance-request.timeline.request_created',
    'filament-resources/maintenance-request.timeline.under_review',
    'filament-resources/maintenance-request.timeline.review_completed',
    'filament-resources/maintenance-request.timeline.awaiting_review',
    'filament-resources/maintenance-request.timeline.technician_assigned',
    'filament-resources/maintenance-request.timeline.awaiting_assignment',
    'filament-resources/maintenance-request.timeline.work_in_progress',
    'filament-resources/maintenance-request.timeline.awaiting_work_start',
    'filament-resources/maintenance-request.timeline.contract_created',
    'filament-resources/maintenance-request.timeline.contract_number',
    'filament-resources/maintenance-request.timeline.awaiting_contract_creation',
    'filament-resources/maintenance-request.timeline.contract_creation',
    'filament-resources/maintenance-request.timeline.processing_completed',
    'filament-resources/maintenance-request.timeline.processing_completion',
    'filament-resources/maintenance-request.timeline.awaiting_final_procedures',
    
    // Tabs
    'filament-resources/maintenance-request.tabs.notes_details',
    'filament-resources/maintenance-request.tabs.payments',
    'filament-resources/maintenance-request.tabs.linked_contract',
    
    // Actions
    'filament-resources/maintenance-request.actions.print_request',
    'filament-resources/maintenance-request.actions.download_pdf',
    'filament-resources/maintenance-request.actions.close',
    'filament-resources/maintenance-request.actions.download_contract',
    'filament-resources/maintenance-request.actions.contact_support',
    'filament-resources/maintenance-request.actions.cancel_request',
    
    // Tooltips
    'filament-resources/maintenance-request.tooltips.print_request',
    'filament-resources/maintenance-request.tooltips.download_contract',
    'filament-resources/maintenance-request.tooltips.contact_support',
    'filament-resources/maintenance-request.tooltips.cancel_request',
    
    // Support form
    'filament-resources/maintenance-request.support.inquiry_type_label',
    'filament-resources/maintenance-request.support.inquiry_types.status',
    'filament-resources/maintenance-request.support.inquiry_types.payment',
    'filament-resources/maintenance-request.support.inquiry_types.contract',
    'filament-resources/maintenance-request.support.inquiry_types.technical',
    'filament-resources/maintenance-request.support.inquiry_types.other',
    'filament-resources/maintenance-request.support.inquiry_type_placeholder',
    'filament-resources/maintenance-request.support.message_label',
    'filament-resources/maintenance-request.support.message_placeholder',
    'filament-resources/maintenance-request.support.message_helper',
    
    // Cancel modal
    'filament-resources/maintenance-request.cancel.modal_heading',
    'filament-resources/maintenance-request.cancel.modal_description',
    'filament-resources/maintenance-request.cancel.submit_label',
    'filament-resources/maintenance-request.cancel.cancel_label',
    
    // Notifications
    'filament-resources/maintenance-request.notifications.pdf_loaded',
    'filament-resources/maintenance-request.notifications.pdf_loaded_body',
    'filament-resources/maintenance-request.notifications.pdf_generated',
    'filament-resources/maintenance-request.notifications.pdf_generated_body',
    'filament-resources/maintenance-request.notifications.pdf_generated_temp',
    'filament-resources/maintenance-request.notifications.pdf_storage_failed',
    'filament-resources/maintenance-request.notifications.pdf_generation_failed',
    'filament-resources/maintenance-request.notifications.pdf_generation_failed_body',
    'filament-resources/maintenance-request.notifications.support_sent_title',
    'filament-resources/maintenance-request.notifications.support_sent_body',
    'filament-resources/maintenance-request.notifications.support_failed_title',
    'filament-resources/maintenance-request.notifications.support_failed_body',
    'filament-resources/maintenance-request.notifications.cancel_success_title',
    'filament-resources/maintenance-request.notifications.cancel_success_body',
    'filament-resources/maintenance-request.notifications.cancel_failed_title',
    'filament-resources/maintenance-request.notifications.cancel_failed_body',
    
    // Accessibility (ARIA)
    'filament-resources/maintenance-request.aria.progress_timeline',
    'filament-resources/maintenance-request.aria.notes',
    'filament-resources/maintenance-request.aria.contract_type_description',
    'filament-resources/maintenance-request.aria.contract_benefits',
    'filament-resources/maintenance-request.aria.notes_details_tab',
    'filament-resources/maintenance-request.aria.total_amount_required',
    'filament-resources/maintenance-request.aria.amount_paid',
    'filament-resources/maintenance-request.aria.amount_remaining',
    'filament-resources/maintenance-request.aria.payment_instructions',
    'filament-resources/maintenance-request.aria.payments_tab',
    'filament-resources/maintenance-request.aria.contract_number',
    'filament-resources/maintenance-request.aria.contract_start_date',
    'filament-resources/maintenance-request.aria.contract_end_date',
    'filament-resources/maintenance-request.aria.contract_status',
    'filament-resources/maintenance-request.aria.contract_terms',
    'filament-resources/maintenance-request.aria.linked_contract_tab',
    'filament-resources/maintenance-request.aria.generate_pdf',
    'filament-resources/maintenance-request.aria.download_contract',
    'filament-resources/maintenance-request.aria.contact_support',
];

echo "Testing " . count($viewMaintenanceRequestKeys) . " translation keys in English...\n\n";

$missingKeys = [];
$foundKeys = [];

foreach ($viewMaintenanceRequestKeys as $key) {
    $translation = __($key);
    
    // Check if translation exists (not returning the key itself)
    if ($translation === $key) {
        $missingKeys[] = $key;
        echo "❌ Missing: {$key}\n";
    } else {
        $foundKeys[] = $key;
        echo "✅ Found: {$key}\n";
    }
}

$foundCount = count($foundKeys);
$missingCount = count($missingKeys);
$totalKeys = count($viewMaintenanceRequestKeys);
$percentage = round(($foundCount / $totalKeys) * 100, 1);

echo "\n" . str_repeat("=", 80) . "\n";
echo "ENGLISH TRANSLATION AUDIT SUMMARY\n";
echo str_repeat("=", 80) . "\n";

echo "\nViewMaintenanceRequest.php Translation Coverage:\n";
echo "  Found: {$foundCount}/{$totalKeys} ({$percentage}%)\n";
echo "  Missing: {$missingCount}\n";

if ($missingCount > 0) {
    echo "\n❌ CRITICAL ISSUES IDENTIFIED:\n";
    echo "  - {$missingCount} English translation keys are missing\n";
    echo "  - This causes raw translation keys to display when locale is 'en'\n";
    echo "  - Missing keys need to be added to lang/en/filament-resources/maintenance-request.php\n";
    
    echo "\n📋 MISSING KEYS BY CATEGORY:\n";
    
    $categories = [
        'pages' => [],
        'cards' => [],
        'messages' => [],
        'sections' => [],
        'fields' => [],
        'placeholders' => [],
        'units' => [],
        'timeline' => [],
        'tabs' => [],
        'actions' => [],
        'tooltips' => [],
        'support' => [],
        'cancel' => [],
        'notifications' => [],
        'aria' => [],
    ];
    
    foreach ($missingKeys as $key) {
        $parts = explode('.', str_replace('filament-resources/maintenance-request.', '', $key));
        $category = $parts[0] ?? 'other';
        if (isset($categories[$category])) {
            $categories[$category][] = $key;
        }
    }
    
    foreach ($categories as $category => $keys) {
        if (!empty($keys)) {
            echo "\n  {$category}: " . count($keys) . " missing\n";
            foreach ($keys as $key) {
                echo "    - {$key}\n";
            }
        }
    }
} else {
    echo "\n✅ ALL ENGLISH TRANSLATIONS FOUND!\n";
    echo "  - All ViewMaintenanceRequest translation keys exist in English\n";
    echo "  - No missing translations to add\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "NEXT STEPS:\n";
echo str_repeat("=", 80) . "\n";

if ($missingCount > 0) {
    echo "1. Add missing English translations to lang/en/filament-resources/maintenance-request.php\n";
    echo "2. Follow the same structure as Arabic translations\n";
    echo "3. Use professional English business terminology\n";
    echo "4. Clear all caches after adding translations\n";
    echo "5. Test with English locale to verify translations display properly\n";
} else {
    echo "1. All translations are complete - no action needed\n";
    echo "2. If seeing raw keys in browser, clear caches and refresh\n";
    echo "3. Verify locale is set correctly in application\n";
}

echo "\n=== AUDIT COMPLETE ===\n";
