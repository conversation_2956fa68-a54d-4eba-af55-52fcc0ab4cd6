<?php

/**
 * Manual test script for client registration
 * 
 * This script can be used to manually test the registration functionality
 * Run with: php tests/Manual/test_registration.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use App\Models\Client;
use App\Models\UserOTP;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Client Registration Manual Test ===\n\n";

// Test 1: Check if Client model has proper fillable fields
echo "1. Testing Client model fillable fields...\n";
$client = new Client();
$fillable = $client->getFillable();
echo "Fillable fields: " . implode(', ', $fillable) . "\n";

$expectedFields = ['name', 'email', 'phone', 'national_id', 'address', 'notes'];
$missingFields = array_diff($expectedFields, $fillable);

if (empty($missingFields)) {
    echo "✅ All expected fields are fillable\n\n";
} else {
    echo "❌ Missing fillable fields: " . implode(', ', $missingFields) . "\n\n";
}

// Test 2: Check phone formatting function
echo "2. Testing phone formatting...\n";
$testPhones = [
    '0550000001',
    '966550000001',
    '+966550000001',
    '05xxx' // Invalid format
];

foreach ($testPhones as $phone) {
    $formatted = phone_format($phone, ['SA', 'AUTO'], false);
    echo "Input: {$phone} -> Formatted: " . ($formatted ?: 'INVALID') . "\n";
}
echo "\n";

// Test 3: Test validation rules
echo "3. Testing validation rules...\n";

// Test phone validation
try {
    $validator = validator(['phone' => '0550000001'], [
        'phone' => [\Illuminate\Validation\Rule::phone()->country(['AUTO'])->mobile()]
    ]);
    
    if ($validator->passes()) {
        echo "✅ Phone validation passes for valid number\n";
    } else {
        echo "❌ Phone validation failed: " . implode(', ', $validator->errors()->all()) . "\n";
    }
} catch (Exception $e) {
    echo "❌ Phone validation error: " . $e->getMessage() . "\n";
}

// Test email validation
try {
    $validator = validator(['email' => '<EMAIL>'], [
        'email' => ['email']
    ]);
    
    if ($validator->passes()) {
        echo "✅ Email validation passes for valid email\n";
    } else {
        echo "❌ Email validation failed: " . implode(', ', $validator->errors()->all()) . "\n";
    }
} catch (Exception $e) {
    echo "❌ Email validation error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check translation files
echo "4. Testing translation files...\n";

$languages = ['ar', 'en', 'ur', 'fil'];
$requiredKeys = [
    'fields.name',
    'fields.phone', 
    'fields.email',
    'labels.register_title',
    'errors.phone_exists',
    'messages.registration_success'
];

foreach ($languages as $lang) {
    echo "Checking {$lang} translations...\n";
    $translationFile = __DIR__ . "/../../lang/{$lang}/client/auth.php";
    
    if (file_exists($translationFile)) {
        $translations = include $translationFile;
        $missingKeys = [];
        
        foreach ($requiredKeys as $key) {
            $keyParts = explode('.', $key);
            $value = $translations;
            
            foreach ($keyParts as $part) {
                if (!isset($value[$part])) {
                    $missingKeys[] = $key;
                    break;
                }
                $value = $value[$part];
            }
        }
        
        if (empty($missingKeys)) {
            echo "  ✅ All required translation keys present\n";
        } else {
            echo "  ❌ Missing keys: " . implode(', ', $missingKeys) . "\n";
        }
    } else {
        echo "  ❌ Translation file not found\n";
    }
}

echo "\n";

// Test 5: Check if registration route exists
echo "5. Testing registration route...\n";

try {
    $routes = app('router')->getRoutes();
    $registrationRouteExists = false;
    
    foreach ($routes as $route) {
        if (str_contains($route->getName() ?: '', 'register')) {
            $registrationRouteExists = true;
            echo "✅ Registration route found: " . $route->getName() . "\n";
            break;
        }
    }
    
    if (!$registrationRouteExists) {
        echo "❌ No registration route found\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking routes: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Check UserOTP model
echo "6. Testing UserOTP model...\n";

try {
    $otp = new UserOTP();
    $fillable = $otp->getFillable();
    echo "UserOTP fillable fields: " . implode(', ', $fillable) . "\n";
    
    // Check if polymorphic relationship works
    if (method_exists($otp, 'auth')) {
        echo "✅ UserOTP has auth() relationship method\n";
    } else {
        echo "❌ UserOTP missing auth() relationship method\n";
    }
} catch (Exception $e) {
    echo "❌ UserOTP model error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";

echo "\nTo test the registration form in browser:\n";
echo "1. Start your development server: php artisan serve\n";
echo "2. Navigate to: http://localhost:8000/register\n";
echo "3. Fill in the registration form\n";
echo "4. Test OTP verification with code: 1436 (in development)\n";
