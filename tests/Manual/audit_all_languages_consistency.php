<?php

/**
 * Comprehensive Translation Consistency Audit Across All Languages
 * 
 * This script checks translation consistency for ViewMaintenanceRequest across all 4 languages
 * Run with: php tests/Manual/audit_all_languages_consistency.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\App;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TRANSLATION CONSISTENCY AUDIT ACROSS ALL LANGUAGES ===\n\n";

// Define all translation keys used in ViewMaintenanceRequest.php
$viewMaintenanceRequestKeys = [
    'filament-resources/maintenance-request.pages.view.title',
    'filament-resources/maintenance-request.pages.view.heading',
    'filament-resources/maintenance-request.cards.status',
    'filament-resources/maintenance-request.cards.request_number',
    'filament-resources/maintenance-request.cards.contract_type',
    'filament-resources/maintenance-request.cards.creation_date',
    'filament-resources/maintenance-request.messages.request_number_copied',
    'filament-resources/maintenance-request.messages.phone_copied',
    'filament-resources/maintenance-request.messages.email_copied',
    'filament-resources/maintenance-request.messages.contract_number_copied',
    'filament-resources/maintenance-request.messages.payment_instructions',
    'filament-resources/maintenance-request.sections.request_details',
    'filament-resources/maintenance-request.sections.client_contract',
    'filament-resources/maintenance-request.sections.assignment_financial',
    'filament-resources/maintenance-request.sections.progress_timeline',
    'filament-resources/maintenance-request.sections.additional_info',
    'filament-resources/maintenance-request.sections.contract_details',
    'filament-resources/maintenance-request.fields.request_number',
    'filament-resources/maintenance-request.fields.title',
    'filament-resources/maintenance-request.fields.created_at',
    'filament-resources/maintenance-request.fields.status',
    'filament-resources/maintenance-request.fields.visits_included',
    'filament-resources/maintenance-request.fields.notes',
    'filament-resources/maintenance-request.fields.client_id',
    'filament-resources/maintenance-request.fields.client_phone',
    'filament-resources/maintenance-request.fields.client_email',
    'filament-resources/maintenance-request.fields.contract_number',
    'filament-resources/maintenance-request.fields.contract_type',
    'filament-resources/maintenance-request.fields.contract_status',
    'filament-resources/maintenance-request.fields.assigned_technician',
    'filament-resources/maintenance-request.fields.price',
    'filament-resources/maintenance-request.fields.payment_status',
    'filament-resources/maintenance-request.fields.contract_period',
    'filament-resources/maintenance-request.fields.contract_start_date',
    'filament-resources/maintenance-request.fields.contract_end_date',
    'filament-resources/maintenance-request.fields.contract_type_description',
    'filament-resources/maintenance-request.fields.contract_benefits',
    'filament-resources/maintenance-request.fields.total_amount_required',
    'filament-resources/maintenance-request.fields.amount_paid',
    'filament-resources/maintenance-request.fields.amount_remaining',
    'filament-resources/maintenance-request.fields.payment_instructions',
    'filament-resources/maintenance-request.fields.start_date',
    'filament-resources/maintenance-request.fields.end_date',
    'filament-resources/maintenance-request.fields.contract_terms',
    'filament-resources/maintenance-request.placeholders.no_title',
    'filament-resources/maintenance-request.placeholders.no_notes',
    'filament-resources/maintenance-request.placeholders.not_specified',
    'filament-resources/maintenance-request.placeholders.no_contract',
    'filament-resources/maintenance-request.placeholders.not_assigned',
    'filament-resources/maintenance-request.placeholders.not_priced',
    'filament-resources/maintenance-request.placeholders.no_payments',
    'filament-resources/maintenance-request.placeholders.not_set',
    'filament-resources/maintenance-request.placeholders.no_notes_available',
    'filament-resources/maintenance-request.placeholders.no_description_available',
    'filament-resources/maintenance-request.placeholders.no_benefits_specified',
    'filament-resources/maintenance-request.placeholders.not_set_yet',
    'filament-resources/maintenance-request.placeholders.no_contract_created',
    'filament-resources/maintenance-request.placeholders.no_terms_set',
    'filament-resources/maintenance-request.units.months',
    'filament-resources/maintenance-request.units.riyal',
    'filament-resources/maintenance-request.timeline.request_created',
    'filament-resources/maintenance-request.timeline.under_review',
    'filament-resources/maintenance-request.timeline.review_completed',
    'filament-resources/maintenance-request.timeline.awaiting_review',
    'filament-resources/maintenance-request.timeline.technician_assigned',
    'filament-resources/maintenance-request.timeline.awaiting_assignment',
    'filament-resources/maintenance-request.timeline.work_in_progress',
    'filament-resources/maintenance-request.timeline.awaiting_work_start',
    'filament-resources/maintenance-request.timeline.contract_created',
    'filament-resources/maintenance-request.timeline.contract_number',
    'filament-resources/maintenance-request.timeline.awaiting_contract_creation',
    'filament-resources/maintenance-request.timeline.contract_creation',
    'filament-resources/maintenance-request.timeline.processing_completed',
    'filament-resources/maintenance-request.timeline.processing_completion',
    'filament-resources/maintenance-request.timeline.awaiting_final_procedures',
    'filament-resources/maintenance-request.tabs.notes_details',
    'filament-resources/maintenance-request.tabs.payments',
    'filament-resources/maintenance-request.tabs.linked_contract',
    'filament-resources/maintenance-request.actions.print_request',
    'filament-resources/maintenance-request.actions.download_pdf',
    'filament-resources/maintenance-request.actions.close',
    'filament-resources/maintenance-request.actions.download_contract',
    'filament-resources/maintenance-request.actions.contact_support',
    'filament-resources/maintenance-request.actions.cancel_request',
    'filament-resources/maintenance-request.tooltips.print_request',
    'filament-resources/maintenance-request.tooltips.download_contract',
    'filament-resources/maintenance-request.tooltips.contact_support',
    'filament-resources/maintenance-request.tooltips.cancel_request',
    'filament-resources/maintenance-request.support.inquiry_type_label',
    'filament-resources/maintenance-request.support.inquiry_types.status',
    'filament-resources/maintenance-request.support.inquiry_types.payment',
    'filament-resources/maintenance-request.support.inquiry_types.contract',
    'filament-resources/maintenance-request.support.inquiry_types.technical',
    'filament-resources/maintenance-request.support.inquiry_types.other',
    'filament-resources/maintenance-request.support.inquiry_type_placeholder',
    'filament-resources/maintenance-request.support.message_label',
    'filament-resources/maintenance-request.support.message_placeholder',
    'filament-resources/maintenance-request.support.message_helper',
    'filament-resources/maintenance-request.cancel.modal_heading',
    'filament-resources/maintenance-request.cancel.modal_description',
    'filament-resources/maintenance-request.cancel.submit_label',
    'filament-resources/maintenance-request.cancel.cancel_label',
    'filament-resources/maintenance-request.notifications.pdf_loaded',
    'filament-resources/maintenance-request.notifications.pdf_loaded_body',
    'filament-resources/maintenance-request.notifications.pdf_generated',
    'filament-resources/maintenance-request.notifications.pdf_generated_body',
    'filament-resources/maintenance-request.notifications.pdf_generated_temp',
    'filament-resources/maintenance-request.notifications.pdf_storage_failed',
    'filament-resources/maintenance-request.notifications.pdf_generation_failed',
    'filament-resources/maintenance-request.notifications.pdf_generation_failed_body',
    'filament-resources/maintenance-request.notifications.support_sent_title',
    'filament-resources/maintenance-request.notifications.support_sent_body',
    'filament-resources/maintenance-request.notifications.support_failed_title',
    'filament-resources/maintenance-request.notifications.support_failed_body',
    'filament-resources/maintenance-request.notifications.cancel_success_title',
    'filament-resources/maintenance-request.notifications.cancel_success_body',
    'filament-resources/maintenance-request.notifications.cancel_failed_title',
    'filament-resources/maintenance-request.notifications.cancel_failed_body',
    'filament-resources/maintenance-request.aria.progress_timeline',
    'filament-resources/maintenance-request.aria.notes',
    'filament-resources/maintenance-request.aria.contract_type_description',
    'filament-resources/maintenance-request.aria.contract_benefits',
    'filament-resources/maintenance-request.aria.notes_details_tab',
    'filament-resources/maintenance-request.aria.total_amount_required',
    'filament-resources/maintenance-request.aria.amount_paid',
    'filament-resources/maintenance-request.aria.amount_remaining',
    'filament-resources/maintenance-request.aria.payment_instructions',
    'filament-resources/maintenance-request.aria.payments_tab',
    'filament-resources/maintenance-request.aria.contract_number',
    'filament-resources/maintenance-request.aria.contract_start_date',
    'filament-resources/maintenance-request.aria.contract_end_date',
    'filament-resources/maintenance-request.aria.contract_status',
    'filament-resources/maintenance-request.aria.contract_terms',
    'filament-resources/maintenance-request.aria.linked_contract_tab',
    'filament-resources/maintenance-request.aria.generate_pdf',
    'filament-resources/maintenance-request.aria.download_contract',
    'filament-resources/maintenance-request.aria.contact_support',
];

// Define the languages to test
$languages = [
    'ar' => 'Arabic (العربية)',
    'en' => 'English',
    'ur' => 'Urdu (اردو)',
    'fil' => 'Filipino (Tagalog)',
];

$totalKeys = count($viewMaintenanceRequestKeys);
$results = [];

echo "Testing " . $totalKeys . " ViewMaintenanceRequest translation keys across " . count($languages) . " languages...\n\n";

foreach ($languages as $locale => $languageName) {
    echo "🔍 Testing {$languageName} ({$locale}):\n";
    
    // Set the application locale
    App::setLocale($locale);
    
    $missingKeys = [];
    $foundKeys = [];
    
    foreach ($viewMaintenanceRequestKeys as $key) {
        $translation = __($key);
        
        // Check if translation exists (not returning the key itself)
        if ($translation === $key) {
            $missingKeys[] = $key;
        } else {
            $foundKeys[] = $key;
        }
    }
    
    $foundCount = count($foundKeys);
    $missingCount = count($missingKeys);
    $percentage = round(($foundCount / $totalKeys) * 100, 1);
    
    $results[$locale] = [
        'language' => $languageName,
        'found' => $foundCount,
        'missing' => $missingCount,
        'percentage' => $percentage,
        'missing_keys' => $missingKeys,
    ];
    
    echo "  📊 Coverage: {$foundCount}/{$totalKeys} ({$percentage}%)\n";
    
    if ($missingCount > 0) {
        echo "  ⚠️  Missing {$missingCount} translations\n";
    } else {
        echo "  🎉 Complete translation coverage!\n";
    }
    
    echo "\n";
}

// Summary Report
echo "=== CONSISTENCY SUMMARY ===\n\n";

$allComplete = true;
foreach ($results as $locale => $result) {
    $status = $result['missing'] === 0 ? '✅ COMPLETE' : '❌ INCOMPLETE';
    echo "{$result['language']} ({$locale}): {$status} - {$result['percentage']}% coverage\n";
    
    if ($result['missing'] > 0) {
        $allComplete = false;
    }
}

echo "\n";

if ($allComplete) {
    echo "🎉 **ALL LANGUAGES HAVE COMPLETE VIEWMAINTENANCEREQUEST TRANSLATIONS!**\n";
    echo "✅ All 137 translation keys are available in all 4 languages.\n";
} else {
    echo "⚠️  **SOME LANGUAGES HAVE MISSING TRANSLATIONS**\n";
    echo "❌ Inconsistency detected across language files.\n";
}

echo "\n=== DETAILED MISSING TRANSLATIONS ===\n\n";

foreach ($results as $locale => $result) {
    if ($result['missing'] > 0) {
        echo "{$result['language']} ({$locale}) - Missing {$result['missing']} translations:\n";
        foreach ($result['missing_keys'] as $key) {
            echo "  - {$key}\n";
        }
        echo "\n";
    }
}

echo "=== AUDIT COMPLETE ===\n";
