<?php

/**
 * Manual test script for wizard messages translation audit
 * 
 * This script audits the wizard messages translation keys across all 4 languages
 * Run with: php tests/Manual/test_wizard_messages_audit.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\App;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Wizard Messages Translation Audit ===\n\n";

// Define the translation keys to audit
$auditKeys = [
    'filament-resources/maintenance-request.wizard.step1.messages.contract_selected_body',
    'filament-resources/maintenance-request.wizard.step1.messages.contract_selected_title',
    'filament-resources/maintenance-request.wizard.step3.messages.request_submitted_body',
    'filament-resources/maintenance-request.wizard.step3.messages.request_submitted_title',
];

// Define the languages to test
$languages = [
    'ar' => 'Arabic (العربية)',
    'en' => 'English',
    'ur' => 'Urdu (اردو)',
    'fil' => 'Filipino (Tagalog)',
];

$totalKeys = count($auditKeys);
$results = [];

echo "Auditing " . $totalKeys . " wizard message translation keys across " . count($languages) . " languages...\n\n";

foreach ($languages as $locale => $languageName) {
    echo "🔍 Auditing {$languageName} ({$locale}):\n";
    
    // Set the application locale
    App::setLocale($locale);
    
    $missingKeys = [];
    $foundKeys = [];
    
    foreach ($auditKeys as $key) {
        $translation = __($key);
        
        // Check if translation exists (not returning the key itself)
        if ($translation === $key) {
            $missingKeys[] = $key;
            echo "  ❌ Missing: {$key}\n";
        } else {
            $foundKeys[] = $key;
            echo "  ✅ Found: {$key}\n";
            echo "      Translation: \"{$translation}\"\n";
        }
    }
    
    $foundCount = count($foundKeys);
    $missingCount = count($missingKeys);
    $percentage = round(($foundCount / $totalKeys) * 100, 1);
    
    $results[$locale] = [
        'language' => $languageName,
        'found' => $foundCount,
        'missing' => $missingCount,
        'percentage' => $percentage,
        'missing_keys' => $missingKeys,
        'found_keys' => $foundKeys,
    ];
    
    echo "  📊 Coverage: {$foundCount}/{$totalKeys} ({$percentage}%)\n";
    
    if ($missingCount > 0) {
        echo "  ⚠️  Missing {$missingCount} translations\n";
    } else {
        echo "  🎉 Complete translation coverage!\n";
    }
    
    echo "\n";
}

// Summary Report
echo "=== AUDIT SUMMARY ===\n\n";

$allComplete = true;
$totalMissing = 0;

foreach ($results as $locale => $result) {
    $status = $result['missing'] === 0 ? '✅ COMPLETE' : '❌ INCOMPLETE';
    echo "{$result['language']} ({$locale}): {$status} - {$result['percentage']}% coverage\n";
    
    if ($result['missing'] > 0) {
        $allComplete = false;
        $totalMissing += $result['missing'];
        echo "  Missing keys:\n";
        foreach ($result['missing_keys'] as $missingKey) {
            echo "    - {$missingKey}\n";
        }
    }
}

echo "\n";

if ($allComplete) {
    echo "🎉 **ALL WIZARD MESSAGE TRANSLATIONS ARE COMPLETE!**\n";
    echo "✅ All 4 wizard message keys are available in all 4 languages.\n";
} else {
    echo "⚠️  **SOME WIZARD MESSAGE TRANSLATIONS ARE MISSING**\n";
    echo "❌ Total missing translations: {$totalMissing}\n";
    echo "🔧 Action required: Add missing translations to achieve 100% coverage.\n";
}

echo "\n=== DETAILED TRANSLATION ANALYSIS ===\n\n";

// Analyze each key across all languages
foreach ($auditKeys as $key) {
    echo "📋 Key: {$key}\n";
    
    $keyResults = [];
    foreach ($languages as $locale => $languageName) {
        App::setLocale($locale);
        $translation = __($key);
        $exists = $translation !== $key;
        
        $keyResults[$locale] = [
            'exists' => $exists,
            'translation' => $exists ? $translation : 'MISSING',
        ];
        
        $status = $exists ? '✅' : '❌';
        echo "  {$status} {$languageName}: {$keyResults[$locale]['translation']}\n";
    }
    
    // Check if key is complete across all languages
    $completeCount = array_sum(array_column($keyResults, 'exists'));
    $completionRate = round(($completeCount / count($languages)) * 100, 1);
    
    echo "  📊 Completion: {$completeCount}/" . count($languages) . " languages ({$completionRate}%)\n";
    echo "\n";
}

echo "=== RECOMMENDATIONS ===\n\n";

if ($allComplete) {
    echo "🎯 **EXCELLENT WIZARD MESSAGE COVERAGE**\n";
    echo "• All wizard message translations are complete across all languages\n";
    echo "• Contract selection and request submission messages are properly localized\n";
    echo "• Arabic-first design maintained with RTL support\n";
    echo "• Professional business terminology used across all languages\n\n";
    
    echo "📋 **MAINTENANCE RECOMMENDATIONS:**\n";
    echo "• Test wizard message display in browser across all languages\n";
    echo "• Verify placeholder variable substitution (e.g., :name, :number)\n";
    echo "• Ensure message timing and display duration are appropriate\n";
    echo "• Maintain consistency in message tone and terminology\n";
} else {
    echo "🔧 **ACTION REQUIRED:**\n";
    echo "• Add missing wizard message translations to incomplete language files\n";
    echo "• Ensure message translations follow established patterns\n";
    echo "• Verify placeholder variable syntax uses colon notation (:variable)\n";
    echo "• Test all translations in browser for proper display\n";
    echo "• Maintain professional business tone across all languages\n";
}

echo "\n=== AUDIT COMPLETE ===\n";

echo "\nNext Steps:\n";
echo "1. Review missing translations identified above\n";
echo "2. Add missing keys to appropriate language files\n";
echo "3. Test wizard message display in browser\n";
echo "4. Verify placeholder variable substitution works correctly\n";
echo "5. Ensure RTL support for Arabic and Urdu messages\n";
