<?php

namespace Tests;

use App\Models\User;
use App\Models\Tenant;
use App\Models\Client;
use App\Models\ContractType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Stancl\Tenancy\Features\TenantConfig;
use Stancl\Tenancy\Database\Concerns\HasDatabase;

/**
 * TenantTestCase
 *
 * Base test case for tenant-specific tests in a multi-tenant Laravel application.
 * Provides utilities for tenant creation, switching, and isolation testing.
 *
 * @package Tests
 */
abstract class TenantTestCase extends TestCase
{
    use RefreshDatabase;

    /**
     * The current tenant instance.
     *
     * @var Tenant
     */
    protected Tenant $tenant;

    /**
     * The authenticated user for testing.
     *
     * @var User
     */
    protected User $user;

    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Create and initialize a tenant for testing
        $this->tenant = $this->createTenant();
        $this->initializeTenancy($this->tenant);

        // Create a test user (tenant users don't have passwords)
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);

        // Authenticate the user
        $this->actingAs($this->user);

        // Seed basic data if needed
        $this->seedBasicData();
    }

    /**
     * Clean up after each test.
     */
    protected function tearDown(): void
    {
        // Clean up tenant context
        if (tenancy()->initialized) {
            tenancy()->end();
        }

        parent::tearDown();
    }

    /**
     * Create a new tenant for testing.
     *
     * @param array $attributes
     * @return Tenant
     */
    protected function createTenant(array $attributes = []): Tenant
    {
        $defaultAttributes = [
            'id' => 'test-tenant-' . uniqid(),
        ];

        $tenant = Tenant::create(array_merge($defaultAttributes, $attributes));

        // Create domain for the tenant
        $tenant->domains()->create([
            'domain' => $tenant->id . '.localhost',
        ]);

        return $tenant;
    }

    /**
     * Initialize tenancy for the given tenant.
     *
     * @param Tenant $tenant
     * @return void
     */
    protected function initializeTenancy(Tenant $tenant): void
    {
        tenancy()->initialize($tenant);

        // Run tenant migrations
        $this->artisan('migrate', [
            '--database' => 'tenant',
            '--path' => 'database/migrations/tenant',
            '--force' => true,
        ])->assertExitCode(0);
    }

    /**
     * Create and switch to a new tenant.
     *
     * @param array $attributes
     * @return Tenant
     */
    protected function createAndSwitchTenant(array $attributes = []): Tenant
    {
        $newTenant = $this->createTenant($attributes);
        $this->switchTenant($newTenant);
        return $newTenant;
    }

    /**
     * Switch to a different tenant.
     *
     * @param Tenant $tenant
     * @return void
     */
    protected function switchTenant(Tenant $tenant): void
    {
        tenancy()->end();
        $this->initializeTenancy($tenant);
        $this->tenant = $tenant;

        // Re-authenticate user in new tenant context
        if (isset($this->user)) {
            $this->actingAs($this->user);
        }
    }

    /**
     * Get the current tenant.
     *
     * @return Tenant
     */
    protected function getCurrentTenant(): Tenant
    {
        return $this->tenant;
    }

    /**
     * Seed basic data for testing.
     *
     * @return void
     */
    protected function seedBasicData(): void
    {
        // Create basic contract types
        ContractType::factory()->create([
            'name' => 'Basic Maintenance',
            'description' => 'Basic maintenance contract',
            'is_active' => true,
        ]);

        ContractType::factory()->create([
            'name' => 'Premium Maintenance',
            'description' => 'Premium maintenance contract',
            'is_active' => true,
        ]);

        // Create a test client
        Client::factory()->create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
        ]);
    }

    /**
     * Create a technician user for testing.
     *
     * @param array $attributes
     * @return User
     */
    protected function createTechnician(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'role' => 'technician',
            'name' => 'Test Technician',
            'email' => '<EMAIL>',
        ], $attributes));
    }

    /**
     * Create a client user for testing.
     *
     * @param array $attributes
     * @return User
     */
    protected function createClientUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'role' => 'client',
            'name' => 'Test Client User',
            'email' => '<EMAIL>',
        ], $attributes));
    }

    /**
     * Create a manager user for testing.
     *
     * @param array $attributes
     * @return User
     */
    protected function createManager(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'role' => 'manager',
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
        ], $attributes));
    }

    /**
     * Assert that tenant isolation is working correctly.
     *
     * @param string $model
     * @param mixed $data1
     * @param mixed $data2
     * @return void
     */
    protected function assertTenantIsolation(string $model, $data1, $data2): void
    {
        $tenant1 = $this->getCurrentTenant();

        // Create data in tenant 1
        $model::create($data1);
        $this->assertCount(1, $model::all());

        // Switch to tenant 2
        $tenant2 = $this->createAndSwitchTenant();

        // Should not see tenant 1 data
        $this->assertCount(0, $model::all());

        // Create data in tenant 2
        $model::create($data2);
        $this->assertCount(1, $model::all());

        // Switch back to tenant 1
        $this->switchTenant($tenant1);

        // Should only see tenant 1 data
        $this->assertCount(1, $model::all());
    }

    /**
     * Get the tenant database connection name.
     *
     * @return string
     */
    protected function getTenantConnection(): string
    {
        return 'tenant';
    }

    /**
     * Assert that the current context is properly tenant-aware.
     *
     * @return void
     */
    protected function assertTenantContext(): void
    {
        $this->assertNotNull(tenant());
        $this->assertEquals($this->tenant->id, tenant()->id);
        $this->assertTrue(tenancy()->initialized);
    }

    /**
     * Refresh the tenant database.
     *
     * @return void
     */
    protected function refreshTenantDatabase(): void
    {
        $this->artisan('migrate:fresh', [
            '--database' => 'tenant',
            '--path' => 'database/migrations/tenant',
            '--force' => true,
        ]);

        $this->seedBasicData();
    }
}
