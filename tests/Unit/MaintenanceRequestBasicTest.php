<?php

declare(strict_types=1);

use App\Models\MaintenanceRequest;

/**
 * Basic MaintenanceRequest Tests
 *
 * Simple tests to verify the MaintenanceRequest refactoring without complex tenant setup.
 */

test('status constants are properly defined', function () {
    expect(MaintenanceRequest::STATUS_NEW)->toBe('new');
    expect(MaintenanceRequest::STATUS_ASSIGNED)->toBe('assigned');
    expect(MaintenanceRequest::STATUS_IN_PROGRESS)->toBe('in_progress');
    expect(MaintenanceRequest::STATUS_COMPLETED)->toBe('completed');
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');
});

test('getStatusOptions returns all status options', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options)->toBeArray()
        ->toHaveCount(5)
        ->toHaveKey(MaintenanceRequest::STATUS_NEW)
        ->toHaveKey(MaintenanceRequest::STATUS_ASSIGNED)
        ->toHaveKey(MaintenanceRequest::STATUS_IN_PROGRESS)
        ->toHaveKey(MaintenanceRequest::STATUS_COMPLETED)
        ->toHaveKey(MaintenanceRequest::STATUS_CANCELLED);

    expect($options[MaintenanceRequest::STATUS_NEW])->toBe('New');
    expect($options[MaintenanceRequest::STATUS_CANCELLED])->toBe('Canceled');
});

test('canceled status uses correct spelling', function () {
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');
    expect(MaintenanceRequest::STATUS_CANCELLED)->not->toBe('canceled');
});

test('status options do not include old canceled spelling', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options)->not->toHaveKey('canceled');
    expect($options)->toHaveKey('canceled');
});

test('all status constants are strings', function () {
    expect(MaintenanceRequest::STATUS_NEW)->toBeString();
    expect(MaintenanceRequest::STATUS_ASSIGNED)->toBeString();
    expect(MaintenanceRequest::STATUS_IN_PROGRESS)->toBeString();
    expect(MaintenanceRequest::STATUS_COMPLETED)->toBeString();
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBeString();
});

test('status constants have expected values', function () {
    $expectedStatuses = [
        'new' => MaintenanceRequest::STATUS_NEW,
        'assigned' => MaintenanceRequest::STATUS_ASSIGNED,
        'in_progress' => MaintenanceRequest::STATUS_IN_PROGRESS,
        'completed' => MaintenanceRequest::STATUS_COMPLETED,
        'canceled' => MaintenanceRequest::STATUS_CANCELLED,
    ];

    foreach ($expectedStatuses as $expected => $actual) {
        expect($actual)->toBe($expected);
    }
});

test('maintenance request model exists and is accessible', function () {
    expect(class_exists(MaintenanceRequest::class))->toBeTrue();
    expect(method_exists(MaintenanceRequest::class, 'getStatusOptions'))->toBeTrue();
});

test('status options method returns correct structure', function () {
    $options = MaintenanceRequest::getStatusOptions();

    // Should be an associative array
    expect($options)->toBeArray();

    // All keys should be status constants
    $keys = array_keys($options);
    expect($keys)->toContain(MaintenanceRequest::STATUS_NEW);
    expect($keys)->toContain(MaintenanceRequest::STATUS_CANCELLED);

    // All values should be strings
    foreach ($options as $value) {
        expect($value)->toBeString();
    }
});

test('canceled status is properly formatted in options', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options[MaintenanceRequest::STATUS_CANCELLED])->toBe('Canceled');
    expect($options[MaintenanceRequest::STATUS_CANCELLED])->not->toBe('Canceled');
});

test('model constants are immutable', function () {
    // These should never change
    expect(MaintenanceRequest::STATUS_NEW)->toBe('new');
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');

    // Verify the old spelling is not used
    expect(MaintenanceRequest::STATUS_CANCELLED)->not->toBe('canceled');
});
