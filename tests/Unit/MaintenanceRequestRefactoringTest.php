<?php

declare(strict_types=1);

use App\Models\MaintenanceRequest;
use App\Filament\Resources\MaintenanceRequestResource;
use App\Filament\Traits\HasMaintenanceRequestFormHelpers;
use App\Filament\Traits\HasMaintenanceRequestTableHelpers;

/**
 * MaintenanceRequest Refactoring Verification Tests
 *
 * Tests to verify that the refactoring changes have been properly implemented:
 * 1. Priority field removal
 * 2. "Canceled" status standardization
 * 3. Filament component fixes
 */

test('MaintenanceRequest model has correct status constants', function () {
    expect(MaintenanceRequest::STATUS_NEW)->toBe('new');
    expect(MaintenanceRequest::STATUS_ASSIGNED)->toBe('assigned');
    expect(MaintenanceRequest::STATUS_IN_PROGRESS)->toBe('in_progress');
    expect(MaintenanceRequest::STATUS_COMPLETED)->toBe('completed');
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');
});

test('MaintenanceRequest model does not have priority constants', function () {
    expect(defined('App\Models\MaintenanceRequest::PRIORITY_LOW'))->toBeFalse();
    expect(defined('App\Models\MaintenanceRequest::PRIORITY_MEDIUM'))->toBeFalse();
    expect(defined('App\Models\MaintenanceRequest::PRIORITY_HIGH'))->toBeFalse();
    expect(defined('App\Models\MaintenanceRequest::PRIORITY_CRITICAL'))->toBeFalse();
});

test('status options use canceled not canceled', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options)->toHaveKey('canceled');
    expect($options)->not->toHaveKey('canceled');
    expect($options['canceled'])->toBe('Canceled');
});

test('MaintenanceRequestResource class exists and is accessible', function () {
    expect(class_exists(MaintenanceRequestResource::class))->toBeTrue();
    expect(method_exists(MaintenanceRequestResource::class, 'getModel'))->toBeTrue();
    expect(MaintenanceRequestResource::getModel())->toBe(MaintenanceRequest::class);
});

test('HasMaintenanceRequestFormHelpers trait exists', function () {
    expect(trait_exists(HasMaintenanceRequestFormHelpers::class))->toBeTrue();
});

test('HasMaintenanceRequestTableHelpers trait exists', function () {
    expect(trait_exists(HasMaintenanceRequestTableHelpers::class))->toBeTrue();
});

test('form helpers trait does not have priority field method', function () {
    $methods = get_class_methods(HasMaintenanceRequestFormHelpers::class);

    expect($methods)->not->toContain('getPriorityField');
    expect($methods)->not->toContain('getPriorityOptions');
});

test('table helpers trait does not have priority column method', function () {
    $methods = get_class_methods(HasMaintenanceRequestTableHelpers::class);

    expect($methods)->not->toContain('getPriorityColumn');
    expect($methods)->not->toContain('getPriorityFilter');
});

test('form helpers trait methods exist (via reflection)', function () {
    $reflection = new ReflectionClass(HasMaintenanceRequestFormHelpers::class);
    $methods = array_map(fn($method) => $method->getName(), $reflection->getMethods());

    expect($methods)->toContain('getStatusField');
});

test('table helpers trait methods exist (via reflection)', function () {
    $reflection = new ReflectionClass(HasMaintenanceRequestTableHelpers::class);
    $methods = array_map(fn($method) => $method->getName(), $reflection->getMethods());

    expect($methods)->toContain('getStatusColumn');
    expect($methods)->toContain('getStatusFilter');
});

test('table helpers trait has date range filter method (via reflection)', function () {
    $reflection = new ReflectionClass(HasMaintenanceRequestTableHelpers::class);
    $methods = array_map(fn($method) => $method->getName(), $reflection->getMethods());

    expect($methods)->toContain('getDateRangeFilter');
});

test('MaintenanceRequest resource has navigation badge method', function () {
    expect(method_exists(MaintenanceRequestResource::class, 'getNavigationBadge'))->toBeTrue();
});

test('MaintenanceRequest resource has pages defined', function () {
    $pages = MaintenanceRequestResource::getPages();

    expect($pages)->toBeArray();
    expect($pages)->toHaveKey('index');
    expect($pages)->toHaveKey('create');
    expect($pages)->toHaveKey('edit');
    expect($pages)->toHaveKey('view');
});

test('MaintenanceRequest resource has correct model label', function () {
    expect(method_exists(MaintenanceRequestResource::class, 'getModelLabel'))->toBeTrue();
    expect(method_exists(MaintenanceRequestResource::class, 'getPluralModelLabel'))->toBeTrue();
});

test('status constants are immutable and correct', function () {
    // These values should never change
    expect(MaintenanceRequest::STATUS_NEW)->toBe('new');
    expect(MaintenanceRequest::STATUS_ASSIGNED)->toBe('assigned');
    expect(MaintenanceRequest::STATUS_IN_PROGRESS)->toBe('in_progress');
    expect(MaintenanceRequest::STATUS_COMPLETED)->toBe('completed');
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');

    // Ensure old spelling is not used
    expect(MaintenanceRequest::STATUS_CANCELLED)->not->toBe('canceled');
});

test('getStatusOptions returns exactly 5 options', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options)->toHaveCount(5);
    expect(array_keys($options))->toEqual([
        'new',
        'assigned',
        'in_progress',
        'completed',
        'canceled'
    ]);
});

test('all status option values are properly capitalized', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options['new'])->toBe('New');
    expect($options['assigned'])->toBe('Assigned');
    expect($options['in_progress'])->toBe('In Progress');
    expect($options['completed'])->toBe('Completed');
    expect($options['canceled'])->toBe('Canceled');
});

test('MaintenanceRequest class has proper namespace', function () {
    $reflection = new ReflectionClass(MaintenanceRequest::class);
    expect($reflection->getNamespaceName())->toBe('App\Models');
});

test('MaintenanceRequestResource class has proper namespace', function () {
    $reflection = new ReflectionClass(MaintenanceRequestResource::class);
    expect($reflection->getNamespaceName())->toBe('App\Filament\Resources');
});

test('helper traits have proper namespace', function () {
    $formHelperReflection = new ReflectionClass(HasMaintenanceRequestFormHelpers::class);
    expect($formHelperReflection->getNamespaceName())->toBe('App\Filament\Traits');

    $tableHelperReflection = new ReflectionClass(HasMaintenanceRequestTableHelpers::class);
    expect($tableHelperReflection->getNamespaceName())->toBe('App\Filament\Traits');
});

test('refactoring removed priority-related methods completely', function () {
    // Check that no priority-related methods exist in the codebase
    $formMethods = get_class_methods(HasMaintenanceRequestFormHelpers::class);
    $tableMethods = get_class_methods(HasMaintenanceRequestTableHelpers::class);
    $resourceMethods = get_class_methods(MaintenanceRequestResource::class);

    $allMethods = array_merge($formMethods, $tableMethods, $resourceMethods);

    foreach ($allMethods as $method) {
        expect(strtolower($method))->not->toContain('priority');
    }
});

test('refactoring uses canceled spelling consistently', function () {
    // Test that the status constant uses correct spelling
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');

    // Test that status options use correct spelling
    $options = MaintenanceRequest::getStatusOptions();
    expect($options)->toHaveKey('canceled');
    expect($options['canceled'])->toBe('Canceled');

    // Ensure old spelling is not present
    expect($options)->not->toHaveKey('canceled');
});
