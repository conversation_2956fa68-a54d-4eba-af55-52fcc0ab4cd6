<?php

declare(strict_types=1);

use App\Models\MaintenanceRequest;
use App\Models\Client;
use App\Models\ContractType;
use App\Models\User;
use App\Models\Contract;
use App\Models\Payment;
use Tests\TenantTestCase;
use Carbon\Carbon;

/**
 * MaintenanceRequest Model Tests
 *
 * Unit tests for the MaintenanceRequest model.
 * Tests model methods, relationships, status constants, and business logic.
 */

uses(TenantTestCase::class);
test('status constants are properly defined', function () {
    expect(MaintenanceRequest::STATUS_NEW)->toBe('new');
    expect(MaintenanceRequest::STATUS_ASSIGNED)->toBe('assigned');
    expect(MaintenanceRequest::STATUS_IN_PROGRESS)->toBe('in_progress');
    expect(MaintenanceRequest::STATUS_COMPLETED)->toBe('completed');
    expect(MaintenanceRequest::STATUS_CANCELLED)->toBe('canceled');
});

test('getStatusOptions returns all status options', function () {
    $options = MaintenanceRequest::getStatusOptions();

    expect($options)->toBeArray()
        ->toHaveCount(5)
        ->toHave<PERSON>ey(MaintenanceRequest::STATUS_NEW)
        ->toHaveKey(MaintenanceRequest::STATUS_ASSIGNED)
        ->toHaveKey(MaintenanceRequest::STATUS_IN_PROGRESS)
        ->toHaveKey(MaintenanceRequest::STATUS_COMPLETED)
        ->toHaveKey(MaintenanceRequest::STATUS_CANCELLED);

    expect($options[MaintenanceRequest::STATUS_NEW])->toBe('New');
    expect($options[MaintenanceRequest::STATUS_CANCELLED])->toBe('Canceled');
});

test('maintenance request can be created', function () {
    $maintenanceRequest = MaintenanceRequest::factory()->create();

    expect($maintenanceRequest)->toBeInstanceOf(MaintenanceRequest::class);
    expect($maintenanceRequest->request_number)->not->toBeEmpty();
    expect($maintenanceRequest->title)->not->toBeEmpty();
    expect($maintenanceRequest->description)->not->toBeEmpty();
    expect($maintenanceRequest->status)->toBeIn([
        MaintenanceRequest::STATUS_NEW,
        MaintenanceRequest::STATUS_ASSIGNED,
        MaintenanceRequest::STATUS_IN_PROGRESS,
        MaintenanceRequest::STATUS_COMPLETED,
        MaintenanceRequest::STATUS_CANCELLED,
    ]);
});

test('maintenance request relationships work correctly', function () {
    $client = Client::factory()->create();
    $contractType = ContractType::factory()->create();
    $technician = User::factory()->technician()->create();

    $maintenanceRequest = MaintenanceRequest::factory()->create([
        'client_id' => $client->id,
        'contract_type_id' => $contractType->id,
        'assigned_to' => $technician->id,
    ]);

    // Test client relationship
    expect($maintenanceRequest->client)->toBeInstanceOf(Client::class);
    expect($maintenanceRequest->client->id)->toBe($client->id);

    // Test contract type relationship
    expect($maintenanceRequest->contractType)->toBeInstanceOf(ContractType::class);
    expect($maintenanceRequest->contractType->id)->toBe($contractType->id);

    // Test assigned technician relationship
    expect($maintenanceRequest->assignedTechnician)->toBeInstanceOf(User::class);
    expect($maintenanceRequest->assignedTechnician->id)->toBe($technician->id);
});

test('status transitions work correctly', function () {
    // Test new to assigned
    $request = MaintenanceRequest::factory()->new()->create();
    expect($request->status)->toBe(MaintenanceRequest::STATUS_NEW);

    $request->update(['status' => MaintenanceRequest::STATUS_ASSIGNED]);
    expect($request->status)->toBe(MaintenanceRequest::STATUS_ASSIGNED);

    // Test assigned to in progress
    $request->update(['status' => MaintenanceRequest::STATUS_IN_PROGRESS]);
    expect($request->status)->toBe(MaintenanceRequest::STATUS_IN_PROGRESS);

    // Test in progress to completed
    $request->update(['status' => MaintenanceRequest::STATUS_COMPLETED]);
    expect($request->status)->toBe(MaintenanceRequest::STATUS_COMPLETED);
});

test('maintenance request can be canceled', function () {
    $request = MaintenanceRequest::factory()->new()->create();

    $request->update(['status' => MaintenanceRequest::STATUS_CANCELLED]);

    expect($request->status)->toBe(MaintenanceRequest::STATUS_CANCELLED);
});

test('completed request has completion date', function () {
    $request = MaintenanceRequest::factory()->completed()->create();

    expect($request->status)->toBe(MaintenanceRequest::STATUS_COMPLETED);
    expect($request->completed_at)->not->toBeNull();
    expect($request->completed_at)->toBeInstanceOf(Carbon::class);
});

test('request number is unique', function () {
    $request1 = MaintenanceRequest::factory()->create();
    $request2 = MaintenanceRequest::factory()->create();

    expect($request1->request_number)->not->toBe($request2->request_number);
});

test('date casting works correctly', function () {
    $request = MaintenanceRequest::factory()->create([
        'request_date' => '2024-01-15',
        'completion_date' => '2024-02-15',
    ]);

    expect($request->request_date)->toBeInstanceOf(Carbon::class);
    expect($request->completion_date)->toBeInstanceOf(Carbon::class);
});

test('factory states work correctly', function () {
    // Test new state
    $newRequest = MaintenanceRequest::factory()->new()->create();
    expect($newRequest->status)->toBe(MaintenanceRequest::STATUS_NEW);
    expect($newRequest->assigned_to)->toBeNull();

    // Test assigned state
    $assignedRequest = MaintenanceRequest::factory()->assigned()->create();
    expect($assignedRequest->status)->toBe(MaintenanceRequest::STATUS_ASSIGNED);
    expect($assignedRequest->assigned_to)->not->toBeNull();

    // Test completed state
    $completedRequest = MaintenanceRequest::factory()->completed()->create();
    expect($completedRequest->status)->toBe(MaintenanceRequest::STATUS_COMPLETED);
    expect($completedRequest->completed_at)->not->toBeNull();

    // Test canceled state
    $canceledRequest = MaintenanceRequest::factory()->canceled()->create();
    expect($canceledRequest->status)->toBe(MaintenanceRequest::STATUS_CANCELLED);
});

test('overdue requests work correctly', function () {
    $overdueRequest = MaintenanceRequest::factory()->overdue()->create();

    expect($overdueRequest->completion_date)->not->toBeNull();
    expect($overdueRequest->completion_date->isPast())->toBeTrue();
    expect($overdueRequest->status)->toBeIn([
        MaintenanceRequest::STATUS_NEW,
        MaintenanceRequest::STATUS_ASSIGNED,
        MaintenanceRequest::STATUS_IN_PROGRESS,
    ]);
});

test('maintenance request belongs to tenant', function () {
    $request = MaintenanceRequest::factory()->create();

    expect(tenant())->not->toBeNull();
    expect(tenant()->id)->not->toBeEmpty();
});
