<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Client;
use App\Models\Visit;
use App\Models\Contract;
use App\Models\MaintenanceRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class ClientDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up basic environment for testing
        config(['app.env' => 'testing']);
    }

    /** @test */
    public function client_dashboard_loads_without_database_errors()
    {
        // Create a client
        $client = Client::factory()->create();
        
        // Create a maintenance request for the client
        $maintenanceRequest = MaintenanceRequest::factory()->create([
            'client_id' => $client->id,
        ]);
        
        // Create a contract for the maintenance request
        $contract = Contract::factory()->create([
            'maintenance_request_id' => $maintenanceRequest->id,
            'client_id' => $client->id,
        ]);
        
        // Create a technician
        $technician = User::factory()->create();
        
        // Create a visit associated with the contract (polymorphic)
        $visit = Visit::factory()->create([
            'visitable_type' => 'App\Models\Contract',
            'visitable_id' => $contract->id,
            'technician_id' => $technician->id,
            'status' => 'scheduled',
            'scheduled_at' => now()->addDays(1),
        ]);
        
        // Authenticate as the client
        Auth::guard('client')->login($client);
        
        // Access the dashboard
        $response = $this->get('/dashboard');
        
        // Should load without database errors
        $response->assertStatus(200);
    }

    /** @test */
    public function dashboard_upcoming_visits_count_works_with_polymorphic_relationships()
    {
        // Create a client
        $client = Client::factory()->create();
        
        // Create a maintenance request for the client
        $maintenanceRequest = MaintenanceRequest::factory()->create([
            'client_id' => $client->id,
        ]);
        
        // Create a contract for the maintenance request
        $contract = Contract::factory()->create([
            'maintenance_request_id' => $maintenanceRequest->id,
            'client_id' => $client->id,
        ]);
        
        // Create a technician
        $technician = User::factory()->create();
        
        // Create visits with different polymorphic associations
        
        // 1. Visit associated with contract
        Visit::factory()->create([
            'visitable_type' => 'App\Models\Contract',
            'visitable_id' => $contract->id,
            'technician_id' => $technician->id,
            'status' => 'scheduled',
            'scheduled_at' => now()->addDays(1),
        ]);
        
        // 2. Visit associated with maintenance request
        Visit::factory()->create([
            'visitable_type' => 'App\Models\MaintenanceRequest',
            'visitable_id' => $maintenanceRequest->id,
            'technician_id' => $technician->id,
            'status' => 'in_progress',
            'scheduled_at' => now()->addHours(2),
        ]);
        
        // 3. Visit for different client (should not be counted)
        $otherClient = Client::factory()->create();
        $otherMaintenanceRequest = MaintenanceRequest::factory()->create([
            'client_id' => $otherClient->id,
        ]);
        Visit::factory()->create([
            'visitable_type' => 'App\Models\MaintenanceRequest',
            'visitable_id' => $otherMaintenanceRequest->id,
            'technician_id' => $technician->id,
            'status' => 'scheduled',
            'scheduled_at' => now()->addDays(2),
        ]);
        
        // Authenticate as the client
        Auth::guard('client')->login($client);
        
        // Test the dashboard page class directly
        $dashboardPage = new \App\Filament\Client\Pages\Dashboard();
        
        // Should return 2 visits for this client (1 contract-based + 1 maintenance request-based)
        $upcomingVisitsCount = $dashboardPage->getUpcomingVisitsCount();
        
        $this->assertEquals(2, $upcomingVisitsCount);
    }

    /** @test */
    public function dashboard_upcoming_visits_list_works_with_polymorphic_relationships()
    {
        // Create a client
        $client = Client::factory()->create();
        
        // Create a maintenance request for the client
        $maintenanceRequest = MaintenanceRequest::factory()->create([
            'client_id' => $client->id,
            'request_number' => 'REQ-202506-0001',
        ]);
        
        // Create a contract for the maintenance request
        $contract = Contract::factory()->create([
            'maintenance_request_id' => $maintenanceRequest->id,
            'client_id' => $client->id,
        ]);
        
        // Create a technician
        $technician = User::factory()->create([
            'name' => 'Test Technician',
        ]);
        
        // Create a visit associated with maintenance request
        Visit::factory()->create([
            'visitable_type' => 'App\Models\MaintenanceRequest',
            'visitable_id' => $maintenanceRequest->id,
            'technician_id' => $technician->id,
            'status' => 'scheduled',
            'scheduled_at' => now()->addDays(1),
        ]);
        
        // Authenticate as the client
        Auth::guard('client')->login($client);
        
        // Test the dashboard page class directly
        $dashboardPage = new \App\Filament\Client\Pages\Dashboard();
        
        // Should return visit data without errors
        $upcomingVisits = $dashboardPage->getUpcomingVisits();
        
        $this->assertIsArray($upcomingVisits);
        $this->assertCount(1, $upcomingVisits);
        $this->assertEquals('REQ-202506-0001', $upcomingVisits[0]['contract_number']);
        $this->assertEquals('Test Technician', $upcomingVisits[0]['technician']);
    }
}
