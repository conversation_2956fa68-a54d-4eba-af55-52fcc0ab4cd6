<?php

declare(strict_types=1);

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;

/**
 * MaintenanceRequest Database Tests
 *
 * Tests for database structure and migration verification.
 */

uses(RefreshDatabase::class);

test('maintenance_requests table exists', function () {
    expect(Schema::hasTable('maintenance_requests'))->toBeTrue();
});

test('priority column does not exist in maintenance_requests table', function () {
    expect(Schema::hasColumn('maintenance_requests', 'priority'))->toBeFalse();
});

test('required columns exist in maintenance_requests table', function () {
    $requiredColumns = [
        'id',
        'request_number',
        'client_id',
        'contract_type_id',
        'title',
        'description',
        'status',
        'request_date',
        'completion_date',
        'assigned_to',
        'notes',
        'created_at',
        'updated_at'
    ];

    foreach ($requiredColumns as $column) {
        expect(Schema::hasColumn('maintenance_requests', $column))
            ->toBeTrue("Column {$column} should exist");
    }
});

test('maintenance_requests table structure is correct', function () {
    expect(Schema::hasTable('maintenance_requests'))->toBeTrue();

    // Test that we can get column listing without errors
    $columns = Schema::getColumnListing('maintenance_requests');
    expect($columns)->toBeArray();
    expect($columns)->toContain('id');
    expect($columns)->toContain('status');
    expect($columns)->not->toContain('priority');
});

test('database can handle canceled status', function () {
    // This test verifies that the database can store the 'canceled' status
    // without throwing any constraint errors

    $this->artisan('migrate:fresh');

    // Insert a record with canceled status directly into database
    DB::table('maintenance_requests')->insert([
        'request_number' => 'TEST-001',
        'client_id' => 1,
        'contract_type_id' => 1,
        'title' => 'Test Request',
        'description' => 'Test Description',
        'status' => 'canceled',
        'request_date' => now(),
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    // Verify it was stored correctly
    $record = DB::table('maintenance_requests')->where('request_number', 'TEST-001')->first();
    expect($record)->not->toBeNull();
    expect($record->status)->toBe('canceled');
});

test('database does not contain old canceled status', function () {
    $this->artisan('migrate:fresh');

    // Try to insert with old spelling - this should work but we'll verify conversion
    DB::table('maintenance_requests')->insert([
        'request_number' => 'TEST-002',
        'client_id' => 1,
        'contract_type_id' => 1,
        'title' => 'Test Request 2',
        'description' => 'Test Description 2',
        'status' => 'canceled', // Old spelling
        'request_date' => now(),
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    // Run the migration logic to convert old spelling
    DB::table('maintenance_requests')
        ->where('status', 'canceled')
        ->update(['status' => 'canceled']);

    // Verify conversion
    $record = DB::table('maintenance_requests')->where('request_number', 'TEST-002')->first();
    expect($record->status)->toBe('canceled');
    expect($record->status)->not->toBe('canceled');
});

test('all valid statuses can be stored in database', function () {
    $this->artisan('migrate:fresh');

    $statuses = [
        'new',
        'assigned',
        'in_progress',
        'completed',
        'canceled'
    ];

    foreach ($statuses as $index => $status) {
        DB::table('maintenance_requests')->insert([
            'request_number' => "TEST-{$index}",
            'client_id' => 1,
            'contract_type_id' => 1,
            'title' => "Test Request {$index}",
            'description' => "Test Description {$index}",
            'status' => $status,
            'request_date' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $record = DB::table('maintenance_requests')->where('request_number', "TEST-{$index}")->first();
        expect($record->status)->toBe($status);
    }
});

test('migration removes priority column if it exists', function () {
    // This test verifies that the migration properly removes the priority column
    // if it existed in a previous version

    expect(Schema::hasColumn('maintenance_requests', 'priority'))->toBeFalse();
});

test('database schema supports all required maintenance request fields', function () {
    $this->artisan('migrate:fresh');

    // Test inserting a complete maintenance request record
    $data = [
        'request_number' => 'FULL-TEST-001',
        'client_id' => 1,
        'contract_type_id' => 1,
        'title' => 'Complete Test Request',
        'description' => 'Complete test description with all fields',
        'status' => 'new',
        'request_date' => now()->format('Y-m-d'),
        'completion_date' => now()->addDays(7)->format('Y-m-d'),
        'assigned_to' => 1,
        'notes' => 'Test notes',
        'price' => 1500.00,
        'visits_included' => 3,
        'created_at' => now(),
        'updated_at' => now(),
    ];

    DB::table('maintenance_requests')->insert($data);

    $record = DB::table('maintenance_requests')->where('request_number', 'FULL-TEST-001')->first();
    expect($record)->not->toBeNull();
    expect($record->status)->toBe('new');
    expect($record->title)->toBe('Complete Test Request');
});
