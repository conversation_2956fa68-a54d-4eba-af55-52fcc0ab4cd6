<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Client;
use Livewire\Livewire;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Client\Pages\Auth\Register;

class ClientRegistrationFormTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up basic environment for testing
        config(['app.env' => 'testing']);
    }

    /** @test */
    public function registration_form_loads_without_validation_errors()
    {
        $component = Livewire::test(Register::class);
        
        $component->assertStatus(200);
        $component->assertSee(__('client/auth.labels.register_title'));
        $component->assertSee(__('client/auth.fields.name'));
        $component->assertSee(__('client/auth.fields.phone'));
    }

    /** @test */
    public function registration_form_validates_required_fields()
    {
        $component = Livewire::test(Register::class);
        
        // Try to submit empty form
        $component->call('authenticate');
        
        // Should show validation errors for required fields
        $component->assertHasErrors(['name', 'phone']);
    }

    /** @test */
    public function registration_form_accepts_valid_data()
    {
        $component = Livewire::test(Register::class);
        
        // Fill in valid registration data
        $component->set('data.name', 'Test Client');
        $component->set('data.phone', '966550000001');
        $component->set('data.email', '<EMAIL>');
        
        // Submit the form
        $component->call('authenticate');
        
        // Should not have validation errors for these fields
        $component->assertHasNoErrors(['name', 'phone', 'email']);
        
        // Should move to OTP verification step
        $component->assertSet('currentStep', 'otp_verification');
    }

    /** @test */
    public function registration_form_validates_phone_uniqueness()
    {
        // Create existing client
        Client::factory()->create(['phone' => '966550000001']);
        
        $component = Livewire::test(Register::class);
        
        // Try to register with existing phone
        $component->set('data.name', 'Test Client');
        $component->set('data.phone', '966550000001');
        
        $component->call('authenticate');
        
        // Should show phone uniqueness error
        $component->assertHasErrors(['phone']);
    }

    /** @test */
    public function registration_form_validates_email_uniqueness()
    {
        // Create existing client
        Client::factory()->create(['email' => '<EMAIL>']);
        
        $component = Livewire::test(Register::class);
        
        // Try to register with existing email
        $component->set('data.name', 'Test Client');
        $component->set('data.phone', '966550000001');
        $component->set('data.email', '<EMAIL>');
        
        $component->call('authenticate');
        
        // Should show email uniqueness error
        $component->assertHasErrors(['email']);
    }

    /** @test */
    public function registration_form_handles_step_transitions()
    {
        $component = Livewire::test(Register::class);
        
        // Should start at registration step
        $component->assertSet('currentStep', 'registration');
        
        // Fill in valid data and submit
        $component->set('data.name', 'Test Client');
        $component->set('data.phone', '966550000001');
        $component->call('authenticate');
        
        // Should move to OTP verification step
        $component->assertSet('currentStep', 'otp_verification');
        $component->assertSet('showMethods', true);
        
        // Should be able to go back
        $component->call('backToRegistration');
        $component->assertSet('currentStep', 'registration');
        $component->assertSet('showMethods', false);
    }

    /** @test */
    public function otp_verification_step_works_correctly()
    {
        $component = Livewire::test(Register::class);
        
        // Move to OTP step
        $component->set('data.name', 'Test Client');
        $component->set('data.phone', '966550000001');
        $component->call('authenticate');
        
        // Select OTP method
        $component->call('selectMethod', 'sms');
        $component->assertSet('selectedMethod', 'sms');
        $component->assertSet('showMethods', false);
        
        // Enter OTP (using development code)
        $component->set('data.otp', '1436');
        $component->call('authenticate');
        
        // Should create client and login
        $this->assertDatabaseHas('clients', [
            'name' => 'Test Client',
            'phone' => '966550000001',
        ]);
    }

    /** @test */
    public function form_data_persists_between_steps()
    {
        $component = Livewire::test(Register::class);
        
        // Fill in registration data
        $component->set('data.name', 'Test Client');
        $component->set('data.phone', '966550000001');
        $component->set('data.email', '<EMAIL>');
        $component->set('data.address', 'Test Address');
        
        // Move to OTP step
        $component->call('authenticate');
        
        // Data should be preserved
        $component->assertSet('data.name', 'Test Client');
        $component->assertSet('data.phone', '966550000001');
        $component->assertSet('data.email', '<EMAIL>');
        $component->assertSet('data.address', 'Test Address');
        
        // Go back to registration
        $component->call('backToRegistration');
        
        // Data should still be preserved
        $component->assertSet('data.name', 'Test Client');
        $component->assertSet('data.phone', '966550000001');
        $component->assertSet('data.email', '<EMAIL>');
        $component->assertSet('data.address', 'Test Address');
    }
}
