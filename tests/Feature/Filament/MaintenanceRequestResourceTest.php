<?php

declare(strict_types=1);

namespace Tests\Feature\Filament;

use App\Filament\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;
use App\Models\Client;
use App\Models\ContractType;
use App\Models\User;
use Tests\TenantTestCase;
use Livewire\Livewire;

/**
 * MaintenanceRequestResourceTest
 *
 * Feature tests for the MaintenanceRequestResource Filament resource.
 * Tests CRUD operations, form validation, table functionality, and business logic.
 *
 * @package Tests\Feature\Filament
 */
class MaintenanceRequestResourceTest extends TenantTestCase
{
    /**
     * Test that the maintenance request index page can be rendered.
     */
    public function test_can_render_index_page(): void
    {
        $this->get(MaintenanceRequestResource::getUrl('index'))
            ->assertSuccessful();
    }

    /**
     * Test that maintenance requests are listed on index page.
     */
    public function test_can_list_maintenance_requests(): void
    {
        $requests = MaintenanceRequest::factory()->count(3)->create();

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->assertCanSeeTableRecords($requests);
    }

    /**
     * Test that the create page can be rendered.
     */
    public function test_can_render_create_page(): void
    {
        $this->get(MaintenanceRequestResource::getUrl('create'))
            ->assertSuccessful();
    }

    /**
     * Test that a maintenance request can be created.
     */
    public function test_can_create_maintenance_request(): void
    {
        $client = Client::factory()->create();
        $contractType = ContractType::factory()->create();

        $newData = [
            'client_id' => $client->id,
            'contract_type_id' => $contractType->id,
            'title' => 'Test Maintenance Request',
            'description' => 'This is a test maintenance request description.',
            'request_date' => now()->format('Y-m-d'),
            'status' => MaintenanceRequest::STATUS_NEW,
        ];

        Livewire::test(MaintenanceRequestResource\Pages\CreateMaintenanceRequest::class)
            ->fillForm($newData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('maintenance_requests', [
            'client_id' => $client->id,
            'contract_type_id' => $contractType->id,
            'title' => 'Test Maintenance Request',
            'status' => MaintenanceRequest::STATUS_NEW,
        ]);
    }

    /**
     * Test that form validation works for required fields.
     */
    public function test_create_form_validation(): void
    {
        Livewire::test(MaintenanceRequestResource\Pages\CreateMaintenanceRequest::class)
            ->fillForm([
                'title' => '',
                'description' => '',
            ])
            ->call('create')
            ->assertHasFormErrors([
                'client_id' => 'required',
                'contract_type_id' => 'required',
                'title' => 'required',
                'description' => 'required',
            ]);
    }

    /**
     * Test that the edit page can be rendered.
     */
    public function test_can_render_edit_page(): void
    {
        $request = MaintenanceRequest::factory()->create();

        $this->get(MaintenanceRequestResource::getUrl('edit', ['record' => $request]))
            ->assertSuccessful();
    }

    /**
     * Test that a maintenance request can be updated.
     */
    public function test_can_update_maintenance_request(): void
    {
        $request = MaintenanceRequest::factory()->create();
        $technician = User::factory()->technician()->create();

        $newData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'status' => MaintenanceRequest::STATUS_ASSIGNED,
            'assigned_to' => $technician->id,
        ];

        Livewire::test(MaintenanceRequestResource\Pages\EditMaintenanceRequest::class, ['record' => $request->getRouteKey()])
            ->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('maintenance_requests', [
            'id' => $request->id,
            'title' => 'Updated Title',
            'status' => MaintenanceRequest::STATUS_ASSIGNED,
            'assigned_to' => $technician->id,
        ]);
    }

    /**
     * Test that the view page can be rendered.
     */
    public function test_can_render_view_page(): void
    {
        $request = MaintenanceRequest::factory()->create();

        $this->get(MaintenanceRequestResource::getUrl('view', ['record' => $request]))
            ->assertSuccessful();
    }

    /**
     * Test that a maintenance request can be deleted.
     */
    public function test_can_delete_maintenance_request(): void
    {
        $request = MaintenanceRequest::factory()->create();

        Livewire::test(MaintenanceRequestResource\Pages\EditMaintenanceRequest::class, ['record' => $request->getRouteKey()])
            ->callAction('delete');

        $this->assertModelMissing($request);
    }

    /**
     * Test table filtering by status.
     */
    public function test_can_filter_by_status(): void
    {
        $newRequest = MaintenanceRequest::factory()->new()->create();
        $completedRequest = MaintenanceRequest::factory()->completed()->create();
        $canceledRequest = MaintenanceRequest::factory()->canceled()->create();

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->filterTable('status', [MaintenanceRequest::STATUS_NEW])
            ->assertCanSeeTableRecords([$newRequest])
            ->assertCanNotSeeTableRecords([$completedRequest, $canceledRequest]);
    }

    /**
     * Test table filtering by client.
     */
    public function test_can_filter_by_client(): void
    {
        $client1 = Client::factory()->create();
        $client2 = Client::factory()->create();

        $request1 = MaintenanceRequest::factory()->forClient($client1)->create();
        $request2 = MaintenanceRequest::factory()->forClient($client2)->create();

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->filterTable('client_id', [$client1->id])
            ->assertCanSeeTableRecords([$request1])
            ->assertCanNotSeeTableRecords([$request2]);
    }

    /**
     * Test table sorting.
     */
    public function test_can_sort_table(): void
    {
        $requests = MaintenanceRequest::factory()->count(3)->create();

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->sortTable('created_at')
            ->assertCanSeeTableRecords($requests, inOrder: true);
    }

    /**
     * Test table search functionality.
     */
    public function test_can_search_table(): void
    {
        $request1 = MaintenanceRequest::factory()->create(['title' => 'Unique Search Term']);
        $request2 = MaintenanceRequest::factory()->create(['title' => 'Different Title']);

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->searchTable('Unique Search')
            ->assertCanSeeTableRecords([$request1])
            ->assertCanNotSeeTableRecords([$request2]);
    }

    /**
     * Test bulk action for assigning technician.
     */
    public function test_can_bulk_assign_technician(): void
    {
        $requests = MaintenanceRequest::factory()->new()->count(3)->create();
        $technician = User::factory()->technician()->create();

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->selectTableRecords($requests)
            ->callTableBulkAction('assign_technician', data: [
                'assigned_to' => $technician->id,
            ]);

        foreach ($requests as $request) {
            $this->assertDatabaseHas('maintenance_requests', [
                'id' => $request->id,
                'assigned_to' => $technician->id,
                'status' => MaintenanceRequest::STATUS_ASSIGNED,
            ]);
        }
    }

    /**
     * Test bulk action for updating status.
     */
    public function test_can_bulk_update_status(): void
    {
        $requests = MaintenanceRequest::factory()->new()->count(3)->create();

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->selectTableRecords($requests)
            ->callTableBulkAction('update_status', data: [
                'status' => MaintenanceRequest::STATUS_CANCELLED,
            ]);

        foreach ($requests as $request) {
            $this->assertDatabaseHas('maintenance_requests', [
                'id' => $request->id,
                'status' => MaintenanceRequest::STATUS_CANCELLED,
            ]);
        }
    }

    /**
     * Test that canceled status is properly handled.
     */
    public function test_canceled_status_is_properly_handled(): void
    {
        $request = MaintenanceRequest::factory()->create([
            'status' => MaintenanceRequest::STATUS_CANCELLED,
        ]);

        Livewire::test(MaintenanceRequestResource\Pages\ListMaintenanceRequests::class)
            ->assertCanSeeTableRecords([$request]);

        // Test that the status is displayed correctly
        $this->get(MaintenanceRequestResource::getUrl('view', ['record' => $request]))
            ->assertSee('ملغي'); // Arabic for "canceled"
    }

    /**
     * Test that priority field is not present in forms.
     */
    public function test_priority_field_is_not_present(): void
    {
        Livewire::test(MaintenanceRequestResource\Pages\CreateMaintenanceRequest::class)
            ->assertFormFieldDoesNotExist('priority');

        $request = MaintenanceRequest::factory()->create();

        Livewire::test(MaintenanceRequestResource\Pages\EditMaintenanceRequest::class, ['record' => $request->getRouteKey()])
            ->assertFormFieldDoesNotExist('priority');
    }
}
