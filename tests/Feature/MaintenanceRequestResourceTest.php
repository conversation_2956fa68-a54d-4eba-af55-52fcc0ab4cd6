<?php

declare(strict_types=1);

use App\Filament\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;

/**
 * MaintenanceRequest Resource Feature Tests
 *
 * Simple tests for the MaintenanceRequestResource Filament resource.
 * Tests basic functionality without complex database operations.
 */

test('MaintenanceRequestResource class exists and is properly configured', function () {
    expect(class_exists(MaintenanceRequestResource::class))->toBeTrue();
    expect(method_exists(MaintenanceRequestResource::class, 'getModel'))->toBeTrue();
    expect(MaintenanceRequestResource::getModel())->toBe(MaintenanceRequest::class);
});

test('MaintenanceRequestResource has required pages defined', function () {
    $pages = MaintenanceRequestResource::getPages();

    expect($pages)->toBeArray();
    expect($pages)->toHaveKey('index');
    expect($pages)->toHaveKey('create');
    expect($pages)->toHaveKey('edit');
    expect($pages)->toHaveKey('view');
});

test('MaintenanceRequestResource has navigation configuration', function () {
    expect(method_exists(MaintenanceRequestResource::class, 'getNavigationGroup'))->toBeTrue();
    expect(method_exists(MaintenanceRequestResource::class, 'getModelLabel'))->toBeTrue();
    expect(method_exists(MaintenanceRequestResource::class, 'getPluralModelLabel'))->toBeTrue();
});

test('MaintenanceRequestResource has navigation badge method', function () {
    expect(method_exists(MaintenanceRequestResource::class, 'getNavigationBadge'))->toBeTrue();
});

test('MaintenanceRequestResource URL generation works', function () {
    expect(method_exists(MaintenanceRequestResource::class, 'getUrl'))->toBeTrue();

    // Test that URL generation doesn't throw errors
    $indexUrl = MaintenanceRequestResource::getUrl('index');
    $createUrl = MaintenanceRequestResource::getUrl('create');

    expect($indexUrl)->toBeString();
    expect($createUrl)->toBeString();
    expect($indexUrl)->toContain('maintenance-requests');
    expect($createUrl)->toContain('maintenance-requests');
});

test('MaintenanceRequestResource form and table methods exist', function () {
    expect(method_exists(MaintenanceRequestResource::class, 'form'))->toBeTrue();
    expect(method_exists(MaintenanceRequestResource::class, 'table'))->toBeTrue();
});

test('MaintenanceRequestResource uses correct model', function () {
    $model = MaintenanceRequestResource::getModel();
    expect($model)->toBe(MaintenanceRequest::class);

    // Verify the model class exists
    expect(class_exists($model))->toBeTrue();
});

test('MaintenanceRequestResource has proper Filament resource structure', function () {
    // Test that the resource follows Filament conventions
    expect(is_subclass_of(MaintenanceRequestResource::class, \Filament\Resources\Resource::class))->toBeTrue();
});

test('MaintenanceRequestResource page classes exist', function () {
    $pages = MaintenanceRequestResource::getPages();

    foreach ($pages as $pageClass) {
        expect(class_exists($pageClass))->toBeTrue();
    }
});

test('MaintenanceRequestResource can generate URLs without errors', function () {
    // Test URL generation for all pages
    $pages = ['index', 'create'];

    foreach ($pages as $page) {
        $url = MaintenanceRequestResource::getUrl($page);
        expect($url)->toBeString();
        expect($url)->not->toBeEmpty();
    }
});

test('MaintenanceRequestResource has correct resource configuration', function () {
    // Test basic resource configuration
    expect(MaintenanceRequestResource::getModel())->toBe(MaintenanceRequest::class);
    expect(MaintenanceRequestResource::getModelLabel())->toBeString();
    expect(MaintenanceRequestResource::getPluralModelLabel())->toBeString();
});

test('MaintenanceRequestResource navigation badge returns string or null', function () {
    $badge = MaintenanceRequestResource::getNavigationBadge();
    expect($badge === null || is_string($badge))->toBeTrue();
});
