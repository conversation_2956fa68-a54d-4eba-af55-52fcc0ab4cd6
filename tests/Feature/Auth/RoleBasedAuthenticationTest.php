<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Filament\Facades\Filament;

class RoleBasedAuthenticationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test environment
        $this->artisan('migrate');
    }

    /** @test */
    public function admin_user_is_redirected_to_admin_panel()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'role' => 'admin',
            'phone' => '966550000001',
        ]);

        // Simulate login to admin panel
        $response = $this->actingAs($adminUser)
            ->get('/admin');

        $response->assertStatus(200);
    }

    /** @test */
    public function technician_user_is_redirected_to_technician_panel()
    {
        // Create a technician user
        $technicianUser = User::factory()->create([
            'role' => 'technician',
            'phone' => '966550000002',
        ]);

        // Simulate login to technician panel
        $response = $this->actingAs($technicianUser)
            ->get('/technician');

        $response->assertStatus(200);
    }

    /** @test */
    public function admin_user_cannot_access_technician_panel()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'role' => 'admin',
            'phone' => '966550000003',
        ]);

        // Try to access technician panel as admin
        $response = $this->actingAs($adminUser)
            ->get('/technician');

        // Should be redirected or forbidden
        $this->assertTrue(
            $response->isRedirection() || $response->status() === 403
        );
    }

    /** @test */
    public function technician_user_cannot_access_admin_panel()
    {
        // Create a technician user
        $technicianUser = User::factory()->create([
            'role' => 'technician',
            'phone' => '966550000004',
        ]);

        // Try to access admin panel as technician
        $response = $this->actingAs($technicianUser)
            ->get('/admin');

        // Should be redirected or forbidden
        $this->assertTrue(
            $response->isRedirection() || $response->status() === 403
        );
    }

    /** @test */
    public function manager_user_can_access_admin_panel()
    {
        // Create a manager user
        $managerUser = User::factory()->create([
            'role' => 'manager',
            'phone' => '966550000005',
        ]);

        // Simulate login to admin panel
        $response = $this->actingAs($managerUser)
            ->get('/admin');

        $response->assertStatus(200);
    }

    /** @test */
    public function user_with_invalid_role_cannot_access_panels()
    {
        // Create a user with invalid role
        $invalidUser = User::factory()->create([
            'role' => 'invalid_role',
            'phone' => '966550000006',
        ]);

        // Try to access admin panel
        $adminResponse = $this->actingAs($invalidUser)
            ->get('/admin');

        // Try to access technician panel
        $technicianResponse = $this->actingAs($invalidUser)
            ->get('/technician');

        // Both should be redirected or forbidden
        $this->assertTrue(
            $adminResponse->isRedirection() || $adminResponse->status() === 403
        );
        $this->assertTrue(
            $technicianResponse->isRedirection() || $technicianResponse->status() === 403
        );
    }
}
