<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Client;
use App\Models\UserOTP;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ClientRegistrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize tenancy for testing
        $this->initializeTenancy();
    }

    /** @test */
    public function client_can_access_registration_page()
    {
        $response = $this->get('/register');
        
        $response->assertStatus(200);
        $response->assertSee(__('client/auth.labels.register_title'));
    }

    /** @test */
    public function client_registration_requires_valid_data()
    {
        $response = $this->post('/register', []);
        
        $response->assertSessionHasErrors(['name', 'phone']);
    }

    /** @test */
    public function client_registration_validates_phone_uniqueness()
    {
        // Create existing client
        $existingClient = Client::factory()->create([
            'phone' => '************'
        ]);

        $response = $this->post('/register', [
            'name' => 'Test Client',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        $response->assertSessionHasErrors(['phone']);
    }

    /** @test */
    public function client_registration_validates_email_uniqueness()
    {
        // Create existing client
        $existingClient = Client::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $response = $this->post('/register', [
            'name' => 'Test Client',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function client_can_register_with_valid_data()
    {
        $registrationData = [
            'name' => 'Test Client',
            'phone' => '************',
            'email' => '<EMAIL>',
            'national_id' => '**********',
            'address' => 'Test Address',
        ];

        // Mock OTP verification in test environment
        config(['app.env' => 'testing']);

        $response = $this->post('/register', $registrationData);

        // Should proceed to OTP verification step
        $response->assertStatus(200);
        
        // Verify OTP was created
        $this->assertDatabaseHas('user_otp', [
            'auth_type' => 'App\\Models\\Client',
        ]);
    }

    /** @test */
    public function client_registration_creates_account_after_otp_verification()
    {
        $registrationData = [
            'name' => 'Test Client',
            'phone' => '************',
            'email' => '<EMAIL>',
            'national_id' => '**********',
            'address' => 'Test Address',
        ];

        // In test environment, use development OTP
        config(['app.env' => 'local']);

        // First step: Submit registration data
        $this->post('/register', $registrationData);

        // Second step: Verify OTP (using development code 1436)
        $response = $this->post('/register', [
            'otp' => '1436'
        ]);

        // Should create client and login
        $this->assertDatabaseHas('clients', [
            'name' => 'Test Client',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        // Should be authenticated
        $this->assertAuthenticated('client');
    }

    /** @test */
    public function client_registration_handles_invalid_otp()
    {
        $registrationData = [
            'name' => 'Test Client',
            'phone' => '************',
            'email' => '<EMAIL>',
        ];

        // Submit registration data first
        $this->post('/register', $registrationData);

        // Try with invalid OTP
        $response = $this->post('/register', [
            'otp' => '0000'
        ]);

        $response->assertSessionHasErrors(['otp']);
        
        // Should not create client
        $this->assertDatabaseMissing('clients', [
            'phone' => '************',
        ]);
    }

    /** @test */
    public function registration_page_has_proper_arabic_translations()
    {
        app()->setLocale('ar');
        
        $response = $this->get('/register');
        
        $response->assertStatus(200);
        $response->assertSee('إنشاء حساب عميل جديد');
        $response->assertSee('المعلومات الشخصية');
        $response->assertSee('الاسم الكامل');
        $response->assertSee('رقم الجوال');
    }

    /** @test */
    public function registration_page_supports_rtl_layout()
    {
        app()->setLocale('ar');
        
        $response = $this->get('/register');
        
        $response->assertStatus(200);
        // Check for RTL support in CSS
        $response->assertSee('[dir="rtl"]');
    }

    /**
     * Initialize tenancy for testing
     */
    protected function initializeTenancy()
    {
        // This would be implemented based on your tenancy setup
        // For now, we'll assume single-tenant testing
    }
}
