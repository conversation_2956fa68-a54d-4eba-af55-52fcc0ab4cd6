<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Filament\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;
use App\Models\ContractType;
use App\Models\User;
use Tests\TenantTestCase;
use Illuminate\Support\Facades\Cache;

/**
 * MaintenanceRequestCacheTest
 *
 * Tests for caching functionality in the MaintenanceRequestResource
 * and related components.
 *
 * @package Tests\Feature
 */
class MaintenanceRequestCacheTest extends TenantTestCase
{
    /**
     * Test that navigation badge count is cached.
     */
    public function test_navigation_badge_count_is_cached(): void
    {
        // Create some pending requests
        MaintenanceRequest::factory()->new()->count(2)->create();
        MaintenanceRequest::factory()->assigned()->count(3)->create();

        $cacheKey = 'maintenance_requests_pending_count_' . $this->user->id;

        // First call should cache the result
        $badge = MaintenanceRequestResource::getNavigationBadge();
        $this->assertEquals('5', $badge);

        // Verify it's cached
        $this->assertTrue(Cache::has($cacheKey));
        $this->assertEquals('5', Cache::get($cacheKey));
    }

    /**
     * Test that contract type options are cached.
     */
    public function test_contract_type_options_are_cached(): void
    {
        $tenantId = tenant()->id ?? 'default';
        $cacheKey = "contract_types_active_{$tenantId}";

        // Create contract types
        ContractType::factory()->count(3)->create();

        // Clear any existing cache
        Cache::forget($cacheKey);

        // This would be called when loading the form
        $options = Cache::remember($cacheKey, now()->addMinutes(15), function () {
            return ContractType::where('is_active', true)
                ->orderBy('display_order')
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();
        });

        $this->assertCount(3, $options);
        $this->assertTrue(Cache::has($cacheKey));
    }

    /**
     * Test that technician options are cached.
     */
    public function test_technician_options_are_cached(): void
    {
        $tenantId = tenant()->id ?? 'default';
        $cacheKey = "technicians_active_{$tenantId}";

        // Create technicians
        User::factory()->technician()->count(3)->create();

        // Clear any existing cache
        Cache::forget($cacheKey);

        // This would be called when loading the form
        $options = Cache::remember($cacheKey, now()->addMinutes(15), function () {
            return User::where('role', 'technician')
                ->where('is_active', true)
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();
        });

        $this->assertCount(3, $options);
        $this->assertTrue(Cache::has($cacheKey));
    }

    /**
     * Test that tab counts are cached.
     */
    public function test_tab_counts_are_cached(): void
    {
        // Create requests with different statuses
        MaintenanceRequest::factory()->new()->count(2)->create();
        MaintenanceRequest::factory()->assigned()->count(3)->create();
        MaintenanceRequest::factory()->completed()->count(1)->create();
        MaintenanceRequest::factory()->canceled()->count(1)->create();

        $cacheKey = 'maintenance_requests_tabs_counts_' . $this->user->id;

        // Clear any existing cache
        Cache::forget($cacheKey);

        // Simulate the tab counts calculation
        $counts = Cache::remember($cacheKey, now()->addMinutes(5), function () {
            return [
                'all' => MaintenanceRequest::count(),
                'new' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_NEW)->count(),
                'assigned' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_ASSIGNED)->count(),
                'in_progress' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_IN_PROGRESS)->count(),
                'completed' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_COMPLETED)->count(),
                'canceled' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_CANCELLED)->count(),
                'overdue' => MaintenanceRequest::where('completion_date', '<', now())
                    ->whereNotIn('status', [MaintenanceRequest::STATUS_COMPLETED, MaintenanceRequest::STATUS_CANCELLED])
                    ->count(),
            ];
        });

        $this->assertEquals(7, $counts['all']);
        $this->assertEquals(2, $counts['new']);
        $this->assertEquals(3, $counts['assigned']);
        $this->assertEquals(1, $counts['completed']);
        $this->assertEquals(1, $counts['canceled']);
        $this->assertTrue(Cache::has($cacheKey));
    }

    /**
     * Test cache invalidation when creating new request.
     */
    public function test_cache_invalidation_on_create(): void
    {
        $cacheKey = 'maintenance_requests_pending_count_' . $this->user->id;

        // Set initial cache
        Cache::put($cacheKey, '5', now()->addMinutes(5));
        $this->assertEquals('5', Cache::get($cacheKey));

        // Create new request (in real implementation, this would trigger cache clearing)
        MaintenanceRequest::factory()->new()->create();

        // Simulate cache clearing that would happen in the real implementation
        Cache::forget($cacheKey);

        $this->assertNull(Cache::get($cacheKey));
    }

    /**
     * Test cache invalidation when updating request status.
     */
    public function test_cache_invalidation_on_status_update(): void
    {
        $request = MaintenanceRequest::factory()->new()->create();

        $cacheKeys = [
            'maintenance_requests_pending_count_' . $this->user->id,
            'maintenance_requests_tabs_counts_' . $this->user->id,
        ];

        // Set cache values
        foreach ($cacheKeys as $key) {
            Cache::put($key, 'test_value', now()->addMinutes(5));
            $this->assertEquals('test_value', Cache::get($key));
        }

        // Update request status
        $request->update(['status' => MaintenanceRequest::STATUS_COMPLETED]);

        // Simulate cache clearing that would happen in the real implementation
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        foreach ($cacheKeys as $key) {
            $this->assertNull(Cache::get($key));
        }
    }

    /**
     * Test that cache keys are tenant-specific.
     */
    public function test_cache_keys_are_tenant_specific(): void
    {
        $tenant1 = $this->getCurrentTenant();

        // Set cache in first tenant
        $cacheKey1 = "contract_types_active_{$tenant1->id}";
        Cache::put($cacheKey1, ['tenant1_data'], now()->addMinutes(5));

        // Switch to second tenant
        $tenant2 = $this->createAndSwitchTenant();

        // Set cache in second tenant
        $cacheKey2 = "contract_types_active_{$tenant2->id}";
        Cache::put($cacheKey2, ['tenant2_data'], now()->addMinutes(5));

        // Verify tenant isolation
        $this->assertEquals(['tenant2_data'], Cache::get($cacheKey2));
        $this->assertEquals(['tenant1_data'], Cache::get($cacheKey1));
        $this->assertNotEquals($cacheKey1, $cacheKey2);
    }

    /**
     * Test cache TTL (Time To Live) settings.
     */
    public function test_cache_ttl_settings(): void
    {
        $shortCacheKey = 'test_short_cache';
        $longCacheKey = 'test_long_cache';

        // Set short cache (5 minutes)
        Cache::put($shortCacheKey, 'short_value', now()->addMinutes(5));

        // Set long cache (15 minutes)
        Cache::put($longCacheKey, 'long_value', now()->addMinutes(15));

        $this->assertTrue(Cache::has($shortCacheKey));
        $this->assertTrue(Cache::has($longCacheKey));

        // Simulate time passing (in real tests, you might use Carbon::setTestNow())
        // For now, we'll just verify the cache exists
        $this->assertEquals('short_value', Cache::get($shortCacheKey));
        $this->assertEquals('long_value', Cache::get($longCacheKey));
    }

    /**
     * Test cache performance with large datasets.
     */
    public function test_cache_performance_with_large_datasets(): void
    {
        // Create a larger dataset
        MaintenanceRequest::factory()->count(100)->create();
        ContractType::factory()->count(20)->create();
        User::factory()->technician()->count(50)->create();

        $tenantId = tenant()->id ?? 'default';

        // Test contract types caching
        $start = microtime(true);
        $contractTypes = Cache::remember("contract_types_active_{$tenantId}", now()->addMinutes(15), function () {
            return ContractType::where('is_active', true)
                ->orderBy('display_order')
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();
        });
        $firstCallTime = microtime(true) - $start;

        // Second call should be faster (cached)
        $start = microtime(true);
        $cachedContractTypes = Cache::get("contract_types_active_{$tenantId}");
        $secondCallTime = microtime(true) - $start;

        $this->assertEquals($contractTypes, $cachedContractTypes);
        $this->assertLessThan($firstCallTime, $secondCallTime);
    }

    /**
     * Test cache clearing methods.
     */
    public function test_cache_clearing_methods(): void
    {
        $tenantId = tenant()->id ?? 'default';

        // Set up various caches
        $cacheKeys = [
            "contract_types_active_{$tenantId}",
            "technicians_active_{$tenantId}",
            'last_maintenance_request_id',
            'maintenance_requests_pending_count_' . $this->user->id,
        ];

        foreach ($cacheKeys as $key) {
            Cache::put($key, 'test_value', now()->addMinutes(5));
            $this->assertTrue(Cache::has($key));
        }

        // Simulate calling the clearAllCaches method
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Verify all caches are cleared
        foreach ($cacheKeys as $key) {
            $this->assertFalse(Cache::has($key));
        }
    }

    /**
     * Test that cache works correctly with different user contexts.
     */
    public function test_cache_with_different_user_contexts(): void
    {
        $user1 = $this->user;
        $user2 = User::factory()->admin()->create();

        // Set cache for user 1
        $cacheKey1 = 'maintenance_requests_pending_count_' . $user1->id;
        Cache::put($cacheKey1, '5', now()->addMinutes(5));

        // Set cache for user 2
        $cacheKey2 = 'maintenance_requests_pending_count_' . $user2->id;
        Cache::put($cacheKey2, '10', now()->addMinutes(5));

        // Verify user-specific caching
        $this->assertEquals('5', Cache::get($cacheKey1));
        $this->assertEquals('10', Cache::get($cacheKey2));
        $this->assertNotEquals($cacheKey1, $cacheKey2);
    }
}
