<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\MaintenanceRequest;
use App\Models\User;
use Tests\TenantTestCase;
use Illuminate\Support\Facades\Cache;

/**
 * MaintenanceRequestStatusTest
 *
 * Tests for maintenance request status functionality, business logic,
 * and the standardized "canceled" status implementation.
 *
 * @package Tests\Feature
 */
class MaintenanceRequestStatusTest extends TenantTestCase
{
    /**
     * Test that new maintenance requests have correct default status.
     */
    public function test_new_maintenance_request_has_default_status(): void
    {
        $request = MaintenanceRequest::factory()->create();

        // Should default to 'new' status if not specified
        $this->assertContains($request->status, [
            MaintenanceRequest::STATUS_NEW,
            MaintenanceRequest::STATUS_ASSIGNED,
            MaintenanceRequest::STATUS_IN_PROGRESS,
            MaintenanceRequest::STATUS_COMPLETED,
            MaintenanceRequest::STATUS_CANCELLED,
        ]);
    }

    /**
     * Test status transition from new to assigned.
     */
    public function test_status_transition_new_to_assigned(): void
    {
        $request = MaintenanceRequest::factory()->new()->create();
        $technician = User::factory()->technician()->create();

        $request->update([
            'status' => MaintenanceRequest::STATUS_ASSIGNED,
            'assigned_to' => $technician->id,
        ]);

        $this->assertEquals(MaintenanceRequest::STATUS_ASSIGNED, $request->status);
        $this->assertEquals($technician->id, $request->assigned_to);
    }

    /**
     * Test status transition from assigned to in progress.
     */
    public function test_status_transition_assigned_to_in_progress(): void
    {
        $request = MaintenanceRequest::factory()->assigned()->create();

        $request->update(['status' => MaintenanceRequest::STATUS_IN_PROGRESS]);

        $this->assertEquals(MaintenanceRequest::STATUS_IN_PROGRESS, $request->status);
    }

    /**
     * Test status transition to completed sets completion date.
     */
    public function test_status_transition_to_completed_sets_date(): void
    {
        $request = MaintenanceRequest::factory()->inProgress()->create();

        $request->update([
            'status' => MaintenanceRequest::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);

        $this->assertEquals(MaintenanceRequest::STATUS_COMPLETED, $request->status);
        $this->assertNotNull($request->completed_at);
    }

    /**
     * Test that requests can be canceled from any status.
     */
    public function test_can_cancel_from_any_status(): void
    {
        $statuses = [
            MaintenanceRequest::STATUS_NEW,
            MaintenanceRequest::STATUS_ASSIGNED,
            MaintenanceRequest::STATUS_IN_PROGRESS,
        ];

        foreach ($statuses as $status) {
            $request = MaintenanceRequest::factory()->create(['status' => $status]);

            $request->update(['status' => MaintenanceRequest::STATUS_CANCELLED]);

            $this->assertEquals(MaintenanceRequest::STATUS_CANCELLED, $request->status);
        }
    }

    /**
     * Test that canceled status uses correct spelling.
     */
    public function test_canceled_status_uses_correct_spelling(): void
    {
        $request = MaintenanceRequest::factory()->canceled()->create();

        $this->assertEquals('canceled', $request->status);
        $this->assertEquals(MaintenanceRequest::STATUS_CANCELLED, $request->status);

        // Verify it's stored correctly in database
        $this->assertDatabaseHas('maintenance_requests', [
            'id' => $request->id,
            'status' => 'canceled',
        ]);

        // Verify old spelling is not used
        $this->assertDatabaseMissing('maintenance_requests', [
            'id' => $request->id,
            'status' => 'canceled',
        ]);
    }

    /**
     * Test status filtering in queries.
     */
    public function test_status_filtering_in_queries(): void
    {
        // Create requests with different statuses
        $newRequest = MaintenanceRequest::factory()->new()->create();
        $assignedRequest = MaintenanceRequest::factory()->assigned()->create();
        $completedRequest = MaintenanceRequest::factory()->completed()->create();
        $canceledRequest = MaintenanceRequest::factory()->canceled()->create();

        // Test filtering by new status
        $newRequests = MaintenanceRequest::where('status', MaintenanceRequest::STATUS_NEW)->get();
        $this->assertCount(1, $newRequests);
        $this->assertEquals($newRequest->id, $newRequests->first()->id);

        // Test filtering by canceled status
        $canceledRequests = MaintenanceRequest::where('status', MaintenanceRequest::STATUS_CANCELLED)->get();
        $this->assertCount(1, $canceledRequests);
        $this->assertEquals($canceledRequest->id, $canceledRequests->first()->id);

        // Test filtering pending requests (not completed or canceled)
        $pendingRequests = MaintenanceRequest::whereNotIn('status', [
            MaintenanceRequest::STATUS_COMPLETED,
            MaintenanceRequest::STATUS_CANCELLED,
        ])->get();
        $this->assertCount(2, $pendingRequests);
    }

    /**
     * Test navigation badge count calculation.
     */
    public function test_navigation_badge_count(): void
    {
        // Create requests with different statuses
        MaintenanceRequest::factory()->new()->count(2)->create();
        MaintenanceRequest::factory()->assigned()->count(3)->create();
        MaintenanceRequest::factory()->completed()->count(1)->create();
        MaintenanceRequest::factory()->canceled()->count(1)->create();

        // Calculate pending count (new + assigned)
        $pendingCount = MaintenanceRequest::where('status', MaintenanceRequest::STATUS_NEW)
            ->orWhere('status', MaintenanceRequest::STATUS_ASSIGNED)
            ->count();

        $this->assertEquals(5, $pendingCount);
    }

    /**
     * Test overdue requests calculation.
     */
    public function test_overdue_requests_calculation(): void
    {
        // Create overdue requests
        MaintenanceRequest::factory()->overdue()->count(2)->create();

        // Create non-overdue requests
        MaintenanceRequest::factory()->create([
            'completion_date' => now()->addDays(5),
            'status' => MaintenanceRequest::STATUS_NEW,
        ]);

        // Create completed request (should not be overdue)
        MaintenanceRequest::factory()->create([
            'completion_date' => now()->subDays(5),
            'status' => MaintenanceRequest::STATUS_COMPLETED,
        ]);

        // Calculate overdue count
        $overdueCount = MaintenanceRequest::where('completion_date', '<', now())
            ->whereNotIn('status', [
                MaintenanceRequest::STATUS_COMPLETED,
                MaintenanceRequest::STATUS_CANCELLED,
            ])
            ->count();

        $this->assertEquals(2, $overdueCount);
    }

    /**
     * Test cache invalidation on status change.
     */
    public function test_cache_invalidation_on_status_change(): void
    {
        // Set up cache
        $cacheKey = 'maintenance_requests_pending_count_' . $this->user->id;
        Cache::put($cacheKey, 10, now()->addMinutes(5));

        $this->assertEquals(10, Cache::get($cacheKey));

        // Create a new request (should invalidate cache in real implementation)
        MaintenanceRequest::factory()->new()->create();

        // In a real implementation, the cache would be cleared
        // For testing, we'll manually clear it to simulate the behavior
        Cache::forget($cacheKey);

        $this->assertNull(Cache::get($cacheKey));
    }

    /**
     * Test status constants are immutable.
     */
    public function test_status_constants_are_immutable(): void
    {
        $this->assertEquals('new', MaintenanceRequest::STATUS_NEW);
        $this->assertEquals('assigned', MaintenanceRequest::STATUS_ASSIGNED);
        $this->assertEquals('in_progress', MaintenanceRequest::STATUS_IN_PROGRESS);
        $this->assertEquals('completed', MaintenanceRequest::STATUS_COMPLETED);
        $this->assertEquals('canceled', MaintenanceRequest::STATUS_CANCELLED);

        // These should never change
        $this->assertNotEquals('canceled', MaintenanceRequest::STATUS_CANCELLED);
    }

    /**
     * Test bulk status updates.
     */
    public function test_bulk_status_updates(): void
    {
        $requests = MaintenanceRequest::factory()->new()->count(3)->create();

        // Bulk update to canceled
        MaintenanceRequest::whereIn('id', $requests->pluck('id'))
            ->update(['status' => MaintenanceRequest::STATUS_CANCELLED]);

        foreach ($requests as $request) {
            $request->refresh();
            $this->assertEquals(MaintenanceRequest::STATUS_CANCELLED, $request->status);
        }
    }

    /**
     * Test that status options method returns correct values.
     */
    public function test_status_options_method(): void
    {
        $options = MaintenanceRequest::getStatusOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey(MaintenanceRequest::STATUS_CANCELLED, $options);
        $this->assertEquals('Canceled', $options[MaintenanceRequest::STATUS_CANCELLED]);

        // Ensure old spelling is not present
        $this->assertArrayNotHasKey('canceled', $options);
    }

    /**
     * Test status validation in different contexts.
     */
    public function test_status_validation(): void
    {
        $validStatuses = [
            MaintenanceRequest::STATUS_NEW,
            MaintenanceRequest::STATUS_ASSIGNED,
            MaintenanceRequest::STATUS_IN_PROGRESS,
            MaintenanceRequest::STATUS_COMPLETED,
            MaintenanceRequest::STATUS_CANCELLED,
        ];

        foreach ($validStatuses as $status) {
            $request = MaintenanceRequest::factory()->create(['status' => $status]);
            $this->assertEquals($status, $request->status);
        }
    }
}
