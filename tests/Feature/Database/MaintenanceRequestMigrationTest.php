<?php

declare(strict_types=1);

namespace Tests\Feature\Database;

use App\Models\MaintenanceRequest;
use Tests\TenantTestCase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

/**
 * MaintenanceRequestMigrationTest
 *
 * Tests for the maintenance request database migration that removes priority
 * and standardizes the "canceled" status spelling.
 *
 * @package Tests\Feature\Database
 */
class MaintenanceRequestMigrationTest extends TenantTestCase
{
    /**
     * Test that the maintenance_requests table exists and has correct structure.
     */
    public function test_maintenance_requests_table_structure(): void
    {
        $this->assertTrue(Schema::hasTable('maintenance_requests'));

        // Test that required columns exist
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'id'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'request_number'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'client_id'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'contract_type_id'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'title'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'description'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'status'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'request_date'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'completion_date'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'assigned_to'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'notes'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'created_at'));
        $this->assertTrue(Schema::hasColumn('maintenance_requests', 'updated_at'));
    }

    /**
     * Test that priority column does not exist.
     */
    public function test_priority_column_does_not_exist(): void
    {
        $this->assertFalse(Schema::hasColumn('maintenance_requests', 'priority'));
    }

    /**
     * Test that canceled status works correctly.
     */
    public function test_canceled_status_works(): void
    {
        $request = MaintenanceRequest::factory()->create([
            'status' => MaintenanceRequest::STATUS_CANCELLED,
        ]);

        $this->assertEquals(MaintenanceRequest::STATUS_CANCELLED, $request->status);
        $this->assertEquals('canceled', $request->status);

        // Test that it's saved correctly in database
        $this->assertDatabaseHas('maintenance_requests', [
            'id' => $request->id,
            'status' => 'canceled',
        ]);
    }

    /**
     * Test that old "canceled" status is converted to "canceled".
     */
    public function test_canceled_status_conversion(): void
    {
        // Manually insert a record with old "canceled" status
        DB::table('maintenance_requests')->insert([
            'request_number' => 'REQ-TEST-001',
            'client_id' => \App\Models\Client::factory()->create()->id,
            'contract_type_id' => \App\Models\ContractType::factory()->create()->id,
            'title' => 'Test Request',
            'description' => 'Test Description',
            'status' => 'canceled', // Old spelling
            'request_date' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Run the migration logic (update canceled to canceled)
        DB::table('maintenance_requests')
            ->where('status', 'canceled')
            ->update(['status' => 'canceled']);

        // Verify the conversion
        $this->assertDatabaseMissing('maintenance_requests', [
            'status' => 'canceled',
        ]);

        $this->assertDatabaseHas('maintenance_requests', [
            'request_number' => 'REQ-TEST-001',
            'status' => 'canceled',
        ]);
    }

    /**
     * Test that all valid statuses can be stored.
     */
    public function test_all_valid_statuses_can_be_stored(): void
    {
        $statuses = [
            MaintenanceRequest::STATUS_NEW,
            MaintenanceRequest::STATUS_ASSIGNED,
            MaintenanceRequest::STATUS_IN_PROGRESS,
            MaintenanceRequest::STATUS_COMPLETED,
            MaintenanceRequest::STATUS_CANCELLED,
        ];

        foreach ($statuses as $status) {
            $request = MaintenanceRequest::factory()->create(['status' => $status]);

            $this->assertEquals($status, $request->status);
            $this->assertDatabaseHas('maintenance_requests', [
                'id' => $request->id,
                'status' => $status,
            ]);
        }
    }

    /**
     * Test that the migration preserves existing data.
     */
    public function test_migration_preserves_existing_data(): void
    {
        // Create test data
        $originalData = [
            'request_number' => 'REQ-PRESERVE-001',
            'title' => 'Original Title',
            'description' => 'Original Description',
            'status' => MaintenanceRequest::STATUS_NEW,
        ];

        $request = MaintenanceRequest::factory()->create($originalData);

        // Verify data is preserved
        $this->assertDatabaseHas('maintenance_requests', [
            'id' => $request->id,
            'request_number' => 'REQ-PRESERVE-001',
            'title' => 'Original Title',
            'description' => 'Original Description',
            'status' => MaintenanceRequest::STATUS_NEW,
        ]);
    }

    /**
     * Test that foreign key relationships work correctly.
     */
    public function test_foreign_key_relationships(): void
    {
        $client = \App\Models\Client::factory()->create();
        $contractType = \App\Models\ContractType::factory()->create();
        $technician = \App\Models\User::factory()->technician()->create();

        $request = MaintenanceRequest::factory()->create([
            'client_id' => $client->id,
            'contract_type_id' => $contractType->id,
            'assigned_to' => $technician->id,
        ]);

        // Test relationships work
        $this->assertEquals($client->id, $request->client->id);
        $this->assertEquals($contractType->id, $request->contractType->id);
        $this->assertEquals($technician->id, $request->assignedTechnician->id);
    }

    /**
     * Test that date columns are properly cast.
     */
    public function test_date_columns_casting(): void
    {
        $request = MaintenanceRequest::factory()->create([
            'request_date' => '2024-01-15',
            'completion_date' => '2024-02-15',
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $request->request_date);
        $this->assertInstanceOf(\Carbon\Carbon::class, $request->completion_date);
    }

    /**
     * Test that nullable columns work correctly.
     */
    public function test_nullable_columns(): void
    {
        $request = MaintenanceRequest::factory()->create([
            'assigned_to' => null,
            'completion_date' => null,
            'notes' => null,
            'price' => null,
            'contract_id' => null,
        ]);

        $this->assertNull($request->assigned_to);
        $this->assertNull($request->completion_date);
        $this->assertNull($request->notes);
        $this->assertNull($request->price);
        $this->assertNull($request->contract_id);
    }

    /**
     * Test that the table supports tenant isolation.
     */
    public function test_tenant_isolation(): void
    {
        // Create request in current tenant
        $request1 = MaintenanceRequest::factory()->create(['title' => 'Tenant 1 Request']);

        // Switch to different tenant
        $tenant2 = $this->createAndSwitchTenant();

        // Create request in second tenant
        $request2 = MaintenanceRequest::factory()->create(['title' => 'Tenant 2 Request']);

        // Verify isolation - should only see request from current tenant
        $this->assertCount(1, MaintenanceRequest::all());
        $this->assertEquals('Tenant 2 Request', MaintenanceRequest::first()->title);

        // Switch back to first tenant
        $this->switchTenant($this->tenant);

        // Should only see first tenant's request
        $this->assertCount(1, MaintenanceRequest::all());
        $this->assertEquals('Tenant 1 Request', MaintenanceRequest::first()->title);
    }
}
