<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TenantTestCase;
use Illuminate\Support\Facades\Artisan;

/**
 * MaintenanceRequestTestSuite
 *
 * Test suite runner for all maintenance request related tests.
 * Provides utilities for running the complete test suite.
 *
 * @package Tests\Feature
 */
class MaintenanceRequestTestSuite extends TenantTestCase
{
    /**
     * Test that all maintenance request tests can run successfully.
     * This is a meta-test to ensure the test suite is properly configured.
     */
    public function test_maintenance_request_test_suite_setup(): void
    {
        // Verify that the tenant is properly initialized
        $this->assertNotNull(tenant());
        $this->assertNotNull($this->user);
        $this->assertTrue($this->user->exists);

        // Verify that the database is properly set up
        $this->assertTrue(\Schema::hasTable('maintenance_requests'));
        $this->assertTrue(\Schema::hasTable('clients'));
        $this->assertTrue(\Schema::hasTable('contract_types'));
        $this->assertTrue(\Schema::hasTable('users'));

        // Verify that factories work
        $request = \App\Models\MaintenanceRequest::factory()->create();
        $this->assertNotNull($request);
        $this->assertTrue($request->exists);

        // Verify that the resource is accessible
        $this->assertNotNull(\App\Filament\Resources\MaintenanceRequestResource::class);
    }

    /**
     * Test that all required models and relationships are working.
     */
    public function test_all_required_models_are_working(): void
    {
        // Test MaintenanceRequest model
        $request = \App\Models\MaintenanceRequest::factory()->create();
        $this->assertInstanceOf(\App\Models\MaintenanceRequest::class, $request);

        // Test Client model
        $client = \App\Models\Client::factory()->create();
        $this->assertInstanceOf(\App\Models\Client::class, $client);

        // Test ContractType model
        $contractType = \App\Models\ContractType::factory()->create();
        $this->assertInstanceOf(\App\Models\ContractType::class, $contractType);

        // Test User model
        $user = \App\Models\User::factory()->technician()->create();
        $this->assertInstanceOf(\App\Models\User::class, $user);
        $this->assertEquals('technician', $user->role);

        // Test relationships
        $requestWithRelations = \App\Models\MaintenanceRequest::factory()->create([
            'client_id' => $client->id,
            'contract_type_id' => $contractType->id,
            'assigned_to' => $user->id,
        ]);

        $this->assertEquals($client->id, $requestWithRelations->client->id);
        $this->assertEquals($contractType->id, $requestWithRelations->contractType->id);
        $this->assertEquals($user->id, $requestWithRelations->assignedTechnician->id);
    }

    /**
     * Test that all status constants are properly defined and working.
     */
    public function test_all_status_constants_are_working(): void
    {
        $statuses = [
            \App\Models\MaintenanceRequest::STATUS_NEW,
            \App\Models\MaintenanceRequest::STATUS_ASSIGNED,
            \App\Models\MaintenanceRequest::STATUS_IN_PROGRESS,
            \App\Models\MaintenanceRequest::STATUS_COMPLETED,
            \App\Models\MaintenanceRequest::STATUS_CANCELLED,
        ];

        foreach ($statuses as $status) {
            $request = \App\Models\MaintenanceRequest::factory()->create(['status' => $status]);
            $this->assertEquals($status, $request->status);
        }

        // Verify that the canceled status uses the correct spelling
        $this->assertEquals('canceled', \App\Models\MaintenanceRequest::STATUS_CANCELLED);
        $this->assertNotEquals('canceled', \App\Models\MaintenanceRequest::STATUS_CANCELLED);
    }

    /**
     * Test that the Filament resource is properly configured.
     */
    public function test_filament_resource_is_properly_configured(): void
    {
        $resource = \App\Filament\Resources\MaintenanceRequestResource::class;

        // Test that the resource exists and is properly configured
        $this->assertTrue(class_exists($resource));

        // Test that the model is correctly set
        $this->assertEquals(\App\Models\MaintenanceRequest::class, $resource::getModel());

        // Test that the pages are defined
        $pages = $resource::getPages();
        $this->assertArrayHasKey('index', $pages);
        $this->assertArrayHasKey('create', $pages);
        $this->assertArrayHasKey('edit', $pages);
        $this->assertArrayHasKey('view', $pages);

        // Test that the navigation is properly configured
        $this->assertNotEmpty($resource::getNavigationGroup());
        $this->assertNotEmpty($resource::getModelLabel());
        $this->assertNotEmpty($resource::getPluralModelLabel());
    }

    /**
     * Test that caching is properly configured.
     */
    public function test_caching_is_properly_configured(): void
    {
        // Test that cache is working
        \Cache::put('test_key', 'test_value', now()->addMinutes(5));
        $this->assertEquals('test_value', \Cache::get('test_key'));

        // Test that cache can be cleared
        \Cache::forget('test_key');
        $this->assertNull(\Cache::get('test_key'));

        // Test tenant-specific cache keys
        $tenantId = tenant()->id;
        $this->assertNotEmpty($tenantId);

        $tenantCacheKey = "test_tenant_cache_{$tenantId}";
        \Cache::put($tenantCacheKey, 'tenant_value', now()->addMinutes(5));
        $this->assertEquals('tenant_value', \Cache::get($tenantCacheKey));
    }

    /**
     * Test that translations are working.
     */
    public function test_translations_are_working(): void
    {
        // Test that Arabic translations exist
        $this->assertNotEmpty(__('filament-resources/maintenance-request.status_options.new'));
        $this->assertNotEmpty(__('filament-resources/maintenance-request.status_options.canceled'));

        // Test that the canceled translation uses the correct spelling
        $canceledTranslation = __('filament-resources/maintenance-request.status_options.canceled');
        $this->assertNotEmpty($canceledTranslation);

        // Test that priority translations are removed (should fall back to key)
        $priorityTranslation = __('filament-resources/maintenance-request.priority_options.high');
        $this->assertEquals('filament-resources/maintenance-request.priority_options.high', $priorityTranslation);
    }

    /**
     * Test that the database migration was successful.
     */
    public function test_database_migration_was_successful(): void
    {
        // Test that the maintenance_requests table exists
        $this->assertTrue(\Schema::hasTable('maintenance_requests'));

        // Test that the priority column does not exist
        $this->assertFalse(\Schema::hasColumn('maintenance_requests', 'priority'));

        // Test that required columns exist
        $requiredColumns = [
            'id', 'request_number', 'client_id', 'contract_type_id',
            'title', 'description', 'status', 'request_date',
            'completion_date', 'assigned_to', 'notes', 'created_at', 'updated_at'
        ];

        foreach ($requiredColumns as $column) {
            $this->assertTrue(\Schema::hasColumn('maintenance_requests', $column), "Column {$column} should exist");
        }
    }

    /**
     * Test that tenant isolation is working correctly.
     */
    public function test_tenant_isolation_is_working(): void
    {
        $tenant1 = $this->getCurrentTenant();

        // Create data in tenant 1
        $request1 = \App\Models\MaintenanceRequest::factory()->create(['title' => 'Tenant 1 Request']);

        // Switch to tenant 2
        $tenant2 = $this->createAndSwitchTenant();

        // Verify isolation - should not see tenant 1 data
        $this->assertCount(0, \App\Models\MaintenanceRequest::all());

        // Create data in tenant 2
        $request2 = \App\Models\MaintenanceRequest::factory()->create(['title' => 'Tenant 2 Request']);
        $this->assertCount(1, \App\Models\MaintenanceRequest::all());

        // Switch back to tenant 1
        $this->switchTenant($tenant1);

        // Should only see tenant 1 data
        $this->assertCount(1, \App\Models\MaintenanceRequest::all());
        $this->assertEquals('Tenant 1 Request', \App\Models\MaintenanceRequest::first()->title);
    }

    /**
     * Run a comprehensive test of the entire maintenance request workflow.
     */
    public function test_complete_maintenance_request_workflow(): void
    {
        // 1. Create supporting data
        $client = \App\Models\Client::factory()->create();
        $contractType = \App\Models\ContractType::factory()->create();
        $technician = \App\Models\User::factory()->technician()->create();

        // 2. Create a new maintenance request
        $request = \App\Models\MaintenanceRequest::factory()->create([
            'client_id' => $client->id,
            'contract_type_id' => $contractType->id,
            'status' => \App\Models\MaintenanceRequest::STATUS_NEW,
        ]);

        $this->assertEquals(\App\Models\MaintenanceRequest::STATUS_NEW, $request->status);

        // 3. Assign to technician
        $request->update([
            'status' => \App\Models\MaintenanceRequest::STATUS_ASSIGNED,
            'assigned_to' => $technician->id,
        ]);

        $this->assertEquals(\App\Models\MaintenanceRequest::STATUS_ASSIGNED, $request->status);
        $this->assertEquals($technician->id, $request->assigned_to);

        // 4. Start work
        $request->update(['status' => \App\Models\MaintenanceRequest::STATUS_IN_PROGRESS]);
        $this->assertEquals(\App\Models\MaintenanceRequest::STATUS_IN_PROGRESS, $request->status);

        // 5. Complete work
        $request->update([
            'status' => \App\Models\MaintenanceRequest::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);

        $this->assertEquals(\App\Models\MaintenanceRequest::STATUS_COMPLETED, $request->status);
        $this->assertNotNull($request->completed_at);

        // 6. Test cancellation workflow (create another request)
        $cancelRequest = \App\Models\MaintenanceRequest::factory()->create([
            'status' => \App\Models\MaintenanceRequest::STATUS_NEW,
        ]);

        $cancelRequest->update(['status' => \App\Models\MaintenanceRequest::STATUS_CANCELLED]);
        $this->assertEquals(\App\Models\MaintenanceRequest::STATUS_CANCELLED, $cancelRequest->status);
    }
}
