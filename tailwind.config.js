import preset from './vendor/filament/support/tailwind.config.preset'
import colors from 'tailwindcss/colors'
import forms from '@tailwindcss/forms'
import typography from '@tailwindcss/typography'
export default {
    presets: [preset],
    theme: {
        extend: {
            colors: {
                danger: colors.rose,
                primary: colors.blue,
                success: colors.green,
                warning: colors.yellow,
                'harvest-gold': {
                    50: 'rgb(251, 247, 236)',
                    100: 'rgb(246, 238, 217)',
                    200: 'rgb(237, 221, 179)',
                    300: 'rgb(219, 168, 82)',
                    400: 'rgb(207, 149, 63)',
                    500: 'rgb(189, 131, 45)',
                    600: 'rgb(158, 109, 37)',
                    700: 'rgb(126, 87, 30)',
                    800: 'rgb(95, 66, 22)',
                    900: 'rgb(63, 44, 15)',
                    950: 'rgb(32, 22, 7)',
                },
                forest: {
                    50: 'rgb(237, 246, 242)',
                    100: 'rgb(219, 237, 229)',
                    200: 'rgb(183, 219, 201)',
                    300: 'rgb(147, 201, 173)',
                    400: 'rgb(75, 165, 117)',
                    500: 'rgb(28, 106, 77)',
                    600: 'rgb(25, 95, 69)',
                    700: 'rgb(17, 63, 46)',
                    800: 'rgb(13, 47, 35)',
                    900: 'rgb(11, 31, 23)',
                    950: 'rgb(4, 16, 12)',
                },

            },
        }
    },
    content: [
        './app/Filament/**/*.php',
        './resources/views/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
    plugins: [
        forms,
        typography,
    ],
}
