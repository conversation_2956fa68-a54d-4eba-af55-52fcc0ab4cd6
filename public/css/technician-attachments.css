/* Enhanced Attachments Display for Technician Reports */

/* Enhanced File Upload Component */
.enhanced-attachments {
    border: 2px dashed #e5e7eb;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    position: relative;
    overflow: hidden;
}

.enhanced-attachments::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.enhanced-attachments:hover::before {
    opacity: 1;
}

.enhanced-attachments:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.enhanced-attachments.fi-fo-file-upload--has-files {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.enhanced-attachments.fi-fo-file-upload--has-files:hover {
    border-color: #059669;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

/* File Preview Enhancements */
.fi-fo-file-upload-file {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.fi-fo-file-upload-file::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.fi-fo-file-upload-file:hover::before {
    opacity: 1;
}

.fi-fo-file-upload-file:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

/* Enhanced File Type Indicators */
.fi-fo-file-upload-file[data-file-type="drawing"] {
    border-left: 5px solid #8b5cf6;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.fi-fo-file-upload-file[data-file-type="image"] {
    border-left: 5px solid #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.fi-fo-file-upload-file[data-file-type="document"] {
    border-left: 5px solid #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

/* Enhanced File Type Icons */
.fi-fo-file-upload-file[data-filename*="رسم-توضيحي"]::after,
.fi-fo-file-upload-file[data-filename*="drawing-"]::after {
    content: "🎨";
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 20px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.fi-fo-file-upload-file[data-filename*="رسم-توضيحي"]:hover::after,
.fi-fo-file-upload-file[data-filename*="drawing-"]:hover::after {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* PDF File Icons */
.fi-fo-file-upload-file[data-filename$=".pdf"]::after {
    content: "📄";
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 18px;
    z-index: 10;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 6px;
    padding: 4px 6px;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Image File Icons */
.fi-fo-file-upload-file[data-filename$=".jpg"]::after,
.fi-fo-file-upload-file[data-filename$=".jpeg"]::after,
.fi-fo-file-upload-file[data-filename$=".png"]::after,
.fi-fo-file-upload-file[data-filename$=".gif"]::after,
.fi-fo-file-upload-file[data-filename$=".webp"]::after {
    content: "🖼️";
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 18px;
    z-index: 10;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 6px;
    padding: 4px 6px;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* Document File Icons */
.fi-fo-file-upload-file[data-filename$=".doc"]::after,
.fi-fo-file-upload-file[data-filename$=".docx"]::after {
    content: "📝";
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 18px;
    z-index: 10;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 6px;
    padding: 4px 6px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Image File Indicators */
.fi-fo-file-upload-file[data-filename$=".jpg"],
.fi-fo-file-upload-file[data-filename$=".jpeg"],
.fi-fo-file-upload-file[data-filename$=".png"],
.fi-fo-file-upload-file[data-filename$=".gif"],
.fi-fo-file-upload-file[data-filename$=".webp"] {
    border-left: 4px solid #10b981;
}

/* Document File Indicators */
.fi-fo-file-upload-file[data-filename$=".pdf"] {
    border-left: 4px solid #ef4444;
}

.fi-fo-file-upload-file[data-filename$=".doc"],
.fi-fo-file-upload-file[data-filename$=".docx"] {
    border-left: 4px solid #3b82f6;
}

/* File Name Display */
.fi-fo-file-upload-file-name {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
    line-height: 1.4;
    word-break: break-word;
}

/* File Size Display */
.fi-fo-file-upload-file-size {
    color: #6b7280;
    font-size: 12px;
    margin-top: 4px;
}

/* Enhanced Drop Zone */
.fi-fo-file-upload-dropzone {
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    text-align: center;
}

.fi-fo-file-upload-dropzone-text {
    color: #6b7280;
    font-size: 14px;
    margin-top: 8px;
}

/* Grid Layout for File Previews */
.fi-fo-file-upload-files {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

/* Enhanced Image Previews */
.fi-fo-file-upload-file-preview img {
    border-radius: 6px;
    object-fit: cover;
    width: 100%;
    height: 120px;
    background: #f9fafb;
}

/* File Actions */
.fi-fo-file-upload-file-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.fi-fo-file-upload-file-action {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.fi-fo-file-upload-file-action:hover {
    transform: scale(1.05);
}

/* Reorder Handle */
.fi-fo-file-upload-file-reorder-handle {
    cursor: grab;
    color: #9ca3af;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.fi-fo-file-upload-file-reorder-handle:hover {
    color: #6b7280;
    background: #f3f4f6;
}

.fi-fo-file-upload-file-reorder-handle:active {
    cursor: grabbing;
}

/* Enhanced Loading States */
.fi-fo-file-upload-file--uploading {
    opacity: 0.8;
    position: relative;
    overflow: hidden;
}

.fi-fo-file-upload-file--uploading::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
    z-index: 1;
}

.fi-fo-file-upload-file--uploading::after {
    content: "⏳";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    z-index: 2;
    animation: pulse 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.1); }
}

/* Upload Progress Indicator */
.fi-fo-file-upload-file--uploading .fi-fo-file-upload-file-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 0 0 12px 12px;
    animation: progress 2s infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Error States */
.fi-fo-file-upload-file--error {
    border-color: #ef4444;
    background: #fef2f2;
}

/* Success States */
.fi-fo-file-upload-file--uploaded {
    border-color: #10b981;
    background: #ecfdf5;
}

/* Enhanced RTL Support */
[dir="rtl"] .fi-fo-file-upload-file[data-filename*="رسم-توضيحي"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename*="drawing-"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".pdf"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".jpg"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".jpeg"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".png"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".gif"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".webp"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".doc"]::after,
[dir="rtl"] .fi-fo-file-upload-file[data-filename$=".docx"]::after {
    right: auto;
    left: 12px;
}

[dir="rtl"] .fi-fo-file-upload-file[data-file-type="drawing"],
[dir="rtl"] .fi-fo-file-upload-file[data-file-type="image"],
[dir="rtl"] .fi-fo-file-upload-file[data-file-type="document"] {
    border-left: none;
    border-right: 5px solid;
}

[dir="rtl"] .fi-fo-file-upload-file[data-file-type="drawing"] {
    border-right-color: #8b5cf6;
}

[dir="rtl"] .fi-fo-file-upload-file[data-file-type="image"] {
    border-right-color: #10b981;
}

[dir="rtl"] .fi-fo-file-upload-file[data-file-type="document"] {
    border-right-color: #ef4444;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    .enhanced-attachments {
        border-radius: 12px;
        margin: 8px 0;
    }

    .fi-fo-file-upload-files {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
        padding: 12px;
    }

    .fi-fo-file-upload-file {
        border-radius: 8px;
    }

    .fi-fo-file-upload-file-preview img {
        height: 100px;
        border-radius: 6px;
    }

    .fi-fo-file-upload-dropzone {
        min-height: 120px;
        padding: 20px 16px;
    }

    .fi-fo-file-upload-dropzone-text {
        font-size: 13px;
    }

    /* Smaller icons on mobile */
    .fi-fo-file-upload-file[data-filename*="رسم-توضيحي"]::after,
    .fi-fo-file-upload-file[data-filename*="drawing-"]::after {
        width: 28px;
        height: 28px;
        font-size: 16px;
        top: 8px;
        right: 8px;
    }

    .fi-fo-file-upload-file[data-filename$=".pdf"]::after,
    .fi-fo-file-upload-file[data-filename$=".jpg"]::after,
    .fi-fo-file-upload-file[data-filename$=".jpeg"]::after,
    .fi-fo-file-upload-file[data-filename$=".png"]::after,
    .fi-fo-file-upload-file[data-filename$=".gif"]::after,
    .fi-fo-file-upload-file[data-filename$=".webp"]::after,
    .fi-fo-file-upload-file[data-filename$=".doc"]::after,
    .fi-fo-file-upload-file[data-filename$=".docx"]::after {
        font-size: 14px;
        padding: 3px 5px;
        top: 8px;
        right: 8px;
    }

    [dir="rtl"] .fi-fo-file-upload-file[data-filename*="رسم-توضيحي"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename*="drawing-"]::after {
        left: 8px;
        right: auto;
    }

    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".pdf"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".jpg"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".jpeg"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".png"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".gif"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".webp"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".doc"]::after,
    [dir="rtl"] .fi-fo-file-upload-file[data-filename$=".docx"]::after {
        left: 8px;
        right: auto;
    }
}

@media (max-width: 480px) {
    .fi-fo-file-upload-files {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
        padding: 8px;
    }

    .fi-fo-file-upload-file-preview img {
        height: 80px;
    }

    .fi-fo-file-upload-dropzone {
        min-height: 100px;
        padding: 16px 12px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .enhanced-attachments {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }
    
    .enhanced-attachments:hover {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        border-color: #3b82f6;
    }
    
    .fi-fo-file-upload-file {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .fi-fo-file-upload-file-name {
        color: #f9fafb;
    }
    
    .fi-fo-file-upload-file-size {
        color: #9ca3af;
    }
}
