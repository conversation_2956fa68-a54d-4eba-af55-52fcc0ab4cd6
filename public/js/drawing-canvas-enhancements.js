/**
 * Enhanced Drawing Canvas Integration
 * Provides smooth transitions, visual feedback, and file highlighting
 */

// Enhanced Drawing Integration for Filament
function drawingIntegration() {
    return {
        lastAddedFilename: null,
        fileUploadComponent: null,
        tabsContainer: null,

        init() {
            console.log('Enhanced drawing integration initialized');

            // Cache DOM elements for better performance
            this.cacheElements();

            // Listen for drawing save events
            window.addEventListener('addDrawingToAttachments', (event) => {
                this.handleDrawingFile(event.detail.file, event.detail.filename);
            });

            // Listen for tab switching events
            window.addEventListener('switchToUploadsTab', (event) => {
                this.switchToUploadsTab(event.detail?.filename);
            });

            // Listen for drawing file added events
            window.addEventListener('drawing-file-added', (event) => {
                this.onDrawingFileAdded(event.detail);
            });

            // Enhanced Livewire integration
            if (window.Livewire) {
                window.Livewire.on('file-uploaded', (data) => {
                    if (data.isDrawing) {
                        this.onDrawingUploaded(data);
                    }
                });
            }
        },

        cacheElements() {
            // Cache frequently used elements
            this.fileUploadComponent = this.findFileUploadComponent();
            this.tabsContainer = this.findTabsContainer();

            console.log('Cached elements:', {
                fileUpload: !!this.fileUploadComponent,
                tabs: !!this.tabsContainer
            });
        },

        findFileUploadComponent() {
            const selectors = [
                '[x-ref="attachmentsUpload"]',
                '[data-field-wrapper-id*="attachments"]',
                '.fi-fo-file-upload',
                '[wire\\:model*="attachments"]'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    return element;
                }
            }
            return null;
        },

        findTabsContainer() {
            const selectors = [
                '#attachment-tabs-container',
                '[role="tablist"]',
                '.fi-tabs',
                '[x-data*="tabs"]'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    return element;
                }
            }
            return null;
        },

        handleDrawingFile(file, filename) {
            console.log(`Handling drawing file: ${filename}`);

            // Find file upload component
            const fileUpload = this.fileUploadComponent || this.findFileUploadComponent();
            if (!fileUpload) {
                console.error('File upload component not found');
                return;
            }

            // Add file to upload component
            this.addFileToUploadComponent(fileUpload, file, filename);

            // Store for highlighting
            this.lastAddedFilename = filename;
        },

        addFileToUploadComponent(container, file, filename) {
            const fileInput = container.querySelector('input[type="file"]');
            if (!fileInput) {
                console.error('File input not found');
                return;
            }

            // Create DataTransfer to add file
            const dataTransfer = new DataTransfer();

            // Add existing files
            if (fileInput.files) {
                for (let i = 0; i < fileInput.files.length; i++) {
                    dataTransfer.items.add(fileInput.files[i]);
                }
            }

            // Add new file
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;

            // Trigger events
            this.triggerFileEvents(fileInput, filename);
        },

        triggerFileEvents(fileInput, filename) {
            const events = ['change', 'input', 'livewire:update'];

            events.forEach(eventType => {
                fileInput.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            // Custom event for drawing
            window.dispatchEvent(new CustomEvent('drawing-file-added', {
                detail: { filename, timestamp: Date.now() }
            }));
        },

        switchToUploadsTab(filename) {
            const container = this.tabsContainer || this.findTabsContainer();
            if (!container) {
                console.error('Tabs container not found');
                return;
            }

            const uploadTab = container.querySelector('button:first-child') ||
                            container.querySelector('[data-tab="file_uploads"]');

            if (uploadTab) {
                uploadTab.click();

                if (filename) {
                    setTimeout(() => {
                        this.highlightFile(filename);
                    }, 800);
                }
            }
        },

        highlightFile(filename) {
            // Use the enhanced highlighting function
            if (typeof enhancedFileHighlighting === 'function') {
                enhancedFileHighlighting().highlightFileByName(filename);
            }
        },

        onDrawingFileAdded(detail) {
            console.log('Drawing file added:', detail);
            // Additional processing if needed
        },

        onDrawingUploaded(data) {
            console.log('Drawing uploaded via Livewire:', data);
            // Handle Livewire upload completion
        }
    };
}

// Enhanced file highlighting with better detection
function enhancedFileHighlighting() {
    let highlightObserver = null;
    let pendingHighlights = new Set();

    function createHighlightObserver() {
        if (highlightObserver) {
            highlightObserver.disconnect();
        }

        highlightObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Enhanced selectors for Filament file upload items
                            const selectors = [
                                '[data-testid="file-upload-item"]',
                                '.fi-fo-file-upload-item',
                                '.fi-fo-file-upload-file',
                                '[data-file-key]',
                                '.relative img[src*="blob:"]',
                                '.relative img[src*="data:"]'
                            ];

                            let fileItems = [];
                            selectors.forEach(selector => {
                                if (node.querySelectorAll) {
                                    fileItems = [...fileItems, ...node.querySelectorAll(selector)];
                                }
                            });

                            fileItems.forEach(item => {
                                checkAndHighlightDrawing(item);
                            });

                            // Also check the node itself
                            if (node.matches) {
                                selectors.forEach(selector => {
                                    if (node.matches(selector)) {
                                        checkAndHighlightDrawing(node);
                                    }
                                });
                            }
                        }
                    });
                }
            });
        });

        // Observe the file upload container specifically
        const uploadContainer = document.querySelector('[x-ref="attachmentsUpload"]') ||
                              document.querySelector('[data-field-wrapper-id*="attachments"]') ||
                              document.body;

        highlightObserver.observe(uploadContainer, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['src', 'data-file-key']
        });
    }
    
    function checkAndHighlightDrawing(element) {
        // Look for drawing files by checking various text content
        const textContent = element.textContent || '';
        const hasDrawingFile = textContent.includes('رسم-توضيحي') || 
                              textContent.includes('drawing-') ||
                              textContent.includes('.png');
        
        if (hasDrawingFile) {
            // Check if this element was recently added (within last 2 seconds)
            const now = Date.now();
            const elementTime = element.dataset.addedTime || now;
            
            if (now - elementTime < 2000) {
                highlightElement(element);
            }
        }
    }
    
    function highlightElement(element) {
        // Add timestamp to track when element was processed
        element.dataset.addedTime = Date.now();
        
        // Apply highlight styles
        element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        element.style.transform = 'scale(1.05)';
        element.style.boxShadow = '0 0 25px rgba(34, 197, 94, 0.6)';
        element.style.border = '3px solid #22c55e';
        element.style.borderRadius = '12px';
        element.style.backgroundColor = 'rgba(34, 197, 94, 0.1)';
        
        // Add pulsing animation
        element.classList.add('animate-pulse');
        
        // Scroll into view smoothly
        setTimeout(() => {
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'nearest'
            });
        }, 200);
        
        // Create a "NEW" badge
        const badge = document.createElement('div');
        badge.innerHTML = 'جديد';
        badge.style.cssText = `
            position: absolute;
            top: -8px;
            right: -8px;
            background: #22c55e;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            z-index: 10;
            animation: bounce 0.6s ease-in-out;
        `;
        
        // Make sure the parent has relative positioning
        element.style.position = 'relative';
        element.appendChild(badge);
        
        // Remove highlight after 4 seconds
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.boxShadow = '';
            element.style.border = '';
            element.style.backgroundColor = '';
            element.classList.remove('animate-pulse');
            
            // Remove badge
            if (badge.parentNode) {
                badge.remove();
            }
        }, 4000);
    }
    
    // Initialize observer
    createHighlightObserver();
    
    return {
        highlight: highlightElement,
        destroy: () => {
            if (highlightObserver) {
                highlightObserver.disconnect();
            }
        }
    };
}

// Enhanced tab switching with smooth animations
function enhancedTabSwitching() {
    function switchToTab(tabId, callback) {
        const tabButton = document.querySelector(`[id*="${tabId}"]`);
        if (!tabButton) {
            console.warn(`Tab button for ${tabId} not found`);
            return false;
        }
        
        // Add transition effects
        const tabsContainer = tabButton.closest('[role="tablist"]');
        if (tabsContainer) {
            tabsContainer.style.transition = 'all 0.3s ease-in-out';
        }
        
        // Highlight the target tab briefly
        tabButton.style.transition = 'all 0.3s ease-in-out';
        tabButton.style.transform = 'scale(1.05)';
        tabButton.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
        
        // Click the tab
        setTimeout(() => {
            tabButton.click();
            
            // Reset tab button styles
            setTimeout(() => {
                tabButton.style.transform = 'scale(1)';
                tabButton.style.boxShadow = '';
            }, 300);
            
            // Execute callback after tab content loads
            if (callback) {
                setTimeout(callback, 400);
            }
        }, 100);
        
        return true;
    }
    
    return {
        switchTo: switchToTab
    };
}

// Initialize enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced file highlighting
    const fileHighlighter = enhancedFileHighlighting();
    
    // Initialize enhanced tab switching
    const tabSwitcher = enhancedTabSwitching();
    
    // Make functions globally available
    window.drawingCanvasEnhancements = {
        fileHighlighter,
        tabSwitcher
    };
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0,-8px,0);
            }
            70% {
                transform: translate3d(0,-4px,0);
            }
            90% {
                transform: translate3d(0,-2px,0);
            }
        }
        
        .file-upload-enhanced {
            transition: all 0.3s ease-in-out;
        }
        
        .file-upload-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
    `;
    document.head.appendChild(style);
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.drawingCanvasEnhancements?.fileHighlighter?.destroy) {
        window.drawingCanvasEnhancements.fileHighlighter.destroy();
    }
});
