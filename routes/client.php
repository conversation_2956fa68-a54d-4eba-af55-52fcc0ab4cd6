<?php

use Illuminate\Support\Facades\Route;
//use App\Http\Controllers\CertificateController;
//use App\Http\Controllers\ClientPortalController;
//use App\Http\Controllers\MaintenanceWizardController;

/*
|--------------------------------------------------------------------------
| Client Portal Routes
|--------------------------------------------------------------------------
*/

Route::get('/t1', \App\Filament\Client\Pages\LandingPage::class)->name('filament.client.pages.certificates');
Route::get('/t2', \App\Filament\Client\Pages\LandingPage::class)->name('filament.client.pages.maintenance');
Route::get('/t3', \App\Filament\Client\Pages\LandingPage::class)->name('client.maintenance-request.print');


//
//// Client authentication routes
//Route::get('/login', [ClientPortalController::class, 'showLogin'])->name('login');
//Route::post('/login', [ClientPortalController::class, 'processLogin'])->name('login.process');
//
//Route::view('profile', 'profile')
//    ->middleware(['auth'])
//    ->name('profile');
//
//
//Route::middleware(\App\Http\Middleware\ClientAuthenticate::class)->post('/logout', [ClientPortalController::class, 'logout'])->name('logout');
//
//// Client portal routes (protected by client auth middleware)
//Route::middleware(\App\Http\Middleware\ClientAuthenticate::class)->group(function () {
//    Route::prefix('maintenance')->name('maintenance.')->group(function () {
//        // Step 1: Contract Type Selection
//        Route::get('/', [MaintenanceWizardController::class, 'index'])->name('step1');
//        Route::post('/step1', [MaintenanceWizardController::class, 'processStep1'])->name('processStep1');
//
//        // Step 2: Client Information
//        Route::get('/step2', [MaintenanceWizardController::class, 'step2'])->name('step2');
//        Route::post('/step2', [MaintenanceWizardController::class, 'processStep2'])->name('processStep2');
//
//        // Step 3: Contract Details
//        Route::get('/step3', [MaintenanceWizardController::class, 'step3'])->name('step3');
//        Route::post('/step3', [MaintenanceWizardController::class, 'processStep3'])->name('processStep3');
//
//        // Success Page
//        Route::get('/success/{request}', [MaintenanceWizardController::class, 'success'])->name('success');
//    });
//
//    Route::prefix('certificates')->name('certificate.')->group(function () {
//        // Steps navigation
//        Route::get('/', [CertificateController::class, 'index'])->name('index');
//        Route::get('/step1', [CertificateController::class, 'step1'])->name('step1');
//        Route::get('/step2', [CertificateController::class, 'step2'])->name('step2');
//        Route::get('/step3', [CertificateController::class, 'step3'])->name('step3');
//        Route::get('/step4', [CertificateController::class, 'step4'])->name('step4');
//        Route::get('/success', [CertificateController::class, 'success'])->name('success');
//
//        // Form processing
//        Route::post('/process-step1', [CertificateController::class, 'processStep1'])->name('processStep1');
//        Route::post('/process-step2', [CertificateController::class, 'processStep2'])->name('processStep2');
//        Route::post('/process-step3', [CertificateController::class, 'processStep3'])->name('processStep3');
//        Route::post('/submit', [CertificateController::class, 'submitRequest'])->name('submit');
//    });
//
//    Route::prefix('client')->name('client.')->group(function () {
//        // Dashboard
//        Route::get('/', [ClientPortalController::class, 'dashboard'])->name('dashboard');
//
//        // Contracts
//        Route::get('/certificates', [ClientPortalController::class, 'certificateRequests'])->name('certificates.index');
//        Route::get('/certificates/{id}', [ClientPortalController::class, 'showCertificateRequest'])->name('certificates.show');
//        Route::get('/certificates/{id}/cancel', [ClientPortalController::class, 'cancelCertificateRequest'])
//            ->middleware('signed')
//            ->name('certificates.cancel');
//
//        // Contracts
//        Route::get('/contracts', [ClientPortalController::class, 'contracts'])->name('contracts');
//        Route::get('/contracts/{id}', [ClientPortalController::class, 'showContract'])->name('contracts.show');
//
//        // Payments
//        Route::get('/contracts/{id}/payment', [ClientPortalController::class, 'showPaymentForm'])->name('payments.create');
//        Route::post('/contracts/{id}/payment', [ClientPortalController::class, 'processPayment'])->name('payments.process');
//
//        // Visits
//        Route::get('/visits', [ClientPortalController::class, 'visits'])->name('visits');
//        Route::get('/visits/{id}', [ClientPortalController::class, 'showVisit'])->name('visits.show');
//        Route::get('/contracts/{id}/schedule-visit', [ClientPortalController::class, 'showScheduleVisitForm'])->name('visits.schedule');
//        Route::post('/contracts/{id}/schedule-visit', [ClientPortalController::class, 'scheduleVisit'])->name('visits.process');
//
//        // Maintenance Requests
//        Route::get('/maintenance-requests', [ClientPortalController::class, 'maintenanceRequests'])->name('maintenance.index');
//        Route::get('/maintenance-requests/{id}', [ClientPortalController::class, 'showMaintenanceRequest'])->name('maintenance.show');
//        Route::get('/contracts/{id}/maintenance-request', [ClientPortalController::class, 'showCreateRequestForm'])->name('maintenance.create');
//        Route::post('/contracts/{id}/maintenance-request', [ClientPortalController::class, 'createMaintenanceRequest'])->name('maintenance.store');
//
//        // Profile
//        Route::get('/profile', [ClientPortalController::class, 'showProfile'])->name('profile');
//        Route::put('/profile', [ClientPortalController::class, 'updateProfile'])->name('profile.update');
//    });
//});
