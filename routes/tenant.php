<?php

declare(strict_types=1);

use App\Http\Controllers\Admin\DocumentTemplateController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\MaintenanceRequestController;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::get('/', \App\Filament\Client\Pages\LandingPage::class)
     ->middleware(['web', InitializeTenancyByDomain::class]);

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    /*Route::get('/', function () {
        return view('welcome');
    })->name('home');

    Route::view('dashboard', 'dashboard')
        ->middleware(['auth', 'verified'])
        ->name('dashboard');

    Route::middleware(['auth'])->group(function () {
        Route::redirect('settings', 'settings/profile');

        Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
        Volt::route('settings/password', 'settings.password')->name('settings.password');
        Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
    });*/

    Route::get('/maintenance-requests/{record}/print', [MaintenanceRequestController::class, 'print'])
        ->name('maintenance-request.print')
        ->middleware(['auth']);

    Route::get('/contracts/{record}/print', [ContractController::class, 'print'])
        ->name('contract.print')
        ->middleware(['auth']);

    /*
    |--------------------------------------------------------------------------
    | Document Template Routes
    |--------------------------------------------------------------------------
    */

    Route::prefix('_admin/document-templates')->name('admin.document-templates.')->group(function () {
        // Template editor
        Route::get('/edit/{template}', [DocumentTemplateController::class, 'edit'])->name('edit');

        // Update template
        Route::post('/update/{template}', [DocumentTemplateController::class, 'update'])->name('update');

        // Sync template with DocKing
        Route::post('/sync/{template}', [DocumentTemplateController::class, 'sync'])->name('sync');

        // Preview template
        Route::get('/preview/{template}', [DocumentTemplateController::class, 'preview'])->name('preview');

        // Preview template with custom content
        Route::post('/preview-content', [DocumentTemplateController::class, 'previewContent'])->name('preview-content');

        // Generate PDF
        Route::get('/generate-pdf/{template}', [DocumentTemplateController::class, 'generatePdf'])->name('generate-pdf');

        // Export template to file
        Route::post('/export/{template}', [DocumentTemplateController::class, 'exportToFile'])->name('export');

        // Import template from file
        Route::post('/import/{template}', [DocumentTemplateController::class, 'importFromFile'])->name('import');

        // Reset template to default
        Route::post('/reset/{template}', [DocumentTemplateController::class, 'resetToDefault'])->name('reset');
    });


    //require __DIR__ . '/auth.php';
    require __DIR__ . '/client.php';
});
