<?php

use Illuminate\Support\Facades\Route;

Route::domain('{central_domain}')
    ->withoutMiddleware([
        \Stancl\Tenancy\Middleware\InitializeTenancyByDomain::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \Stancl\Tenancy\Middleware\CheckTenantForMaintenanceMode::class,
    ])
    ->middleware(['universal'])->group(function () {
        //Route::get('/', fn() => '');
        Route::any('/{any?}', fn() => view('landing'))->where('any', '.*');
    });

// Drawing upload route for technician reports (tenant-aware)
Route::post('/technician/upload-drawing', [App\Http\Controllers\DrawingUploadController::class, 'upload'])
    ->middleware(['auth'])
    ->name('technician.upload-drawing');
