@extends('layouts.app')

@section('content')
    <div>
        <div class="page-header">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <h1>عقود الصيانة</h1>
                        <p>اختر نوع العقد المناسب لاحتياجاتك وقم بتعبئة البيانات المطلوبة</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            @include('maintenance.partials.step-indicator', ['currentStep' => $currentStep])

            <!-- Success Message -->
            <ion-card class="success-card">
                <ion-card-content>
                    <div class="ion-text-center">
                        <ion-icon name="checkmark-circle" class="success-icon"></ion-icon>

                        <h2>تم إرسال طلب عقد الصيانة بنجاح!</h2>
                        <p>رقم العقد: <strong>{{ $request->request_number }}</strong></p>
                        <p>سيتواصل معك فريقنا قريباً للتنسيق وإتمام العقد.</p>

                        <ion-grid>
                            <ion-row>
                                <ion-col size="12">
                                    <a href="{{ route('maintenance.step1') }}">
                                        <ion-button expand="block" color="primary">
                                            طلب عقد جديد
                                        </ion-button>
                                    </a>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </div>
                </ion-card-content>
            </ion-card>
        </div>
    </div>

    @include('maintenance.partials.style')
@endsection
