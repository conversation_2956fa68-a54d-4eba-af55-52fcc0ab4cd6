<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .step-indicator {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        position: relative;
    }

    .step-indicator::before {
        content: '';
        position: absolute;
        top: 15px;
        left: 0;
        right: 0;
        height: 2px;
        background: #e0e0e0;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
    }

    .step.active .step-circle {
        background-color: var(--ion-color-primary);
        color: white;
    }

    .step-title {
        font-size: 14px;
        color: #666;
        text-align: center;
    }

    .step.active .step-title {
        color: var(--ion-color-primary);
        font-weight: bold;
    }

    .contract-type-card {
        height: 100%;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }

    .contract-type-card.selected {
        border: 2px solid var(--ion-color-primary);
        box-shadow: 0 4px 12px rgba(56, 128, 255, 0.15);
    }

    .contract-icon {
        font-size: 48px;
        margin-bottom: 15px;
        color: var(--ion-color-primary);
    }

    .features-list {
        text-align: right;
        padding-right: 20px;
        margin-bottom: 20px;
    }

    .price {
        font-size: 18px;
        margin-top: 15px;
    }

    .price-note {
        font-size: 12px;
        color: #666;
        display: block;
        margin-top: 5px;
    }

    .visit-limit {
        margin-top: 10px;
        font-size: 14px;
        color: #444;
    }

    .visits-info {
        margin-top: 20px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-right: 3px solid var(--ion-color-primary);
    }

    .form-card {
        margin-bottom: 20px;
    }

    .contract-value-card {
        background-color: #f8f9fa;
        border: 1px solid var(--ion-color-primary);
    }

    .contract-price {
        font-size: 36px;
        color: var(--ion-color-primary);
        margin: 15px 0;
    }

    .form-navigation {
        margin-top: 20px;
    }

    .error-message {
        color: var(--ion-color-danger);
        font-size: 12px;
        margin-top: 5px;
        margin-right: 16px;
    }

    .terms-agreement {
        margin-top: 20px;
    }

    .info-label {
        font-weight: bold;
        color: #555;
    }

    .info-value {
        color: #333;
    }

    .success-card {
        text-align: center;
        padding: 30px;
    }

    .success-icon {
        font-size: 80px;
        color: var(--ion-color-success);
        margin-bottom: 20px;
    }

    .required {
        color: var(--ion-color-danger);
    }

    /* Ionic-specific compatibility fixes */
    ion-button a {
        text-decoration: none;
        color: inherit;
    }

    ion-item {
        --padding-start: 0;
    }

    @media (max-width: 576px) {
        .step-title {
            font-size: 12px;
        }

        .step-circle {
            width: 25px;
            height: 25px;
        }

        .step-indicator::before {
            top: 12px;
        }
    }

    /* RTL support for Arabic */
    .form-container {
        direction: rtl;
    }

    ion-card-content {
        text-align: right;
    }
</style>
