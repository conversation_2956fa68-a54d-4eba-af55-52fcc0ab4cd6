@extends('layouts.app')

@section('content')
    <div>
        <div class="page-header">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <h1>عقود الصيانة</h1>
                        <p>اختر نوع العقد المناسب لاحتياجاتك وقم بتعبئة البيانات المطلوبة</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            @include('maintenance.partials.step-indicator', ['currentStep' => $currentStep])

            <!-- Form Steps -->
            <div>
                <h3 class="ion-text-center">المراجعة والتأكيد</h3>

                <form method="POST" action="{{ route('maintenance.processStep3') }}">
                    @csrf

                    <!-- Contract Summary Cards -->
                    <ion-card class="form-card contract-value-card">
                        <ion-card-content class="ion-text-center">
                            <h4>قيمة العقد الإجمالية</h4>
                            <div class="contract-price">
                                سيتم تحديد التكلفة بعد الزيارة الفنية !
                            </div>
                            <div class="visit-limit">
                                عدد الزيارات: {{ $contractSummary['contractInfo']['visitLimit'] }}
                            </div>
                        </ion-card-content>
                    </ion-card>

                    <ion-card class="form-card">
                        <ion-card-header>
                            <ion-card-title>معلومات العقد</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="6" class="info-label">نوع العقد:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['contractInfo']['type'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">تاريخ البداية:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['contractInfo']['startDate'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">تاريخ الانتهاء:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['contractInfo']['endDate'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">مدة العقد:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['contractInfo']['duration'] }}</ion-col>
                                </ion-row>
                            </ion-grid>
                        </ion-card-content>
                    </ion-card>

                    <ion-card class="form-card">
                        <ion-card-header>
                            <ion-card-title>معلومات العميل</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="6" class="info-label">اسم الشركة:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['clientInfo']['companyName'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">اسم المسؤول:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['clientInfo']['contactName'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">رقم الهاتف:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['clientInfo']['phone'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">البريد الإلكتروني:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['clientInfo']['email'] }}</ion-col>
                                </ion-row>
                                <ion-row>
                                    <ion-col size="6" class="info-label">العنوان:</ion-col>
                                    <ion-col size="6" class="info-value">{{ $contractSummary['clientInfo']['address'] }}</ion-col>
                                </ion-row>
                            </ion-grid>
                        </ion-card-content>
                    </ion-card>

                    <!-- Terms Agreement -->
                    <div class="terms-agreement">
                        <ion-item>
                            <ion-checkbox name="termsAgreement" slot="start"></ion-checkbox>
                            <ion-label>أوافق على <a href="javascript:void(0)" onclick="showContractTerms()">الشروط والأحكام</a></ion-label>
                        </ion-item>
                        @error('termsAgreement') <div class="error-message">{{ $message }}</div> @enderror
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="form-navigation">
                        <ion-grid>
                            <ion-row>
                                <ion-col size="6">
                                    <a href="{{ route('maintenance.step2') }}">
                                        <ion-button expand="block" color="medium">
                                            <ion-icon name="arrow-back" slot="start"></ion-icon>
                                            السابق
                                        </ion-button>
                                    </a>
                                </ion-col>
                                <ion-col size="6">
                                    <ion-button expand="block" color="success" type="submit">
                                        تأكيد وإرسال
                                        <ion-icon name="checkmark" slot="end"></ion-icon>
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showContractTerms() {
            const alertElement = document.createElement('ion-alert');
            alertElement.header = 'الشروط والأحكام';
            alertElement.message = `
                <p>1. يقر العميل بأن جميع المعلومات المقدمة في هذا الطلب صحيحة ودقيقة.</p>
                <p>2. يوافق العميل على دفع قيمة العقد وفقاً لطريقة وتكرار الدفع المحدد.</p>
                <p>3. يتم توقيع العقد النهائي بعد التنسيق مع العميل وتأكيد جميع التفاصيل.</p>
                <p>4. تبدأ فترة العقد من تاريخ التوقيع النهائي.</p>
                <p>5. يلتزم مقدم الخدمة بتنفيذ زيارات الصيانة حسب الجدول المتفق عليه.</p>
                <p>6. يمكن تعديل شروط العقد بموافقة الطرفين.</p>
                <p>7. في حال الإنهاء المبكر للعقد، يتم احتساب الرسوم وفقاً للمدة المستخدمة.</p>
                <p>8. تجديد العقد يخضع لموافقة الطرفين إلا في حال تفعيل خاصية التجديد التلقائي.</p>
                <p>9. الزيارات الإضافية خارج حدود العقد ستكون بتكلفة إضافية حسب نوع العقد.</p>
            `;
            alertElement.buttons = ['موافق'];

            document.body.appendChild(alertElement);
            alertElement.present();
        }
    </script>

    @include('maintenance.partials.style')
@endsection
