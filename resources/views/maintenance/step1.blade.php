@extends('layouts.app')

@section('content')
    <div>
        <div class="page-header">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <h1>عقود الصيانة</h1>
                        <p>اختر نوع العقد المناسب لاحتياجاتك وقم بتعبئة البيانات المطلوبة</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            @include('maintenance.partials.step-indicator', ['currentStep' => $currentStep])

            <!-- Form Steps -->
            <div>
                <h3 class="ion-text-center">اختر نوع العقد:</h3>

                <!-- Debug info to check contract types -->
                @if(empty($contractTypes))
                    <div class="alert alert-warning">
                        لا توجد أنواع عقود متاحة. يرجى التحقق من إعدادات النظام.
                    </div>
                @else
                    <form method="POST" action="{{ route('maintenance.processStep1') }}">
                        @csrf
                        <input type="hidden" name="contractType" id="contractType" value="{{ $selectedType }}">

                        <ion-grid>
                            <ion-row>
                                @foreach($contractTypes as $type => $details)
                                    <ion-col size="12" size-md="6">
                                        <ion-card
                                            class="contract-type-card {{ $selectedType == $type ? 'selected' : '' }}"
                                            onclick="selectContractType('{{ $type }}')">
                                            <ion-card-header>
                                                <ion-card-title>{{ $details['name'] }}</ion-card-title>
                                                <ion-card-subtitle>{{ $details['description'] }}</ion-card-subtitle>
                                            </ion-card-header>
                                            <ion-card-content>
                                                <div class="ion-text-center">
                                                    <ion-icon name="{{ $details['icon'] }}" class="contract-icon"></ion-icon>
                                                </div>

                                                <ul class="features-list">
                                                    @foreach($details['benefits'] as $benefit)
                                                        <li>{{ $benefit }}</li>
                                                    @endforeach
                                                </ul>

                                                <div class="ion-text-center price">
                                                    {{--<strong>{{ number_format($details['price'], 0) }} ريال</strong>--}}
                                                    <span class="price-note">
                                                        لمدة {{ $details['period'] }} شهر
                                                    </span>
                                                </div>

                                                <div class="ion-text-center visit-limit">
                                                    <span>عدد الزيارات: {{ $details['visit_limit'] }}</span>
                                                </div>
                                            </ion-card-content>
                                        </ion-card>
                                    </ion-col>
                                @endforeach
                            </ion-row>
                        </ion-grid>

                        @error('contractType') <div class="error-message ion-text-center">{{ $message }}</div> @enderror

                        <!-- Navigation Buttons -->
                        <div class="form-navigation">
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="6">
                                        <!-- No back button on first step -->
                                    </ion-col>
                                    <ion-col size="6">
                                        <ion-button expand="block" color="primary" type="submit" id="nextButton"
                                                    @if(empty($selectedType)) disabled @endif>
                                            التالي
                                            <ion-icon name="arrow-forward" slot="end"></ion-icon>
                                        </ion-button>
                                    </ion-col>
                                </ion-row>
                            </ion-grid>
                        </div>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <script>
        function selectContractType(type) {
            // Update hidden input
            document.getElementById('contractType').value = type;

            // Update UI - remove selected class from all cards
            document.querySelectorAll('.contract-type-card').forEach(function(card) {
                card.classList.remove('selected');
            });

            // Add selected class to the clicked card
            event.currentTarget.classList.add('selected');

            // Enable next button
            document.getElementById('nextButton').removeAttribute('disabled');
        }
    </script>

    @include('maintenance.partials.style')
@endsection
