@extends('layouts.app')

@section('content')
    <div>
        <div class="page-header">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <h1>عقود الصيانة</h1>
                        <p>اختر نوع العقد المناسب لاحتياجاتك وقم بتعبئة البيانات المطلوبة</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            @include('maintenance.partials.step-indicator', ['currentStep' => $currentStep])

            <!-- Form Steps -->
            <div>
                <h3 class="ion-text-center">معلومات العميل</h3>

                <form method="POST" action="{{ route('maintenance.processStep2') }}">
                    @csrf

                    <ion-card class="form-card">
                        <ion-card-content>
                            <ion-item>
                                <ion-label position="floating">اسم الشركة / المؤسسة <span class="required">*</span></ion-label>
                                <ion-input name="companyName" value="{{ old('companyName', $companyName) }}" required></ion-input>
                            </ion-item>
                            @error('companyName') <div class="error-message">{{ $message }}</div> @enderror

                            <ion-item>
                                <ion-label position="floating">اسم المسؤول <span class="required">*</span></ion-label>
                                <ion-input name="contactName" value="{{ old('contactName', $contactName) }}" required></ion-input>
                            </ion-item>
                            @error('contactName') <div class="error-message">{{ $message }}</div> @enderror

                            <ion-item>
                                <ion-label position="floating">رقم الهاتف <span class="required">*</span></ion-label>
                                <ion-input name="phone" type="tel" value="{{ old('phone', $phone) }}" required></ion-input>
                            </ion-item>
                            @error('phone') <div class="error-message">{{ $message }}</div> @enderror

                            <ion-item>
                                <ion-label position="floating">البريد الإلكتروني <span class="required">*</span></ion-label>
                                <ion-input name="email" type="email" value="{{ old('email', $email) }}" required></ion-input>
                            </ion-item>
                            @error('email') <div class="error-message">{{ $message }}</div> @enderror

                            <ion-item>
                                <ion-label position="floating">العنوان <span class="required">*</span></ion-label>
                                <ion-textarea name="address" rows="3" value="{{ old('address', $address) }}"></ion-textarea>
                            </ion-item>
                            @error('address') <div class="error-message">{{ $message }}</div> @enderror
                        </ion-card-content>
                    </ion-card>

                    <!-- Navigation Buttons -->
                    <div class="form-navigation">
                        <ion-grid>
                            <ion-row>
                                <ion-col size="6">
                                    <a href="{{ route('maintenance.step1') }}">
                                        <ion-button expand="block" color="medium">
                                            <ion-icon name="arrow-back" slot="start"></ion-icon>
                                            السابق
                                        </ion-button>
                                    </a>
                                </ion-col>
                                <ion-col size="6">
                                    <ion-button expand="block" color="primary" type="submit">
                                        التالي
                                        <ion-icon name="arrow-forward" slot="end"></ion-icon>
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @include('maintenance.partials.style')
@endsection
