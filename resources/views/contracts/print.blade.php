<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract #{{ $contract->contract_number }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            font-size: 14px;
        }
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .company-info {
            text-align: right;
        }
        .document-title {
            text-align: center;
            margin: 20px 0;
            color: #222;
            font-size: 24px;
            font-weight: bold;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .info-group {
            margin-bottom: 15px;
        }
        .info-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        .info-value {
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        table th {
            background-color: #f5f5f5;
        }
        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .badge-primary { background-color: #4a6cf7; }
        .badge-secondary { background-color: #6c757d; }
        .badge-success { background-color: #28a745; }
        .badge-danger { background-color: #dc3545; }
        .badge-warning { background-color: #ffc107; color: #333; }
        .badge-info { background-color: #17a2b8; }
        .badge-gray { background-color: #6c757d; }

        .statistics {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }

        .amount {
            font-weight: bold;
            text-align: right;
        }

        .terms, .notes {
            white-space: pre-line;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        .signature-section {
            margin-top: 50px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
        }

        .signature-box {
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 50px;
            text-align: center;
        }

        @media print {
            body {
                padding: 0;
                background-color: white;
            }
            .print-container {
                border: none;
                box-shadow: none;
                padding: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
<div class="print-container">
    <div class="header">
        <div>
            <img src="{{ $company['logo'] }}" alt="Company Logo" class="logo">
        </div>
        <div class="company-info">
            <h3>{{ $company['name'] }}</h3>
            <p>{{ $company['address'] }}<br>
                Phone: {{ $company['phone'] }}<br>
                Email: {{ $company['email'] }}</p>
        </div>
    </div>

    <div class="document-title">
        Contract #{{ $contract->contract_number }}
    </div>

    <div class="section">
        <div class="section-title">Contract Information</div>
        <div class="grid">
            <div>
                <div class="info-group">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        @php
                            $statusColors = [
                                'active' => 'badge-success',
                                'pending' => 'badge-warning',
                                'expired' => 'badge-danger',
                                'terminated' => 'badge-secondary',
                            ];
                            $statusColor = $statusColors[$contract->status] ?? 'badge-gray';
                        @endphp
                        <span class="badge {{ $statusColor }}">
                                {{ ucfirst($contract->status) }}
                            </span>
                    </div>
                </div>
                <div class="info-group">
                    <div class="info-label">Contract Type</div>
                    <div class="info-value">{{ $contract->contractType->name ?? 'N/A' }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Start Date</div>
                    <div class="info-value">{{ $contract->start_date->format('F d, Y') }}</div>
                </div>
            </div>
            <div>
                <div class="info-group">
                    <div class="info-label">Contract Value</div>
                    <div class="info-value">SAR {{ number_format($contract->contract_value ?? 0, 2) }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">End Date</div>
                    <div class="info-value">{{ $contract->end_date->format('F d, Y') }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Visits Included</div>
                    <div class="info-value">{{ $contract->visits_included }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Client Information</div>
        <div class="grid">
            <div>
                <div class="info-group">
                    <div class="info-label">Client Name</div>
                    <div class="info-value">{{ $contract->client->name }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Client Phone</div>
                    <div class="info-value">{{ $contract->client->phone ?? 'N/A' }}</div>
                </div>
            </div>
            <div>
                <div class="info-group">
                    <div class="info-label">Client Email</div>
                    <div class="info-value">{{ $contract->client->email ?? 'N/A' }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Client Address</div>
                    <div class="info-value">{{ $contract->client->address ?? 'N/A' }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Visit Information</div>
        <div class="statistics">
            <div class="grid">
                <div>
                    <div class="info-group">
                        <div class="info-label">Total Visits Included</div>
                        <div class="info-value">{{ $contract->visits_included }}</div>
                    </div>
                </div>
                <div>
                    <div class="info-group">
                        <div class="info-label">Remaining Visits</div>
                        <div class="info-value">{{ $remainingVisits }}</div>
                    </div>
                </div>
                <div>
                    <div class="info-group">
                        <div class="info-label">Completed Visits</div>
                        <div class="info-value">{{ $completedVisits }}</div>
                    </div>
                </div>
                <div>
                    <div class="info-group">
                        <div class="info-label">Scheduled Visits</div>
                        <div class="info-value">{{ $scheduledVisits }}</div>
                    </div>
                </div>
            </div>
        </div>

        @if(count($contract->visits) > 0)
            <table class="mt-4">
                <thead>
                <tr>
                    <th>Scheduled For</th>
                    <th>Technician</th>
                    <th>Status</th>
                    <th>Completed At</th>
                </tr>
                </thead>
                <tbody>
                @foreach($contract->visits->take(10) as $visit)
                    <tr>
                        <td>{{ $visit->scheduled_at->format('F d, Y h:i A') }}</td>
                        <td>{{ $visit->technician ? $visit->technician->name : 'Not assigned' }}</td>
                        <td>
                            @php
                                $visitStatusColors = [
                                    'scheduled' => 'badge-primary',
                                    'in_progress' => 'badge-warning',
                                    'completed' => 'badge-success',
                                    'canceled' => 'badge-danger',
                                ];
                                $visitStatusColor = $visitStatusColors[$visit->status] ?? 'badge-gray';
                            @endphp
                            <span class="badge {{ $visitStatusColor }}">
                                {{ ucfirst($visit->status) }}
                            </span>
                        </td>
                        <td>{{ $visit->completed_at ? $visit->completed_at->format('F d, Y h:i A') : 'Not completed' }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            @if(count($contract->visits) > 10)
                <p><em>Note: Only showing the 10 most recent visits. The contract has {{ count($contract->visits) }} visits in total.</em></p>
            @endif
        @else
            <p>No visits have been scheduled for this contract yet.</p>
        @endif
    </div>

    @if($contract->terms)
        <div class="section">
            <div class="section-title">Contract Terms</div>
            <div class="terms">
                {{ $contract->terms }}
            </div>
        </div>
    @endif

    @if($contract->notes)
        <div class="section">
            <div class="section-title">Notes</div>
            <div class="notes">
                {{ $contract->notes }}
            </div>
        </div>
    @endif

    <div class="signature-section">
        <div>
            <div class="signature-box">
                Company Representative
            </div>
        </div>
        <div>
            <div class="signature-box">
                Client
            </div>
        </div>
    </div>

    <div class="footer">
        <p>This document was generated on {{ now()->format('F d, Y \a\t h:i A') }}</p>
        <p>{{ $company['name'] }} &copy; {{ date('Y') }}. All Rights Reserved.</p>
    </div>

    <div class="no-print" style="margin-top: 30px; text-align: center;">
        <button onclick="window.print()" style="padding: 10px 20px; background-color: #4a6cf7; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Print Contract
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>
</div>
</body>
</html>
