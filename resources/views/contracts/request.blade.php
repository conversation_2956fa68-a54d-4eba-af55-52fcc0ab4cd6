<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب عقد صيانة</title>
    <!-- Include Ionic CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ionic/core/css/ionic.bundle.css">
    <style>
        :root {
            --ion-color-primary: #3880ff;
            --ion-color-secondary: #3dc2ff;
            --ion-color-success: #2dd36f;
            --ion-font-family: '<PERSON><PERSON>wal', sans-serif;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            text-align: right;
        }

        .page-header {
            background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .step-indicator::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }

        .step.active .step-circle {
            background-color: var(--ion-color-primary);
            color: white;
        }

        .step-title {
            font-size: 14px;
            color: #666;
        }

        .step.active .step-title {
            color: var(--ion-color-primary);
            font-weight: bold;
        }

        .contract-type-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .contract-type-card.selected {
            border: 2px solid var(--ion-color-primary);
            box-shadow: 0 4px 12px rgba(56, 128, 255, 0.15);
        }

        .contract-type-card ion-card-header {
            padding-bottom: 8px;
        }

        .form-card {
            margin-bottom: 20px;
        }

        .footer {
            background-color: #f4f5f8;
            padding: 30px 0;
            text-align: center;
        }
    </style>
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
<ion-app>
    <!-- Header -->
    <ion-header translucent>
        <ion-toolbar color="primary">
            <ion-buttons slot="start">
                <ion-back-button defaultHref="{{ route('landing') }}"></ion-back-button>
            </ion-buttons>
            <ion-title>طلب عقد صيانة</ion-title>
        </ion-toolbar>
    </ion-header>

    <!-- Content -->
    <ion-content fullscreen>
        <!-- Page Header -->
        <div class="page-header">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <h1>عقود الصيانة</h1>
                        <p>اختر نوع العقد المناسب لاحتياجاتك وقم بتعبئة البيانات المطلوبة</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active">
                    <div class="step-circle">1</div>
                    <div class="step-title">اختيار نوع العقد</div>
                </div>
                <div class="step">
                    <div class="step-circle">2</div>
                    <div class="step-title">تفاصيل العميل</div>
                </div>
                <div class="step">
                    <div class="step-circle">3</div>
                    <div class="step-title">جدولة الزيارات</div>
                </div>
                <div class="step">
                    <div class="step-circle">4</div>
                    <div class="step-title">التأكيد والدفع</div>
                </div>
            </div>

            <!-- Form -->
            <form id="contract-request-form">
                <!-- Contract Type Selection -->
                <div class="form-section" id="contract-type-section">
                    <h2>اختر نوع العقد:</h2>

                    <ion-grid>
                        <ion-row>
                            <!-- Comprehensive Contract -->
                            <ion-col size="12" size-md="6">
                                <ion-card class="contract-type-card" data-contract-type="comprehensive">
                                    <ion-card-header>
                                        <ion-card-title>عقد صيانة شامل</ion-card-title>
                                        <ion-card-subtitle>تغطية شاملة لجميع الأنظمة</ion-card-subtitle>
                                    </ion-card-header>
                                    <ion-card-content>
                                        <div class="ion-text-center">
                                            <ion-icon name="shield-checkmark-outline" style="font-size: 40px; color: var(--ion-color-primary);"></ion-icon>
                                        </div>
                                        <ul class="features-list">
                                            <li>صيانة دورية شاملة</li>
                                            <li>تغطية جميع قطع الغيار</li>
                                            <li>استجابة خلال 24 ساعة</li>
                                            <li>تقارير فنية دورية</li>
                                            <li>زيارات غير محدودة</li>
                                        </ul>
                                    </ion-card-content>
                                </ion-card>
                            </ion-col>

                            <!-- Periodic Contract -->
                            <ion-col size="12" size-md="6">
                                <ion-card class="contract-type-card" data-contract-type="periodic">
                                    <ion-card-header>
                                        <ion-card-title>عقد صيانة دوري</ion-card-title>
                                        <ion-card-subtitle>زيارات دورية منتظمة</ion-card-subtitle>
                                    </ion-card-header>
                                    <ion-card-content>
                                        <div class="ion-text-center">
                                            <ion-icon name="calendar-outline" style="font-size: 40px; color: var(--ion-color-primary);"></ion-icon>
                                        </div>
                                        <ul class="features-list">
                                            <li>زيارات دورية مجدولة</li>
                                            <li>فحص وتنظيف كامل للأنظمة</li>
                                            <li>تشخيص الأعطال وإصلاحها</li>
                                            <li>قطع الغيار بتكلفة إضافية</li>
                                            <li>تقارير فنية بعد كل زيارة</li>
                                        </ul>
                                    </ion-card-content>
                                </ion-card>
                            </ion-col>

                            <!-- Preventive Contract -->
                            <ion-col size="12" size-md="6">
                                <ion-card class="contract-type-card" data-contract-type="preventive">
                                    <ion-card-header>
                                        <ion-card-title>عقد صيانة وقائي</ion-card-title>
                                        <ion-card-subtitle>للوقاية من الأعطال المستقبلية</ion-card-subtitle>
                                    </ion-card-header>
                                    <ion-card-content>
                                        <div class="ion-text-center">
                                            <ion-icon name="fitness-outline" style="font-size: 40px; color: var(--ion-color-primary);"></ion-icon>
                                        </div>
                                        <ul class="features-list">
                                            <li>فحص وقائي دوري</li>
                                            <li>كشف المشاكل المحتملة</li>
                                            <li>صيانة استباقية</li>
                                            <li>تحديثات برمجية</li>
                                            <li>تقارير الصيانة الوقائية</li>
                                        </ul>
                                    </ion-card-content>
                                </ion-card>
                            </ion-col>

                            <!-- One Visit Contract -->
                            <ion-col size="12" size-md="6">
                                <ion-card class="contract-type-card" data-contract-type="one_visit">
                                    <ion-card-header>
                                        <ion-card-title>عقد زيارة واحدة</ion-card-title>
                                        <ion-card-subtitle>زيارة صيانة شاملة لمرة واحدة</ion-card-subtitle>
                                    </ion-card-header>
                                    <ion-card-content>
                                        <div class="ion-text-center">
                                            <ion-icon name="timer-outline" style="font-size: 40px; color: var(--ion-color-primary);"></ion-icon>
                                        </div>
                                        <ul class="features-list">
                                            <li>زيارة صيانة شاملة واحدة</li>
                                            <li>فحص كامل للأنظمة</li>
                                            <li>تشخيص وإصلاح الأعطال</li>
                                            <li>تقرير فني مفصل</li>
                                            <li>توصيات للصيانة المستقبلية</li>
                                        </ul>
                                    </ion-card-content>
                                </ion-card>
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </div>

                <!-- Client Details Section (Initially Hidden) -->
                <div class="form-section" id="client-details-section" style="display: none;">
                    <h2>معلومات العميل:</h2>

                    <ion-card class="form-card">
                        <ion-card-header>
                            <ion-card-title>معلومات العميل الأساسية</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-item>
                                <ion-label position="floating">اسم الشركة / المؤسسة <ion-text color="danger">*</ion-text></ion-label>
                                <ion-input required type="text" name="company_name"></ion-input>
                            </ion-item>

                            <ion-item>
                                <ion-label position="floating">اسم المسؤول <ion-text color="danger">*</ion-text></ion-label>
                                <ion-input required type="text" name="contact_name"></ion-input>
                            </ion-item>

                            <ion-item>
                                <ion-label position="floating">رقم الهاتف <ion-text color="danger">*</ion-text></ion-label>
                                <ion-input required type="tel" name="phone" pattern="[0-9]{10}"></ion-input>
                                <ion-note slot="helper">مثال: 05xxxxxxxx</ion-note>
                            </ion-item>

                            <ion-item>
                                <ion-label position="floating">البريد الإلكتروني <ion-text color="danger">*</ion-text></ion-label>
                                <ion-input required type="email" name="email"></ion-input>
                            </ion-item>

                            <ion-item>
                                <ion-label position="floating">العنوان <ion-text color="danger">*</ion-text></ion-label>
                                <ion-textarea required name="address" rows="2"></ion-textarea>
                            </ion-item>
                        </ion-card-content>
                    </ion-card>

                    <ion-card class="form-card">
                        <ion-card-header>
                            <ion-card-title>تفاصيل العقد</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-item>
                                <ion-label>تاريخ بداية العقد <ion-text color="danger">*</ion-text></ion-label>
                                <ion-datetime required display-format="DD/MM/YYYY" name="start_date" min="{{ date('Y-m-d') }}"></ion-datetime>
                            </ion-item>

                            <ion-item>
                                <ion-label>مدة العقد <ion-text color="danger">*</ion-text></ion-label>
                                <ion-select required name="contract_duration" interface="popover">
                                    <ion-select-option value="3">3 أشهر</ion-select-option>
                                    <ion-select-option value="6">6 أشهر</ion-select-option>
                                    <ion-select-option value="12">سنة واحدة</ion-select-option>
                                    <ion-select-option value="24">سنتان</ion-select-option>
                                </ion-select>
                            </ion-item>

                            <ion-item>
                                <ion-label>طريقة الدفع <ion-text color="danger">*</ion-text></ion-label>
                                <ion-select required name="payment_method" interface="popover">
                                    <ion-select-option value="cash">نقداً</ion-select-option>
                                    <ion-select-option value="bank_transfer">تحويل بنكي</ion-select-option>
                                    <ion-select-option value="credit_card">بطاقة ائتمان</ion-select-option>
                                    <ion-select-option value="check">شيك</ion-select-option>
                                </ion-select>
                            </ion-item>

                            <ion-item>
                                <ion-label>تكرار الدفع <ion-text color="danger">*</ion-text></ion-label>
                                <ion-select required name="payment_frequency" interface="popover">
                                    <ion-select-option value="one_time">دفعة واحدة</ion-select-option>
                                    <ion-select-option value="monthly">شهري</ion-select-option>
                                    <ion-select-option value="quarterly">ربع سنوي</ion-select-option>
                                    <ion-select-option value="biannually">نصف سنوي</ion-select-option>
                                    <ion-select-option value="annually">سنوي</ion-select-option>
                                </ion-select>
                            </ion-item>

                            <ion-item>
                                <ion-checkbox slot="start" name="auto_renewal"></ion-checkbox>
                                <ion-label>تجديد تلقائي للعقد</ion-label>
                            </ion-item>
                        </ion-card-content>
                    </ion-card>
                </div>

                <!-- Visits Schedule Section (Initially Hidden) -->
                <div class="form-section" id="visits-schedule-section" style="display: none;">
                    <h2>جدولة الزيارات:</h2>

                    <ion-card class="form-card">
                        <ion-card-header>
                            <ion-card-title>جدول الزيارات الدورية</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-item>
                                <ion-label>تكرار الزيارات <ion-text color="danger">*</ion-text></ion-label>
                                <ion-select required name="visit_frequency" interface="popover" id="visit-frequency">
                                    <ion-select-option value="weekly">أسبوعي</ion-select-option>
                                    <ion-select-option value="monthly">شهري</ion-select-option>
                                    <ion-select-option value="quarterly">ربع سنوي</ion-select-option>
                                    <ion-select-option value="biannually">نصف سنوي</ion-select-option>
                                    <ion-select-option value="annually">سنوي</ion-select-option>
                                    <ion-select-option value="custom">مخصص</ion-select-option>
                                </ion-select>
                            </ion-item>

                            <!-- Weekly Options -->
                            <div id="weekly-options" class="schedule-options" style="display: none;">
                                <ion-item>
                                    <ion-label>يوم الزيارة <ion-text color="danger">*</ion-text></ion-label>
                                    <ion-select name="day_of_week" interface="popover">
                                        <ion-select-option value="1">الأحد</ion-select-option>
                                        <ion-select-option value="2">الإثنين</ion-select-option>
                                        <ion-select-option value="3">الثلاثاء</ion-select-option>
                                        <ion-select-option value="4">الأربعاء</ion-select-option>
                                        <ion-select-option value="5">الخميس</ion-select-option>
                                        <ion-select-option value="6">الجمعة</ion-select-option>
                                        <ion-select-option value="7">السبت</ion-select-option>
                                    </ion-select>
                                </ion-item>
                            </div>

                            <!-- Monthly Options -->
                            <div id="monthly-options" class="schedule-options" style="display: none;">
                                <ion-item>
                                    <ion-label>يوم الشهر <ion-text color="danger">*</ion-text></ion-label>
                                    <ion-select name="day_of_month" interface="popover">
                                        <!-- Generate options for days 1-31 -->
                                        @for ($i = 1; $i <= 31; $i++)
                                            <ion-select-option value="{{ $i }}">{{ $i }}</ion-select-option>
                                        @endfor
                                    </ion-select>
                                </ion-item>
                            </div>

                            <!-- Custom Pattern -->
                            <div id="custom-options" class="schedule-options" style="display: none;">
                                <ion-item>
                                    <ion-label position="floating">نمط مخصص <ion-text color="danger">*</ion-text></ion-label>
                                    <ion-textarea name="custom_pattern" rows="2"></ion-textarea>
                                    <ion-note slot="helper">مثال: أول يوم أحد من كل شهر، أو كل 45 يوم، الخ</ion-note>
                                </ion-item>
                            </div>

                            <ion-item>
                                <ion-label>الوقت المفضل للزيارة <ion-text color="danger">*</ion-text></ion-label>
                                <ion-datetime display-format="HH:mm" picker-format="HH:mm" name="preferred_time"></ion-datetime>
                            </ion-item>

                            <ion-item>
                                <ion-label position="floating">ملاحظات إضافية للجدولة</ion-label>
                                <ion-textarea name="schedule_notes" rows="2"></ion-textarea>
                            </ion-item>
                        </ion-card-content>
                    </ion-card>
                </div>

                <!-- Confirmation Section (Initially Hidden) -->
                <div class="form-section" id="confirmation-section" style="display: none;">
                    <h2>تأكيد طلب العقد:</h2>

                    <ion-card class="form-card">
                        <ion-card-header>
                            <ion-card-title>ملخص العقد</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <div id="contract-summary">
                                <!-- Will be filled dynamically with JavaScript -->
                            </div>

                            <ion-item lines="none">
                                <ion-checkbox slot="start" name="terms_agreement" required></ion-checkbox>
                                <ion-label>أوافق على <a href="#">الشروط والأحكام</a> الخاصة بالعقد</ion-label>
                            </ion-item>
                        </ion-card-content>
                    </ion-card>
                </div>

                <!-- Navigation Buttons -->
                <div class="ion-padding">
                    <ion-grid>
                        <ion-row>
                            <ion-col size="6">
                                <ion-button expand="block" color="medium" id="prev-btn" style="display: none;">
                                    <ion-icon name="arrow-back-outline" slot="start"></ion-icon>
                                    السابق
                                </ion-button>
                            </ion-col>
                            <ion-col size="6">
                                <ion-button expand="block" color="primary" id="next-btn">
                                    التالي
                                    <ion-icon name="arrow-forward-outline" slot="end"></ion-icon>
                                </ion-button>
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </div>
            </form>
        </div>

        <!-- Footer -->
        <ion-footer class="footer">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <p>© {{ date('Y') }} نظام إدارة عقود الصيانة. جميع الحقوق محفوظة.</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-footer>
    </ion-content>
</ion-app>

<!-- Ionic Framework JS -->
<script type="module" src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.esm.js"></script>
<script nomodule src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.js"></script>

<!-- Custom JavaScript -->
<script>
    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        let currentStep = 0;
        const steps = ['contract-type-section', 'client-details-section', 'visits-schedule-section', 'confirmation-section'];
        const stepIndicators = document.querySelectorAll('.step');
        let selectedContractType = null;

        // Contract type selection
        const contractTypeCards = document.querySelectorAll('.contract-type-card');
        contractTypeCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                contractTypeCards.forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Store selected contract type
                selectedContractType = this.getAttribute('data-contract-type');
            });
        });

        // Visit frequency change handler
        document.getElementById('visit-frequency').addEventListener('ionChange', function(event) {
            const value = event.detail.value;

            // Hide all schedule options
            document.querySelectorAll('.schedule-options').forEach(el => {
                el.style.display = 'none';
            });

            // Show relevant options based on selection
            if (value === 'weekly') {
                document.getElementById('weekly-options').style.display = 'block';
            } else if (value === 'monthly') {
                document.getElementById('monthly-options').style.display = 'block';
            } else if (value === 'custom') {
                document.getElementById('custom-options').style.display = 'block';
            }
        });

        // Next button handler
        document.getElementById('next-btn').addEventListener('click', function() {
            if (currentStep === 0 && !selectedContractType) {
                // Show alert if no contract type selected
                const alertElement = document.createElement('ion-alert');
                alertElement.header = 'تنبيه';
                alertElement.message = 'الرجاء اختيار نوع العقد';
                alertElement.buttons = ['موافق'];
                document.body.appendChild(alertElement);
                alertElement.present();
                return;
            }

            if (currentStep < steps.length - 1) {
                // Hide current step
                document.getElementById(steps[currentStep]).style.display = 'none';

                // Update step indicators
                stepIndicators[currentStep].classList.remove('active');

                // Move to next step
                currentStep++;

                // Show next step
                document.getElementById(steps[currentStep]).style.display = 'block';

                // Update step indicators
                stepIndicators[currentStep].classList.add('active');

                // Show previous button
                document.getElementById('prev-btn').style.display = 'block';

                // Update next button text on last step
                if (currentStep === steps.length - 1) {
                    this.textContent = 'تأكيد وإرسال';
                    this.innerHTML = 'تأكيد وإرسال <ion-icon name="checkmark-outline" slot="end"></ion-icon>';

                    // Generate contract summary
                    generateContractSummary();
                }
            } else {
                // Submit the form
                submitContractRequest();
            }
        });

        // Previous button handler
        document.getElementById('prev-btn').addEventListener('click', function() {
            if (currentStep > 0) {
                // Hide current step
                document.getElementById(steps[currentStep]).style.display = 'none';

                // Update step indicators
                stepIndicators[currentStep].classList.remove('active');

                // Move to previous step
                currentStep--;

                // Show previous step
                document.getElementById(steps[currentStep]).style.display = 'block';

                // Update step indicators
                stepIndicators[currentStep].classList.add('active');

                // Hide previous button on first step
                if (currentStep === 0) {
                    this.style.display = 'none';
                }

                // Reset next button text if not on last step
                if (currentStep < steps.length - 1) {
                    const nextBtn = document.getElementById('next-btn');
                    nextBtn.textContent = 'التالي';
                    nextBtn.innerHTML = 'التالي <ion-icon name="arrow-forward-outline" slot="end"></ion-icon>';
                }
            }
        });

        // Generate contract summary
        function generateContractSummary() {
            const form = document.getElementById('contract-request-form');
            const contractTypeCard = document.querySelector('.contract-type-card.selected');

            // Get contract type name
            const contractTypeName = contractTypeCard.querySelector('ion-card-title').textContent;

            // Gather form data
            const companyName = form.elements['company_name'].value;
            const contactName = form.elements['contact_name'].value;
            const startDate = form.elements['start_date'].value;
            const duration = form.elements['contract_duration'].value;
            const visitFrequency = form.elements['visit_frequency'].value;

            // Create summary HTML
            let summaryHTML = `
                    <ion-list>
                        <ion-item>
                            <ion-label>
                                <h2>نوع العقد</h2>
                                <p>${contractTypeName}</p>
                            </ion-label>
                        </ion-item>
                        <ion-item>
                            <ion-label>
                                <h2>اسم الشركة / المؤسسة</h2>
                                <p>${companyName}</p>
                            </ion-label>
                        </ion-item>
                        <ion-item>
                            <ion-label>
                                <h2>اسم المسؤول</h2>
                                <p>${contactName}</p>
                            </ion-label>
                        </ion-item>
                        <ion-item>
                            <ion-label>
                                <h2>تاريخ بداية العقد</h2>
                                <p>${startDate}</p>
                            </ion-label>
                        </ion-item>
                        <ion-item>
                            <ion-label>
                                <h2>مدة العقد</h2>
                                <p>${duration} شهر</p>
                            </ion-label>
                        </ion-item>
                        <ion-item>
                            <ion-label>
                                <h2>تكرار الزيارات</h2>
                                <p>${getVisitFrequencyName(visitFrequency)}</p>
                            </ion-label>
                        </ion-item>
                    </ion-list>

                    <div class="ion-text-center ion-padding">
                        <h2>التكلفة التقديرية</h2>
                        <h1 style="color: var(--ion-color-primary);">٣٠٠٠ ريال</h1>
                        <p>*قد تختلف التكلفة النهائية بناءً على التفاصيل الدقيقة للعقد</p>
                    </div>
                `;

            // Update the summary container
            document.getElementById('contract-summary').innerHTML = summaryHTML;
        }

        // Helper function to get visit frequency name in Arabic
        function getVisitFrequencyName(frequency) {
            const frequencies = {
                'weekly': 'أسبوعي',
                'monthly': 'شهري',
                'quarterly': 'ربع سنوي',
                'biannually': 'نصف سنوي',
                'annually': 'سنوي',
                'custom': 'مخصص'
            };

            return frequencies[frequency] || frequency;
        }

        // Submit contract request
        function submitContractRequest() {
            // Show loading indicator
            const loadingElement = document.createElement('ion-loading');
            loadingElement.message = 'جاري تقديم طلب العقد...';
            loadingElement.duration = 2000;
            document.body.appendChild(loadingElement);
            loadingElement.present();

            // Simulate form submission
            setTimeout(function() {
                // Show success message
                const alertElement = document.createElement('ion-alert');
                alertElement.header = 'تم تقديم طلب العقد بنجاح';
                alertElement.message = 'تم استلام طلب العقد بنجاح وسيتم التواصل معك قريباً لاستكمال الإجراءات.';
                alertElement.buttons = [
                    {
                        text: 'العودة للرئيسية',
                        handler: () => {
                            window.location.href = "{{ route('landing') }}";
                        }
                    }
                ];
                document.body.appendChild(alertElement);
                alertElement.present();
            }, 2000);
        }
    });
</script>
</body>
</html>
