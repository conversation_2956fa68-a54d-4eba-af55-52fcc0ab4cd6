@extends('layouts.app')

@section('content')
    <div>
        <ion-content fullscreen>
            <!-- Page Header -->
            <div class="page-header">
                <ion-grid>
                    <ion-row>
                        <ion-col size="12">
                            <h1>تم تقديم طلب العقد بنجاح</h1>
                            <p>شكراً لك على تقديم طلب عقد الصيانة</p>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </div>

            <div class="success-container">
                <ion-card class="success-card">
                    <ion-card-content>
                        <div class="success-icon">
                            <ion-icon name="checkmark-circle"></ion-icon>
                        </div>
                        <h2>تم تقديم طلب عقد الصيانة بنجاح!</h2>
                        <p>رقم العقد: {{ $contract }}</p>
                        <p>سيتم التواصل معك قريباً لاستكمال إجراءات العقد.</p>

                        <div class="ion-padding">
                            <ion-button expand="block" color="primary" href="{{ route('landing') }}">
                                العودة للصفحة الرئيسية
                            </ion-button>
                        </div>
                    </ion-card-content>
                </ion-card>
            </div>
        </ion-content>
    </div>

    <style>
        .success-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .success-card {
            text-align: center;
            padding: 30px;
        }

        .success-icon {
            font-size: 80px;
            color: var(--ion-color-success);
            margin-bottom: 20px;
        }
    </style>
@endsection
