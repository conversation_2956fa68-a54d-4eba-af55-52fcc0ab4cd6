<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance Request #{{ $request->request_number }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            font-size: 14px;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }

        .logo {
            max-width: 150px;
            height: auto;
        }

        .company-info {
            text-align: right;
        }

        .document-title {
            text-align: center;
            margin: 20px 0;
            color: #222;
            font-size: 24px;
            font-weight: bold;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .info-group {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }

        .info-value {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th, table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        table th {
            background-color: #f5f5f5;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .badge-primary {
            background-color: #4a6cf7;
        }

        .badge-secondary {
            background-color: #6c757d;
        }

        .badge-success {
            background-color: #28a745;
        }

        .badge-danger {
            background-color: #dc3545;
        }

        .badge-warning {
            background-color: #ffc107;
            color: #333;
        }

        .badge-info {
            background-color: #17a2b8;
        }

        .badge-gray {
            background-color: #6c757d;
        }

        .financial-summary {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }

        .amount {
            font-weight: bold;
            text-align: right;
        }

        .notes {
            white-space: pre-line;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        @media print {
            body {
                padding: 0;
                background-color: white;
            }

            .print-container {
                border: none;
                box-shadow: none;
                padding: 0;
            }

            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
<div class="print-container">
    <div class="header">
        <div>
            <img src="{{ $company['logo'] }}" alt="Company Logo" class="logo">
        </div>
        <div class="company-info">
            <h3>{{ $company['name'] }}</h3>
            <p>{{ $company['address'] }}<br>
                Phone: {{ $company['phone'] }}<br>
                Email: {{ $company['email'] }}</p>
        </div>
    </div>

    <div class="document-title">
        Maintenance Request #{{ $request->request_number }}
    </div>

    <div class="section">
        <div class="section-title">Request Information</div>
        <div class="grid">
            <div>
                <div class="info-group">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        @php
                            $statusColors = [
                                'new' => 'badge-primary',
                                'assigned' => 'badge-secondary',
                                'in_progress' => 'badge-warning',
                                'paid' => 'badge-info',
                                'completed' => 'badge-success',
                                'canceled' => 'badge-danger',
                            ];
                            $statusColor = $statusColors[$request->status] ?? 'badge-gray';
                        @endphp
                        <span class="badge {{ $statusColor }}">
                                {{ ucfirst($request->status) }}
                            </span>
                    </div>
                </div>
                <div class="info-group">
                    <div class="info-label">Request Date</div>
                    <div class="info-value">{{ $request->created_at->format('F d, Y') }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Target Completion Date</div>
                    <div class="info-value">
                        {{ $request->completed_at ? $request->completed_at->format('F d, Y') : 'Not set' }}
                    </div>
                </div>
            </div>
            <div>
                <div class="info-group">
                    <div class="info-label">Assigned Technician</div>
                    <div class="info-value">
                        {{ $request->assignedTechnician ? $request->assignedTechnician->name : 'Not assigned yet' }}
                    </div>
                </div>
                <div class="info-group">
                    <div class="info-label">Actual Completion Date</div>
                    <div class="info-value">
                        {{ $request->completed_at ? $request->completed_at->format('F d, Y') : 'Not completed yet' }}
                    </div>
                </div>
                <div class="info-group">
                    <div class="info-label">Visits Included</div>
                    <div class="info-value">{{ $request->visits_included ?? 0 }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Client Information</div>
        <div class="grid">
            <div>
                <div class="info-group">
                    <div class="info-label">Client Name</div>
                    <div class="info-value">{{ $request->client->name }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Client Phone</div>
                    <div class="info-value">{{ $request->client->phone ?? 'N/A' }}</div>
                </div>
            </div>
            <div>
                <div class="info-group">
                    <div class="info-label">Client Email</div>
                    <div class="info-value">{{ $request->client->email ?? 'N/A' }}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Client Address</div>
                    <div class="info-value">{{ $request->client->address ?? 'N/A' }}</div>
                </div>
            </div>
        </div>
    </div>

    @if($request->contract)
        <div class="section">
            <div class="section-title">Contract Information</div>
            <div class="grid">
                <div>
                    <div class="info-group">
                        <div class="info-label">Contract Number</div>
                        <div class="info-value">{{ $request->contract->contract_number }}</div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Contract Type</div>
                        <div class="info-value">{{ $request->contract_type->name ?? 'N/A' }}</div>
                    </div>
                </div>
                <div>
                    <div class="info-group">
                        <div class="info-label">Start Date</div>
                        <div class="info-value">
                            {{ $request->contract->start_date ? $request->contract->start_date->format('F d, Y') : 'N/A' }}
                        </div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">End Date</div>
                        <div class="info-value">
                            {{ $request->contract->end_date ? $request->contract->end_date->format('F d, Y') : 'N/A' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="section">
        <div class="section-title">Financial Information</div>
        <table>
            <tr>
                <th>Description</th>
                <th>Amount</th>
            </tr>
            <tr>
                <td>Price</td>
                <td class="amount">${{ number_format($request->price ?? 0, 2) }}</td>
            </tr>
        </table>

        @if(count($request->payments) > 0)
            <div class="section-title">Payment Records</div>
            <table>
                <tr>
                    <th>Payment Number</th>
                    <th>Date</th>
                    <th>Amount</th>
                    <th>Status</th>
                </tr>
                @foreach($request->payments as $payment)
                    <tr>
                        <td>{{ $payment->payment_number }}</td>
                        <td>{{ $payment->created_at->format('F d, Y') }}</td>
                        <td class="amount">${{ number_format($payment->amount, 2) }}</td>
                        <td>
                            @php
                                $paymentStatusColors = [
                                    'pending' => 'badge-warning',
                                    'paid' => 'badge-success',
                                    'overdue' => 'badge-danger',
                                    'canceled' => 'badge-danger',
                                ];
                                $paymentStatusColor = $paymentStatusColors[$payment->status] ?? 'badge-gray';
                            @endphp
                            <span class="badge {{ $paymentStatusColor }}">
                            {{ ucfirst($payment->status) }}
                        </span>
                        </td>
                    </tr>
                @endforeach
            </table>

            <div class="financial-summary">
                <div class="grid">
                    <div>
                        <div class="info-group">
                            <div class="info-label">Total Amount</div>
                            <div class="info-value">${{ number_format($totalAmount, 2) }}</div>
                        </div>
                    </div>
                    <div>
                        <div class="info-group">
                            <div class="info-label">Amount Paid</div>
                            <div class="info-value">${{ number_format($paidAmount, 2) }}</div>
                        </div>
                    </div>
                    <div>
                        <div class="info-group">
                            <div class="info-label">Amount Pending</div>
                            <div class="info-value">${{ number_format($pendingAmount, 2) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    @if($request->notes)
        <div class="section">
            <div class="section-title">Notes</div>
            <div class="notes">
                {{ $request->notes }}
            </div>
        </div>
    @endif

    <div class="footer">
        <p>This document was generated on {{ now()->format('F d, Y \a\t h:i A') }}</p>
        <p>{{ $company['name'] }} &copy; {{ date('Y') }}. All Rights Reserved.</p>
    </div>

    <div class="no-print" style="margin-top: 30px; text-align: center;">
        <button onclick="window.print()"
                style="padding: 10px 20px; background-color: #4a6cf7; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Print Document
        </button>
        <button onclick="window.close()"
                style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>
</div>
</body>
</html>
