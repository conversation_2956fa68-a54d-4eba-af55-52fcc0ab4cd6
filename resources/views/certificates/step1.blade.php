@extends('certificates.wizard.layout')

@section('wizard-content')
    <form action="{{ route('certificate.processStep1') }}" method="POST">
        @csrf
        <ion-card class="form-card">
            <ion-card-header>
                <ion-card-title>معلومات مقدم الطلب</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-item>
                    <ion-label position="floating">الاسم الكامل
                        <span class="required">*</span>
                    </ion-label>
                    <ion-input name="fullName" value="{{ old('fullName', $data['fullName']) }}" required></ion-input>
                    @error('fullName')
                    <div class="error-message">{{ $message }}</div>
                    @enderror
                </ion-item>

                <ion-item>
                    <ion-label position="floating">رقم الهاتف
                        <span class="required">*</span>
                    </ion-label>
                    <ion-input name="phone" value="{{ old('phone', $data['phone']) }}" required></ion-input>
                    <ion-note slot="helper">مثال: 05xxxxxxxx</ion-note>
                    @error('phone')
                    <div class="error-message">{{ $message }}</div>
                    @enderror
                </ion-item>

                <ion-item>
                    <ion-label position="floating">البريد الإلكتروني
                        <span class="required">*</span>
                    </ion-label>
                    <ion-input name="email" type="email" value="{{ old('email', $data['email']) }}" required></ion-input>
                    @error('email')
                    <div class="error-message">{{ $message }}</div>
                    @enderror
                </ion-item>

                <ion-item>
                    <ion-label position="floating">الشركة / المؤسسة</ion-label>
                    <ion-input name="company" value="{{ old('company', $data['company']) }}"></ion-input>
                </ion-item>

                <ion-item>
                    <ion-label position="floating">العنوان</ion-label>
                    <ion-textarea name="address" rows="2"
                                  value="{{ old('address', $data['address']) }}"></ion-textarea>
                </ion-item>
            </ion-card-content>
        </ion-card>

        <div class="form-navigation">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <ion-button expand="block" color="primary" type="submit">
                            التالي
                            <ion-icon name="arrow-forward" slot="end"></ion-icon>
                        </ion-button>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>
    </form>
@endsection
