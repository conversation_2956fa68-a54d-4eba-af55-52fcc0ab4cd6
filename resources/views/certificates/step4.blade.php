@extends('certificates.wizard.layout')

@section('wizard-content')
    <form action="{{ route('certificate.submit') }}" method="POST">
        @csrf
        <ion-card class="form-card">
            <ion-card-header>
                <ion-card-title>مراجعة وتأكيد الطلب</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-list>
                    <ion-list-header>
                        <ion-label>معلومات مقدم الطلب</ion-label>
                    </ion-list-header>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">الاسم الكامل</h2>
                            <p class="info-value">{{ $requestSummary['clientInfo']['fullName'] }}</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">رقم الهاتف</h2>
                            <p class="info-value">{{ $requestSummary['clientInfo']['phone'] }}</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">البريد الإلكتروني</h2>
                            <p class="info-value">{{ $requestSummary['clientInfo']['email'] }}</p>
                        </ion-label>
                    </ion-item>
                    @if(!empty($requestSummary['clientInfo']['company']))
                        <ion-item>
                            <ion-label>
                                <h2 class="info-label">الشركة / المؤسسة</h2>
                                <p class="info-value">{{ $requestSummary['clientInfo']['company'] }}</p>
                            </ion-label>
                        </ion-item>
                    @endif

                    <ion-list-header>
                        <ion-label>تفاصيل الطلب</ion-label>
                    </ion-list-header>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">عنوان الطلب</h2>
                            <p class="info-value">{{ $requestSummary['requestDetails']['title'] }}</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">الوصف</h2>
                            <p class="info-value">{{ $requestSummary['requestDetails']['description'] }}</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">الأولوية</h2>
                            <p class="info-value">{{ $requestSummary['requestDetails']['priority'] }}</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">تاريخ الفحص المفضل</h2>
                            <p class="info-value">{{ $requestSummary['requestDetails']['preferredDate'] }}</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">المرفقات</h2>
                            <p class="info-value">{{ $requestSummary['attachments'] }}</p>
                        </ion-label>
                    </ion-item>
                </ion-list>
            </ion-card-content>
        </ion-card>

        <ion-card class="form-card contract-value-card">
            <ion-card-header>
                <ion-card-title>رسوم الخدمة</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-list>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">رسوم الفحص</h2>
                            <p class="info-value">{{ $requestSummary['fees']['inspectionFee'] }} ريال</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label>
                            <h2 class="info-label">رسوم إصدار الشهادة</h2>
                            <p class="info-value">{{ $requestSummary['fees']['certificateFee'] }} ريال</p>
                        </ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label class="price-total">
                            <h2 class="info-label">إجمالي الرسوم</h2>
                            <div class="contract-price">{{ $requestSummary['fees']['totalFee'] }} ريال</div>
                        </ion-label>
                    </ion-item>
                </ion-list>
            </ion-card-content>
        </ion-card>

        <ion-item lines="none" class="terms-agreement">
            <ion-checkbox slot="start" name="termsAgreement" value="1"></ion-checkbox>
            <ion-label>أوافق على <a href="#" onclick="showTerms()">الشروط والأحكام</a> الخاصة بالخدمة</ion-label>
        </ion-item>
        @error('termsAgreement')
        <div class="error-message">{{ $message }}</div>
        @enderror

        <div class="form-navigation">
            <ion-grid>
                <ion-row>
                    <ion-col size="6">
                        <a href="{{ route('certificate.step3') }}">
                            <ion-button expand="block" color="medium">
                                <ion-icon name="arrow-back" slot="start"></ion-icon>
                                السابق
                            </ion-button>
                        </a>
                    </ion-col>
                    <ion-col size="6">
                        <ion-button expand="block" color="success" type="submit">
                            تأكيد وإرسال
                            <ion-icon name="checkmark" slot="end"></ion-icon>
                        </ion-button>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>
    </form>

    @push('scripts')
        <script>
            function showTerms() {
                const alertElement = document.createElement('ion-alert');
                alertElement.header = 'الشروط والأحكام';
                alertElement.message = `
            <p>1. يقر العميل بأن جميع المعلومات المقدمة في هذا الطلب صحيحة ودقيقة.</p>
            <p>2. يوافق العميل على دفع الرسوم المحددة لخدمة استخراج شهادة الفحص.</p>
            <p>3. يتم تحديد موعد الفحص بالتنسيق مع العميل خلال 48 ساعة من تقديم الطلب.</p>
            <p>4. في حال عدم اجتياز الفحص، يمكن للعميل طلب إعادة الفحص مقابل رسوم إضافية.</p>
            <p>5. تصدر الشهادة خلال 5 أيام عمل من تاريخ اجتياز الفحص.</p>
            <p>6. تكون شهادة الفحص سارية المفعول لمدة سنة واحدة من تاريخ إصدارها.</p>
        `;
                alertElement.buttons = ['موافق'];

                document.body.appendChild(alertElement);
                alertElement.present();
            }
        </script>
    @endpush
@endsection
