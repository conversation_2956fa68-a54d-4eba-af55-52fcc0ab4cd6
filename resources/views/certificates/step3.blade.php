@extends('certificates.wizard.layout')

@section('wizard-content')
    <form action="{{ route('certificate.processStep3') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <ion-card class="form-card">
            <ion-card-header>
                <ion-card-title>المرفقات والملاحظات</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-item>
                    <ion-label position="stacked">إرفاق ملفات (اختياري)</ion-label>
                    <div class="file-upload-container">
                        <input type="file" name="attachments[]" multiple class="file-input" id="attachments">
                        <label for="attachments" class="file-label">
                            <ion-icon name="cloud-upload-outline"></ion-icon>
                            اختر الملفات أو اسحبها هنا
                        </label>
                    </div>
                    @error('attachments.*')
                    <div class="error-message">{{ $message }}</div>
                    @enderror

                    @if(!empty($data['attachments']))
                        <div class="attached-files">
                            <p>الملفات المرفقة:</p>
                            <ul>
                                @foreach($data['attachments'] as $attachment)
                                    <li>{{ $attachment['name'] ?? 'ملف مرفق' }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </ion-item>

                <ion-item>
                    <ion-label position="floating">ملاحظات إضافية (اختياري)</ion-label>
                    <ion-textarea name="notes" rows="3" value="{{ old('notes', $data['notes']) }}"></ion-textarea>
                </ion-item>
            </ion-card-content>
        </ion-card>

        <div class="visits-info">
            <p>
                <ion-icon name="information-circle-outline"></ion-icon>
                يمكنك إرفاق أي مستندات أو صور متعلقة بطلب الفحص لتسهيل عملية إصدار الشهادة.
            </p>
        </div>

        <div class="form-navigation">
            <ion-grid>
                <ion-row>
                    <ion-col size="6">
                        <a href="{{ route('certificate.step2') }}">
                            <ion-button expand="block" color="medium">
                                <ion-icon name="arrow-back" slot="start"></ion-icon>
                                السابق
                            </ion-button>
                        </a>
                    </ion-col>
                    <ion-col size="6">
                        <ion-button expand="block" color="primary" type="submit">
                            التالي
                            <ion-icon name="arrow-forward" slot="end"></ion-icon>
                        </ion-button>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>
    </form>
@endsection
