@extends('certificates.wizard.layout')

@section('wizard-content')
    <form action="{{ route('certificate.processStep2') }}" method="POST">
        @csrf
        <ion-card class="form-card">
            <ion-card-header>
                <ion-card-title>تفاصيل الطلب</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-item>
                    <ion-label position="floating">عنوان الطلب
                        <span class="required">*</span>
                    </ion-label>
                    <ion-input name="title" value="{{ old('title', $data['title']) }}" required></ion-input>
                    @error('title')
                    <div class="error-message">{{ $message }}</div>
                    @enderror
                </ion-item>

                <ion-item>
                    <ion-label position="floating">وصف الطلب
                        <span class="required">*</span>
                    </ion-label>
                    <ion-textarea name="description" rows="4" required value="{{ old('description', $data['description']) }}"></ion-textarea>
                    @error('description')
                    <div class="error-message">{{ $message }}</div>
                    @enderror
                </ion-item>



                <ion-item>
                    <ion-label>تاريخ الفحص المفضل
                        <span class="required">*</span>
                    </ion-label>
                    <ion-input name="preferredDate" type="date" value="{{ old('preferredDate', $data['preferredDate']) }}" required></ion-input>
                    @error('preferredDate')
                    <div class="error-message">{{ $message }}</div>
                    @enderror
                </ion-item>
            </ion-card-content>
        </ion-card>

        <div class="visits-info">
            <p>
                <ion-icon name="information-circle-outline"></ion-icon>
                سيتم تحديد موعد الفحص بالتنسيق معك خلال 48 ساعة من تقديم الطلب.
            </p>
        </div>

        <div class="form-navigation">
            <ion-grid>
                <ion-row>
                    <ion-col size="6">
                        <a href="{{ route('certificate.step1') }}">
                            <ion-button expand="block" color="medium">
                                <ion-icon name="arrow-back" slot="start"></ion-icon>
                                السابق
                            </ion-button>
                        </a>
                    </ion-col>
                    <ion-col size="6">
                        <ion-button expand="block" color="primary" type="submit">
                            التالي
                            <ion-icon name="arrow-forward" slot="end"></ion-icon>
                        </ion-button>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>
    </form>
@endsection
