<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب شهادة فحص</title>
    <!-- Include Ionic CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ionic/core/css/ionic.bundle.css">
    <style>
        :root {
            --ion-color-primary: #3880ff;
            --ion-color-secondary: #3dc2ff;
            --ion-color-success: #2dd36f;
            --ion-font-family: '<PERSON><PERSON>wal', sans-serif;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            text-align: right;
        }

        .page-header {
            background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .step-indicator::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }

        .step.active .step-circle {
            background-color: var(--ion-color-primary);
            color: white;
        }

        .step-title {
            font-size: 14px;
            color: #666;
        }

        .step.active .step-title {
            color: var(--ion-color-primary);
            font-weight: bold;
        }

        .form-section {
            margin-bottom: 20px;
        }

        .form-card {
            margin-bottom: 20px;
        }

        .footer {
            background-color: #f4f5f8;
            padding: 30px 0;
            text-align: center;
        }
    </style>
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
<ion-app>
    <!-- Header -->
    <ion-header translucent>
        <ion-toolbar color="primary">
            <ion-buttons slot="start">
                <ion-back-button defaultHref="{{ route('landing') }}"></ion-back-button>
            </ion-buttons>
            <ion-title>طلب شهادة فحص</ion-title>
        </ion-toolbar>
    </ion-header>

    <!-- Content -->
    <ion-content fullscreen>
        <!-- Page Header -->
        <div class="page-header">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <h1>خدمة استخراج شهادة فحص</h1>
                        <p>اتبع الخطوات التالية لطلب شهادة فحص</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active">
                    <div class="step-circle">1</div>
                    <div class="step-title">تقديم الطلب</div>
                </div>
                <div class="step">
                    <div class="step-circle">2</div>
                    <div class="step-title">جدولة الموعد</div>
                </div>
                <div class="step">
                    <div class="step-circle">3</div>
                    <div class="step-title">إصدار الشهادة</div>
                </div>
                <div class="step">
                    <div class="step-circle">4</div>
                    <div class="step-title">إرسال SMS</div>
                </div>
            </div>

            <!-- Form -->
            <form id="certificate-request-form">
                <ion-card class="form-card">
                    <ion-card-header>
                        <ion-card-title>معلومات مقدم الطلب</ion-card-title>
                    </ion-card-header>
                    <ion-card-content>
                        <ion-item>
                            <ion-label position="floating">الاسم الكامل <ion-text color="danger">*</ion-text></ion-label>
                            <ion-input required type="text" name="full_name"></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="floating">رقم الهاتف <ion-text color="danger">*</ion-text></ion-label>
                            <ion-input required type="tel" name="phone" pattern="[0-9]{10}"></ion-input>
                            <ion-note slot="helper">مثال: 05xxxxxxxx</ion-note>
                        </ion-item>

                        <ion-item>
                            <ion-label position="floating">البريد الإلكتروني <ion-text color="danger">*</ion-text></ion-label>
                            <ion-input required type="email" name="email"></ion-input>
                        </ion-item>
                    </ion-card-content>
                </ion-card>

                <ion-card class="form-card">
                    <ion-card-header>
                        <ion-card-title>تفاصيل الطلب</ion-card-title>
                    </ion-card-header>
                    <ion-card-content>
                        <ion-item>
                            <ion-label position="floating">عنوان الطلب <ion-text color="danger">*</ion-text></ion-label>
                            <ion-input required type="text" name="title"></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="floating">وصف الطلب <ion-text color="danger">*</ion-text></ion-label>
                            <ion-textarea required name="description" rows="4"></ion-textarea>
                        </ion-item>



                        <ion-item>
                            <ion-label>تاريخ الطلب المفضل <ion-text color="danger">*</ion-text></ion-label>
                            <ion-datetime required display-format="DD/MM/YYYY" name="preferred_date" min="{{ date('Y-m-d') }}"></ion-datetime>
                        </ion-item>
                    </ion-card-content>
                </ion-card>

                <ion-card class="form-card">
                    <ion-card-header>
                        <ion-card-title>المرفقات والملاحظات</ion-card-title>
                    </ion-card-header>
                    <ion-card-content>
                        <ion-item>
                            <ion-label position="stacked">إرفاق ملفات (اختياري)</ion-label>
                            <input type="file" name="attachments" multiple>
                        </ion-item>

                        <ion-item>
                            <ion-label position="floating">ملاحظات إضافية (اختياري)</ion-label>
                            <ion-textarea name="notes" rows="3"></ion-textarea>
                        </ion-item>
                    </ion-card-content>
                </ion-card>

                <ion-card class="form-card">
                    <ion-card-header>
                        <ion-card-title>الموافقة على الشروط والأحكام</ion-card-title>
                    </ion-card-header>
                    <ion-card-content>
                        <ion-item lines="none">
                            <ion-checkbox slot="start" name="terms_agreement" required></ion-checkbox>
                            <ion-label>أوافق على <a href="#">الشروط والأحكام</a> الخاصة بطلب الخدمة</ion-label>
                        </ion-item>

                        <div class="ion-text-center" style="margin-top: 20px;">
                            <h4>رسوم الخدمة:</h4>
                            <p>رسوم الفحص: 500 ريال</p>
                            <p>رسوم إصدار الشهادة: 200 ريال</p>
                        </div>
                    </ion-card-content>
                </ion-card>

                <div class="ion-padding">
                    <ion-button expand="block" type="submit" color="primary" id="submit-request-btn">
                        <ion-icon name="checkmark-circle-outline" slot="start"></ion-icon>
                        تقديم الطلب
                    </ion-button>
                </div>
            </form>
        </div>

        <!-- Footer -->
        <ion-footer class="footer">
            <ion-grid>
                <ion-row>
                    <ion-col size="12">
                        <p>© {{ date('Y') }} نظام إدارة عقود الصيانة. جميع الحقوق محفوظة.</p>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-footer>
    </ion-content>
</ion-app>

<!-- Ionic Framework JS -->
<script type="module" src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.esm.js"></script>
<script nomodule src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.js"></script>

<!-- Custom JavaScript -->
<script>
    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Form submission
        document.getElementById('certificate-request-form').addEventListener('submit', function(event) {
            event.preventDefault();

            // Show loading indicator
            const loadingElement = document.createElement('ion-loading');
            loadingElement.message = 'جاري تقديم الطلب...';
            loadingElement.duration = 2000;
            document.body.appendChild(loadingElement);
            loadingElement.present();

            // Simulate form submission
            setTimeout(function() {
                // Show success message
                const alertElement = document.createElement('ion-alert');
                alertElement.header = 'تم تقديم الطلب بنجاح';
                alertElement.message = 'تم استلام طلبك بنجاح وسيتم التواصل معك قريباً لتحديد موعد الفحص.';
                alertElement.buttons = [
                    {
                        text: 'العودة للرئيسية',
                        handler: () => {
                            window.location.href = "{{ route('landing') }}";
                        }
                    }
                ];
                document.body.appendChild(alertElement);
                alertElement.present();
            }, 2000);
        });
    });
</script>
</body>
</html>
