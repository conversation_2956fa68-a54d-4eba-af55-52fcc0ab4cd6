@php
    // Preload critical assets
    $preloadedAssets = [
        ['href' => asset('fonts/The_Year_of_The_Camel/TheYearofTheCamel-Regular.otf'), 'as' => 'font', 'type' => 'font/otf', 'crossorigin' => true],
        ['href' => asset('fonts/The_Year_of_The_Camel/TheYearofTheCamel-Bold.otf'), 'as' => 'font', 'type' => 'font/otf', 'crossorigin' => true],
    ];

    use Filament\Support\Enums\IconPosition;
@endphp

@push('meta')
    @foreach($preloadedAssets as $asset)
        <link rel="preload" href="{{ $asset['href'] }}" as="{{ $asset['as'] }}" type="{{ $asset['type'] }}" crossorigin="{{ $asset['crossorigin'] ? 'anonymous' : 'false' }}">
    @endforeach
    <meta name="description" content="نظام متكامل لإدارة عقود الصيانة وإدارة العملاء وتنظيم المواعيد وإصدار الشهادات وتتبع المدفوعات بكفاءة عالية">
@endpush

<x-filament-panels::page.simple>
    <!-- Hero Section - Minimalist Design -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center space-y-4 mx-auto" style="max-width: 800px;">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl">
                {{ __('client.navigation.system_title') }}
            </h1>
            <p class="text-base text-gray-600 dark:text-gray-400 mx-auto">
                {{ __('landing.hero.description') }}
            </p>
            <div class="flex flex-wrap justify-center gap-3 pt-2">
                <x-filament::button
                    href="{{ route('filament.client.pages.certificates') }}"
                    color="primary"
                    size="md"
                    icon="heroicon-m-document-text"
                    :icon-position="IconPosition::After"
                    class="rounded-full"
                >
                    {{ __('landing.cta.inspection_button') }}
                </x-filament::button>

                <x-filament::button
                    href="{{ route('filament.client.pages.maintenance') }}"
                    color="gray"
                    size="md"
                    icon="heroicon-m-wrench-screwdriver"
                    :icon-position="IconPosition::After"
                    class="rounded-full"
                >
                    طلب عقد صيانة
                </x-filament::button>
            </div>
        </div>
    </div>

    <!-- Stats Section - Compact Design -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 container mx-auto">
            @php
                $stats = [
                    ['value' => '500+', 'label' => 'عميل', 'icon' => 'heroicon-o-user-group'],
                    ['value' => '1000+', 'label' => 'شهادة', 'icon' => 'heroicon-o-document-check'],
                    ['value' => '300+', 'label' => 'عقد صيانة', 'icon' => 'heroicon-o-clipboard-document-list'],
                    ['value' => '24/7', 'label' => 'دعم فني', 'icon' => 'heroicon-o-phone']
                ];
            @endphp

            @foreach($stats as $stat)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 text-center">
                    <div class="flex justify-center mb-2">
                        <div class="w-8 h-8 bg-primary-50 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                            <x-dynamic-component
                                :component="$stat['icon']"
                                class="w-4 h-4 text-primary-600 dark:text-primary-400"
                            />
                        </div>
                    </div>
                    <p class="text-xl font-bold text-primary-600 dark:text-primary-400">{{ $stat['value'] }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $stat['label'] }}</p>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Services Section - Clean Cards -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                خدماتنا الرئيسية
            </h2>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                اختر الخدمة التي تناسب احتياجاتك
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 container mx-auto">
            <!-- Service 1 -->
            <x-filament::card class="transition-all duration-200 hover:-translate-y-1 rounded-xl overflow-hidden">
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-10 h-10 bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                            <x-heroicon-o-document-text class="w-5 h-5 text-primary-600 dark:text-primary-400" />
                        </div>
                        <h3 class="text-base font-semibold text-gray-900 dark:text-white">
                            خدمة استخراج شهادة فحص
                        </h3>
                    </div>

                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        خدمة شاملة لطلب وإصدار شهادات الفحص الفني بخطوات بسيطة وسريعة
                    </p>

                    <div class="space-y-2">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ __('landing.services.inspection_certificate.features_title') }}</h4>
                        <ul class="grid grid-cols-2 gap-1 text-xs">
                            @foreach([
                                __('landing.services.inspection_certificate.features.online_application'),
                                __('landing.services.inspection_certificate.features.status_tracking'),
                                __('landing.services.inspection_certificate.features.appointment_scheduling'),
                                __('landing.services.inspection_certificate.features.certificate_issuance'),
                                __('landing.services.inspection_certificate.features.sms_notifications')
                            ] as $feature)
                                <li class="flex items-center space-x-1 space-x-reverse">
                                    <x-heroicon-s-check-circle class="w-3 h-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                                    <span class="text-gray-600 dark:text-gray-400">{{ $feature }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>

                    <div class="pt-2">
                        <x-filament::button
                            href="{{ route('filament.client.pages.certificates') }}"
                            color="primary"
                            size="sm"
                            class="w-full rounded-lg"
                        >
                            {{ __('landing.cta.inspection_button') }}
                        </x-filament::button>
                    </div>
                </div>
            </x-filament::card>

            <!-- Service 2 -->
            <x-filament::card class="transition-all duration-200 hover:-translate-y-1 rounded-xl overflow-hidden">
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-10 h-10 bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                            <x-heroicon-o-wrench-screwdriver class="w-5 h-5 text-primary-600 dark:text-primary-400" />
                        </div>
                        <h3 class="text-base font-semibold text-gray-900 dark:text-white">
                            {{ __('landing.services.maintenance_contract.title') }}
                        </h3>
                    </div>

                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ __('landing.services.maintenance_contract.description') }}
                    </p>

                    <div class="space-y-2">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ __('landing.services.maintenance_contract.features_title') }}</h4>
                        <ul class="grid grid-cols-2 gap-1 text-xs">
                            @foreach([
                                __('landing.services.maintenance_contract.features.periodic_maintenance'),
                                __('landing.services.maintenance_contract.features.emergency_support'),
                                __('landing.services.maintenance_contract.features.qualified_technicians'),
                                __('landing.services.maintenance_contract.features.spare_parts')
                            ] as $contract)
                                <li class="flex items-center space-x-1 space-x-reverse">
                                    <x-heroicon-s-check-circle class="w-3 h-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                                    <span class="text-gray-600 dark:text-gray-400">{{ $contract }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>

                    <div class="pt-2">
                        <x-filament::button
                            href="{{ route('filament.client.pages.maintenance') }}"
                            color="primary"
                            size="sm"
                            class="w-full rounded-lg"
                        >
                            {{ __('landing.cta.maintenance_button') }}
                        </x-filament::button>
                    </div>
                </div>
            </x-filament::card>
        </div>
    </div>

    <!-- Benefits Section - Compact Grid -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                {{ __('landing.benefits.title') }}
            </h2>
            <div class="mt-2 w-16 h-1 bg-primary-500 mx-auto rounded-full"></div>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 gap-3 container mx-auto">
            @php
                $benefits = [
                    ['icon' => 'heroicon-o-shield-check', 'title' => __('landing.benefits.items.certified_quality.title'), 'description' => __('landing.benefits.items.certified_quality.description')],
                    ['icon' => 'heroicon-o-clock', 'title' => __('landing.benefits.items.instant_response.title'), 'description' => __('landing.benefits.items.instant_response.description')],
                    ['icon' => 'heroicon-o-lock-closed', 'title' => __('landing.benefits.items.digital_documentation.title'), 'description' => __('landing.benefits.items.digital_documentation.description')],
                    ['icon' => 'heroicon-o-calendar', 'title' => __('landing.benefits.items.smart_scheduling.title'), 'description' => __('landing.benefits.items.smart_scheduling.description')],
                    ['icon' => 'heroicon-o-device-phone-mobile', 'title' => __('landing.benefits.items.mobile_app.title'), 'description' => __('landing.benefits.items.mobile_app.description')],
                    ['icon' => 'heroicon-o-credit-card', 'title' => __('landing.benefits.items.electronic_payment.title'), 'description' => __('landing.benefits.items.electronic_payment.description')]
                ];
            @endphp

            @foreach($benefits as $benefit)
                <div class="group p-3 rounded-xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700">
                    <div class="flex flex-col items-center text-center space-y-2">
                        <div class="w-8 h-8 bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                            <x-dynamic-component
                                :component="$benefit['icon']"
                                class="w-4 h-4 text-primary-600 dark:text-primary-400"
                            />
                        </div>
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                            {{ $benefit['title'] }}
                        </h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">
                            {{ $benefit['description'] }}
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- How It Works Section - Simplified -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                {{ __('landing.how_it_works.title') }}
            </h2>
            <div class="mt-2 w-16 h-1 bg-primary-500 mx-auto rounded-full"></div>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 container mx-auto">
            @php
                $steps = [
                    ['number' => '01', 'title' => __('landing.how_it_works.steps.create_account'), 'icon' => 'heroicon-o-user-plus'],
                    ['number' => '02', 'title' => __('landing.how_it_works.steps.choose_service'), 'icon' => 'heroicon-o-clipboard-document-list'],
                    ['number' => '03', 'title' => __('landing.how_it_works.steps.schedule_appointment'), 'icon' => 'heroicon-o-calendar-days'],
                    ['number' => '04', 'title' => __('landing.how_it_works.steps.track_request'), 'icon' => 'heroicon-o-bell-alert'],
                ];
            @endphp

            @foreach($steps as $step)
                <div class="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300 text-center">
                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary-600 text-white font-bold text-xs mb-3">
                        {{ $step['number'] }}
                    </div>
                    <div class="flex justify-center mb-2">
                        <div class="w-8 h-8 bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                            <x-dynamic-component
                                :component="$step['icon']"
                                class="w-4 h-4 text-primary-600 dark:text-primary-400"
                            />
                        </div>
                    </div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white">
                        {{ $step['title'] }}
                    </h3>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Testimonials Section - Compact Cards -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                {{ __('landing.testimonials.title') }}
            </h2>
            <div class="mt-2 w-16 h-1 bg-primary-500 mx-auto rounded-full"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 container mx-auto">
            @php
                $testimonials = [
                    [
                        'name' => __('landing.testimonials.items.testimonial1.name'),
                        'position' => __('landing.testimonials.items.testimonial1.position'),
                        'content' => __('landing.testimonials.items.testimonial1.content'),
                        'rating' => 5
                    ],
                    [
                        'name' => __('landing.testimonials.items.testimonial2.name'),
                        'position' => __('landing.testimonials.items.testimonial2.position'),
                        'content' => __('landing.testimonials.items.testimonial2.content'),
                        'rating' => 5
                    ],
                    [
                        'name' => __('landing.testimonials.items.testimonial3.name'),
                        'position' => __('landing.testimonials.items.testimonial3.position'),
                        'content' => __('landing.testimonials.items.testimonial3.content'),
                        'rating' => 4
                    ],
                ];
            @endphp

            @foreach($testimonials as $testimonial)
                <div class="p-4 rounded-xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700">
                    <div class="flex mb-2">
                        @for($i = 0; $i < $testimonial['rating']; $i++)
                            <x-heroicon-s-star class="w-3 h-3 text-amber-400" />
                        @endfor
                        @for($i = $testimonial['rating']; $i < 5; $i++)
                            <x-heroicon-o-star class="w-3 h-3 text-amber-400" />
                        @endfor
                    </div>

                    <p class="text-xs text-gray-600 dark:text-gray-400 italic mb-3">
                        "{{ $testimonial['content'] }}"
                    </p>

                    <div class="pt-2 border-t border-gray-100 dark:border-gray-800">
                        <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ $testimonial['name'] }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $testimonial['position'] }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- FAQ Section - Compact Accordion -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                {{ __('landing.faq.title') }}
            </h2>
            <div class="mt-2 w-16 h-1 bg-primary-500 mx-auto rounded-full"></div>
        </div>

        <div class="space-y-2">
            @php
                $faqs = [
                    [
                        'question' => __('landing.faq.items.certificate_duration.question'),
                        'answer' => __('landing.faq.items.certificate_duration.answer')
                    ],
                    [
                        'question' => __('landing.faq.items.auto_renewal.question'),
                        'answer' => __('landing.faq.items.auto_renewal.answer')
                    ],
                    [
                        'question' => __('landing.faq.items.pricing.question'),
                        'answer' => __('landing.faq.items.pricing.answer')
                    ],
                    [
                        'question' => __('landing.faq.items.emergency_visits.question'),
                        'answer' => __('landing.faq.items.emergency_visits.answer')
                    ],
                ];
            @endphp

            @foreach($faqs as $index => $faq)
                <x-filament::section
                    :collapsible="true"
                    :collapsed="true"
                    class="rounded-xl overflow-hidden hover:shadow-sm transition-shadow duration-300"
                >
                    <x-slot name="heading">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $faq['question'] }}
                        </h3>
                    </x-slot>

                    <p class="text-xs text-gray-600 dark:text-gray-400">
                        {{ $faq['answer'] }}
                    </p>
                </x-filament::section>
            @endforeach
        </div>
    </div>

    <!-- CTA Section - Clean Minimalist -->
    {{--<div class="max-w-4xl mx-auto px-4 py-8">
        <x-filament::card class="bg-primary-50 dark:bg-primary-900/10 border-0 rounded-2xl overflow-hidden">
            <div class="text-center space-y-4 py-4">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                    جاهز للبدء؟
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 max-w-xl mx-auto">
                    سجل طلبك الآن واستمتع بخصم 10% على أول عقد صيانة
                </p>
                <div class="flex flex-wrap justify-center gap-3 pt-2">
                    <x-filament::button
                        href="{{ route('filament.client.pages.certificates') }}"
                        color="primary"
                        size="sm"
                        class="rounded-full px-6"
                    >
                        طلب شهادة فحص
                    </x-filament::button>

                    <x-filament::button
                        href="{{ route('filament.client.pages.maintenance') }}"
                        color="gray"
                        size="sm"
                        class="rounded-full px-6"
                    >
                        طلب عقد صيانة
                    </x-filament::button>
                </div>
            </div>
        </x-filament::card>
    </div>--}}
</x-filament-panels::page.simple>
