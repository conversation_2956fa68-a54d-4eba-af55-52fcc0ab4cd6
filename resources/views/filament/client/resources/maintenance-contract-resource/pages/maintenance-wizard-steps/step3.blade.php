<x-filament-panels::page>
    <x-filament::section>
        <x-slot name="heading">
            {{ __('client/resources/maintenance_request.wizard.step3.heading') }}
        </x-slot>

        <x-slot name="description">
            {{ __('client/resources/maintenance_request.wizard.step3.description') }}
        </x-slot>

        <div class="space-y-6">
            {{-- Contract Summary --}}
            <x-filament::section>
                <x-slot name="heading">
                    {{ __('client/resources/maintenance_request.wizard.step3.contract_summary') }}
                </x-slot>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <x-filament::card>
                        <x-slot name="heading">
                            {{ __('client/resources/maintenance_request.wizard.step3.contract_info') }}
                        </x-slot>

                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.contract_type') }}:</span>
                                <span>{{ $contractSummary['contractInfo']['type'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.start_date') }}:</span>
                                <span>{{ $contractSummary['contractInfo']['startDate'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.end_date') }}:</span>
                                <span>{{ $contractSummary['contractInfo']['endDate'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.contract_duration') }}:</span>
                                <span>{{ $contractSummary['contractInfo']['duration'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.visits_count') }}:</span>
                                <span>{{ $contractSummary['contractInfo']['visitLimit'] }}</span>
                            </div>
                        </div>
                    </x-filament::card>

                    <x-filament::card>
                        <x-slot name="heading">
                            {{ __('client/resources/maintenance_request.wizard.step3.client_info') }}
                        </x-slot>

                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.company_name') }}:</span>
                                <span>{{ $contractSummary['clientInfo']['companyName'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.contact_name') }}:</span>
                                <span>{{ $contractSummary['clientInfo']['contactName'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.phone') }}:</span>
                                <span>{{ $contractSummary['clientInfo']['phone'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.email') }}:</span>
                                <span>{{ $contractSummary['clientInfo']['email'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('client/resources/maintenance_request.wizard.step3.address') }}:</span>
                                <span>{{ $contractSummary['clientInfo']['address'] }}</span>
                            </div>
                        </div>
                    </x-filament::card>
                </div>
            </x-filament::section>

            {{-- Contract Value --}}
            <x-filament::section>
                <x-slot name="heading">
                    {{ __('client/resources/maintenance_request.wizard.step3.contract_value') }}
                </x-slot>

                <div class="text-center py-6">
                    <div class="text-2xl font-bold text-primary-600 mb-2">
                        {{ __('client/resources/maintenance_request.wizard.step3.cost_after_visit') }}
                    </div>
                    <div class="text-gray-600">
                        {{ __('client/resources/maintenance_request.wizard.step3.visits_included') }}: {{ $contractSummary['contractInfo']['visitLimit'] }}
                    </div>
                </div>
            </x-filament::section>

            {{-- Terms and Conditions Form --}}
            {{ $this->form }}
        </div>

        <x-slot name="footerActions">
            <x-filament::button
                wire:click="previousStep"
                color="gray"
                icon="heroicon-o-arrow-left"
            >
                {{ __('client/resources/maintenance_request.wizard.navigation.previous') }}
            </x-filament::button>

            <x-filament::button
                wire:click="submitStep3"
                color="success"
                icon="heroicon-o-check"
                icon-position="after"
            >
                {{ __('client/resources/maintenance_request.wizard.navigation.confirm_submit') }}
            </x-filament::button>
        </x-slot>
    </x-filament::section>

    {{-- Terms Modal --}}
    <x-filament::modal
        id="terms-modal"
        width="4xl"
    >
        <x-slot name="heading">
            {{ __('client/resources/maintenance_request.wizard.step3.terms_conditions') }}
        </x-slot>

        <div class="prose prose-sm max-w-none">
            <ol class="space-y-2">
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term1') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term2') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term3') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term4') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term5') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term6') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term7') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term8') }}</li>
                <li>{{ __('client/resources/maintenance_request.wizard.step3.terms.term9') }}</li>
            </ol>
        </div>

        <x-slot name="footerActions">
            <x-filament::button
                x-on:click="close"
                color="primary"
            >
                {{ __('client/resources/maintenance_request.wizard.step3.terms.agree') }}
            </x-filament::button>
        </x-slot>
    </x-filament::modal>
</x-filament-panels::page>
