<div class="space-y-6">
    {{-- Contract Value Card --}}
    <x-filament::section>
        <x-slot name="heading">
            {{ __('client/resources/maintenance_request.wizard.summary.contract_value') }}
        </x-slot>

        <div class="text-center py-6">
            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {{ __('client/resources/maintenance_request.wizard.summary.cost_after_visit') }}
            </div>
            <div class="text-gray-600 dark:text-gray-400">
                {{ __('client/resources/maintenance_request.wizard.summary.visits_included') }}: {{ $contractSummary['contractInfo']['visitLimit'] }}
            </div>
        </div>
    </x-filament::section>

    {{-- Contract and Client Info Cards --}}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {{-- Contract Info Card --}}
        <x-filament::section>
            <x-slot name="heading">
                {{ __('client/resources/maintenance_request.wizard.summary.contract_info') }}
            </x-slot>

            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.contract_type') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $contractSummary['contractInfo']['type'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.start_date') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $contractSummary['contractInfo']['startDate'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.end_date') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $contractSummary['contractInfo']['endDate'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.contract_duration') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $contractSummary['contractInfo']['duration'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.visits_count') }}:</span>
                    <span class="text-primary-600 dark:text-primary-400 font-semibold">{{ $contractSummary['contractInfo']['visitLimit'] }}</span>
                </div>
            </div>
        </x-filament::section>

        {{-- Client Info Card --}}
        <x-filament::section>
            <x-slot name="heading">
                {{ __('client/resources/maintenance_request.wizard.summary.client_info') }}
            </x-slot>

            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.company_name') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $contractSummary['clientInfo']['companyName'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.contact_name') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $contractSummary['clientInfo']['contactName'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.phone') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100 dir-ltr text-right">{{ $contractSummary['clientInfo']['phone'] }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('client/resources/maintenance_request.wizard.summary.email') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100 dir-ltr text-right">{{ $contractSummary['clientInfo']['email'] }}</span>
                </div>
                <div class="py-2">
                    <span class="font-medium text-gray-700 dark:text-gray-300 block mb-2">{{ __('client/resources/maintenance_request.wizard.summary.address') }}:</span>
                    <span class="text-gray-900 dark:text-gray-100 text-sm leading-relaxed">{{ $contractSummary['clientInfo']['address'] }}</span>
                </div>
            </div>
        </x-filament::section>
    </div>
</div>
