@php
    $metadata = $getState() ?? [];
@endphp

<div class="px-4 py-2">
    @if (empty($metadata))
        <div class="text-sm text-gray-500">{{ __('لا توجد بيانات تعريفية') }}</div>
    @else
        <div class="space-y-3">
            @foreach ($metadata as $key => $value)
                <div>
                    <div class="text-sm font-semibold text-gray-600">{{ $key }}</div>
                    <div class="text-sm mt-1">
                        @if (is_array($value))
                            <pre class="text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto">{{ json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                        @elseif (is_bool($value))
                            <span class="px-2 py-1 text-xs rounded {{ $value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $value ? __('نعم') : __('لا') }}
                            </span>
                        @else
                            {{ $value }}
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>
