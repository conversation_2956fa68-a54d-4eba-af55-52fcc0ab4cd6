@php
    use Filament\Support\Enums\IconPosition;
@endphp

<x-filament::page>
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-lg font-semibold">{{ __('معاينة القالب') }}</h2>
            <p class="text-sm text-gray-500">{{ $record->name }}</p>
        </div>

        <div class="flex items-center space-x-2 rtl:space-x-reverse">
            <x-filament::button
                wire:click="refreshPreview"
                icon="heroicon-o-arrow-path"
                color="gray"
            >
                {{ __('تحديث المعاينة') }}
            </x-filament::button>

            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.document-templates.generate-pdf', ['record' => $record])"
                target="_blank"
                icon="heroicon-o-document"
                color="warning"
            >
                {{ __('إنشاء PDF') }}
            </x-filament::button>

            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.document-templates.edit', ['record' => $record])"
                icon="heroicon-o-pencil"
                color="primary"
            >
                {{ __('تعديل القالب') }}
            </x-filament::button>
        </div>
    </div>

    @if ($record->is_synced)
        @if ($previewHtml)
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b flex items-center justify-between">
                    <h3 class="text-sm font-medium">{{ __('معاينة القالب') }}</h3>

                    <button
                        onclick="document.getElementById('previewFrame').contentWindow.print()"
                        class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
                    >
                        <x-heroicon-o-printer class="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
                        {{ __('طباعة') }}
                    </button>
                </div>

                <iframe
                    id="previewFrame"
                    srcdoc="{{ $previewHtml }}"
                    class="w-full bg-white"
                    style="height: 70vh;"
                ></iframe>
            </div>
        @else
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <x-heroicon-o-exclamation-triangle class="w-10 h-10 text-yellow-400 mx-auto mb-2" />
                <p class="text-yellow-800 font-medium">{{ __('لا يمكن تحميل المعاينة') }}</p>
                <p class="text-yellow-700 text-sm mt-1">{{ __('تأكد من صحة إعدادات DocKing ومحتوى القالب') }}</p>
            </div>
        @endif
    @else
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
            <x-heroicon-o-exclamation-triangle class="w-10 h-10 text-yellow-400 mx-auto mb-2" />
            <p class="text-yellow-800 font-medium">{{ __('القالب غير متزامن مع DocKing') }}</p>
            <p class="text-yellow-700 text-sm mt-1">{{ __('يجب مزامنة القالب أولاً لعرض المعاينة') }}</p>

            <div class="mt-4">
                <x-filament::button
                    wire:click="$refresh"
                    icon="heroicon-o-arrow-path"
                    color="warning"
                >
                    {{ __('مزامنة الآن') }}
                </x-filament::button>
            </div>

            @if ($record->sync_error)
                <div class="mt-4 text-sm text-red-600 bg-red-50 p-2 rounded">
                    <p class="font-semibold">{{ __('خطأ المزامنة:') }}</p>
                    <p>{{ $record->sync_error }}</p>
                </div>
            @endif
        </div>
    @endif

    {{-- Sample Data Section --}}
    <div class="mt-8">
        <h3 class="text-lg font-semibold mb-3">{{ __('بيانات المعاينة') }}</h3>

        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="bg-gray-50 px-4 py-2 border-b">
                <h4 class="text-sm font-medium">{{ __('البيانات المستخدمة في إنشاء المعاينة') }}</h4>
            </div>

            <div class="p-4 overflow-auto" style="max-height: 300px;">
                <pre class="text-xs bg-gray-50 p-4 rounded"><code>{{ json_encode($previewData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
            </div>
        </div>
    </div>
</x-filament::page>
