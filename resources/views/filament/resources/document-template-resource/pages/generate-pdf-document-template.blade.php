@php
    use Filament\Support\Enums\IconPosition;
@endphp

<x-filament::page>
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-lg font-semibold">{{ __('إنشاء PDF') }}</h2>
            <p class="text-sm text-gray-500">{{ $record->name }}</p>
        </div>

        <div class="flex items-center space-x-2 rtl:space-x-reverse">
            <x-filament::button
                wire:click="generatePdf"
                icon="heroicon-o-arrow-path"
                color="gray"
            >
                {{ __('إعادة الإنشاء') }}
            </x-filament::button>

            <x-filament::button
                tag="a"
                :href="$pdfUrl"
                target="_blank"
                download="{{ $record->name }}.pdf"
                icon="heroicon-o-arrow-down-tray"
                color="success"
                :disabled="!$pdfUrl"
            >
                {{ __('تنزيل PDF') }}
            </x-filament::button>

            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.document-templates.preview', ['record' => $record])"
                icon="heroicon-o-eye"
                color="info"
            >
                {{ __('معاينة') }}
            </x-filament::button>

            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.document-templates.edit', ['record' => $record])"
                icon="heroicon-o-pencil"
                color="primary"
            >
                {{ __('تعديل القالب') }}
            </x-filament::button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {{-- PDF Preview Section --}}
        <div>
            <h3 class="text-lg font-semibold mb-3">{{ __('معاينة PDF') }}</h3>

            @if ($pdfUrl)
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b flex items-center justify-between">
                        <h4 class="text-sm font-medium">{{ __('معاينة ملف PDF') }}</h4>

                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <button
                                onclick="document.getElementById('pdfFrame').contentWindow.print()"
                                class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
                            >
                                <x-heroicon-o-printer class="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
                                {{ __('طباعة') }}
                            </button>
                        </div>
                    </div>

                    <iframe
                        id="pdfFrame"
                        src="{{ $pdfUrl }}"
                        class="w-full bg-white"
                        style="height: 70vh;"
                    ></iframe>
                </div>
            @else
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                    <x-heroicon-o-exclamation-triangle class="w-10 h-10 text-yellow-400 mx-auto mb-2" />
                    <p class="text-yellow-800 font-medium">{{ __('لم يتم إنشاء ملف PDF بعد') }}</p>
                    <p class="text-yellow-700 text-sm mt-1">{{ __('اضغط على زر "إعادة الإنشاء" لتوليد ملف PDF') }}</p>
                </div>
            @endif
        </div>

        {{-- Sample Data Section --}}
        <div>
            <h3 class="text-lg font-semibold mb-3">{{ __('بيانات PDF') }}</h3>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b">
                    <h4 class="text-sm font-medium">{{ __('البيانات المستخدمة في إنشاء PDF') }}</h4>
                </div>

                <div class="p-4 overflow-auto" style="max-height: 300px;">
                    <pre class="text-xs bg-gray-50 p-4 rounded"><code>{{ json_encode($pdfData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                </div>
            </div>

            @if (!$record->is_synced)
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center mt-4">
                    <x-heroicon-o-exclamation-triangle class="w-6 h-6 text-yellow-400 mx-auto mb-1" />
                    <p class="text-yellow-800 text-sm font-medium">{{ __('القالب غير متزامن مع DocKing') }}</p>

                    @if ($record->sync_error)
                        <div class="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                            <p class="font-semibold">{{ __('خطأ المزامنة:') }}</p>
                            <p>{{ $record->sync_error }}</p>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </div>
</x-filament::page>
