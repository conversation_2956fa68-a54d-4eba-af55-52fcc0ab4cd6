<x-filament::page>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {{-- Contract Statistics --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.widgets.contract_statistics') }}
            </h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="px-4 py-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.active_contracts') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getActiveContractsCount() }}</p>
                </div>
                <div class="px-4 py-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.pending_contracts') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getPendingContractsCount() }}</p>
                </div>
                <div class="px-4 py-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.expiring_soon') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getExpiringContractsCount() }}</p>
                </div>
                <div class="px-4 py-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.total_clients') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getTotalClientsCount() }}</p>
                </div>
            </div>
        </x-filament::card>

        {{-- Maintenance Request Statistics --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.widgets.maintenance_requests') }}
            </h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="px-4 py-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.active_requests') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getActiveMaintenanceRequestsCount() }}</p>
                </div>
                <div class="px-4 py-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.completed') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getCompletedMaintenanceRequestsCount() }}</p>
                </div>
                <div class="px-4 py-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.unassigned') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getUnassignedMaintenanceRequestsCount() }}</p>
                </div>
            </div>
        </x-filament::card>

        {{-- Revenue Statistics --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.widgets.revenue') }}
            </h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="px-4 py-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.monthly_revenue') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($this->getMonthlyRevenue(), 2) }} {{ __('dashboard.currency.sar') }}</p>
                </div>
                <div class="px-4 py-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('dashboard.stats.yearly_revenue') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($this->getYearlyRevenue(), 2) }} {{ __('dashboard.currency.sar') }}</p>
                </div>
            </div>
        </x-filament::card>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        {{-- Monthly Revenue Chart --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.charts.monthly_revenue', ['year' => date('Y')]) }}
            </h2>
            <div id="monthly-revenue-chart" style="height: 300px;"></div>
        </x-filament::card>

        {{-- Requests by Status Chart --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.charts.requests_by_status') }}
            </h2>
            <div id="requests-by-status-chart" style="height: 300px;"></div>
        </x-filament::card>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        {{-- Recent Maintenance Requests --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.tables.recent_maintenance_requests') }}
            </h2>
            <div class="overflow-hidden overflow-x-auto rounded-lg">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                    <tr>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.request') }}</th>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.client') }}</th>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.status') }}</th>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.date') }}</th>
                    </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                    @foreach($this->getRecentMaintenanceRequests() as $request)
                        <tr>
                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                <a href="{{ \App\Filament\Resources\MaintenanceRequestResource::getUrl('edit', [$request]) }}" class="hover:underline">
                                    {{ $request->request_number }}
                                </a>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $request->client->name }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                @php
                                    $statusColors = [
                                        'new' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'assigned' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                                        'in_progress' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                        'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'canceled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                    ];
                                    $statusColor = $statusColors[$request->status] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
                                @endphp
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $statusColor }}">
                                        {{ __('dashboard.status_options.' . $request->status) }}
                                    </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $request->created_at->format('Y-m-d') }}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </x-filament::card>

        {{-- Expiring Contracts --}}
        <x-filament::card>
            <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                {{ __('dashboard.tables.contracts_expiring_soon') }}
            </h2>
            <div class="overflow-hidden overflow-x-auto rounded-lg">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                    <tr>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.contract') }}</th>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.client') }}</th>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.end_date') }}</th>
                        <th class="px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{{ __('dashboard.table_headers.days_left') }}</th>
                    </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                    @foreach($this->getExpiringContracts() as $contract)
                        @php
                            $daysLeft = now()->diffInDays($contract->end_date, false);
                            $textColor = $daysLeft <= 7 ? 'text-red-600 dark:text-red-400 font-bold' : 'text-gray-500 dark:text-gray-400';
                        @endphp
                        <tr>
                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                <a href="{{ route('filament.resources.contracts.edit', $contract) }}" class="hover:underline">
                                    {{ $contract->contract_number }}
                                </a>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $contract->client->name }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $contract->end_date->format('Y-m-d') }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm {{ $textColor }}">
                                {{ $daysLeft }} {{ __('dashboard.labels.days') }}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </x-filament::card>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Monthly Revenue Chart
                const monthlyRevenueData = @json($this->getMonthlyRevenueChart());
                const monthlyRevenueOptions = {
                    chart: {
                        type: 'bar',
                        height: 300,
                        toolbar: {
                            show: false
                        }
                    },
                    series: [{
                        name: '{{ __('dashboard.chart_labels.revenue') }}',
                        data: monthlyRevenueData.map(item => item.total)
                    }],
                    xaxis: {
                        categories: monthlyRevenueData.map(item => item.month)
                    },
                    colors: ['#3B82F6'],
                    plotOptions: {
                        bar: {
                            borderRadius: 4,
                            dataLabels: {
                                position: 'top',
                            },
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    yaxis: {
                        title: {
                            text: '{{ __('dashboard.chart_labels.revenue_sar') }}'
                        }
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                return val.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' {{ __('dashboard.currency.sar') }}';
                            }
                        }
                    }
                };
                new ApexCharts(document.querySelector("#monthly-revenue-chart"), monthlyRevenueOptions).render();

                // Requests by Status Chart
                const requestsStatusData = @json($this->getRequestsByStatusChart());
                const requestsStatusOptions = {
                    chart: {
                        type: 'pie',
                        height: 300,
                        toolbar: {
                            show: false
                        }
                    },
                    series: requestsStatusData.map(item => item.count),
                    labels: requestsStatusData.map(item => item.status),
                    colors: ['#3B82F6', '#8B5CF6', '#F59E0B', '#10B981', '#EF4444'],
                    dataLabels: {
                        enabled: true,
                        formatter: function(val, opts) {
                            return opts.w.config.series[opts.seriesIndex];
                        }
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                return val + ' {{ __('dashboard.chart_labels.requests') }}';
                            }
                        }
                    }
                };
                new ApexCharts(document.querySelector("#requests-by-status-chart"), requestsStatusOptions).render();
            });
        </script>
    @endpush
</x-filament::page>
