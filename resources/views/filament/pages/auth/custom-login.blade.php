@php use Filament\Support\Facades\FilamentView; @endphp
@php use Filament\View\PanelsRenderHook; @endphp
<x-filament-panels::page.simple>
    {{ FilamentView::renderHook(PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE, scopes: $this->getRenderHookScopes()) }}

    <x-filament-panels::form id="form" wire:submit="authenticate">
        @if ($showQRCode)
            <div class="flex flex-col items-center justify-center space-y-4">
                <div class="text-center mb-4">
                    <h2 class="text-xl font-semibold">{{ __('auth.qr_login_title', ['default' => 'مسح رمز QR للدخول']) }}</h2>
                    <p class="text-sm text-gray-500">{{ __('auth.qr_login_description', ['default' => 'استخدم تطبيق الهاتف للمسح']) }}</p>
                </div>

                <div class="bg-white p-4 rounded-lg shadow-md">
                    @if ($qrcodeText)
                        <div wire:ignore>
                            <div
                                x-data="{
                                    qrCode: null,
                                    value: @js($qrcodeText),
                                    init() {
                                        this.generateQR();
                                    },
                                    generateQR() {
                                        if (typeof QRCode !== 'undefined') {
                                            this.qrCode = new QRCode(this.$el, {
                                                text: this.value,
                                                width: 250,
                                                height: 250,
                                                colorDark: '#000000',
                                                colorLight: '#ffffff',
                                                correctLevel: QRCode.CorrectLevel.H
                                            });
                                        }
                                    }
                                }"
                                class="flex justify-center"
                            ></div>
                        </div>
                    @endif
                </div>
            </div>
        @else
            {{ $this->form }}
        @endif

        <x-filament-panels::form.actions
            :actions="$this->getCachedFormActions()"
            :full-width="$this->hasFullWidthFormActions()"
        />
    </x-filament-panels::form>

    {{ FilamentView::renderHook(PanelsRenderHook::AUTH_LOGIN_FORM_AFTER, scopes: $this->getRenderHookScopes()) }}

    @pushOnce('scripts')
        <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>

        @if(class_exists('App\Events\LoginWithQRCodeEvent'))
            <script>
                document.addEventListener('DOMContentLoaded', () => {
                    // Setup listeners for Reverb events using Livewire 3
                    Livewire.on('reverb:subscribe', (detail) => {
                        if (window.Echo) {
                            detail = detail[0]
                            // Subscribe to channel and listen for the event
                            window.Echo.channel(detail.channel)
                                .listen(detail.event, (data) => {
                                    // Call the Livewire method to verify the token
                                    @this.verifyQrToken(data.token);
                                });
                        }
                    });

                    // Handle channel unsubscription
                    Livewire.on('reverb:leave', (detail) => {
                        if (window.Echo) {
                            detail = detail[0]
                            window.Echo.leaveChannel(detail.channel);
                        }
                    });
                });
            </script>
        @endif
    @endPushOnce
</x-filament-panels::page.simple>
