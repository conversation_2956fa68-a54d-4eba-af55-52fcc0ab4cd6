@php
    $record = $getRecord();
    $total = $record->checklistItems()->count();
    $completed = $record->checklistItems()->where('status', '!=', 'not_checked')->count();
    $passed = $record->checklistItems()->where('status', 'passed')->count();
    $failed = $record->checklistItems()->where('status', 'failed')->count();
    $notApplicable = $record->checklistItems()->where('status', 'not_applicable')->count();
    
    if ($total === 0) {
        $percentage = 0;
        $color = 'gray';
    } else {
        $percentage = round(($completed / $total) * 100);
        $color = $percentage >= 80 ? 'success' : ($percentage >= 50 ? 'warning' : 'danger');
    }
    
    $bgColor = match($color) {
        'success' => 'bg-green-100',
        'warning' => 'bg-yellow-100', 
        'danger' => 'bg-red-100',
        'gray' => 'bg-gray-100',
        default => 'bg-gray-100'
    };
    $textColor = match($color) {
        'success' => 'text-green-700',
        'warning' => 'text-yellow-700',
        'danger' => 'text-red-700',
        'gray' => 'text-gray-500',
        default => 'text-gray-700'
    };
@endphp

<div class="flex items-center gap-3 min-w-0">
    @if($total === 0)
        <div class="flex items-center gap-2 text-gray-500">
            <x-heroicon-s-minus-circle class="w-4 h-4" />
            <span class="text-xs">لا توجد قائمة تحقق</span>
        </div>
    @else
        {{-- Summary Stats --}}
        <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium {{ $textColor }}">
                    {{ $completed }}/{{ $total }}
                </span>
                <span class="text-xs {{ $textColor }}">
                    {{ $percentage }}%
                </span>
            </div>
            
            {{-- Progress Segments --}}
            <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden flex">
                @if($passed > 0)
                    <div 
                        class="bg-green-500 h-2 transition-all duration-500"
                        style="width: {{ ($passed / $total) * 100 }}%"
                        title="نجح: {{ $passed }}"
                    ></div>
                @endif
                
                @if($failed > 0)
                    <div 
                        class="bg-red-500 h-2 transition-all duration-500"
                        style="width: {{ ($failed / $total) * 100 }}%"
                        title="فشل: {{ $failed }}"
                    ></div>
                @endif
                
                @if($notApplicable > 0)
                    <div 
                        class="bg-yellow-500 h-2 transition-all duration-500"
                        style="width: {{ ($notApplicable / $total) * 100 }}%"
                        title="غير قابل للتطبيق: {{ $notApplicable }}"
                    ></div>
                @endif
                
                @if(($total - $completed) > 0)
                    <div 
                        class="bg-gray-300 h-2 transition-all duration-500"
                        style="width: {{ (($total - $completed) / $total) * 100 }}%"
                        title="لم يتم فحصه: {{ $total - $completed }}"
                    ></div>
                @endif
            </div>
            
            {{-- Status Icons --}}
            <div class="mt-1 flex items-center gap-1">
                @if($passed > 0)
                    <div class="flex items-center gap-1">
                        <x-heroicon-s-check-circle class="w-3 h-3 text-green-500" />
                        <span class="text-xs text-green-600">{{ $passed }}</span>
                    </div>
                @endif
                
                @if($failed > 0)
                    <div class="flex items-center gap-1">
                        <x-heroicon-s-x-circle class="w-3 h-3 text-red-500" />
                        <span class="text-xs text-red-600">{{ $failed }}</span>
                    </div>
                @endif
                
                @if($notApplicable > 0)
                    <div class="flex items-center gap-1">
                        <x-heroicon-s-minus-circle class="w-3 h-3 text-yellow-500" />
                        <span class="text-xs text-yellow-600">{{ $notApplicable }}</span>
                    </div>
                @endif
                
                @if(($total - $completed) > 0)
                    <div class="flex items-center gap-1">
                        <x-heroicon-s-clock class="w-3 h-3 text-gray-400" />
                        <span class="text-xs text-gray-500">{{ $total - $completed }}</span>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
