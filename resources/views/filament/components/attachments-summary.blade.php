@php
    $attachments = $getRecord()->attachments ?? [];
    
    if (empty($attachments) || !is_array($attachments)) {
        $count = 0;
        $imageCount = 0;
        $documentCount = 0;
        $drawingCount = 0;
    } else {
        $count = count($attachments);
        $imageCount = 0;
        $documentCount = 0;
        $drawingCount = 0;
        
        foreach ($attachments as $file) {
            if (is_string($file)) {
                $filename = basename($file);
                if (str_contains($filename, 'رسم-توضيحي') || str_contains($filename, 'drawing-')) {
                    $drawingCount++;
                } else {
                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                        $imageCount++;
                    } else {
                        $documentCount++;
                    }
                }
            }
        }
    }
@endphp

<div class="flex items-center gap-2 min-w-0">
    @if($count === 0)
        <div class="flex items-center gap-1 text-gray-400">
            <x-heroicon-s-paper-clip class="w-4 h-4" />
            <span class="text-xs">لا توجد مرفقات</span>
        </div>
    @else
        {{-- Total Count Badge --}}
        <div class="flex items-center gap-1">
            <x-heroicon-s-paper-clip class="w-4 h-4 text-blue-500" />
            <span class="text-sm font-medium text-blue-700">{{ $count }}</span>
        </div>
        
        {{-- File Type Breakdown --}}
        <div class="flex items-center gap-1">
            @if($drawingCount > 0)
                <div class="flex items-center gap-1 px-2 py-1 bg-purple-100 rounded-full">
                    <span class="text-xs">🎨</span>
                    <span class="text-xs font-medium text-purple-700">{{ $drawingCount }}</span>
                </div>
            @endif
            
            @if($imageCount > 0)
                <div class="flex items-center gap-1 px-2 py-1 bg-green-100 rounded-full">
                    <span class="text-xs">🖼️</span>
                    <span class="text-xs font-medium text-green-700">{{ $imageCount }}</span>
                </div>
            @endif
            
            @if($documentCount > 0)
                <div class="flex items-center gap-1 px-2 py-1 bg-red-100 rounded-full">
                    <span class="text-xs">📄</span>
                    <span class="text-xs font-medium text-red-700">{{ $documentCount }}</span>
                </div>
            @endif
        </div>
        
        {{-- Quick Preview for Images --}}
        @if($imageCount > 0 && count($attachments) > 0)
            <div class="flex -space-x-1">
                @foreach(array_slice($attachments, 0, 3) as $file)
                    @php
                        $filename = basename($file);
                        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                        $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                    @endphp
                    
                    @if($isImage)
                        <div class="w-6 h-6 rounded-full border-2 border-white bg-gray-100 overflow-hidden">
                            <img 
                                src="{{ Storage::disk('public')->url($file) }}" 
                                alt="{{ $filename }}"
                                class="w-full h-full object-cover"
                                loading="lazy"
                                onerror="this.style.display='none'"
                            />
                        </div>
                    @endif
                @endforeach
                
                @if($imageCount > 3)
                    <div class="w-6 h-6 rounded-full border-2 border-white bg-gray-200 flex items-center justify-center">
                        <span class="text-xs text-gray-600">+{{ $imageCount - 3 }}</span>
                    </div>
                @endif
            </div>
        @endif
    @endif
</div>
