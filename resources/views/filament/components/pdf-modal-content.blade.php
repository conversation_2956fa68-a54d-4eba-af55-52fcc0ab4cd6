{{-- PDF Modal Content Component --}}
<div class="pdf-modal-container" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    @if($isGenerating)
        {{-- Loading State --}}
        <div class="pdf-loading-state">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            <div class="text-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ __('filament-resources/maintenance-request.messages.generating_pdf', [], 'جاري إنشاء ملف PDF...') }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {{ __('filament-resources/maintenance-request.messages.please_wait', [], 'يرجى الانتظار، قد تستغرق هذه العملية بضع ثوانٍ') }}
                </p>
            </div>
        </div>
    @elseif(!empty($pdfUrl))
        {{-- PDF Viewer --}}
        <div class="pdf-viewer-container">
            {{-- PDF Header --}}
            <div class="pdf-header">
                <div class="pdf-header-info">
                    <x-heroicon-o-document-text class="h-5 w-5 text-gray-500 flex-shrink-0" />
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 truncate">
                        {{ __('filament-resources/maintenance-request.labels.maintenance_request_pdf', [], 'ملف PDF لطلب الصيانة') }}
                        {{ $record->request_number }}
                    </span>
                </div>
                <div class="pdf-header-meta">
                    @if(isset($generatedAt) && $generatedAt)
                        {{-- Enhanced metadata for persistent PDFs --}}
                        <div class="flex flex-col items-end space-y-1">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ __('filament-resources/maintenance-request.labels.generated_at', [], 'تم الإنشاء في') }}
                                {{ $generatedAt->format('d/m/Y H:i') }}
                            </span>
                            @if(isset($fileSize) && $fileSize)
                                <span class="text-xs text-gray-400 dark:text-gray-500">
                                    {{ __('filament-resources/maintenance-request.labels.file_size', [], 'حجم الملف') }}: {{ $fileSize }}
                                </span>
                            @endif
                            @if(isset($isPersistent) && $isPersistent)
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    <svg class="w-3 h-3 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ __('filament-resources/maintenance-request.labels.stored', [], 'محفوظ') }}
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <svg class="w-3 h-3 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ __('filament-resources/maintenance-request.labels.temporary', [], 'مؤقت') }}
                                </span>
                            @endif
                        </div>
                    @else
                        {{-- Fallback for temporary PDFs --}}
                        <div class="flex flex-col items-end space-y-1">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ __('filament-resources/maintenance-request.labels.generated_at', [], 'تم الإنشاء في') }}
                                {{ now()->format('d/m/Y H:i') }}
                            </span>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                <svg class="w-3 h-3 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('filament-resources/maintenance-request.labels.temporary', [], 'مؤقت') }}
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            {{-- PDF Viewer Frame --}}
            <div class="pdf-viewer-frame">
                <iframe
                    src="{{ $pdfUrl }}"
                    class="pdf-iframe"
                    frameborder="0"
                    title="{{ __('filament-resources/maintenance-request.aria.pdf_viewer', [], 'عارض ملف PDF') }}"
                    aria-label="{{ __('filament-resources/maintenance-request.aria.pdf_content', [], 'محتوى ملف PDF لطلب الصيانة') }}"
                    loading="lazy"
                >
                    {{-- Fallback for browsers that don't support iframes --}}
                    <div class="pdf-fallback">
                        <p class="text-center text-gray-500">
                            {{ __('filament-resources/maintenance-request.messages.pdf_not_supported', [], 'متصفحك لا يدعم عرض ملفات PDF. يرجى تحميل الملف لعرضه.') }}
                        </p>
                        <a href="{{ $pdfUrl }}"
                           target="_blank"
                           class="text-primary-600 hover:text-primary-500 underline">
                            {{ __('filament-resources/maintenance-request.actions.download_pdf', [], 'تحميل PDF') }}
                        </a>
                    </div>
                </iframe>
            </div>

            {{-- PDF Controls --}}
            <div class="pdf-controls">
                <div class="pdf-controls-info">
                    <x-heroicon-o-information-circle class="h-4 w-4 flex-shrink-0" />
                    <div class="flex flex-col space-y-1">
                        <span class="hidden sm:inline">
                            {{ __('filament-resources/maintenance-request.messages.pdf_controls_tip', [], 'استخدم أدوات التحكم في المتصفح للتكبير والتصغير') }}
                        </span>
                        <span class="sm:hidden">
                            {{ __('filament-resources/maintenance-request.messages.pdf_controls_tip_mobile', [], 'اضغط مرتين للتكبير') }}
                        </span>
                        @if(isset($isPersistent) && $isPersistent)
                            <span class="text-xs text-green-600 dark:text-green-400">
                                {{ __('filament-resources/maintenance-request.messages.pdf_stored_locally', [], 'هذا الملف محفوظ محلياً ويمكن الوصول إليه في أي وقت') }}
                            </span>
                        @else
                            <span class="text-xs text-yellow-600 dark:text-yellow-400">
                                {{ __('filament-resources/maintenance-request.messages.pdf_temporary', [], 'هذا ملف مؤقت - يُنصح بتحميله للحفظ') }}
                            </span>
                        @endif
                    </div>
                </div>
                <div class="pdf-controls-actions">
                    <a href="{{ $pdfUrl }}"
                       target="_blank"
                       class="pdf-control-button">
                        <x-heroicon-o-arrow-top-right-on-square class="h-3 w-3" />
                        <span class="hidden sm:inline">{{ __('filament-resources/maintenance-request.actions.open_in_new_tab', [], 'فتح في تبويب جديد') }}</span>
                        <span class="sm:hidden">{{ __('filament-resources/maintenance-request.actions.open', [], 'فتح') }}</span>
                    </a>
                </div>
            </div>
        </div>
    @else
        {{-- Error State --}}
        <div class="pdf-error-state">
            <div class="rounded-full bg-red-100 dark:bg-red-900/20 p-3">
                <x-heroicon-o-exclamation-triangle class="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <div class="text-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ __('filament-resources/maintenance-request.messages.pdf_generation_error', [], 'خطأ في إنشاء ملف PDF') }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 max-w-md">
                    {{ __('filament-resources/maintenance-request.messages.pdf_generation_error_description', [], 'حدث خطأ أثناء إنشاء ملف PDF. يرجى التأكد من إعدادات النظام والمحاولة مرة أخرى.') }}
                </p>
                <div class="mt-4">
                    <button
                        type="button"
                        onclick="window.location.reload()"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <x-heroicon-o-arrow-path class="h-4 w-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" />
                        {{ __('filament-resources/maintenance-request.actions.retry', [], 'إعادة المحاولة') }}
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>

{{-- Enhanced Responsive Styles for PDF Modal --}}
<style>
/* Main Container - Flexbox Layout */
.pdf-modal-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 70vh;
    max-height: 90vh;
    overflow: hidden;
}

/* Loading State */
.pdf-loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-height: 300px;
    gap: 1rem;
}

/* Error State */
.pdf-error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-height: 300px;
    gap: 1rem;
    padding: 3rem 1rem;
}

/* PDF Viewer Container */
.pdf-viewer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* PDF Header */
.pdf-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 0;
    border-bottom: 1px solid rgb(229 231 235);
    flex-shrink: 0;
    gap: 1rem;
}

.dark .pdf-header {
    border-bottom-color: rgb(55 65 81);
}

.pdf-header-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 0;
    flex: 1;
}

.pdf-header-meta {
    flex-shrink: 0;
    text-align: right;
}

[dir="rtl"] .pdf-header-meta {
    text-align: left;
}

/* Storage Status Badges */
.pdf-header-meta .inline-flex {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.pdf-header-meta svg {
    flex-shrink: 0;
}

/* PDF Viewer Frame */
.pdf-viewer-frame {
    flex: 1;
    display: flex;
    border: 1px solid rgb(229 231 235);
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: #f9fafb;
    margin: 1rem 0;
}

.dark .pdf-viewer-frame {
    border-color: rgb(55 65 81);
    background-color: rgb(31 41 55);
}

/* PDF Iframe */
.pdf-iframe {
    width: 100%;
    height: 100%;
    min-height: 500px;
    border: none;
    background-color: white;
}

.dark .pdf-iframe {
    background-color: rgb(31 41 55);
}

/* PDF Fallback */
.pdf-fallback {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 1rem;
    width: 100%;
    height: 100%;
    min-height: 200px;
}

/* PDF Controls */
.pdf-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid rgb(229 231 235);
    flex-shrink: 0;
    gap: 1rem;
}

.dark .pdf-controls {
    border-top-color: rgb(55 65 81);
}

.pdf-controls-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: rgb(107 114 128);
    min-width: 0;
    flex: 1;
}

.dark .pdf-controls-info {
    color: rgb(156 163 175);
}

.pdf-controls-actions {
    flex-shrink: 0;
}

.pdf-control-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid rgb(209 213 219);
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: rgb(55 65 81);
    background-color: white;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.pdf-control-button:hover {
    background-color: rgb(249 250 251);
    border-color: rgb(156 163 175);
}

.pdf-control-button:focus {
    outline: 2px solid rgb(59 130 246);
    outline-offset: 2px;
}

.dark .pdf-control-button {
    border-color: rgb(75 85 99);
    color: rgb(209 213 219);
    background-color: rgb(31 41 55);
}

.dark .pdf-control-button:hover {
    background-color: rgb(55 65 81);
}

/* RTL Support */
[dir="rtl"] .pdf-header,
[dir="rtl"] .pdf-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .pdf-header-info,
[dir="rtl"] .pdf-controls-info {
    flex-direction: row-reverse;
}

[dir="rtl"] .pdf-control-button {
    flex-direction: row-reverse;
}

/* Loading Animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Desktop/Large Screens */
@media (min-width: 1024px) {
    .pdf-modal-container {
        min-height: 80vh;
        max-height: 85vh;
    }

    .pdf-iframe {
        min-height: 600px;
    }
}

/* Tablet Screens */
@media (min-width: 768px) and (max-width: 1023px) {
    .pdf-modal-container {
        min-height: 75vh;
        max-height: 80vh;
    }

    .pdf-iframe {
        min-height: 500px;
    }

    .pdf-header {
        padding: 0.75rem 0;
    }

    .pdf-controls {
        padding-top: 0.75rem;
    }
}

/* Mobile/Small Screens */
@media (max-width: 767px) {
    .pdf-modal-container {
        min-height: 70vh;
        max-height: 85vh;
    }

    .pdf-iframe {
        min-height: 400px;
    }

    .pdf-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem 0;
    }

    .pdf-header-info {
        width: 100%;
    }

    .pdf-header-meta {
        width: 100%;
        text-align: left;
    }

    [dir="rtl"] .pdf-header-meta {
        text-align: right;
    }

    .pdf-header-meta .flex {
        align-items: flex-start;
        gap: 0.5rem;
    }

    .pdf-header-meta .inline-flex {
        font-size: 0.6875rem;
        padding: 0.25rem 0.5rem;
    }

    .pdf-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
        padding-top: 0.75rem;
    }

    .pdf-controls-info {
        justify-content: center;
        text-align: center;
    }

    .pdf-controls-actions {
        align-self: center;
    }

    .pdf-control-button {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Extra Small Screens */
@media (max-width: 480px) {
    .pdf-modal-container {
        min-height: 65vh;
        max-height: 80vh;
    }

    .pdf-iframe {
        min-height: 350px;
    }

    .pdf-loading-state,
    .pdf-error-state {
        min-height: 250px;
        padding: 2rem 1rem;
    }

    .pdf-viewer-frame {
        margin: 0.75rem 0;
    }
}

/* Landscape Mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .pdf-modal-container {
        min-height: 85vh;
        max-height: 90vh;
    }

    .pdf-iframe {
        min-height: 300px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .pdf-iframe {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .pdf-iframe {
        background-color: rgb(31 41 55);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .animate-spin {
        animation: none;
    }

    .pdf-control-button {
        transition: none;
    }
}

/* Print Styles */
@media print {
    .pdf-modal-container {
        background: white;
        color: black;
        height: auto;
        max-height: none;
    }

    .pdf-header,
    .pdf-controls {
        display: none;
    }

    .pdf-viewer-frame {
        border: none;
        margin: 0;
    }

    .pdf-iframe {
        min-height: auto;
        height: 100vh;
    }
}

/* Focus Management */
.pdf-modal-container *:focus {
    outline: 2px solid rgb(59 130 246);
    outline-offset: 2px;
}

/* Smooth Scrolling */
.pdf-modal-container {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Prevent Body Scroll on Mobile when Modal is Open */
body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

@media (min-width: 768px) {
    body.modal-open {
        overflow: auto;
        position: static;
    }
}
</style>

{{-- Enhanced JavaScript for Modal and Scrolling Behavior --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modalContainer = document.querySelector('.pdf-modal-container');
    const iframe = document.querySelector('.pdf-iframe');

    // Modal Management
    const modalManager = {
        init() {
            this.preventBodyScroll();
            this.setupResizeHandler();
            this.setupOrientationHandler();
            this.setupPdfHandlers();
            this.setupKeyboardHandlers();
            this.setupTouchHandlers();
        },

        // Prevent body scroll on mobile when modal is open
        preventBodyScroll() {
            if (window.innerWidth <= 768) {
                document.body.classList.add('modal-open');
                document.body.style.top = `-${window.scrollY}px`;
            }
        },

        // Restore body scroll when modal closes
        restoreBodyScroll() {
            if (document.body.classList.contains('modal-open')) {
                const scrollY = document.body.style.top;
                document.body.classList.remove('modal-open');
                document.body.style.top = '';
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        },

        // Handle window resize
        setupResizeHandler() {
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    this.adjustModalHeight();
                    this.updateScrollBehavior();
                }, 150);
            });
        },

        // Handle orientation change
        setupOrientationHandler() {
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    this.adjustModalHeight();
                    this.updateScrollBehavior();
                }, 300);
            });
        },

        // Adjust modal height based on viewport
        adjustModalHeight() {
            if (!modalContainer) return;

            const viewportHeight = window.innerHeight;
            const isMobile = window.innerWidth <= 767;
            const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;

            let maxHeight;
            if (isMobile) {
                maxHeight = Math.min(viewportHeight * 0.85, viewportHeight - 60);
            } else if (isTablet) {
                maxHeight = Math.min(viewportHeight * 0.80, viewportHeight - 80);
            } else {
                maxHeight = Math.min(viewportHeight * 0.85, viewportHeight - 100);
            }

            modalContainer.style.maxHeight = `${maxHeight}px`;
        },

        // Update scroll behavior
        updateScrollBehavior() {
            if (!modalContainer) return;

            const isMobile = window.innerWidth <= 767;

            if (isMobile) {
                modalContainer.style.overflowY = 'auto';
                modalContainer.style.webkitOverflowScrolling = 'touch';
            } else {
                modalContainer.style.overflowY = 'hidden';
            }
        },

        // Setup PDF-specific handlers
        setupPdfHandlers() {
            if (!iframe) return;

            // Loading state
            iframe.addEventListener('load', () => {
                console.log('PDF loaded successfully');
                this.adjustModalHeight();

                // Add loaded class for potential styling
                iframe.classList.add('pdf-loaded');
            });

            // Error handling
            iframe.addEventListener('error', () => {
                console.error('PDF loading failed');
                iframe.classList.add('pdf-error');
            });

            // Optimize iframe for mobile
            if (window.innerWidth <= 767) {
                iframe.style.minHeight = '350px';
            }
        },

        // Setup keyboard handlers
        setupKeyboardHandlers() {
            document.addEventListener('keydown', (e) => {
                // Escape key to close modal
                if (e.key === 'Escape') {
                    const closeButton = document.querySelector('[data-modal-cancel]') ||
                                      document.querySelector('.fi-modal-close-btn');
                    if (closeButton) {
                        closeButton.click();
                    }
                }

                // Ctrl/Cmd + P to print/open PDF
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    if (iframe && iframe.src) {
                        window.open(iframe.src, '_blank');
                    }
                }

                // F11 for fullscreen (desktop only)
                if (e.key === 'F11' && window.innerWidth > 767) {
                    e.preventDefault();
                    this.toggleFullscreen();
                }
            });
        },

        // Setup touch handlers for mobile
        setupTouchHandlers() {
            if (!modalContainer || window.innerWidth > 767) return;

            let startY = 0;
            let currentY = 0;
            let isDragging = false;

            modalContainer.addEventListener('touchstart', (e) => {
                startY = e.touches[0].clientY;
                isDragging = true;
            }, { passive: true });

            modalContainer.addEventListener('touchmove', (e) => {
                if (!isDragging) return;

                currentY = e.touches[0].clientY;
                const deltaY = currentY - startY;

                // Prevent overscroll at top
                if (modalContainer.scrollTop === 0 && deltaY > 0) {
                    e.preventDefault();
                }

                // Prevent overscroll at bottom
                const isAtBottom = modalContainer.scrollTop + modalContainer.clientHeight >= modalContainer.scrollHeight;
                if (isAtBottom && deltaY < 0) {
                    e.preventDefault();
                }
            }, { passive: false });

            modalContainer.addEventListener('touchend', () => {
                isDragging = false;
            }, { passive: true });
        },

        // Toggle fullscreen mode
        toggleFullscreen() {
            if (!iframe) return;

            if (!document.fullscreenElement) {
                iframe.requestFullscreen().catch(err => {
                    console.log(`Error attempting to enable fullscreen: ${err.message}`);
                });
            } else {
                document.exitFullscreen();
            }
        }
    };

    // Initialize modal manager
    modalManager.init();

    // Cleanup on modal close
    window.addEventListener('beforeunload', () => {
        modalManager.restoreBodyScroll();
    });

    // Handle modal close events
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                const modalExists = document.querySelector('.pdf-modal-container');
                if (!modalExists) {
                    modalManager.restoreBodyScroll();
                }
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Performance optimization for iframe
    if (iframe) {
        // Lazy loading optimization
        if ('loading' in HTMLIFrameElement.prototype) {
            iframe.loading = 'lazy';
        }

        // Intersection observer for performance
        const iframeObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    iframe.style.visibility = 'visible';
                } else {
                    iframe.style.visibility = 'hidden';
                }
            });
        });

        iframeObserver.observe(iframe);
    }
});
</script>
