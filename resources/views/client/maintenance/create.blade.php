@extends('client.layouts.app')

@section('title', 'إنشاء طلب صيانة')

@section('header-title', 'إنشاء طلب صيانة')

@section('header-buttons')
    <ion-buttons slot="end">
        <ion-back-button default-href="{{ route('client.contracts.show', $contract->id) }}" text="رجوع"></ion-back-button>
    </ion-buttons>
@endsection

@section('content')
    <div class="wizard-container">
        <ion-card>
            <ion-card-header>
                <ion-card-title>طلب صيانة جديد</ion-card-title>
                <ion-card-subtitle>عقد رقم: {{ $contract->contract_number }}</ion-card-subtitle>
            </ion-card-header>

            <ion-card-content>
                <form id="request-form" method="POST" action="{{ route('client.maintenance.store', $contract->id) }}">
                    @csrf

                    <div class="wizard-step active" id="step1">
                        <h2>تفاصيل العقد</h2>

                        <ion-item>
                            <ion-label position="stacked">رقم العقد</ion-label>
                            <ion-input value="{{ $contract->contract_number }}" readonly></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">نوع العقد</ion-label>
                            <ion-input value="{{ $contract->contractType->name }}" readonly></ion-input>
                        </ion-item>

                        <div class="ion-padding-top ion-text-center">
                            <p>يمكنك إنشاء طلب صيانة جديد ضمن هذا العقد.</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(2)" color="primary">
                                التالي <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="wizard-step" id="step2">
                        <h2>تفاصيل الطلب</h2>

                        <ion-item>
                            <ion-label position="stacked">عنوان الطلب *</ion-label>
                            <ion-input name="title" required placeholder="مثال: تصليح تسرب المياه"></ion-input>
                        </ion-item>
                        @error('title')
                        <div class="error-message">{{ $message }}</div>
                        @enderror

                        <ion-item>
                            <ion-label position="stacked">وصف المشكلة *</ion-label>
                            <ion-textarea name="description" rows="4" required placeholder="يرجى وصف المشكلة بالتفصيل"></ion-textarea>
                        </ion-item>
                        @error('description')
                        <div class="error-message">{{ $message }}</div>
                        @enderror



                        <div class="ion-padding-top">
                            <p class="note">* الحقول المطلوبة</p>
                            <p class="note">ملاحظة: سيتم مراجعة طلبك من قبل فريق الصيانة وسيتم التواصل معك لتحديد موعد الزيارة.</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(1)" color="medium">
                                <ion-icon slot="start" name="arrow-back-outline"></ion-icon> السابق
                            </ion-button>

                            <ion-button onclick="goToStep(3)" color="primary">
                                التالي <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="wizard-step" id="step3">
                        <h2>تأكيد الطلب</h2>

                        <div class="request-summary">
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="6">رقم العقد:</ion-col>
                                    <ion-col size="6">{{ $contract->contract_number }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">نوع العقد:</ion-col>
                                    <ion-col size="6">{{ $contract->contractType->name }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">عنوان الطلب:</ion-col>
                                    <ion-col size="6" id="title-summary">-</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">الأولوية:</ion-col>
                                    <ion-col size="6" id="priority-summary">-</ion-col>
                                </ion-row>
                            </ion-grid>
                        </div>

                        <div class="ion-padding-top">
                            <h4>وصف المشكلة:</h4>
                            <div id="description-summary" class="description-box">-</div>
                        </div>

                        <div class="ion-padding-top ion-text-center">
                            <p>هل أنت متأكد من رغبتك في إرسال طلب الصيانة هذا؟</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(2)" color="medium">
                                <ion-icon slot="start" name="arrow-back-outline"></ion-icon> السابق
                            </ion-button>

                            <ion-button type="submit" color="success">
                                <ion-icon slot="start" name="send-outline"></ion-icon> إرسال الطلب
                            </ion-button>
                        </div>
                    </div>
                </form>
            </ion-card-content>
        </ion-card>
    </div>

    @if($errors->any())
        <ion-toast-controller id="errors-toast"></ion-toast-controller>
        <script>
            document.addEventListener('DOMContentLoaded', async () => {
                const toastController = document.getElementById('errors-toast');
                const toast = await toastController.create({
                    color: 'danger',
                    message: 'هناك أخطاء في بيانات النموذج، يرجى التحقق من البيانات المدخلة.',
                    duration: 3000,
                    position: 'top'
                });
                toast.present();
            });
        </script>
    @endif
@endsection

@section('styles')
    <style>
        .wizard-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .wizard-step {
            display: none;
        }

        .wizard-step.active {
            display: block;
        }

        .wizard-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .request-summary {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .request-summary ion-row {
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }

        .request-summary ion-row:last-child {
            border-bottom: none;
        }

        .description-box {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 16px;
            white-space: pre-line;
            min-height: 80px;
        }

        .note {
            font-size: 14px;
            color: var(--ion-color-medium);
        }

        .error-message {
            color: var(--ion-color-danger);
            font-size: 14px;
            margin: 5px 15px 15px;
        }
    </style>
@endsection

@section('scripts')
    <script>
        function goToStep(step) {
            // Hide all steps
            const steps = document.querySelectorAll('.wizard-step');
            steps.forEach(s => s.classList.remove('active'));

            // Show the selected step
            document.getElementById('step' + step).classList.add('active');

            // If going to confirmation step, update the summary
            if (step === 3) {
                updateRequestSummary();
            }
        }

        function updateRequestSummary() {
            // Get the form values
            const title = document.getElementsByName('title')[0].value || '-';
            const description = document.getElementsByName('description')[0].value || '-';
            const prioritySelect = document.getElementsByName('priority')[0];

            // Get the selected priority text
            let priorityText = '-';
            if (prioritySelect.value) {
                switch (prioritySelect.value) {
                    case 'low':
                        priorityText = 'منخفضة';
                        break;
                    case 'medium':
                        priorityText = 'متوسطة';
                        break;
                    case 'high':
                        priorityText = 'عالية';
                        break;
                    case 'critical':
                        priorityText = 'حرجة';
                        break;
                    default:
                        priorityText = prioritySelect.value;
                }
            }

            // Update the summary elements
            document.getElementById('title-summary').textContent = title;
            document.getElementById('priority-summary').textContent = priorityText;
            document.getElementById('description-summary').textContent = description;
        }

        // Set default priority to medium
        document.addEventListener('DOMContentLoaded', function() {
            const prioritySelect = document.getElementsByName('priority')[0];
            prioritySelect.value = 'medium';
        });
    </script>
@endsection
