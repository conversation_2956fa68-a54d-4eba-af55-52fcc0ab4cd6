@extends('client.layouts.app')

@section('title', 'طلبات الشهادات')

@section('header-title', 'طلبات الشهادات')

@section('content')
    <div class="ion-padding">
        <ion-grid>
            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>طلبات الشهادات</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($requests) > 0)
                                <ion-list>
                                    @foreach($requests as $request)
                                        <ion-item button href="{{ route('client.certificates.show', $request->id) }}">
                                            <ion-label>
                                                <h2>{{ $request->title }}</h2>
                                                <p>
                                                    <ion-text color="medium">رقم الطلب: {{ $request->request_number }}</ion-text>
                                                    <ion-text color="dark"> | تاريخ الطلب: {{ \Carbon\Carbon::parse($request->request_date)->format('Y-m-d') }}</ion-text>
                                                </p>
                                                <p>
                                                    <ion-text color="medium">تاريخ الفحص المفضل: {{ \Carbon\Carbon::parse($request->preferred_inspection_date)->format('Y-m-d') }}</ion-text>
                                                    @if($request->scheduled_inspection_date)
                                                        <ion-text color="success"> | تاريخ الفحص المجدول: {{ \Carbon\Carbon::parse($request->scheduled_inspection_date)->format('Y-m-d') }}</ion-text>
                                                    @endif
                                                </p>
                                            </ion-label>
                                            <div slot="end">
                                                <ion-badge color="{{
                                                    $request->status == 'pending' ? 'warning' :
                                                    ($request->status == 'processing' ? 'primary' :
                                                    ($request->status == 'inspection_scheduled' ? 'tertiary' :
                                                    ($request->status == 'inspection_completed' ? 'secondary' :
                                                    ($request->status == 'certificate_issued' ? 'success' :
                                                    ($request->status == 'completed' ? 'success' : 'danger')))))
                                                }}">
                                                    {{
                                                        $request->status == 'pending' ? 'قيد الانتظار' :
                                                        ($request->status == 'processing' ? 'قيد المعالجة' :
                                                        ($request->status == 'inspection_scheduled' ? 'الفحص مجدول' :
                                                        ($request->status == 'inspection_completed' ? 'تم الفحص' :
                                                        ($request->status == 'certificate_issued' ? 'تم إصدار الشهادة' :
                                                        ($request->status == 'completed' ? 'مكتمل' : 'ملغي')))))
                                                    }}
                                                </ion-badge>
                                                <p>
                                                    <ion-text color="primary">
                                                        <strong dir="ltr">{{ number_format($request->total_fee, 2) }} ريال</strong>
                                                    </ion-text>
                                                </p>
                                            </div>
                                        </ion-item>
                                    @endforeach
                                </ion-list>
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد طلبات شهادات.</p>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>
        </ion-grid>
    </div>
@endsection
