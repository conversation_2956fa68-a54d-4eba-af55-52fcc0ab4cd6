@extends('client.layouts.app')

@section('title', 'تفاصيل طلب الشهادة')

@section('header-title', 'تفاصيل طلب الشهادة')

@section('content')
    <div class="ion-padding">
        <ion-grid>
            <ion-row>
                <ion-col size="12" class="ion-text-start">
                    <ion-button href="{{ route('client.certificates.index') }}" color="medium" fill="clear">
                        <ion-icon slot="start" name="arrow-back-outline"></ion-icon>
                        العودة إلى قائمة الطلبات
                    </ion-button>
                </ion-col>
            </ion-row>

            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <div class="ion-padding-bottom">
                                <ion-badge color="{{
                                    $request->status == 'pending' ? 'warning' :
                                    ($request->status == 'processing' ? 'primary' :
                                    ($request->status == 'inspection_scheduled' ? 'tertiary' :
                                    ($request->status == 'inspection_completed' ? 'secondary' :
                                    ($request->status == 'certificate_issued' ? 'success' :
                                    ($request->status == 'completed' ? 'success' : 'danger')))))
                                }}">
                                    {{
                                        $request->status == 'pending' ? 'قيد الانتظار' :
                                        ($request->status == 'processing' ? 'قيد المعالجة' :
                                        ($request->status == 'inspection_scheduled' ? 'الفحص مجدول' :
                                        ($request->status == 'inspection_completed' ? 'تم الفحص' :
                                        ($request->status == 'certificate_issued' ? 'تم إصدار الشهادة' :
                                        ($request->status == 'completed' ? 'مكتمل' : 'ملغي')))))
                                    }}
                                </ion-badge>
                            </div>
                            <ion-card-title>{{ $request->title }}</ion-card-title>
                            <ion-card-subtitle>رقم الطلب: {{ $request->request_number }}</ion-card-subtitle>
                        </ion-card-header>

                        <ion-card-content>
                            <ion-list lines="none">
                                <ion-item>
                                    <ion-label>
                                        <ion-text color="medium">تاريخ الطلب</ion-text>
                                        <h2>{{ \Carbon\Carbon::parse($request->request_date)->format('Y-m-d') }}</h2>
                                    </ion-label>
                                </ion-item>

                                <ion-item>
                                    <ion-label>
                                        <ion-text color="medium">الأولوية</ion-text>
                                        <h2>
                                            @if($request->priority == 'low')
                                                <ion-text color="success">منخفضة</ion-text>
                                            @elseif($request->priority == 'medium')
                                                <ion-text color="warning">متوسطة</ion-text>
                                            @elseif($request->priority == 'high')
                                                <ion-text color="danger">عالية</ion-text>
                                            @else
                                                <ion-text color="danger">حرجة</ion-text>
                                            @endif
                                        </h2>
                                    </ion-label>
                                </ion-item>

                                <ion-item>
                                    <ion-label>
                                        <ion-text color="medium">تاريخ الفحص المفضل</ion-text>
                                        <h2>{{ \Carbon\Carbon::parse($request->preferred_inspection_date)->format('Y-m-d') }}</h2>
                                    </ion-label>
                                </ion-item>

                                @if($request->scheduled_inspection_date)
                                    <ion-item>
                                        <ion-label>
                                            <ion-text color="medium">تاريخ الفحص المجدول</ion-text>
                                            <h2>{{ \Carbon\Carbon::parse($request->scheduled_inspection_date)->format('Y-m-d') }}</h2>
                                        </ion-label>
                                    </ion-item>
                                @endif

                                @if($request->completion_date)
                                    <ion-item>
                                        <ion-label>
                                            <ion-text color="medium">تاريخ الإكمال</ion-text>
                                            <h2>{{ \Carbon\Carbon::parse($request->completion_date)->format('Y-m-d') }}</h2>
                                        </ion-label>
                                    </ion-item>
                                @endif

                                @if($request->certificate_number)
                                    <ion-item>
                                        <ion-label>
                                            <ion-text color="medium">رقم الشهادة</ion-text>
                                            <h2>{{ $request->certificate_number }}</h2>
                                        </ion-label>
                                    </ion-item>
                                @endif

                                <ion-item-divider>
                                    <ion-label>الوصف</ion-label>
                                </ion-item-divider>
                                <ion-item>
                                    <ion-label class="ion-text-wrap">
                                        <p>{{ $request->description }}</p>
                                    </ion-label>
                                </ion-item>

                                <ion-item-divider>
                                    <ion-label>الرسوم</ion-label>
                                </ion-item-divider>
                                <ion-item>
                                    <ion-label>
                                        <ion-text color="medium">رسوم الفحص</ion-text>
                                    </ion-label>
                                    <ion-text slot="end" color="dark">
                                        <h2 dir="ltr">{{ number_format($request->inspection_fee, 2) }} ريال</h2>
                                    </ion-text>
                                </ion-item>
                                <ion-item>
                                    <ion-label>
                                        <ion-text color="medium">رسوم إصدار الشهادة</ion-text>
                                    </ion-label>
                                    <ion-text slot="end" color="dark">
                                        <h2 dir="ltr">{{ number_format($request->certificate_fee, 2) }} ريال</h2>
                                    </ion-text>
                                </ion-item>
                                <ion-item>
                                    <ion-label>
                                        <ion-text color="medium">إجمالي الرسوم</ion-text>
                                    </ion-label>
                                    <ion-text slot="end" color="primary">
                                        <h2 dir="ltr"><strong>{{ number_format($request->total_fee, 2) }} ريال</strong></h2>
                                    </ion-text>
                                </ion-item>

                                @if($request->notes)
                                    <ion-item-divider>
                                        <ion-label>ملاحظات</ion-label>
                                    </ion-item-divider>
                                    <ion-item>
                                        <ion-label class="ion-text-wrap">
                                            <p>{{ $request->notes }}</p>
                                        </ion-label>
                                    </ion-item>
                                @endif
                            </ion-list>

                            @if(in_array($request->status, ['pending', 'processing']))
                                <div class="ion-padding ion-text-center">
                                    <ion-button color="danger" id="confirm-cancel">
                                        <ion-icon slot="start" name="close-outline"></ion-icon>
                                        إلغاء الطلب
                                    </ion-button>
                                </div>

                                <ion-alert
                                    trigger="confirm-cancel"
                                    header="تأكيد الإلغاء"
                                    message="هل أنت متأكد من رغبتك في إلغاء هذا الطلب؟">
                                </ion-alert>
                            @endif

                            @if($request->status == 'certificate_issued' || $request->status == 'completed')
                                <div class="ion-padding ion-text-center">
                                    <ion-button color="success">
                                        <ion-icon slot="start" name="document-outline"></ion-icon>
                                        تحميل الشهادة
                                    </ion-button>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>
        </ion-grid>
    </div>
@endsection

@section('scripts')
    <script>
        const alert = document.querySelector('ion-alert');

        alert.buttons = [
            {
                text: 'إلغاء',
                role: 'cancel'
            },
            {
                text: 'تأكيد',
                role: 'confirm',
                handler: () => {
                    window.location.href = '{{ \Illuminate\Support\Facades\URL::signedRoute('client.certificates.cancel', $request->id) }}';
                }
            }
        ];
    </script>
@endsection
