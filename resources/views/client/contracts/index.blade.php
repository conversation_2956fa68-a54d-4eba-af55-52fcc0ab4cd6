@extends('client.layouts.app')

@section('title', 'العقود')

@section('header-title', 'العقود')

@section('content')
    <div>
        <ion-grid>
            <ion-row>
                <ion-col size="12">
                    <div class="ion-padding-bottom">
                        <h1>العقود</h1>
                        <p>قائمة بجميع العقود الخاصة بك</p>
                    </div>
                </ion-col>
            </ion-row>

            <ion-row>
                <ion-col size="12">
                    <ion-segment value="all" id="contract-filter">
                        <ion-segment-button value="all">
                            <ion-label>الكل</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="active">
                            <ion-label>النشطة</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="pending">
                            <ion-label>قيد الانتظار</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="expired">
                            <ion-label>المنتهية</ion-label>
                        </ion-segment-button>
                    </ion-segment>
                </ion-col>
            </ion-row>

            <ion-row class="ion-padding-top">
                <ion-col size="12">
                    @if(count($contracts) > 0)
                        @foreach($contracts as $contract)
                            <ion-card class="card-status-{{ $contract->status }} contract-card" data-status="{{ $contract->status }}">
                                <ion-card-header>
                                    <ion-card-subtitle>عقد رقم: {{ $contract->contract_number }}</ion-card-subtitle>
                                    <ion-card-title>{{ $contract->contractType->name }}</ion-card-title>
                                </ion-card-header>

                                <ion-card-content>
                                    <ion-grid>
                                        <ion-row>
                                            <ion-col size="12" size-md="6">
                                                <ion-item lines="none">
                                                    <ion-icon name="calendar-outline" slot="start"></ion-icon>
                                                    <ion-label>
                                                        <h3>تاريخ البداية</h3>
                                                        <p>{{ \Carbon\Carbon::parse($contract->start_date)->format('Y-m-d') }}</p>
                                                    </ion-label>
                                                </ion-item>
                                            </ion-col>

                                            <ion-col size="12" size-md="6">
                                                <ion-item lines="none">
                                                    <ion-icon name="calendar-outline" slot="start"></ion-icon>
                                                    <ion-label>
                                                        <h3>تاريخ الانتهاء</h3>
                                                        <p>{{ \Carbon\Carbon::parse($contract->end_date)->format('Y-m-d') }}</p>
                                                    </ion-label>
                                                </ion-item>
                                            </ion-col>
                                        </ion-row>

                                        <ion-row>
                                            <ion-col size="12" size-md="6">
                                                <ion-item lines="none">
                                                    <ion-icon name="cash-outline" slot="start"></ion-icon>
                                                    <ion-label>
                                                        <h3>قيمة العقد</h3>
                                                        <p dir="ltr">{{ number_format($contract->contract_value, 2) }} ريال</p>
                                                    </ion-label>
                                                </ion-item>
                                            </ion-col>

                                            <ion-col size="12" size-md="6">
                                                <ion-item lines="none">
                                                    <ion-icon name="analytics-outline" slot="start"></ion-icon>
                                                    <ion-label>
                                                        <h3>الحالة</h3>
                                                        <ion-badge color="{{ $contract->status == 'active' ? 'success' : ($contract->status == 'pending' ? 'warning' : ($contract->status == 'expired' ? 'danger' : 'medium')) }}">
                                                            {{ $contract->status == 'active' ? 'نشط' :
                                                               ($contract->status == 'pending' ? 'قيد الانتظار' :
                                                                ($contract->status == 'expired' ? 'منتهي' :
                                                                 ($contract->status == 'terminated' ? 'منتهي مبكراً' : $contract->status))) }}
                                                        </ion-badge>
                                                    </ion-label>
                                                </ion-item>
                                            </ion-col>
                                        </ion-row>

                                        @if($contract->status == 'active' || $contract->status == 'pending')
                                            <ion-row>
                                                <ion-col size="12" size-md="6">
                                                    <ion-item lines="none">
                                                        <ion-icon name="build-outline" slot="start"></ion-icon>
                                                        <ion-label>
                                                            <h3>الزيارات المتبقية</h3>
                                                            <p>{{ $contract->remainingVisits }}</p>
                                                        </ion-label>
                                                    </ion-item>
                                                </ion-col>

                                                @if($contract->next_payment_date)
                                                    <ion-col size="12" size-md="6">
                                                        <ion-item lines="none">
                                                            <ion-icon name="card-outline" slot="start"></ion-icon>
                                                            <ion-label>
                                                                <h3>الدفعة القادمة</h3>
                                                                <p>{{ \Carbon\Carbon::parse($contract->next_payment_date)->format('Y-m-d') }}</p>
                                                            </ion-label>
                                                        </ion-item>
                                                    </ion-col>
                                                @endif
                                            </ion-row>
                                        @endif

                                        <ion-row class="ion-padding-top">
                                            <ion-col>
                                                <ion-button expand="block" href="{{ route('client.contracts.show', $contract->id) }}">
                                                    التفاصيل
                                                </ion-button>
                                            </ion-col>

                                            @if($contract->status == 'active')
                                                <ion-col>
                                                    <ion-button expand="block" color="tertiary" href="{{ route('client.payments.create', $contract->id) }}">
                                                        <ion-icon name="cash-outline" slot="start"></ion-icon>
                                                        دفع
                                                    </ion-button>
                                                </ion-col>

                                                @if($contract->remainingVisits > 0)
                                                    <ion-col>
                                                        <ion-button expand="block" color="success" href="{{ route('client.visits.schedule', $contract->id) }}">
                                                            <ion-icon name="calendar-outline" slot="start"></ion-icon>
                                                            جدولة زيارة
                                                        </ion-button>
                                                    </ion-col>
                                                @endif
                                            @endif
                                        </ion-row>
                                    </ion-grid>
                                </ion-card-content>
                            </ion-card>
                        @endforeach
                    @else
                        <div class="ion-padding ion-text-center">
                            <ion-icon name="document-outline" size="large" color="medium"></ion-icon>
                            <p>لا توجد عقود متاحة.</p>
                        </div>
                    @endif
                </ion-col>
            </ion-row>
        </ion-grid>
    </div>
@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const contractFilter = document.getElementById('contract-filter');
            const contractCards = document.querySelectorAll('.contract-card');

            contractFilter.addEventListener('ionChange', function(event) {
                const selectedStatus = event.detail.value;

                contractCards.forEach(card => {
                    const cardStatus = card.getAttribute('data-status');

                    if (selectedStatus === 'all') {
                        card.style.display = 'block';
                    } else {
                        if (cardStatus === selectedStatus) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    }
                });
            });
        });
    </script>
@endsection

@section('styles')
    <style>
        .card-status-active {
            border-right: 4px solid var(--ion-color-success);
        }

        .card-status-pending {
            border-right: 4px solid var(--ion-color-warning);
        }

        .card-status-expired {
            border-right: 4px solid var(--ion-color-danger);
        }

        .card-status-terminated {
            border-right: 4px solid var(--ion-color-medium);
        }
    </style>
@endsection
