@extends('client.layouts.app')

@section('title', 'تفاصيل العقد')

@section('header-title', 'تفاصيل العقد')

@section('header-buttons')
    <ion-buttons slot="end">
        <ion-back-button default-href="{{ route('client.contracts') }}" text="العقود"></ion-back-button>
    </ion-buttons>
@endsection

@section('content')
    <div>
        <ion-grid>
            <ion-row>
                <ion-col size="12">
                    <ion-card class="card-status-{{ $contract->status }}">
                        <ion-card-header>
                            <ion-card-subtitle>
                                <ion-badge color="{{ $contract->status == 'active' ? 'success' : ($contract->status == 'pending' ? 'warning' : ($contract->status == 'expired' ? 'danger' : 'medium')) }}">
                                    {{ $contract->status == 'active' ? 'نشط' :
                                       ($contract->status == 'pending' ? 'قيد الانتظار' :
                                        ($contract->status == 'expired' ? 'منتهي' :
                                         ($contract->status == 'terminated' ? 'منتهي مبكراً' : $contract->status))) }}
                                </ion-badge>
                            </ion-card-subtitle>
                            <ion-card-title>
                                عقد رقم: {{ $contract->contract_number }} - {{ $contract->contractType->name }}
                            </ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="12" size-md="6">
                                        <ion-item lines="none">
                                            <ion-icon name="heroicon-o-calendar" slot="start"></ion-icon>
                                            <ion-label>
                                                <h3>تاريخ البداية</h3>
                                                <p>{{ \Carbon\Carbon::parse($contract->start_date)->format('Y-m-d') }}</p>
                                            </ion-label>
                                        </ion-item>
                                    </ion-col>

                                    <ion-col size="12" size-md="6">
                                        <ion-item lines="none">
                                            <ion-icon name="calendar-outline" slot="start"></ion-icon>
                                            <ion-label>
                                                <h3>تاريخ الانتهاء</h3>
                                                <p>{{ \Carbon\Carbon::parse($contract->end_date)->format('Y-m-d') }}</p>
                                            </ion-label>
                                        </ion-item>
                                    </ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="12" size-md="6">
                                        <ion-item lines="none">
                                            <ion-icon name="cash-outline" slot="start"></ion-icon>
                                            <ion-label>
                                                <h3>قيمة العقد</h3>
                                                <p dir="ltr">{{ number_format($contract->contract_value, 2) }} ريال</p>
                                            </ion-label>
                                        </ion-item>
                                    </ion-col>

                                    <ion-col size="12" size-md="6">
                                        <ion-item lines="none">
                                            <ion-icon name="repeat-outline" slot="start"></ion-icon>
                                            <ion-label>
                                                <h3>دورة الدفع</h3>
                                                <p>{{ $contract->payment_frequency == 'one_time' ? 'دفعة واحدة' :
                                                ($contract->payment_frequency == 'monthly' ? 'شهرياً' :
                                                ($contract->payment_frequency == 'quarterly' ? 'ربع سنوي' :
                                                ($contract->payment_frequency == 'biannually' ? 'نصف سنوي' :
                                                ($contract->payment_frequency == 'annually' ? 'سنوياً' : $contract->payment_frequency)))) }}</p>
                                            </ion-label>
                                        </ion-item>
                                    </ion-col>
                                </ion-row>

                                @if($contract->status == 'active' || $contract->status == 'pending')
                                    <ion-row>
                                        <ion-col size="12" size-md="6">
                                            <ion-item lines="none">
                                                <ion-icon name="build-outline" slot="start"></ion-icon>
                                                <ion-label>
                                                    <h3>الزيارات المتبقية</h3>
                                                    <p>{{ $contract->remainingVisits }} / {{ $contract->maintenance_visits_included }}</p>
                                                </ion-label>
                                            </ion-item>
                                        </ion-col>

                                        @if($contract->next_payment_date)
                                            <ion-col size="12" size-md="6">
                                                <ion-item lines="none">
                                                    <ion-icon name="card-outline" slot="start"></ion-icon>
                                                    <ion-label>
                                                        <h3>الدفعة القادمة</h3>
                                                        <p>{{ \Carbon\Carbon::parse($contract->next_payment_date)->format('Y-m-d') }}</p>
                                                    </ion-label>
                                                </ion-item>
                                            </ion-col>
                                        @endif
                                    </ion-row>
                                @endif
                            </ion-grid>

                            <div class="action-buttons ion-padding-top">
                                <ion-grid>
                                    <ion-row>
                                        @if($contract->status == 'active')
                                            @if($contract->next_payment_date)
                                                <ion-col>
                                                    <ion-button expand="block" color="tertiary" href="{{ route('client.payments.create', $contract->id) }}">
                                                        <ion-icon name="cash-outline" slot="start"></ion-icon>
                                                        دفع
                                                    </ion-button>
                                                </ion-col>
                                            @endif

                                            @if($contract->remainingVisits > 0)
                                                <ion-col>
                                                    <ion-button expand="block" color="success" href="{{ route('client.visits.schedule', $contract->id) }}">
                                                        <ion-icon name="calendar-outline" slot="start"></ion-icon>
                                                        جدولة زيارة
                                                    </ion-button>
                                                </ion-col>
                                            @endif

                                            <ion-col>
                                                <ion-button expand="block" color="warning" href="{{ route('client.maintenance.create', $contract->id) }}">
                                                    <ion-icon name="construct-outline" slot="start"></ion-icon>
                                                    طلب صيانة
                                                </ion-button>
                                            </ion-col>
                                        @endif
                                    </ion-row>
                                </ion-grid>
                            </div>
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>

            <!-- Contract Terms Section -->
            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>شروط العقد</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if($contract->terms)
                                <div class="terms-content">
                                    {!! nl2br(e($contract->terms)) !!}
                                </div>
                            @else
                                <p class="ion-text-center">لا توجد شروط محددة لهذا العقد.</p>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>

            <!-- Upcoming Visits Section -->
            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>الزيارات القادمة</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($upcomingVisits) > 0)
                                <ion-list>
                                    @foreach($upcomingVisits as $visit)
                                        <ion-item href="{{ route('client.visits.show', $visit->id) }}">
                                            <ion-label>
                                                <h2>{{ $visit->maintenanceRequest->title }}</h2>
                                                <p>
                                                    <ion-text color="primary">{{ \Carbon\Carbon::parse($visit->scheduled_at)->format('Y-m-d') }}</ion-text>
                                                    في الساعة {{ \Carbon\Carbon::parse($visit->scheduled_at)->format('h:i A') }}
                                                </p>
                                            </ion-label>
                                            <ion-badge slot="end" color="{{ $visit->status == 'scheduled' ? 'primary' : 'success' }}">
                                                {{ $visit->status == 'scheduled' ? 'مجدولة' : 'قيد التنفيذ' }}
                                            </ion-badge>
                                        </ion-item>
                                    @endforeach
                                </ion-list>
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد زيارات قادمة لهذا العقد.</p>
                                    @if($contract->status == 'active' && $contract->remainingVisits > 0)
                                        <ion-button size="small" href="{{ route('client.visits.schedule', $contract->id) }}">
                                            جدولة زيارة جديدة
                                        </ion-button>
                                    @endif
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>

            <!-- Payment History Section -->
            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>سجل المدفوعات</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($contract->payments) > 0)
                                <ion-list>
                                    @foreach($contract->payments as $payment)
                                        <ion-item>
                                            <ion-label>
                                                <h2>رقم المعاملة: {{ $payment->payment_number }}</h2>
                                                <p>
                                                    <ion-text color="medium">{{ \Carbon\Carbon::parse($payment->payment_date)->format('Y-m-d') }}</ion-text>
                                                    <ion-text color="dark"> | الطريقة: {{ $payment->payment_method == 'cash' ? 'نقدًا' :
                                                ($payment->payment_method == 'bank_transfer' ? 'تحويل بنكي' :
                                                ($payment->payment_method == 'check' ? 'شيك' :
                                                ($payment->payment_method == 'credit_card' ? 'بطاقة ائتمان' :
                                                ($payment->payment_method == 'online' ? 'دفع إلكتروني' : $payment->payment_method)))) }}</ion-text>
                                                </p>
                                            </ion-label>
                                            <ion-text slot="end" color="success">
                                                <h3 dir="ltr">{{ number_format($payment->amount, 2) }} ريال</h3>
                                            </ion-text>
                                        </ion-item>
                                    @endforeach
                                </ion-list>
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد مدفوعات لهذا العقد.</p>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>

            <!-- Maintenance Requests Section -->
            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>طلبات الصيانة</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($contract->maintenanceRequests) > 0)
                                <ion-list>
                                    @foreach($contract->maintenanceRequests as $request)
                                        <ion-item href="{{ route('client.maintenance.show', $request->id) }}">
                                            <ion-label>
                                                <h2>{{ $request->title }}</h2>
                                                <p>
                                                    <ion-text color="medium">{{ \Carbon\Carbon::parse($request->request_date)->format('Y-m-d') }}</ion-text>
                                                    <ion-text color="dark"> | الأولوية:
                                                        <ion-text color="{{ $request->priority == 'low' ? 'success' : ($request->priority == 'medium' ? 'warning' : ($request->priority == 'high' ? 'danger' : 'danger')) }}">
                                                            {{ $request->priority == 'low' ? 'منخفضة' :
                                                            ($request->priority == 'medium' ? 'متوسطة' :
                                                            ($request->priority == 'high' ? 'عالية' :
                                                            ($request->priority == 'critical' ? 'حرجة' : $request->priority))) }}
                                                        </ion-text>
                                                    </ion-text>
                                                </p>
                                            </ion-label>
                                            <ion-badge slot="end" color="{{ $request->status == 'new' ? 'primary' :
                                        ($request->status == 'assigned' ? 'tertiary' :
                                        ($request->status == 'in_progress' ? 'warning' :
                                        ($request->status == 'completed' ? 'success' :
                                        ($request->status == 'canceled' ? 'danger' : 'medium')))) }}">
                                                {{ $request->status == 'new' ? 'جديد' :
                                                ($request->status == 'assigned' ? 'تم التعيين' :
                                                ($request->status == 'in_progress' ? 'قيد التنفيذ' :
                                                ($request->status == 'completed' ? 'مكتمل' :
                                                ($request->status == 'canceled' ? 'ملغي' : $request->status)))) }}
                                            </ion-badge>
                                        </ion-item>
                                    @endforeach
                                </ion-list>
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد طلبات صيانة لهذا العقد.</p>
                                    @if($contract->status == 'active')
                                        <ion-button size="small" href="{{ route('client.maintenance.create', $contract->id) }}">
                                            إنشاء طلب صيانة
                                        </ion-button>
                                    @endif
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>
        </ion-grid>
    </div>
@endsection

@section('styles')
    <style>
        .terms-content {
            white-space: pre-line;
        }

        .action-buttons ion-button {
            margin-bottom: 8px;
        }
    </style>
@endsection
