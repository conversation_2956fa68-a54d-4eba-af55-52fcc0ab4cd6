<!DOCTYPE html>
<html lang="en" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - @yield('title', __('client.navigation.system_title'))</title>

    <!-- Ionic Framework CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ionic/core/css/ionic.bundle.css" />

    <!-- Custom CSS -->
    <style>
        :root {
            --ion-color-primary: #F34D02;
            --ion-color-primary-rgb: 243, 77, 2;
            --ion-color-primary-contrast: #ffffff;
            --ion-color-primary-contrast-rgb: 255, 255, 255;
            --ion-color-primary-shade: #DA4502;
            --ion-color-primary-tint: #F46F2E;

            --ion-color-secondary: #F39F07;
            --ion-color-secondary-rgb: 243, 159, 7;
            --ion-color-secondary-contrast: #ffffff;
            --ion-color-secondary-contrast-rgb: 255, 255, 255;
            --ion-color-secondary-shade: #DA8F06;
            --ion-color-secondary-tint: #F5AF33;

            --ion-color-success: #2dd36f;
            --ion-color-success-rgb: 45, 211, 111;
            --ion-color-success-contrast: #ffffff;
            --ion-color-success-contrast-rgb: 255, 255, 255;
            --ion-color-success-shade: #28ba62;
            --ion-color-success-tint: #42d77d;

            --ion-color-warning: #ffc409;
            --ion-color-warning-rgb: 255, 196, 9;
            --ion-color-warning-contrast: #000000;
            --ion-color-warning-contrast-rgb: 0, 0, 0;
            --ion-color-warning-shade: #e0ac08;
            --ion-color-warning-tint: #ffca22;

            --ion-color-danger: #eb445a;
            --ion-color-danger-rgb: 235, 68, 90;
            --ion-color-danger-contrast: #ffffff;
            --ion-color-danger-contrast-rgb: 255, 255, 255;
            --ion-color-danger-shade: #cf3c4f;
            --ion-color-danger-tint: #ed576b;

            --ion-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        .menu-content-open {
            transform: translateX(0px);
        }

        ion-content {
            --background: #f5f5f5;
        }

        .content-container {
            padding: 16px;
        }

        .card-status-active {
            border-right: 4px solid var(--ion-color-success);
        }

        .card-status-pending {
            border-right: 4px solid var(--ion-color-warning);
        }

        .card-status-expired {
            border-right: 4px solid var(--ion-color-danger);
        }

        .card-status-terminated {
            border-right: 4px solid var(--ion-color-medium);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .dashboard-tile {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .dashboard-tile h3 {
            margin-top: 0;
            color: var(--ion-color-dark);
        }

        .dashboard-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 8px 0;
            color: var(--ion-color-primary);
        }

        .wizard-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .wizard-step {
            display: none;
        }

        .wizard-step.active {
            display: block;
        }

        .wizard-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        /* RTL Support */
        [dir="rtl"] ion-back-button {
            transform: scaleX(-1);
        }
    </style>

    @yield('styles')
</head>
<body>
<ion-app>
    <!-- Menu (Side navigation) -->
    <ion-menu content-id="main-content" type="overlay">
        <ion-header>
            <ion-toolbar color="primary">
                <ion-title>{{ __('client.navigation.system_title') }}</ion-title>
            </ion-toolbar>
        </ion-header>

        <ion-content>
            <ion-list>
                <ion-item href="{{ route('client.dashboard') }}" @if(request()->routeIs('client.dashboard')) color="light" @endif>
                    <ion-icon name="home-outline" slot="start"></ion-icon>
                    <ion-label>{{ __('client.navigation.dashboard') }}</ion-label>
                </ion-item>

                <ion-item href="{{ route('client.contracts') }}" @if(request()->routeIs('client.contracts*')) color="light" @endif>
                    <ion-icon name="document-text-outline" slot="start"></ion-icon>
                    <ion-label>{{ __('client.navigation.contracts') }}</ion-label>
                </ion-item>

                <ion-item href="{{ route('client.maintenance.index') }}" @if(request()->routeIs('client.maintenance*')) color="light" @endif>
                    <ion-icon name="construct-outline" slot="start"></ion-icon>
                    <ion-label>{{ __('client.navigation.maintenance_requests') }}</ion-label>
                </ion-item>

                <ion-item href="{{ route('client.visits') }}" @if(request()->routeIs('client.visits*')) color="light" @endif>
                    <ion-icon name="calendar-outline" slot="start"></ion-icon>
                    <ion-label>الزيارات</ion-label>
                </ion-item>

                <ion-item href="{{ route('client.profile') }}" @if(request()->routeIs('client.profile*')) color="light" @endif>
                    <ion-icon name="person-outline" slot="start"></ion-icon>
                    <ion-label>الملف الشخصي</ion-label>
                </ion-item>

                <ion-item href="javascript:void(0)">
                    <ion-icon name="log-out-outline" slot="start"></ion-icon>
                    <ion-label onclick="document.getElementById('logout-form').submit()">تسجيل الخروج</ion-label>
                </ion-item>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </ion-list>
        </ion-content>
    </ion-menu>

    <!-- Main Content -->
    <div class="ion-page" id="main-content">
        <ion-header>
            <ion-toolbar color="primary">
                <ion-buttons slot="start">
                    <ion-menu-button></ion-menu-button>
                </ion-buttons>
                <ion-title>@yield('header-title', 'بوابة العملاء')</ion-title>
                @yield('header-buttons')
            </ion-toolbar>
        </ion-header>

        <ion-content class="ion-padding">
            @if(session('success'))
                <ion-toast-controller id="success-toast"></ion-toast-controller>
                <script>
                    document.addEventListener('DOMContentLoaded', async () => {
                        const toastController = document.getElementById('success-toast');
                        const toast = await toastController.create({
                            color: 'success',
                            message: '{{ session('success') }}',
                            duration: 3000,
                            position: 'top'
                        });
                        toast.present();
                    });
                </script>
            @endif

            @if(session('error'))
                <ion-toast-controller id="error-toast"></ion-toast-controller>
                <script>
                    document.addEventListener('DOMContentLoaded', async () => {
                        const toastController = document.getElementById('error-toast');
                        const toast = await toastController.create({
                            color: 'danger',
                            message: '{{ session('error') }}',
                            duration: 3000,
                            position: 'top'
                        });
                        toast.present();
                    });
                </script>
            @endif

            <div class="content-container">
                @yield('content')
            </div>
        </ion-content>
    </div>
</ion-app>

<!-- Ionic Framework JavaScript -->
<script type="module" src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.esm.js"></script>
<script nomodule src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.js"></script>

<!-- Additional JavaScript -->
@yield('scripts')
</body>
</html>
