@extends('client.layouts.app')

@section('title', 'جدولة زيارة')

@section('header-title', 'جدولة زيارة')

@section('header-buttons')
    <ion-buttons slot="end">
        <ion-back-button default-href="{{ route('client.contracts.show', $contract->id) }}" text="رجوع"></ion-back-button>
    </ion-buttons>
@endsection

@section('content')
    <div class="wizard-container">
        <ion-card>
            <ion-card-header>
                <ion-card-title>جدولة زيارة صيانة</ion-card-title>
                <ion-card-subtitle>عقد رقم: {{ $contract->contract_number }}</ion-card-subtitle>
            </ion-card-header>

            <ion-card-content>
                <form id="schedule-form" method="POST" action="{{ route('client.visits.process', $contract->id) }}">
                    @csrf

                    <div class="wizard-step active" id="step1">
                        <h2>تفاصيل العقد</h2>

                        <ion-item>
                            <ion-label position="stacked">رقم العقد</ion-label>
                            <ion-input value="{{ $contract->contract_number }}" readonly></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">نوع العقد</ion-label>
                            <ion-input value="{{ $contract->contractType->name }}" readonly></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">الزيارات المتبقية</ion-label>
                            <ion-input value="{{ $contract->remainingVisits }} / {{ $contract->maintenance_visits_included }}" readonly></ion-input>
                        </ion-item>

                        <div class="ion-padding-top ion-text-center">
                            <p>يمكنك استخدام إحدى الزيارات المتبقية في عقدك لجدولة زيارة صيانة.</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(2)" color="primary">
                                التالي <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="wizard-step" id="step2">
                        <h2>اختر موعد الزيارة</h2>

                        <ion-item>
                            <ion-label position="stacked">التاريخ المفضل *</ion-label>
                            <ion-input type="date" name="preferred_date" min="{{ date('Y-m-d', strtotime('+1 day')) }}" required></ion-input>
                        </ion-item>
                        @error('preferred_date')
                        <div class="error-message">{{ $message }}</div>
                        @enderror

                        <ion-item>
                            <ion-label position="stacked">الوقت المفضل *</ion-label>
                            <ion-input type="time" name="preferred_time" required></ion-input>
                        </ion-item>
                        @error('preferred_time')
                        <div class="error-message">{{ $message }}</div>
                        @enderror

                        <ion-item>
                            <ion-label position="stacked">وصف المشكلة أو سبب الزيارة (اختياري)</ion-label>
                            <ion-textarea name="description" rows="4" placeholder="يرجى وصف سبب طلب الزيارة أو المشكلة التي تواجهها"></ion-textarea>
                        </ion-item>

                        <div class="ion-padding-top">
                            <p class="note">* الحقول المطلوبة</p>
                            <p class="note">ملاحظة: سيتم تأكيد الموعد النهائي من قبل فريق الصيانة لدينا وقد يختلف عن الموعد المفضل الذي اخترته بناءً على جدول الفنيين.</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(1)" color="medium">
                                <ion-icon slot="start" name="arrow-back-outline"></ion-icon> السابق
                            </ion-button>

                            <ion-button onclick="goToStep(3)" color="primary">
                                التالي <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="wizard-step" id="step3">
                        <h2>تأكيد جدولة الزيارة</h2>

                        <div class="visit-summary">
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="6">رقم العقد:</ion-col>
                                    <ion-col size="6">{{ $contract->contract_number }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">نوع العقد:</ion-col>
                                    <ion-col size="6">{{ $contract->contractType->name }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">التاريخ المفضل:</ion-col>
                                    <ion-col size="6" id="date-summary">-</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">الوقت المفضل:</ion-col>
                                    <ion-col size="6" id="time-summary">-</ion-col>
                                </ion-row>
                            </ion-grid>
                        </div>

                        <div class="ion-padding-top ion-text-center">
                            <p>هل أنت متأكد من رغبتك في جدولة هذه الزيارة؟ سيتم خصم زيارة واحدة من رصيد زياراتك المتبقية.</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(2)" color="medium">
                                <ion-icon slot="start" name="arrow-back-outline"></ion-icon> السابق
                            </ion-button>

                            <ion-button type="submit" color="success">
                                <ion-icon slot="start" name="checkmark-outline"></ion-icon> تأكيد الجدولة
                            </ion-button>
                        </div>
                    </div>
                </form>
            </ion-card-content>
        </ion-card>
    </div>

    @if($errors->any())
        <ion-toast-controller id="errors-toast"></ion-toast-controller>
        <script>
            document.addEventListener('DOMContentLoaded', async () => {
                const toastController = document.getElementById('errors-toast');
                const toast = await toastController.create({
                    color: 'danger',
                    message: 'هناك أخطاء في بيانات النموذج، يرجى التحقق من البيانات المدخلة.',
                    duration: 3000,
                    position: 'top'
                });
                toast.present();
            });
        </script>
    @endif
@endsection

@section('styles')
    <style>
        .wizard-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .wizard-step {
            display: none;
        }

        .wizard-step.active {
            display: block;
        }

        .wizard-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .visit-summary {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .visit-summary ion-row {
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }

        .visit-summary ion-row:last-child {
            border-bottom: none;
        }

        .note {
            font-size: 14px;
            color: var(--ion-color-medium);
        }

        .error-message {
            color: var(--ion-color-danger);
            font-size: 14px;
            margin: 5px 15px 15px;
        }
    </style>
@endsection

@section('scripts')
    <script>
        function goToStep(step) {
            // Hide all steps
            const steps = document.querySelectorAll('.wizard-step');
            steps.forEach(s => s.classList.remove('active'));

            // Show the selected step
            document.getElementById('step' + step).classList.add('active');

            // If going to confirmation step, update the summary
            if (step === 3) {
                updateVisitSummary();
            }
        }

        function updateVisitSummary() {
            // Get the selected date and time
            const preferredDate = document.getElementsByName('preferred_date')[0].value;
            const preferredTime = document.getElementsByName('preferred_time')[0].value;

            // Format the date
            let formattedDate = '-';
            if (preferredDate) {
                const dateObj = new Date(preferredDate);
                formattedDate = dateObj.toLocaleDateString('ar-SA');
            }

            // Format the time
            let formattedTime = '-';
            if (preferredTime) {
                const [hours, minutes] = preferredTime.split(':');
                const timeObj = new Date();
                timeObj.setHours(hours);
                timeObj.setMinutes(minutes);
                formattedTime = timeObj.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
            }

            // Update the summary elements
            document.getElementById('date-summary').textContent = formattedDate;
            document.getElementById('time-summary').textContent = formattedTime;
        }

        // Set default date to tomorrow
        document.addEventListener('DOMContentLoaded', function() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const formattedDate = tomorrow.toISOString().split('T')[0];
            document.getElementsByName('preferred_date')[0].value = formattedDate;

            // Set default time to 10:00 AM
            document.getElementsByName('preferred_time')[0].value = '10:00';
        });
    </script>
@endsection
