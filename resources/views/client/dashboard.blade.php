@extends('client.layouts.app')

@section('title', 'لوحة التحكم')

@section('header-title', 'لوحة التحكم')

@section('content')
    <div>
        <ion-grid>
            <ion-row>
                <ion-col size="12">
                    <div class="welcome-card ion-padding">
                        <h2>مرحباً، {{ $client->name }}</h2>
                        <p>مرحباً بك في بوابة العملاء الخاصة بنظام إدارة عقود الصيانة.</p>
                    </div>
                </ion-col>
            </ion-row>

            <ion-row>
                <ion-col size="12" size-md="6" size-lg="3">
                    <div class="dashboard-tile">
                        <ion-icon name="document-text-outline" size="large" color="primary"></ion-icon>
                        <h3>العقود النشطة</h3>
                        <div class="dashboard-number">{{ count($activeContracts) }}</div>
                        <ion-button expand="block" href="{{ route('client.contracts') }}" fill="outline">
                            عرض العقود
                        </ion-button>
                    </div>
                </ion-col>

                <ion-col size="12" size-md="6" size-lg="3">
                    <div class="dashboard-tile">
                        <ion-icon name="construct-outline" size="large" color="warning"></ion-icon>
                        <h3>طلبات الصيانة النشطة</h3>
                        <div class="dashboard-number">{{ count($activeRequests) }}</div>
                        <ion-button expand="block" href="{{ route('client.maintenance.index') }}" fill="outline">
                            عرض الطلبات
                        </ion-button>
                    </div>
                </ion-col>

                <ion-col size="12" size-md="6" size-lg="3">
                    <div class="dashboard-tile">
                        <ion-icon name="calendar-outline" size="large" color="success"></ion-icon>
                        <h3>الزيارات القادمة</h3>
                        <div class="dashboard-number">{{ count($upcomingVisits) }}</div>
                        <ion-button expand="block" href="{{ route('client.visits') }}" fill="outline">
                            عرض الزيارات
                        </ion-button>
                    </div>
                </ion-col>

                <ion-col size="12" size-md="6" size-lg="3">
                    <div class="dashboard-tile">
                        <ion-icon name="cash-outline" size="large" color="tertiary"></ion-icon>
                        <h3>المدفوعات المستحقة</h3>
                        <div class="dashboard-number">{{ count($upcomingPayments) }}</div>
                        <ion-button expand="block" id="view-payments" fill="outline">
                            عرض المدفوعات
                        </ion-button>
                    </div>
                </ion-col>
            </ion-row>

            <!-- New Row for Certificate Requests -->
            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>طلبات الشهادات</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(isset($pendingCertificates) && count($pendingCertificates) > 0)
                                <ion-list>
                                    @foreach($pendingCertificates->take(3) as $cert)
                                        <ion-item href="{{ route('client.certificates.show', $cert->id) }}">
                                            <ion-label>
                                                <h2>{{ $cert->title }}</h2>
                                                <p>
                                                    <ion-text color="primary">طلب رقم: {{ $cert->request_number }}</ion-text>
                                                    <ion-text color="dark"> | تاريخ الطلب: {{ \Carbon\Carbon::parse($cert->request_date)->format('Y-m-d') }}</ion-text>
                                                </p>
                                            </ion-label>
                                            <ion-badge slot="end" color="{{
                                                $cert->status == 'pending' ? 'warning' :
                                                ($cert->status == 'processing' ? 'primary' :
                                                ($cert->status == 'inspection_scheduled' ? 'tertiary' : 'success'))
                                            }}">
                                                {{
                                                    $cert->status == 'pending' ? 'قيد الانتظار' :
                                                    ($cert->status == 'processing' ? 'قيد المعالجة' :
                                                    ($cert->status == 'inspection_scheduled' ? 'مجدول للفحص' : 'تم إصدار الشهادة'))
                                                }}
                                            </ion-badge>
                                        </ion-item>
                                    @endforeach
                                </ion-list>

                                @if(count($pendingCertificates) > 3)
                                    <div class="ion-text-center ion-padding-top">
                                        <ion-button fill="clear" size="small" href="{{ route('client.certificates.index') }}">
                                            عرض جميع طلبات الشهادات
                                        </ion-button>
                                    </div>
                                @endif
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد طلبات شهادات قيد المعالجة.</p>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>

            <ion-row>
                <ion-col size="12" size-lg="6">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>الزيارات القادمة</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($upcomingVisits) > 0)
                                <ion-list>
                                    @foreach($upcomingVisits->take(3) as $visit)
                                        <ion-item href="{{ route('client.visits.show', $visit->id) }}">
                                            <ion-label>
                                                <h2>{{ $visit->maintenanceRequest->title }}</h2>
                                                <p>
                                                    <ion-text color="primary">{{ \Carbon\Carbon::parse($visit->scheduled_at)->format('Y-m-d') }}</ion-text>
                                                    في الساعة {{ \Carbon\Carbon::parse($visit->scheduled_at)->format('h:i A') }}
                                                </p>
                                            </ion-label>
                                            <ion-badge slot="end" color="{{ $visit->status == 'scheduled' ? 'primary' : 'success' }}">
                                                {{ $visit->status == 'scheduled' ? 'مجدولة' : 'قيد التنفيذ' }}
                                            </ion-badge>
                                        </ion-item>
                                    @endforeach
                                </ion-list>

                                @if(count($upcomingVisits) > 3)
                                    <div class="ion-text-center ion-padding-top">
                                        <ion-button fill="clear" size="small" href="{{ route('client.visits') }}">
                                            عرض جميع الزيارات
                                        </ion-button>
                                    </div>
                                @endif
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد زيارات قادمة.</p>
                                    <ion-button size="small" href="{{ route('client.contracts') }}">
                                        جدولة زيارة جديدة
                                    </ion-button>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>

                <ion-col size="12" size-lg="6">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>المدفوعات الأخيرة</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($recentPayments) > 0)
                                <ion-list>
                                    @foreach($recentPayments as $payment)
                                        <ion-item>
                                            <ion-label>
                                                <h2>رقم المعاملة: {{ $payment->payment_number }}</h2>
                                                <p>
                                                    <ion-text color="medium">{{ \Carbon\Carbon::parse($payment->payment_date)->format('Y-m-d') }}</ion-text>
                                                    <ion-text color="dark"> | العقد: {{ $payment->contract->contract_number }}</ion-text>
                                                </p>
                                            </ion-label>
                                            <ion-text slot="end" color="success">
                                                <h3 dir="ltr">{{ number_format($payment->amount, 2) }} ريال</h3>
                                            </ion-text>
                                        </ion-item>
                                    @endforeach
                                </ion-list>
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد مدفوعات حديثة.</p>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>

            <ion-row>
                <ion-col size="12">
                    <ion-card>
                        <ion-card-header>
                            <ion-card-title>المدفوعات المستحقة</ion-card-title>
                        </ion-card-header>

                        <ion-card-content>
                            @if(count($upcomingPayments) > 0)
                                <ion-list>
                                    @foreach($upcomingPayments as $payment)
                                        <ion-item>
                                            <ion-label>
                                                <h2>{{ $payment['description'] }}</h2>
                                                <p>
                                                    <ion-text color="warning">تاريخ الاستحقاق: {{ $payment['payment_date'] }}</ion-text>
                                                    <ion-text color="dark"> | العقد: {{ $payment['contract_number'] }}</ion-text>
                                                </p>
                                            </ion-label>
                                            <div slot="end">
                                                <ion-text color="primary">
                                                    <h3 dir="ltr">{{ number_format($payment['amount'], 2) }} ريال</h3>
                                                </ion-text>
                                                <ion-button size="small" href="{{ route('client.payments.create', $payment['contract_id']) }}">
                                                    دفع الآن
                                                </ion-button>
                                            </div>
                                        </ion-item>
                                    @endforeach
                                </ion-list>
                            @else
                                <div class="ion-padding ion-text-center">
                                    <p>لا توجد مدفوعات مستحقة.</p>
                                </div>
                            @endif
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            </ion-row>
        </ion-grid>
    </div>

    <!-- Payment modal -->
    <ion-modal trigger="view-payments">
        <ion-header>
            <ion-toolbar>
                <ion-title>المدفوعات المستحقة</ion-title>
                <ion-buttons slot="end">
                    <ion-button onclick="dismissPaymentModal()">إغلاق</ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
            @if(count($upcomingPayments) > 0)
                <ion-list>
                    @foreach($upcomingPayments as $payment)
                        <ion-item>
                            <ion-label>
                                <h2>{{ $payment['description'] }}</h2>
                                <p>
                                    <ion-text color="warning">تاريخ الاستحقاق: {{ $payment['payment_date'] }}</ion-text>
                                    <ion-text color="dark"> | العقد: {{ $payment['contract_number'] }}</ion-text>
                                </p>
                            </ion-label>
                            <div slot="end">
                                <ion-text color="primary">
                                    <h3 dir="ltr">{{ number_format($payment['amount'], 2) }} ريال</h3>
                                </ion-text>
                                <ion-button size="small" href="{{ route('client.payments.create', $payment['contract_id']) }}">
                                    دفع الآن
                                </ion-button>
                            </div>
                        </ion-item>
                    @endforeach
                </ion-list>
            @else
                <div class="ion-padding ion-text-center">
                    <p>لا توجد مدفوعات مستحقة.</p>
                </div>
            @endif
        </ion-content>
    </ion-modal>
@endsection

@section('scripts')
    <script>
        function dismissPaymentModal() {
            const modal = document.querySelector('ion-modal');
            modal.dismiss();
        }
    </script>
@endsection

@section('styles')
    <style>
        .welcome-card {
            background-color: var(--ion-color-primary);
            color: white;
            border-radius: 10px;
            margin-bottom: 16px;
        }

        .welcome-card h2 {
            margin-top: 0;
        }
    </style>
@endsection
