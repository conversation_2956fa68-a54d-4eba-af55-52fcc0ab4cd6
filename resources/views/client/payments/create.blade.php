@extends('client.layouts.app')

@section('title', 'دفع فاتورة')

@section('header-title', 'دفع فاتورة')

@section('header-buttons')
    <ion-buttons slot="end">
        <ion-back-button default-href="{{ route('client.contracts.show', $contract->id) }}" text="رجوع"></ion-back-button>
    </ion-buttons>
@endsection

@section('content')
    <div class="wizard-container">
        <ion-card>
            <ion-card-header>
                <ion-card-title>معالج الدفع</ion-card-title>
                <ion-card-subtitle>عقد رقم: {{ $contract->contract_number }}</ion-card-subtitle>
            </ion-card-header>

            <ion-card-content>
                <form id="payment-form" method="POST" action="{{ route('client.payments.process', $contract->id) }}">
                    @csrf

                    <div class="wizard-step active" id="step1">
                        <h2>تفاصيل الدفع</h2>

                        <ion-item>
                            <ion-label position="stacked">رقم العقد</ion-label>
                            <ion-input value="{{ $contract->contract_number }}" readonly></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">نوع العقد</ion-label>
                            <ion-input value="{{ $contract->contractType->name }}" readonly></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">المبلغ المستحق</ion-label>
                            <ion-input value="{{ number_format($paymentAmount, 2) }} ريال" readonly></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">تاريخ الدفع</ion-label>
                            <ion-input value="{{ date('Y-m-d') }}" readonly></ion-input>
                        </ion-item>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(2)" color="primary">
                                التالي <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="wizard-step" id="step2">
                        <h2>اختر طريقة الدفع</h2>

                        <ion-list>
                            <ion-radio-group name="payment_method">
                                <ion-item>
                                    <ion-label>بطاقة ائتمان</ion-label>
                                    <ion-radio slot="end" value="credit_card"></ion-radio>
                                </ion-item>

                                <ion-item>
                                    <ion-label>تحويل بنكي</ion-label>
                                    <ion-radio slot="end" value="bank_transfer"></ion-radio>
                                </ion-item>

                                <ion-item>
                                    <ion-label>شيك</ion-label>
                                    <ion-radio slot="end" value="check"></ion-radio>
                                </ion-item>

                                <ion-item>
                                    <ion-label>نقدًا</ion-label>
                                    <ion-radio slot="end" value="cash"></ion-radio>
                                </ion-item>

                                <ion-item>
                                    <ion-label>دفع إلكتروني</ion-label>
                                    <ion-radio slot="end" value="online"></ion-radio>
                                </ion-item>
                            </ion-radio-group>
                        </ion-list>

                        <ion-item>
                            <ion-label position="stacked">رقم المرجع (اختياري)</ion-label>
                            <ion-input name="reference_number" placeholder="مثال: رقم الحوالة أو رقم الشيك"></ion-input>
                        </ion-item>

                        <ion-item>
                            <ion-label position="stacked">ملاحظات (اختياري)</ion-label>
                            <ion-textarea name="notes" rows="3"></ion-textarea>
                        </ion-item>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(1)" color="medium">
                                <ion-icon slot="start" name="arrow-back-outline"></ion-icon> السابق
                            </ion-button>

                            <ion-button onclick="goToStep(3)" color="primary">
                                التالي <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="wizard-step" id="step3">
                        <h2>تأكيد الدفع</h2>

                        <div class="payment-summary">
                            <ion-grid>
                                <ion-row>
                                    <ion-col size="6">رقم العقد:</ion-col>
                                    <ion-col size="6">{{ $contract->contract_number }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">نوع العقد:</ion-col>
                                    <ion-col size="6">{{ $contract->contractType->name }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">المبلغ المستحق:</ion-col>
                                    <ion-col size="6" dir="ltr">{{ number_format($paymentAmount, 2) }} ريال</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">تاريخ الدفع:</ion-col>
                                    <ion-col size="6">{{ date('Y-m-d') }}</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">طريقة الدفع:</ion-col>
                                    <ion-col size="6" id="payment-method-summary">-</ion-col>
                                </ion-row>

                                <ion-row>
                                    <ion-col size="6">رقم المرجع:</ion-col>
                                    <ion-col size="6" id="reference-number-summary">-</ion-col>
                                </ion-row>
                            </ion-grid>
                        </div>

                        <div class="ion-padding-top ion-text-center">
                            <p>هل أنت متأكد من رغبتك في تنفيذ عملية الدفع؟</p>
                        </div>

                        <div class="wizard-navigation">
                            <ion-button onclick="goToStep(2)" color="medium">
                                <ion-icon slot="start" name="arrow-back-outline"></ion-icon> السابق
                            </ion-button>

                            <ion-button type="submit" color="success">
                                <ion-icon slot="start" name="checkmark-outline"></ion-icon> تأكيد الدفع
                            </ion-button>
                        </div>
                    </div>
                </form>
            </ion-card-content>
        </ion-card>
    </div>

    @if($errors->any())
        <ion-toast-controller id="errors-toast"></ion-toast-controller>
        <script>
            document.addEventListener('DOMContentLoaded', async () => {
                const toastController = document.getElementById('errors-toast');
                const toast = await toastController.create({
                    color: 'danger',
                    message: 'هناك أخطاء في بيانات النموذج، يرجى التحقق من البيانات المدخلة.',
                    duration: 3000,
                    position: 'top'
                });
                toast.present();
            });
        </script>
    @endif
@endsection

@section('styles')
    <style>
        .wizard-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .wizard-step {
            display: none;
        }

        .wizard-step.active {
            display: block;
        }

        .wizard-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .payment-summary {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .payment-summary ion-row {
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }

        .payment-summary ion-row:last-child {
            border-bottom: none;
        }
    </style>
@endsection

@section('scripts')
    <script>
        function goToStep(step) {
            // Hide all steps
            const steps = document.querySelectorAll('.wizard-step');
            steps.forEach(s => s.classList.remove('active'));

            // Show the selected step
            document.getElementById('step' + step).classList.add('active');

            // If going to confirmation step, update the summary
            if (step === 3) {
                updatePaymentSummary();
            }
        }

        function updatePaymentSummary() {
            // Get the selected payment method
            const paymentMethodRadios = document.getElementsByName('payment_method');
            let selectedMethod = '-';

            for (let radio of paymentMethodRadios) {
                if (radio.checked) {
                    switch (radio.value) {
                        case 'credit_card':
                            selectedMethod = 'بطاقة ائتمان';
                            break;
                        case 'bank_transfer':
                            selectedMethod = 'تحويل بنكي';
                            break;
                        case 'check':
                            selectedMethod = 'شيك';
                            break;
                        case 'cash':
                            selectedMethod = 'نقدًا';
                            break;
                        case 'online':
                            selectedMethod = 'دفع إلكتروني';
                            break;
                        default:
                            selectedMethod = radio.value;
                    }
                    break;
                }
            }

            // Get the reference number
            const referenceNumber = document.getElementsByName('reference_number')[0].value || '-';

            // Update the summary elements
            document.getElementById('payment-method-summary').textContent = selectedMethod;
            document.getElementById('reference-number-summary').textContent = referenceNumber;
        }

        // Set default payment method
        document.addEventListener('DOMContentLoaded', function() {
            const defaultMethod = document.querySelector('ion-radio[value="credit_card"]');
            if (defaultMethod) {
                defaultMethod.checked = true;
            }
        });
    </script>
@endsection
