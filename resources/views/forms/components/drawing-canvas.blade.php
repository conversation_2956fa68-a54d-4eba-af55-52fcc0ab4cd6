@php
    $id = $getId();
    $statePath = $getStatePath();
    $isDisabled = $isDisabled();
    $width = $getWidth();
    $height = $getHeight();
    $backgroundColor = $getBackgroundColor();
    $strokeColor = $getStrokeColor();
    $strokeWidth = $getStrokeWidth();
    $enableTouch = $isTouchEnabled();
    $enableMouse = $isMouseEnabled();
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div class="drawing-canvas-container" x-data="drawingCanvas({
        id: '{{ $id }}',
        statePath: '{{ $statePath }}',
        width: {{ $width }},
        height: {{ $height }},
        backgroundColor: '{{ $backgroundColor }}',
        strokeColor: '{{ $strokeColor }}',
        strokeWidth: {{ $strokeWidth }},
        enableTouch: {{ $enableTouch ? 'true' : 'false' }},
        enableMouse: {{ $enableMouse ? 'true' : 'false' }},
        disabled: {{ $isDisabled ? 'true' : 'false' }}
    })">
        
        {{-- Canvas Controls --}}
        <div class="canvas-controls mb-4 flex flex-wrap gap-2 items-center justify-between bg-gray-50 p-3 rounded-lg">
            <div class="flex flex-wrap gap-2 items-center">
                {{-- Drawing Tools --}}
                <div class="flex gap-1">
                    <button type="button" @click="setTool('pen')" 
                            :class="currentTool === 'pen' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'"
                            class="px-3 py-1 rounded border text-sm font-medium">
                        {{ __('technician.resources.report.drawing.tools.pen') }}
                    </button>
                    <button type="button" @click="setTool('eraser')" 
                            :class="currentTool === 'eraser' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'"
                            class="px-3 py-1 rounded border text-sm font-medium">
                        {{ __('technician.resources.report.drawing.tools.eraser') }}
                    </button>
                </div>

                {{-- Stroke Width --}}
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">
                        {{ __('technician.resources.report.drawing.stroke_width') }}:
                    </label>
                    <select x-model="strokeWidth" @change="updateStrokeWidth()" 
                            class="text-sm border rounded px-2 py-1">
                        <option value="1">1px</option>
                        <option value="2">2px</option>
                        <option value="3">3px</option>
                        <option value="5">5px</option>
                        <option value="8">8px</option>
                        <option value="12">12px</option>
                    </select>
                </div>

                {{-- Color Picker --}}
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">
                        {{ __('technician.resources.report.drawing.color') }}:
                    </label>
                    <input type="color" x-model="strokeColor" @change="updateStrokeColor()"
                           class="w-8 h-8 border rounded cursor-pointer">
                </div>
            </div>

            {{-- Action Buttons --}}
            <div class="flex gap-2">
                <button type="button" @click="clearCanvas()" 
                        class="px-3 py-1 bg-red-500 text-white rounded text-sm font-medium hover:bg-red-600">
                    {{ __('technician.resources.report.drawing.actions.clear') }}
                </button>
                <button type="button" @click="saveDrawingToAttachments()"
                        :disabled="!hasDrawing || isSaving"
                        :class="[
                            'px-3 py-1 rounded text-sm font-medium transition-all duration-200',
                            (!hasDrawing || isSaving)
                                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                : 'bg-green-500 text-white hover:bg-green-600 hover:shadow-lg transform hover:scale-105'
                        ]">
                    <span x-show="!isSaving">{{ __('technician.resources.report.drawing.actions.save_to_attachments') }}</span>
                    <span x-show="isSaving" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ __('technician.resources.report.drawing.actions.saving') }}
                    </span>
                </button>
            </div>
        </div>

        {{-- Canvas Area --}}
        <div class="canvas-wrapper border-2 border-gray-300 rounded-lg overflow-hidden bg-white">
            <canvas 
                x-ref="canvas"
                :width="width" 
                :height="height"
                class="block cursor-crosshair touch-none"
                style="max-width: 100%; height: auto;"
                @mousedown="startDrawing($event)"
                @mousemove="draw($event)"
                @mouseup="stopDrawing()"
                @mouseout="stopDrawing()"
                @touchstart.prevent="startDrawing($event)"
                @touchmove.prevent="draw($event)"
                @touchend.prevent="stopDrawing()"
            ></canvas>
        </div>

        {{-- Drawing Status --}}
        <div class="mt-2 text-sm text-gray-600">
            <span x-show="hasDrawing" class="text-green-600">
                {{ __('technician.resources.report.drawing.status.has_content') }}
            </span>
            <span x-show="!hasDrawing" class="text-gray-500">
                {{ __('technician.resources.report.drawing.status.empty') }}
            </span>
            <span x-show="savedToAttachments" class="text-blue-600 ml-2">
                {{ __('technician.resources.report.drawing.status.saved_to_attachments') }}
            </span>
        </div>

        {{-- Hidden input to store the drawing data --}}
        <input type="hidden" x-model="drawingData" name="{{ $statePath }}" />
    </div>
</x-dynamic-component>

@push('styles')
<link rel="stylesheet" href="{{ asset('css/technician-attachments.css') }}">
<style>
    /* Drawing canvas animations and styles */
    .drawing-canvas-container {
        position: relative;
    }

    .canvas-controls {
        transition: all 0.3s ease-in-out;
    }

    .canvas-controls:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .canvas-wrapper {
        transition: all 0.3s ease-in-out;
        position: relative;
        overflow: hidden;
    }

    .canvas-wrapper:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* File highlight animation */
    @keyframes fileHighlight {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 rgba(34, 197, 94, 0);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
        }
        100% {
            transform: scale(1.02);
            box-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
        }
    }

    .file-highlight {
        animation: fileHighlight 0.6s ease-in-out;
        border: 2px solid #22c55e !important;
        border-radius: 8px !important;
    }

    /* Tab switching animation */
    .tab-switching {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Success pulse animation */
    @keyframes successPulse {
        0% {
            background-color: #22c55e;
            transform: scale(1);
        }
        50% {
            background-color: #16a34a;
            transform: scale(1.02);
        }
        100% {
            background-color: #22c55e;
            transform: scale(1);
        }
    }

    .success-pulse {
        animation: successPulse 0.6s ease-in-out;
    }

    /* Loading spinner for save button */
    .save-button-loading {
        position: relative;
        overflow: hidden;
    }

    .save-button-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: shimmer 1.5s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Mobile responsive improvements */
    @media (max-width: 768px) {
        .canvas-controls {
            flex-direction: column;
            gap: 0.75rem;
        }

        .canvas-controls > div {
            flex-wrap: wrap;
            justify-content: center;
        }

        .canvas-wrapper canvas {
            max-width: 100%;
            height: auto;
        }
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/drawing-canvas-enhancements.js') }}"></script>
<script>
// Global function to handle drawing integration with file uploads
function drawingIntegration() {
    return {
        lastAddedFilename: null,
        pendingHighlight: null,

        init() {
            console.log('Drawing integration initialized');

            // Listen for drawing save events
            window.addEventListener('addDrawingToAttachments', (event) => {
                console.log('Adding drawing to attachments:', event.detail);
                this.addFileToUpload(event.detail.file, event.detail.filename);
            });

            // Listen for tab switching events
            window.addEventListener('switchToUploadsTab', (event) => {
                console.log('Switching to uploads tab:', event.detail);
                this.switchToUploadsTab(event.detail?.filename);
            });

            // Listen for Livewire events
            if (window.Livewire) {
                window.Livewire.on('drawing-added-to-attachments', (event) => {
                    console.log('Livewire event: drawing added to attachments', event);

                    // Show success notification
                    window.dispatchEvent(new CustomEvent('notify', {
                        detail: {
                            type: 'success',
                            title: 'تم إضافة الرسم للمرفقات',
                            message: `تم إضافة الرسم "${event.filename}" بنجاح. العدد الإجمالي: ${event.total}`,
                            duration: 5000
                        }
                    }));

                    // Switch to uploads tab
                    setTimeout(() => {
                        this.switchToUploadsTab(event.filename);
                    }, 1000);
                });
            }

            // Debug: Log available file upload elements
            this.debugFileUploadElements();
        },

        debugFileUploadElements() {
            const selectors = [
                '[x-ref="attachmentsUpload"]',
                '[data-field-wrapper-id*="attachments"]',
                '.fi-fo-file-upload',
                'input[type="file"]'
            ];

            console.log('Available file upload elements:');
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                console.log(`${selector}: ${elements.length} found`);
            });
        },

        findFileUploadElement() {
            // Enhanced selectors for Filament file upload components
            const selectors = [
                '[x-ref="attachmentsUpload"]',
                '[data-field-wrapper-id*="attachments"]',
                '.fi-fo-file-upload',
                '[wire\\:model*="attachments"]',
                '[data-field-wrapper] input[type="file"]',
                '.filament-forms-file-upload-component'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    console.log(`Found file upload element using selector: ${selector}`);
                    return element;
                }
            }

            // Fallback: search for any file input in the form
            const form = document.querySelector('form');
            if (form) {
                const fileInput = form.querySelector('input[type="file"]');
                if (fileInput) {
                    return fileInput.closest('[data-field-wrapper]') || fileInput.parentElement;
                }
            }

            return null;
        },

        findFileInput(container) {
            // Multiple approaches to find the file input
            const selectors = [
                'input[type="file"]',
                'input[accept*="image"]',
                'input[multiple]',
                '[wire\\:model*="attachments"]'
            ];

            for (const selector of selectors) {
                const input = container.querySelector(selector);
                if (input) {
                    console.log(`Found file input using selector: ${selector}`);
                    return input;
                }
            }

            // Try parent containers
            const parentContainer = container.closest('.fi-fo-file-upload') ||
                                  container.closest('[data-field-wrapper]') ||
                                  container.closest('form');

            if (parentContainer && parentContainer !== container) {
                for (const selector of selectors) {
                    const input = parentContainer.querySelector(selector);
                    if (input) {
                        console.log(`Found file input in parent using selector: ${selector}`);
                        return input;
                    }
                }
            }

            return null;
        },

        showErrorMessage(message) {
            // Show error notification
            window.dispatchEvent(new CustomEvent('notify', {
                detail: {
                    type: 'error',
                    title: 'خطأ في رفع الملف',
                    message: message
                }
            }));
        },

        updateFilamentField(fileInput, files, filename) {
            // Multiple approaches to update Filament field
            const formField = fileInput.closest('[data-field-wrapper]') ||
                            fileInput.closest('.fi-fo-file-upload') ||
                            fileInput.closest('[x-data]');

            if (formField) {
                const fieldName = fileInput.name || 'attachments';

                // Approach 1: Alpine.js data stack
                if (window.Alpine && formField._x_dataStack) {
                    const alpineData = formField._x_dataStack[0];
                    if (alpineData && alpineData.state) {
                        // Create array of file objects for Filament
                        const fileArray = Array.from(files).map((file, index) => {
                            return {
                                name: file.name,
                                size: file.size,
                                type: file.type,
                                url: URL.createObjectURL(file),
                                file: file
                            };
                        });

                        alpineData.state[fieldName] = fileArray;
                        console.log('Updated Alpine.js state:', alpineData.state[fieldName]);
                    }
                }

                // Approach 2: Direct Alpine component update
                if (window.Alpine && formField._x_dataStack) {
                    const component = formField._x_dataStack.find(data => data.state);
                    if (component && component.state) {
                        component.state[fieldName] = Array.from(files);
                    }
                }

                // Approach 3: Livewire component update
                if (window.Livewire) {
                    const livewireComponent = formField.closest('[wire\\:id]');
                    if (livewireComponent) {
                        const componentId = livewireComponent.getAttribute('wire:id');
                        const component = window.Livewire.find(componentId);
                        if (component) {
                            // Upload files to Livewire
                            const uploadField = component.uploadManager?.getUploadField?.(fieldName);
                            if (uploadField) {
                                Array.from(files).forEach(file => {
                                    uploadField.addFile(file);
                                });
                            }
                        }
                    }
                }
            }
        },

        triggerFilamentEvents(fileInput, filename) {
            // Comprehensive event triggering for Filament
            const events = [
                'change',
                'input',
                'livewire:update',
                'file-upload-change',
                'files-updated'
            ];

            // Trigger standard events
            events.forEach(eventType => {
                try {
                    fileInput.dispatchEvent(new Event(eventType, {
                        bubbles: true,
                        cancelable: true
                    }));
                } catch (e) {
                    // Try as custom event if standard event fails
                    fileInput.dispatchEvent(new CustomEvent(eventType, {
                        bubbles: true,
                        detail: { files: fileInput.files, filename }
                    }));
                }
            });

            // Alpine.js specific events
            if (window.Alpine) {
                fileInput.dispatchEvent(new CustomEvent('file-upload-change', {
                    bubbles: true,
                    detail: {
                        files: fileInput.files,
                        filename: filename,
                        isDrawing: true
                    }
                }));

                // Trigger Alpine refresh on the component
                const alpineComponent = fileInput.closest('[x-data]');
                if (alpineComponent && alpineComponent._x_dataStack) {
                    alpineComponent._x_dataStack.forEach(data => {
                        if (data.$refresh) {
                            data.$refresh();
                        }
                    });
                }
            }

            // Livewire specific events
            if (window.Livewire) {
                const livewireElement = fileInput.closest('[wire\\:id]');
                if (livewireElement) {
                    const componentId = livewireElement.getAttribute('wire:id');
                    const component = window.Livewire.find(componentId);
                    if (component) {
                        // Try multiple Livewire update methods
                        try {
                            component.call('$refresh');
                        } catch (e) {
                            try {
                                component.$wire.$refresh();
                            } catch (e2) {
                                console.log('Livewire refresh methods not available');
                            }
                        }
                    }
                }

                // Global Livewire event
                window.Livewire.emit('file-uploaded', {
                    filename: filename,
                    isDrawing: true
                });
            }

            // Custom drawing-specific event
            window.dispatchEvent(new CustomEvent('drawing-file-added', {
                detail: {
                    filename: filename,
                    fileInput: fileInput,
                    timestamp: Date.now()
                }
            }));

            console.log(`Triggered events for file: ${filename}`);
        },

        addFileToUpload(file, filename) {
            // Enhanced file upload element detection
            let fileUploadElement = this.findFileUploadElement();

            if (!fileUploadElement) {
                console.error('File upload element not found');
                this.showErrorMessage('لم يتم العثور على منطقة رفع الملفات');
                return;
            }

            // Find the actual file input within the upload component
            let fileInput = this.findFileInput(fileUploadElement);

            if (!fileInput) {
                console.error('File input not found within upload component');
                this.showErrorMessage('لم يتم العثور على حقل رفع الملفات');
                return;
            }

            // Enhanced file integration with better Filament support
            try {
                // Create a DataTransfer object to simulate file selection
                const dataTransfer = new DataTransfer();

                // Get existing files from the file input
                if (fileInput.files) {
                    for (let i = 0; i < fileInput.files.length; i++) {
                        dataTransfer.items.add(fileInput.files[i]);
                    }
                }

                // Add the new drawing file
                dataTransfer.items.add(file);
                this.lastAddedFilename = filename;

                // Update the file input
                fileInput.files = dataTransfer.files;

                // Enhanced Filament integration
                this.updateFilamentField(fileInput, dataTransfer.files, filename);

                console.log(`Successfully added file: ${filename}`);

            } catch (error) {
                console.error('Error adding file to upload:', error);
                this.showErrorMessage('حدث خطأ أثناء إضافة الملف');
                return;
            }

            // Enhanced event triggering for better Filament integration
            this.triggerFilamentEvents(fileInput, filename);

            // Store file info for later highlighting
            this.pendingHighlight = { filename, timestamp: Date.now() };

            // Wait for Filament to process the file and then highlight it
            setTimeout(() => {
                this.highlightNewFile(filename);
            }, 800);

            // Retry highlighting if needed
            setTimeout(() => {
                if (this.pendingHighlight && this.pendingHighlight.filename === filename) {
                    this.highlightNewFile(filename);
                    this.pendingHighlight = null;
                }
            }, 2000);
        },

        switchToUploadsTab(filename) {
            // Enhanced tab detection and switching
            const tabsContainer = this.findTabsContainer();

            if (!tabsContainer) {
                console.error('Tabs container not found');
                return false;
            }

            const uploadsTabButton = this.findUploadsTabButton(tabsContainer);

            if (uploadsTabButton) {
                // Enhanced visual feedback
                this.animateTabSwitch(uploadsTabButton);

                // Click the tab to switch
                setTimeout(() => {
                    uploadsTabButton.click();

                    // Reset button styles
                    setTimeout(() => {
                        uploadsTabButton.style.transform = 'scale(1)';
                        uploadsTabButton.style.boxShadow = '';
                    }, 300);

                    // If filename provided, highlight the file after tab content loads
                    if (filename) {
                        setTimeout(() => {
                            this.highlightNewFile(filename);
                        }, 800);
                    }
                }, 200);

                return true;
            } else {
                console.error('File uploads tab button not found');
                return false;
            }
        },

        findTabsContainer() {
            const selectors = [
                '#attachment-tabs-container',
                '[role="tablist"]',
                '.fi-tabs',
                '[x-data*="tabs"]',
                '.filament-tabs',
                '[data-tabs]'
            ];

            for (const selector of selectors) {
                const container = document.querySelector(selector);
                if (container) {
                    console.log(`Found tabs container using: ${selector}`);
                    return container;
                }
            }

            return null;
        },

        findUploadsTabButton(container) {
            const selectors = [
                '[id*="file_uploads"]',
                '[aria-controls*="file_uploads"]',
                'button:first-child',
                '[data-tab="file_uploads"]'
            ];

            // Try specific selectors first
            for (const selector of selectors) {
                const button = container.querySelector(selector);
                if (button) {
                    console.log(`Found uploads tab button using: ${selector}`);
                    return button;
                }
            }

            // Fallback: search by text content
            const buttons = container.querySelectorAll('button');
            for (const button of buttons) {
                const text = button.textContent.trim();
                if (text.includes('رفع الملفات') ||
                    text.includes('file_uploads') ||
                    text.includes('ملفات') ||
                    text.includes('Files')) {
                    console.log(`Found uploads tab button by text: ${text}`);
                    return button;
                }
            }

            // Last resort: first button
            if (buttons.length > 0) {
                console.log('Using first button as uploads tab');
                return buttons[0];
            }

            return null;
        },

        animateTabSwitch(button) {
            // Enhanced animation for tab switching
            button.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.transform = 'scale(1.05)';
            button.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';
            button.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
            button.style.borderColor = '#3b82f6';

            // Add a subtle glow effect
            button.style.filter = 'brightness(1.1)';
        },

        highlightNewFile(filename) {
            // Use enhanced file highlighting if available
            if (window.drawingCanvasEnhancements?.fileHighlighter) {
                // The enhanced system will automatically detect and highlight new files
                return;
            }

            // Fallback to basic highlighting
            this.basicHighlightNewFile(filename);
        },

        basicHighlightNewFile(filename) {
            // Enhanced selectors for Filament file upload components
            const selectors = [
                '[data-testid="file-upload-item"]',
                '.fi-fo-file-upload-item',
                '.filament-forms-file-upload-component .relative',
                '.fi-fo-file-upload .relative',
                '[data-file-key]',
                '.fi-fo-file-upload-file'
            ];

            let fileElements = [];
            selectors.forEach(selector => {
                fileElements = [...fileElements, ...document.querySelectorAll(selector)];
            });

            // If no elements found, try a broader search
            if (fileElements.length === 0) {
                const uploadContainer = document.querySelector('[x-ref="attachmentsUpload"]') ||
                                      document.querySelector('[data-field-wrapper-id*="attachments"]');
                if (uploadContainer) {
                    fileElements = [...uploadContainer.querySelectorAll('.relative'),
                                   ...uploadContainer.querySelectorAll('img'),
                                   ...uploadContainer.querySelectorAll('[role="button"]')];
                }
            }

            // Look for the most recently added file
            let targetElement = null;
            let latestTime = 0;

            fileElements.forEach(element => {
                // Look for the filename in the element or its children
                const textContent = element.textContent || '';
                const imgElements = element.querySelectorAll('img');
                const hasDrawingFile = textContent.includes('رسم-توضيحي') ||
                                     textContent.includes('drawing-') ||
                                     (filename && textContent.includes(filename)) ||
                                     Array.from(imgElements).some(img =>
                                         img.src.includes('رسم-توضيحي') ||
                                         img.alt.includes('رسم-توضيحي')
                                     );

                if (hasDrawingFile) {
                    const elementTime = parseInt(element.dataset.addedTime || Date.now());
                    if (elementTime > latestTime) {
                        latestTime = elementTime;
                        targetElement = element;
                    }
                }
            });

            // If no specific element found, try to find the last file in the upload area
            if (!targetElement && fileElements.length > 0) {
                targetElement = fileElements[fileElements.length - 1];
            }

            if (targetElement) {
                this.highlightElement(targetElement, filename);
            } else {
                // Retry after a short delay
                setTimeout(() => {
                    this.basicHighlightNewFile(filename);
                }, 1000);
            }
        },

        highlightElement(element, filename) {
            // Add timestamp for tracking
            element.dataset.addedTime = Date.now();

            // Create a wrapper for the highlight effect if needed
            const wrapper = element.closest('.relative') || element;

            // Add highlight animation
            wrapper.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            wrapper.style.transform = 'scale(1.08)';
            wrapper.style.boxShadow = '0 0 30px rgba(34, 197, 94, 0.8)';
            wrapper.style.border = '3px solid #22c55e';
            wrapper.style.borderRadius = '12px';
            wrapper.style.backgroundColor = 'rgba(34, 197, 94, 0.15)';
            wrapper.style.zIndex = '1000';
            wrapper.style.position = 'relative';

            // Add a "NEW" badge
            const badge = document.createElement('div');
            badge.innerHTML = 'جديد';
            badge.style.cssText = `
                position: absolute;
                top: -8px;
                right: -8px;
                background: linear-gradient(45deg, #22c55e, #16a34a);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: bold;
                z-index: 1001;
                box-shadow: 0 2px 8px rgba(34, 197, 94, 0.4);
                animation: bounce 0.8s ease-in-out;
            `;
            wrapper.appendChild(badge);

            // Add a pulsing effect
            wrapper.classList.add('animate-pulse');

            // Scroll the element into view with better positioning
            setTimeout(() => {
                wrapper.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }, 200);

            // Remove highlight after 5 seconds
            setTimeout(() => {
                wrapper.style.transform = 'scale(1)';
                wrapper.style.boxShadow = '';
                wrapper.style.border = '';
                wrapper.style.backgroundColor = '';
                wrapper.style.zIndex = '';
                wrapper.classList.remove('animate-pulse');

                // Remove badge
                if (badge.parentNode) {
                    badge.remove();
                }
            }, 5000);
        }
    };
}

function drawingCanvas(config) {
    return {
        canvas: null,
        ctx: null,
        isDrawing: false,
        currentTool: 'pen',
        strokeWidth: config.strokeWidth,
        strokeColor: config.strokeColor,
        backgroundColor: config.backgroundColor,
        width: config.width,
        height: config.height,
        enableTouch: config.enableTouch,
        enableMouse: config.enableMouse,
        disabled: config.disabled,
        hasDrawing: false,
        drawingData: '',
        savedToAttachments: false,
        isSaving: false,

        init() {
            this.$nextTick(() => {
                this.canvas = this.$refs.canvas;
                this.ctx = this.canvas.getContext('2d');
                this.setupCanvas();
            });
        },

        setupCanvas() {
            // Set canvas background
            this.ctx.fillStyle = this.backgroundColor;
            this.ctx.fillRect(0, 0, this.width, this.height);
            
            // Set initial drawing properties
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';
            this.updateStrokeWidth();
            this.updateStrokeColor();
        },

        setTool(tool) {
            this.currentTool = tool;
        },

        updateStrokeWidth() {
            this.ctx.lineWidth = this.strokeWidth;
        },

        updateStrokeColor() {
            this.ctx.strokeStyle = this.strokeColor;
        },

        getEventPos(e) {
            const rect = this.canvas.getBoundingClientRect();
            const scaleX = this.canvas.width / rect.width;
            const scaleY = this.canvas.height / rect.height;
            
            let clientX, clientY;
            
            if (e.touches && e.touches.length > 0) {
                clientX = e.touches[0].clientX;
                clientY = e.touches[0].clientY;
            } else {
                clientX = e.clientX;
                clientY = e.clientY;
            }
            
            return {
                x: (clientX - rect.left) * scaleX,
                y: (clientY - rect.top) * scaleY
            };
        },

        startDrawing(e) {
            if (this.disabled) return;
            
            this.isDrawing = true;
            const pos = this.getEventPos(e);
            
            this.ctx.beginPath();
            this.ctx.moveTo(pos.x, pos.y);
        },

        draw(e) {
            if (!this.isDrawing || this.disabled) return;
            
            const pos = this.getEventPos(e);
            
            if (this.currentTool === 'pen') {
                this.ctx.globalCompositeOperation = 'source-over';
                this.ctx.strokeStyle = this.strokeColor;
            } else if (this.currentTool === 'eraser') {
                this.ctx.globalCompositeOperation = 'destination-out';
            }
            
            this.ctx.lineTo(pos.x, pos.y);
            this.ctx.stroke();
            
            this.hasDrawing = true;
        },

        stopDrawing() {
            if (this.isDrawing) {
                this.isDrawing = false;
                this.ctx.beginPath();
            }
        },

        clearCanvas() {
            this.ctx.clearRect(0, 0, this.width, this.height);
            this.ctx.fillStyle = this.backgroundColor;
            this.ctx.fillRect(0, 0, this.width, this.height);
            this.hasDrawing = false;
            this.drawingData = '';
            this.savedToAttachments = false;
        },

        saveDrawing() {
            if (this.hasDrawing) {
                this.drawingData = this.canvas.toDataURL('image/png');

                // Show success message
                window.dispatchEvent(new CustomEvent('notify', {
                    detail: {
                        type: 'success',
                        title: '{{ __("technician.resources.report.drawing.messages.saved") }}',
                        message: '{{ __("technician.resources.report.drawing.messages.saved_description") }}'
                    }
                }));
            }
        },

        async saveDrawingToAttachments() {
            if (!this.hasDrawing) {
                window.dispatchEvent(new CustomEvent('notify', {
                    detail: {
                        type: 'warning',
                        title: '{{ __("technician.resources.report.drawing.messages.no_content") }}',
                        message: '{{ __("technician.resources.report.drawing.messages.no_content_description") }}'
                    }
                }));
                return;
            }

            this.isSaving = true;

            try {
                // Add visual feedback during save
                this.addSaveAnimation();

                // Convert canvas to data URL
                const canvas = this.canvas;
                const drawingData = canvas.toDataURL('image/png', 1.0);

                // Create filename
                const now = new Date();
                const timestamp = now.toLocaleString('ar-SA', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }).replace(/[\/\s:]/g, '-');

                const filename = `رسم-توضيحي-${timestamp}.png`;

                console.log(`Uploading drawing: ${filename}`);

                // Upload to server
                const response = await fetch('/technician/upload-drawing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        drawing_data: drawingData,
                        filename: filename
                    })
                });

                const result = await response.json();

                if (!response.ok || !result.success) {
                    throw new Error(result.message || 'فشل في رفع الرسم');
                }

                console.log('Drawing uploaded successfully:', result.data);

                // Method 1: Try to use Livewire method directly
                if (this.addViaLivewireMethod(result.data.path, result.data.filename)) {
                    console.log('Successfully added via Livewire method');
                } else {
                    // Method 2: Fallback to client-side integration
                    this.addFileToAttachmentsField(result.data.path, result.data.filename);
                }

                this.savedToAttachments = true;

                // Enhanced success message
                window.dispatchEvent(new CustomEvent('notify', {
                    detail: {
                        type: 'success',
                        title: '{{ __("technician.resources.report.drawing.messages.saved_to_attachments") }}',
                        message: '{{ __("technician.resources.report.drawing.messages.saved_to_attachments_description") }}',
                        duration: 5000,
                        actions: [{
                            label: '{{ __("technician.resources.report.drawing.actions.view_in_uploads") }}',
                            action: () => {
                                this.switchToUploadsTab(result.data.filename);
                            }
                        }]
                    }
                }));

                // Auto-switch to uploads tab
                setTimeout(() => {
                    this.switchToUploadsTab(result.data.filename);
                }, 1500);

                // Clear the canvas after saving with confirmation
                setTimeout(() => {
                    if (confirm('{{ __("technician.resources.report.drawing.messages.clear_after_save") }}')) {
                        this.clearCanvas();
                    }
                }, 3000);

            } catch (error) {
                console.error('Error saving drawing:', error);
                window.dispatchEvent(new CustomEvent('notify', {
                    detail: {
                        type: 'error',
                        title: '{{ __("technician.resources.report.drawing.messages.save_error") }}',
                        message: `{{ __("technician.resources.report.drawing.messages.save_error_description") }}: ${error.message}`
                    }
                }));
            } finally {
                this.isSaving = false;
                this.removeSaveAnimation();
            }
        },

        addSaveAnimation() {
            // Add visual feedback during save process
            const canvas = this.canvas;
            canvas.style.filter = 'brightness(1.1) contrast(1.1)';
            canvas.style.transform = 'scale(1.02)';
            canvas.style.transition = 'all 0.3s ease-in-out';
        },

        removeSaveAnimation() {
            // Remove visual feedback
            const canvas = this.canvas;
            canvas.style.filter = '';
            canvas.style.transform = '';
        },

        addViaLivewireMethod(filePath, filename) {
            // Try to call the Livewire method directly
            try {
                // Find the Livewire component
                const livewireElement = document.querySelector('[wire\\:id]');
                if (!livewireElement) {
                    console.log('No Livewire component found for method call');
                    return false;
                }

                const componentId = livewireElement.getAttribute('wire:id');
                if (!componentId || !window.Livewire) {
                    console.log('Livewire not available or no component ID');
                    return false;
                }

                const component = window.Livewire.find(componentId);
                if (!component) {
                    console.log('Livewire component not found');
                    return false;
                }

                // Call the addDrawingToAttachments method
                if (typeof component.call === 'function') {
                    component.call('addDrawingToAttachments', filePath, filename);
                    console.log('Called Livewire addDrawingToAttachments method');
                    return true;
                } else if (typeof component.$wire?.addDrawingToAttachments === 'function') {
                    component.$wire.addDrawingToAttachments(filePath, filename);
                    console.log('Called Livewire method via $wire');
                    return true;
                } else {
                    console.log('Livewire method addDrawingToAttachments not found');
                    return false;
                }

            } catch (error) {
                console.error('Error calling Livewire method:', error);
                return false;
            }
        },

        addFileToAttachmentsField(filePath, filename) {
            // Enhanced approach using Livewire component integration
            console.log(`Attempting to add file to attachments: ${filePath}`);

            // Method 1: Try to find and update Livewire component directly
            if (this.updateLivewireAttachments(filePath, filename)) {
                console.log('Successfully updated via Livewire component');
                return;
            }

            // Method 2: Try Alpine.js form data update
            if (this.updateAlpineFormData(filePath, filename)) {
                console.log('Successfully updated via Alpine.js form data');
                return;
            }

            // Method 3: Fallback to direct field manipulation
            if (this.updateFieldDirectly(filePath, filename)) {
                console.log('Successfully updated via direct field manipulation');
                return;
            }

            console.error('Failed to update attachments field with all methods');

            // Show user notification about manual addition needed
            window.dispatchEvent(new CustomEvent('notify', {
                detail: {
                    type: 'warning',
                    title: 'تم حفظ الرسم',
                    message: 'تم حفظ الرسم بنجاح. يرجى إعادة تحميل الصفحة لرؤية الملف في المرفقات.',
                    duration: 8000
                }
            }));
        },

        updateLivewireAttachments(filePath, filename) {
            // Find the Livewire component that contains the form
            const livewireElement = document.querySelector('[wire\\:id]');
            if (!livewireElement) {
                console.log('No Livewire component found');
                return false;
            }

            const componentId = livewireElement.getAttribute('wire:id');
            if (!componentId || !window.Livewire) {
                console.log('Livewire not available or no component ID');
                return false;
            }

            try {
                const component = window.Livewire.find(componentId);
                if (!component) {
                    console.log('Livewire component not found');
                    return false;
                }

                // Get current attachments
                let currentAttachments = [];
                try {
                    const current = component.get('data.attachments') || component.get('attachments') || [];
                    currentAttachments = Array.isArray(current) ? current : [];
                } catch (e) {
                    console.log('Could not get current attachments from Livewire');
                    currentAttachments = [];
                }

                // Add new file path
                currentAttachments.push(filePath);

                // Update Livewire component
                try {
                    component.set('data.attachments', currentAttachments);
                } catch (e) {
                    try {
                        component.set('attachments', currentAttachments);
                    } catch (e2) {
                        console.log('Failed to set attachments in Livewire component');
                        return false;
                    }
                }

                // Trigger Livewire update
                component.call('$refresh');

                console.log('Updated Livewire attachments:', currentAttachments);
                return true;

            } catch (error) {
                console.error('Error updating Livewire attachments:', error);
                return false;
            }
        },

        updateAlpineFormData(filePath, filename) {
            // Find Alpine.js form data
            const formElement = document.querySelector('form[x-data]') || document.querySelector('[x-data*="form"]');
            if (!formElement || !formElement._x_dataStack) {
                console.log('No Alpine.js form data found');
                return false;
            }

            try {
                const alpineData = formElement._x_dataStack[0];
                if (!alpineData) {
                    console.log('No Alpine data stack found');
                    return false;
                }

                // Try different data structure paths
                const dataPaths = ['data.attachments', 'attachments', 'state.attachments'];
                let updated = false;

                for (const path of dataPaths) {
                    try {
                        const pathParts = path.split('.');
                        let current = alpineData;

                        // Navigate to the parent object
                        for (let i = 0; i < pathParts.length - 1; i++) {
                            if (current[pathParts[i]]) {
                                current = current[pathParts[i]];
                            } else {
                                current[pathParts[i]] = {};
                                current = current[pathParts[i]];
                            }
                        }

                        // Get current attachments
                        const lastKey = pathParts[pathParts.length - 1];
                        let currentAttachments = current[lastKey] || [];
                        if (!Array.isArray(currentAttachments)) {
                            currentAttachments = [];
                        }

                        // Add new file
                        currentAttachments.push(filePath);
                        current[lastKey] = currentAttachments;

                        console.log(`Updated Alpine.js ${path}:`, currentAttachments);
                        updated = true;
                        break;

                    } catch (e) {
                        console.log(`Failed to update ${path}:`, e.message);
                        continue;
                    }
                }

                return updated;

            } catch (error) {
                console.error('Error updating Alpine.js form data:', error);
                return false;
            }
        },

        updateFieldDirectly(filePath, filename) {
            // Find the attachments field in the form
            const attachmentsField = this.findAttachmentsField();

            if (!attachmentsField) {
                console.log('Attachments field not found');
                return false;
            }

            try {
                // Get current attachments value
                let currentAttachments = this.getCurrentAttachments(attachmentsField);

                // Add the new file path
                currentAttachments.push(filePath);

                // Update the field value
                this.updateAttachmentsField(attachmentsField, currentAttachments);

                console.log(`Added file to attachments field: ${filePath}`);
                console.log('Current attachments:', currentAttachments);
                return true;

            } catch (error) {
                console.error('Error updating field directly:', error);
                return false;
            }
        },

        findAttachmentsField() {
            // Enhanced strategies to find the attachments field
            const selectors = [
                // Specific attachments field selectors
                'input[name="attachments"]',
                'input[name="data.attachments"]',
                'input[wire\\:model*="attachments"]',
                'input[wire\\:model="data.attachments"]',

                // Filament-specific selectors
                '[data-field-wrapper-id*="attachments"] input[type="file"]',
                '[data-field-wrapper-id="data.attachments"] input[type="file"]',
                '.fi-fo-file-upload input[type="file"]',

                // ID-based selectors
                '#attachments-upload',
                '#attachments-upload input[type="file"]',

                // Reference-based selectors
                '[x-ref="attachmentsUpload"]',
                '[x-ref="attachmentsUpload"] input[type="file"]'
            ];

            for (const selector of selectors) {
                try {
                    const field = document.querySelector(selector);
                    if (field) {
                        console.log(`Found attachments field using: ${selector}`);
                        return field;
                    }
                } catch (e) {
                    console.log(`Selector failed: ${selector}`, e.message);
                }
            }

            // Try to find by form structure with enhanced search
            const form = document.querySelector('form');
            if (form) {
                const fileInputs = form.querySelectorAll('input[type="file"]');
                for (const input of fileInputs) {
                    // Check various attributes for attachments
                    const attributes = [input.name, input.id, input.getAttribute('wire:model')];
                    for (const attr of attributes) {
                        if (attr && attr.includes('attachments')) {
                            console.log(`Found attachments field by attribute: ${attr}`);
                            return input;
                        }
                    }
                }
            }

            // Last resort: find any file input in the attachments tab
            const attachmentsTab = document.querySelector('#file-uploads-tab') ||
                                 document.querySelector('[data-tab="file_uploads"]');
            if (attachmentsTab) {
                const fileInput = attachmentsTab.querySelector('input[type="file"]');
                if (fileInput) {
                    console.log('Found file input in attachments tab');
                    return fileInput;
                }
            }

            console.log('No attachments field found with any method');
            return null;
        },

        getCurrentAttachments(field) {
            // Get current value from various sources
            let currentValue = [];

            // Try to get from Alpine.js data
            const wrapper = field.closest('[x-data]');
            if (wrapper && wrapper._x_dataStack) {
                const data = wrapper._x_dataStack[0];
                if (data && data.state && data.state.attachments) {
                    currentValue = Array.isArray(data.state.attachments) ? data.state.attachments : [];
                }
            }

            // Try to get from Livewire
            if (window.Livewire && currentValue.length === 0) {
                const livewireElement = field.closest('[wire\\:id]');
                if (livewireElement) {
                    const componentId = livewireElement.getAttribute('wire:id');
                    const component = window.Livewire.find(componentId);
                    if (component && component.get && component.get('attachments')) {
                        const attachments = component.get('attachments');
                        currentValue = Array.isArray(attachments) ? attachments : [];
                    }
                }
            }

            // Fallback: try to parse from field value
            if (currentValue.length === 0 && field.value) {
                try {
                    const parsed = JSON.parse(field.value);
                    currentValue = Array.isArray(parsed) ? parsed : [];
                } catch (e) {
                    currentValue = field.value ? [field.value] : [];
                }
            }

            return currentValue;
        },

        updateAttachmentsField(field, attachments) {
            // Update the field value in multiple ways to ensure it sticks

            // 1. Update the input value
            field.value = JSON.stringify(attachments);

            // 2. Update Alpine.js data if available
            const wrapper = field.closest('[x-data]');
            if (wrapper && wrapper._x_dataStack) {
                const data = wrapper._x_dataStack[0];
                if (data && data.state) {
                    data.state.attachments = attachments;
                }
            }

            // 3. Update Livewire if available
            if (window.Livewire) {
                const livewireElement = field.closest('[wire\\:id]');
                if (livewireElement) {
                    const componentId = livewireElement.getAttribute('wire:id');
                    const component = window.Livewire.find(componentId);
                    if (component && component.set) {
                        component.set('attachments', attachments);
                    }
                }
            }

            // 4. Trigger events to notify the form
            const events = ['change', 'input', 'livewire:update'];
            events.forEach(eventType => {
                field.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            // 5. Custom event for drawing file added
            window.dispatchEvent(new CustomEvent('drawing-file-added', {
                detail: {
                    attachments: attachments,
                    newFile: attachments[attachments.length - 1]
                }
            }));
        }
    };
}
</script>
@endpush
