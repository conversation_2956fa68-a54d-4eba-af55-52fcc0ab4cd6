<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>نظام إدارة عقود الصيانة</title>
    <!-- Include Ionic CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ionic/core/css/ionic.bundle.css">
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --ion-color-primary: #F34D02;
            --ion-color-primary-rgb: 243, 77, 2;
            --ion-color-primary-contrast: #ffffff;
            --ion-color-primary-contrast-rgb: 255, 255, 255;
            --ion-color-primary-shade: #DA4502;
            --ion-color-primary-tint: #F46F2E;

            --ion-color-secondary: #F39F07;
            --ion-color-secondary-rgb: 243, 159, 7;
            --ion-color-secondary-contrast: #ffffff;
            --ion-color-secondary-contrast-rgb: 255, 255, 255;
            --ion-color-secondary-shade: #DA8F06;
            --ion-color-secondary-tint: #F5AF33;

            --ion-color-success: #2dd36f;
            --ion-font-family: 'Tajawal', sans-serif;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            text-align: right;
        }

        .rtl {
            direction: rtl;
        }

        .page-header {
            background: linear-gradient(135deg, #F34D02 0%, #F39F07 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
        }

        .footer {
            background-color: #f4f5f8;
            padding: 30px 0;
            text-align: center;
        }

        /* Custom style for ion-menu-button to flip for RTL */
        ion-menu-button {
            transform: scaleX(-1);
        }

        .auth-button-text {
            margin-right: 6px;
        }
    </style>

    @livewireStyles
</head>
<body>
<ion-app>
    <!-- Header -->
    <ion-header translucent>
        <ion-toolbar color="primary">
            <ion-buttons slot="start">
                <ion-back-button default-href="{{ route('landing') }}"></ion-back-button>
            </ion-buttons>
            <ion-title>نظام إدارة عقود الصيانة</ion-title>
            <ion-buttons slot="end">

            </ion-buttons>
        </ion-toolbar>
    </ion-header>

    <!-- Content - IMPORTANT: Don't wrap the $slot in ion-content -->
    <ion-content fullscreen>
        @yield('content')
    </ion-content>

    <!-- Footer -->
    <ion-footer class="footer">
        <ion-grid>
            <ion-row>
                <ion-col size="12">
                    <p>© {{ date('Y') }} نظام إدارة عقود الصيانة. جميع الحقوق محفوظة.</p>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-footer>
</ion-app>

<!-- Ionic Framework JS -->
<script type="module" src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.esm.js"></script>
<script nomodule src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.js"></script>

<!-- Additional script for responsive handling of auth buttons -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const handleResponsiveButtons = () => {
            const buttonTexts = document.querySelectorAll('.auth-button-text');
            if (window.innerWidth < 576) {
                buttonTexts.forEach(text => text.style.display = 'none');
            } else {
                buttonTexts.forEach(text => text.style.display = 'inline');
            }
        };

        // Initial check
        handleResponsiveButtons();

        // Listen for window resize
        window.addEventListener('resize', handleResponsiveButtons);
    });
</script>
</body>
</html>
