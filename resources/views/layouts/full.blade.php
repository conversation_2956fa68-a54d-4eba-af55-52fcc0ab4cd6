<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'منار النور لمعدات الأمن والسلامة' }}</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    @filamentStyles
    <style>
        /*:root {
            --primary-50: var(--orange-50, #FFF5F0);
            --primary-100: var(--orange-100, #FFE4D6);
            --primary-200: var(--orange-200, #FFCAB3);
            --primary-300: var(--orange-300, #FFAC8A);
            --primary-400: var(--orange-400, #FF8D61);
            --primary-500: var(--orange-500, #F34D02);
            --primary-600: var(--orange-600, #D13C02);
            --primary-700: var(--orange-700, #AF2B01);
            --primary-800: var(--orange-800, #8D1B01);
            --primary-900: var(--orange-900, #6B0E00);
        }*/

        [x-cloak] {
            display: none !important;
        }

        body {
            font-family: 'Tajawal', sans-serif;
        }
    </style>

    <!-- Scripts -->
    @filamentScripts
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="antialiased bg-gray-50">
<!-- Header -->
<header class="bg-primary-500 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <a href="/" class="font-bold text-xl">منار النور لمعدات الأمن والسلامة</a>

            <!-- Mobile menu button -->
            <div class="flex md:hidden">
                <button
                    type="button"
                    class="bg-primary-600 inline-flex items-center justify-center p-2 rounded-md text-white hover:bg-primary-700 focus:outline-none"
                    x-data="{}"
                    x-on:click="$dispatch('open-mobile-menu')"
                >
                    <span class="sr-only">فتح القائمة</span>
                    <x-heroicon-o-bars-3 class="h-6 w-6" />
                </button>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex md:space-x-8 md:space-x-reverse rtl:space-x-reverse">
                <a href="/" class="text-white hover:bg-primary-600 px-3 py-2 rounded-md text-sm font-medium">الرئيسية</a>
                <a href="{{ url('filament.client.pages.certificates') }}" class="text-white hover:bg-primary-600 px-3 py-2 rounded-md text-sm font-medium">شهادات الفحص</a>
                <a href="{{ url('filament.client.pages.maintenance') }}" class="text-white hover:bg-primary-600 px-3 py-2 rounded-md text-sm font-medium">عقود الصيانة</a>
                <a href="#" class="text-white hover:bg-primary-600 px-3 py-2 rounded-md text-sm font-medium">اتصل بنا</a>
            </nav>

            <!-- Authentication links -->
            <div class="hidden md:flex md:items-center md:mr-6 space-x-4 space-x-reverse rtl:space-x-reverse">
                @auth('client')
                    <a href="{{ route('filament.client.pages.dashboard') }}" class="bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium">حسابي</a>
                    <form method="POST" action="{{ route('filament.client.auth.logout') }}">
                        @csrf
                        <button type="submit" class="text-white hover:bg-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                            تسجيل خروج
                        </button>
                    </form>
                @else
                    <a href="{{ route('filament.client.auth.login') }}" class="text-white hover:bg-primary-600 px-3 py-2 rounded-md text-sm font-medium">تسجيل الدخول</a>
                    {{--<a href="{{ url('filament.client.auth.register') }}" class="bg-white text-primary-600 hover:bg-gray-100 px-4 py-2 rounded-md text-sm font-medium">تسجيل جديد</a>--}}
                @endauth
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state -->
    <div
        x-data="{ open: false }"
        x-show="open"
        x-on:open-mobile-menu.window="open = true"
        x-on:click.away="open = false"
        x-on:keydown.escape.window="open = false"
        x-cloak
        class="md:hidden"
    >
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <a href="/" class="text-white hover:bg-primary-600 block px-3 py-2 rounded-md text-base font-medium">الرئيسية</a>
            <a href="{{ url('filament.client.pages.certificates') }}" class="text-white hover:bg-primary-600 block px-3 py-2 rounded-md text-base font-medium">شهادات الفحص</a>
            <a href="{{ url('filament.client.pages.maintenance') }}" class="text-white hover:bg-primary-600 block px-3 py-2 rounded-md text-base font-medium">عقود الصيانة</a>
            <a href="#" class="text-white hover:bg-primary-600 block px-3 py-2 rounded-md text-base font-medium">اتصل بنا</a>
        </div>

        <div class="pt-4 pb-3 border-t border-primary-600">
            <div class="px-2 space-y-1">
                @auth('client')
                    <a href="{{ route('filament.client.pages.dashboard') }}" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-primary-600">حسابي</a>
                    <form method="POST" action="{{ route('filament.client.auth.logout') }}">
                        @csrf
                        <button type="submit" class="block w-full text-right px-3 py-2 rounded-md text-base font-medium text-white hover:bg-primary-600">
                            تسجيل خروج
                        </button>
                    </form>
                @else
                    <a href="{{ url('filament.client.auth.login') }}" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-primary-600">تسجيل الدخول</a>
                    {{--<a href="{{ url('filament.client.auth.register') }}" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-primary-600">تسجيل جديد</a>--}}
                @endauth
            </div>
        </div>
    </div>
</header>

<!-- Main Content -->
<main>
    {{ $slot }}
</main>

<!-- Footer -->
<footer class="bg-primary-500 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Company Info -->
            <div>
                <h3 class="text-xl font-bold mb-4">منار النور لمعدات الأمن والسلامة</h3>
                <p class="mb-4">شركة رائدة في مجال أنظمة الأمن والسلامة ومعدات مكافحة الحرائق</p>
                <p>جميع الحقوق محفوظة © {{ date('Y') }}</p>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-xl font-bold mb-4">روابط سريعة</h3>
                <ul class="space-y-2">
                    <li><a href="{{ url('filament.client.pages.certificates') }}" class="hover:underline">شهادات الفحص</a></li>
                    <li><a href="{{ url('filament.client.pages.maintenance') }}" class="hover:underline">عقود الصيانة</a></li>
                    <li><a href="#" class="hover:underline">عن الشركة</a></li>
                    <li><a href="#" class="hover:underline">اتصل بنا</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-xl font-bold mb-4">بيانات الاتصال</h3>
                <ul class="space-y-2">
                    <li class="flex items-center">
                        <x-heroicon-o-map-pin class="h-5 w-5 ml-2" />
                        <span>القصيم، المملكة العربية السعودية</span>
                    </li>
                    <li class="flex items-center">
                        <x-heroicon-o-phone class="h-5 w-5 ml-2" />
                        <span style="direction: ltr;">966 55xxxxxxxxx</span>
                    </li>
                    <li class="flex items-center">
                        <x-heroicon-o-envelope class="h-5 w-5 ml-2" />
                        <span><EMAIL></span>
                    </li>
                </ul>

                <!-- Social Media -->
                <div class="mt-4 flex space-x-4 space-x-reverse">
                    <a href="#" class="text-white hover:text-primary-200">
                        <span class="sr-only">Facebook</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" class="text-white hover:text-primary-200">
                        <span class="sr-only">Twitter</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                    </a>
                    <a href="#" class="text-white hover:text-primary-200">
                        <span class="sr-only">Instagram</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>

@stack('modals')
</body>
</html>
