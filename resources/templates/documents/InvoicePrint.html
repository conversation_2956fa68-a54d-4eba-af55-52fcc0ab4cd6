<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفاتورة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            direction: rtl;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .document-title {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            color: #2c3e50;
        }
        .info-section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        /* Add more styles as needed for specific templates */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .total-section {
            margin-top: 20px;
            text-align: left;
        }
        .total-label {
            font-weight: bold;
        }
        .total-value {
            font-weight: bold;
            font-size: 18px;
            color: #3498db;
        }
    </style>
</head>
<body>
<div class="header">
    @if(!empty($company['logo']))
    <img src="{{ $company['logo'] }}" alt="Logo" class="logo">
    @endif
    <h2>{{ $company['name'] }}</h2>
</div>

<div class="document-title">الفاتورة</div>
<div class="info-section">
    <div class="section-title">معلومات الفاتورة</div>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">رقم الفاتورة:</span>
            <span>{{ $invoice['number'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">تاريخ الإصدار:</span>
            <span>{{ $invoice['date'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">تاريخ الاستحقاق:</span>
            <span>{{ $invoice['due_date'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الحالة:</span>
            <span>{{ $invoice['status'] }}</span>
        </div>
    </div>
</div>

<div class="info-section">
    <div class="section-title">معلومات العميل</div>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">الاسم:</span>
            <span>{{ $customer['name'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">رقم الهاتف:</span>
            <span>{{ $customer['phone'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">البريد الإلكتروني:</span>
            <span>{{ $customer['email'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">العنوان:</span>
            <span>{{ $customer['address'] }}</span>
        </div>
        @if(!empty($customer['vat_number']))
        <div class="info-item">
            <span class="info-label">الرقم الضريبي:</span>
            <span>{{ $customer['vat_number'] }}</span>
        </div>
        @endif
    </div>
</div>

<table>
    <thead>
    <tr>
        <th>#</th>
        <th>الوصف</th>
        <th>الكمية</th>
        <th>سعر الوحدة</th>
        <th>ضريبة القيمة المضافة</th>
        <th>الإجمالي</th>
    </tr>
    </thead>
    <tbody>
    @foreach($items as $index => $item)
    <tr>
        <td>{{ $index + 1 }}</td>
        <td>{{ $item['description'] }}</td>
        <td>{{ $item['quantity'] }}</td>
        <td>{{ $item['unit_price'] }}</td>
        <td>{{ $item['tax'] }}</td>
        <td>{{ $item['total'] }}</td>
    </tr>
    @endforeach
    </tbody>
</table>

<div class="total-section">
    <div><span class="total-label">المجموع الفرعي:</span> {{ $invoice['subtotal'] }} ريال</div>
    <div><span class="total-label">ضريبة القيمة المضافة (15%):</span> {{ $invoice['tax'] }} ريال</div>
    <div><span class="total-label">الخصم:</span> {{ $invoice['discount'] }} ريال</div>
    <div><span class="total-label">المجموع الكلي:</span> <span class="total-value">{{ $invoice['total'] }} ريال</span></div>
</div>

@if(!empty($invoice['notes']))
<div class="notes">
    <div class="section-title">ملاحظات</div>
    <div>{{ $invoice['notes'] }}</div>
</div>
@endif
<div class="footer">
    <div>{{ $company['name'] }} - {{ $company['address'] }}</div>
    <div>هاتف: {{ $company['phone'] }} - بريد إلكتروني: {{ $company['email'] }}</div>
</div>
</body>
</html>
