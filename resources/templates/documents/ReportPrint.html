<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            direction: rtl;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .document-title {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            color: #2c3e50;
        }
        .info-section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        /* Add more styles as needed for specific templates */
        .summary-box {
            padding: 15px;
            background-color: #f0f7ff;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .details-section {
            margin-top: 20px;
        }
        .recommendations {
            margin-top: 30px;
            padding: 15px;
            background-color: #fff8e6;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="header">
    @if(!empty($company['logo']))
    <img src="{{ $company['logo'] }}" alt="Logo" class="logo">
    @endif
    <h2>{{ $company['name'] }}</h2>
</div>

<div class="document-title">التقرير</div>
<div class="info-section">
    <div class="section-title">معلومات التقرير</div>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">رقم التقرير:</span>
            <span>{{ $report['id'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">العنوان:</span>
            <span>{{ $report['title'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">تاريخ التقرير:</span>
            <span>{{ $report['date'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">نوع التقرير:</span>
            <span>{{ $report['type'] }}</span>
        </div>
    </div>
</div>

<div class="info-section">
    <div class="section-title">معلومات العميل</div>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">الاسم:</span>
            <span>{{ $customer['name'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الشخص المسؤول:</span>
            <span>{{ $customer['contact_person'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">رقم الهاتف:</span>
            <span>{{ $customer['phone'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">البريد الإلكتروني:</span>
            <span>{{ $customer['email'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">العنوان:</span>
            <span>{{ $customer['address'] }}</span>
        </div>
    </div>
</div>

<div class="summary-box">
    <div class="section-title">ملخص</div>
    <div>{{ $report['summary'] }}</div>
</div>

<div class="details-section">
    <div class="section-title">التفاصيل</div>
    <div>{{ $report['details'] }}</div>
</div>

<table>
    <thead>
    <tr>
        <th>#</th>
        <th>البند</th>
        <th>الحالة</th>
        <th>ملاحظات</th>
    </tr>
    </thead>
    <tbody>
    @foreach($maintenance_items as $index => $item)
    <tr>
        <td>{{ $index + 1 }}</td>
        <td>{{ $item['description'] }}</td>
        <td>{{ $item['status'] }}</td>
        <td>{{ $item['notes'] }}</td>
    </tr>
    @endforeach
    </tbody>
</table>

@if(!empty($report['recommendations']))
<div class="recommendations">
    <div class="section-title">التوصيات</div>
    <div>{{ $report['recommendations'] }}</div>
</div>
@endif

<div class="info-section">
    <div class="section-title">معلومات الفني</div>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">الاسم:</span>
            <span>{{ $technician['name'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">المسمى الوظيفي:</span>
            <span>{{ $technician['position'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">رقم الهاتف:</span>
            <span>{{ $technician['phone'] }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">البريد الإلكتروني:</span>
            <span>{{ $technician['email'] }}</span>
        </div>
    </div>
</div>
<div class="footer">
    <div>{{ $company['name'] }} - {{ $company['address'] }}</div>
    <div>هاتف: {{ $company['phone'] }} - بريد إلكتروني: {{ $company['email'] }}</div>
</div>
</body>
</html>
