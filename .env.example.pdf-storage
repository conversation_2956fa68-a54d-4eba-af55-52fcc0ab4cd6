# PDF Storage Configuration for <PERSON><PERSON> Contractly
# Add these variables to your .env file

# PDF Storage Disk Configuration
# Options: local, public, s3, or any configured disk
PDF_STORAGE_DISK=public

# For S3 Storage (if using s3 disk)
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_DEFAULT_REGION=us-east-1
# AWS_BUCKET=your-pdf-bucket
# AWS_USE_PATH_STYLE_ENDPOINT=false

# For Local Storage (default)
# No additional configuration needed - uses storage/app/public

# PDF Cleanup Configuration (optional)
# PDF_CLEANUP_DAYS=30  # Days to keep PDFs before cleanup

# File Upload Limits (optional)
# UPLOAD_MAX_FILESIZE=10M
# POST_MAX_SIZE=10M
