<?php

namespace App\Providers\Filament;

use Filament\Panel;
use Filament\Widgets;
use Filament\PanelProvider;
use Filament\Enums\ThemeMode;
use App\Filament\Client\Pages;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Http\Middleware\Authenticate;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Filament\Http\Middleware\AuthenticateSession;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use App\Http\Middleware\SetClientLocale;
use Stancl\Tenancy\Database\Models\Domain;

class ClientPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        try {
            $cache = Cache::driver('central_redis');
            $Domains = $cache->get('api_domains');
            if (empty($Domains)) {
                $Domains = Domain::pluck('domain')->toArray();
                $cache->set('api_domains', $Domains, 60 * 24 * 7);
            }
        } catch (\Exception $e) {
            $Domains = [];
        }

        return $panel
            ->sidebarCollapsibleOnDesktop()
            ->databaseNotifications()
            ->id('client')
            ->path('')
            ->authGuard('client')
            ->login(Pages\Auth\CustomLogin::class)
            ->registration(Pages\Auth\Register::class)
            ->font('IBM PLEX SANS ARABIC')
            ->colors([
                'primary' => Color::Amber,
            ])
            ->defaultThemeMode(ThemeMode::Light)
            //->simplePageMaxContentWidth(MaxWidth::Full)
            ->simpleProfilePage(false)
            ->discoverResources(in: app_path('Filament/Client/Resources'), for: 'App\\Filament\\Client\\Resources')
            ->discoverPages(in: app_path('Filament/Client/Pages'), for: 'App\\Filament\\Client\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Client/Widgets'), for: 'App\\Filament\\Client\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            /*->domains($Domains)*/
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetClientLocale::class, // Ensure Arabic locale for Client panel
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
