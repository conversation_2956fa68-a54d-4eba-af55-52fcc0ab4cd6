<?php

namespace App\Providers;

use App\Services\DocKingService;
use Illuminate\Support\ServiceProvider;

class DocKingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(DocKingService::class, function ($app) {
            return new DocKingService();
        });

        // Register a shorthand binding for the facade
        $this->app->alias(DocKingService::class, 'docking');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Publish config file
        $this->publishes([
            __DIR__ . '/../../config/docking.php' => config_path('docking.php'),
        ], 'docking-config');
    }
}
