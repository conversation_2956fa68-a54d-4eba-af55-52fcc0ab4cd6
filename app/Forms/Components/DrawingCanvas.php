<?php

namespace App\Forms\Components;

use Filament\Forms\Components\Field;
use Filament\Forms\Components\Concerns\HasExtraAttributes;

class DrawingCanvas extends Field
{
    use HasExtraAttributes;

    protected string $view = 'forms.components.drawing-canvas';

    protected int $width = 800;
    protected int $height = 400;
    protected string $backgroundColor = '#ffffff';
    protected string $strokeColor = '#000000';
    protected int $strokeWidth = 2;
    protected bool $enableTouch = true;
    protected bool $enableMouse = true;

    public function width(int $width): static
    {
        $this->width = $width;
        return $this;
    }

    public function height(int $height): static
    {
        $this->height = $height;
        return $this;
    }

    public function backgroundColor(string $color): static
    {
        $this->backgroundColor = $color;
        return $this;
    }

    public function strokeColor(string $color): static
    {
        $this->strokeColor = $color;
        return $this;
    }

    public function strokeWidth(int $width): static
    {
        $this->strokeWidth = $width;
        return $this;
    }

    public function enableTouch(bool $enable = true): static
    {
        $this->enableTouch = $enable;
        return $this;
    }

    public function enableMouse(bool $enable = true): static
    {
        $this->enableMouse = $enable;
        return $this;
    }

    public function getWidth(): int
    {
        return $this->width;
    }

    public function getHeight(): int
    {
        return $this->height;
    }

    public function getBackgroundColor(): string
    {
        return $this->backgroundColor;
    }

    public function getStrokeColor(): string
    {
        return $this->strokeColor;
    }

    public function getStrokeWidth(): int
    {
        return $this->strokeWidth;
    }

    public function isTouchEnabled(): bool
    {
        return $this->enableTouch;
    }

    public function isMouseEnabled(): bool
    {
        return $this->enableMouse;
    }
}
