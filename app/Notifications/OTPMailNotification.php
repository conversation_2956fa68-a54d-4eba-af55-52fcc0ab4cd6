<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Notifications\Traits\DefaultMessageTrait;
use Illuminate\Notifications\Messages\MailMessage;

class OTPMailNotification extends Notification implements ShouldQueue
{
    use Queueable, DefaultMessageTrait;

    /**
     * @var string
     */
    public static $defaultMessage = 'رمز تسجيل الدخول: {otp}';

    /**
     * @var array
     */
    public static $parameters = [
        '{otp}' => 'رمز التأكيد otp',
    ];

    /**
     * @var string
     */
    private $otp;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(string $otp)
    {
        $this->otp = $otp;
        $this->queue = 'high';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('رمز تسجيل الدخول')
            ->line(str_replace([
                '{otp}',
            ], [
                $this->otp,
            ], self::getDefaultMessage()));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'content' => self::getDefaultMessage(),
            'data' => [
                'otp' => $this->otp,
            ],
        ];
    }
}
