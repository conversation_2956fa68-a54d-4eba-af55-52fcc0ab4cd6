<?php

namespace App\Notifications;

use App\Broadcasting\WahaChannel;
use App\Broadcasting\TaqnyatChannel;
use App\Models\BroadcastChannelSender;
use Illuminate\Notifications\Notification;
use App\Notifications\Traits\DefaultMessageTrait;

class OTPNotification extends Notification //implements ShouldQueue
{
    use DefaultMessageTrait;

    /**
     * @var string
     */
    public static $defaultMessage = 'رمز التحقق: {otp}' . "\r\n" . 'للدخول لمنصة {family_name}';

    /**
     * @var array
     */
    public static $parameters = [
        '{otp}' => 'رمز التأكيد otp',
        '{domain}' => 'المنصة',
        '{family_name}' => 'اسم العائلة',
    ];

    /**
     * Create a new notification instance.
     *
     * @param string                      $otp
     * @param string|null                 $domain
     * @param BroadcastChannelSender|null $sender
     * @param string|null                 $via
     */
    public function __construct(
        private readonly string                      $otp,
        private readonly string|null                 $domain = null,
        private readonly BroadcastChannelSender|null $sender = null,
        private readonly string|null                 $via = null
    )
    {
        $this->queue = 'high';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        if ($this->getSender()) {
            return [
                $this->getSender()->channel->class,
            ];
        }
        return [];
        //return $this->via === 'waha' ? [WahaChannel::class] : [TaqnyatChannel::class];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'content' => self::getDefaultMessage(),
            'data' => [
                'otp' => $this->otp,
                'domain' => $this->domain,
            ],
        ];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toWaha($notifiable)
    {
        return [
            'type' => 'text',
            'text' => $this->toText($notifiable),
        ];
    }

    public function toText($notifiable)
    {
        $message = self::getDefaultMessage();
        return str_replace([
            '{otp}',
            '{domain}',
            '{family_name}',
        ], [
            $this->otp,
            $this->domain,
        ], $message);
    }

    /**
     * @return BroadcastChannelSender|null
     */
    public function getSender()
    {
        if ($this->sender)
            return $this->sender;
        /*elseif ($this->via === 'waha')
            return app(AppSettings::class)->wahaSender;
        else
            return app(AppSettings::class)->smsSender;*/
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return string
     */
    public function toTaqnyat($notifiable)
    {
        return str_replace("\r\n", ' ', $this->toText($notifiable));
    }
}
