<?php

namespace App\Filament\Technician\Resources\TechnicianReportResource\Pages;

use App\Filament\Technician\Resources\TechnicianReportResource;
use App\Models\TechnicianReportChecklistItem;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditTechnicianReport extends EditRecord
{
    protected static string $resource = TechnicianReportResource::class;

    public function getTitle(): string
    {
        return __('technician.resources.report.pages.edit.title');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label(__('technician.resources.report.actions.view')),

            Actions\Action::make('submit')
                ->label(__('technician.resources.report.actions.submit'))
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading(__('technician.resources.report.actions.submit_modal_heading'))
                ->modalDescription(__('technician.resources.report.actions.submit_modal_description'))
                ->action(function () {
                    if ($this->getRecord()->submit()) {
                        Notification::make()
                            ->title(__('technician.resources.report.messages.submitted_successfully'))
                            ->success()
                            ->send();

                        return redirect($this->getResource()::getUrl('view', ['record' => $this->getRecord()]));
                    } else {
                        Notification::make()
                            ->title(__('technician.resources.report.messages.submit_failed'))
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn (): bool => $this->getRecord()->canBeSubmitted()),

            Actions\DeleteAction::make()
                ->label(__('technician.resources.report.actions.delete'))
                ->visible(fn (): bool => $this->getRecord()->status === \App\Models\TechnicianReport::STATUS_DRAFT),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return __('technician.resources.report.messages.updated_successfully');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing checklist items into the form structure
        $checklistItems = $this->getRecord()->checklistItems;
        $checklistData = [];

        foreach ($checklistItems as $item) {
            $checklistData[$item->item_key] = [
                'completed' => $item->status !== \App\Models\TechnicianReportChecklistItem::STATUS_NOT_CHECKED,
                'status' => $item->status,
                'item_key' => $item->item_key,
                'category' => $item->category,
                'description' => json_encode($item->getTranslations('description')),
                'is_required' => $item->is_required,
                'sort_order' => $item->sort_order,
                'id' => $item->id,
            ];
        }

        $data['checklist_items'] = $checklistData;

        // Handle attachments field for proper display in edit mode
        if (isset($data['attachments'])) {
            if (is_string($data['attachments'])) {
                // If it's a JSON string, decode it
                $decoded = json_decode($data['attachments'], true);
                $data['attachments'] = is_array($decoded) ? $decoded : null;
            } elseif (is_array($data['attachments'])) {
                // If it's already an array, use it directly
                $data['attachments'] = $data['attachments'];
            } else {
                // If it's not a string or array, set to null
                $data['attachments'] = null;
            }

            // Log the attachments for debugging
            \Log::info('Loading attachments for edit', [
                'report_id' => $data['id'] ?? 'unknown',
                'attachments_type' => gettype($data['attachments']),
                'attachments_count' => is_array($data['attachments']) ? count($data['attachments']) : 0,
                'attachments' => $data['attachments']
            ]);
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle translatable fields - form sends data as ['ar' => 'text'] or null
        // Spatie Laravel Translatable expects this exact format
        $translatableFields = ['work_summary', 'findings', 'recommendations', 'issues_found', 'parts_used'];

        foreach ($translatableFields as $field) {
            if (isset($data[$field])) {
                // The form dehydrateStateUsing already formats data as ['ar' => $text] or null
                // We just need to ensure empty arrays become null
                if (is_array($data[$field])) {
                    // Check if the array has any non-empty values
                    $hasContent = false;
                    foreach ($data[$field] as $locale => $value) {
                        if (!empty($value) && trim($value) !== '') {
                            $hasContent = true;
                            break;
                        }
                    }

                    if (!$hasContent) {
                        $data[$field] = null;
                    }
                    // If it has content, leave the array as-is for Spatie Translatable
                } elseif (empty($data[$field]) || trim($data[$field]) === '') {
                    // Handle empty strings or null values
                    $data[$field] = null;
                }
            } else {
                // Field not set, ensure it's null
                $data[$field] = null;
            }
        }

        // Extract checklist items from the form data
        $checklistItems = $data['checklist_items'] ?? [];
        unset($data['checklist_items']);

        // Store checklist items for processing after save
        $this->checklistItemsData = $checklistItems;

        // Handle attachments field - ensure it's properly formatted
        if (array_key_exists('attachments', $data)) {
            if (empty($data['attachments']) || $data['attachments'] === [] || $data['attachments'] === '' || $data['attachments'] === null) {
                $data['attachments'] = null;
            } else {
                // Ensure attachments is an array for JSON storage
                if (is_string($data['attachments'])) {
                    // If it's a string, try to decode it as JSON
                    $decoded = json_decode($data['attachments'], true);
                    $data['attachments'] = is_array($decoded) ? $decoded : [$data['attachments']];
                } elseif (!is_array($data['attachments'])) {
                    // If it's not an array, wrap it in an array
                    $data['attachments'] = [$data['attachments']];
                }

                // Filter out any empty values and validate file paths
                $data['attachments'] = array_filter($data['attachments'], function($value) {
                    if (empty($value) || $value === '') {
                        return false;
                    }

                    // Check if it's a valid file path (either relative or full URL)
                    if (is_string($value)) {
                        // Accept paths that start with technician-reports/ or are full URLs
                        return str_starts_with($value, 'technician-reports/') ||
                               str_starts_with($value, 'http') ||
                               str_starts_with($value, '/storage/');
                    }

                    return true;
                });

                // If after filtering we have no items, set to null
                if (empty($data['attachments'])) {
                    $data['attachments'] = null;
                } else {
                    // Re-index the array to ensure sequential keys
                    $data['attachments'] = array_values($data['attachments']);

                    // Log the attachments for debugging
                    \Log::info('Saving attachments for report', [
                        'report_id' => $this->getRecord()?->id,
                        'attachments' => $data['attachments'],
                        'count' => count($data['attachments'])
                    ]);
                }
            }
        } else {
            // If attachments key doesn't exist, set it to null
            $data['attachments'] = null;
        }

        // Handle drawing_data field - ensure it's properly formatted
        if (array_key_exists('drawing_data', $data)) {
            if (empty($data['drawing_data']) || $data['drawing_data'] === '' || $data['drawing_data'] === null) {
                $data['drawing_data'] = null;
            } elseif (is_array($data['drawing_data'])) {
                // If it's an array, JSON encode it
                $data['drawing_data'] = json_encode($data['drawing_data']);
            }
            // If it's already a string, leave it as is (could be base64 data or JSON)
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Update checklist items from form data
        $this->updateChecklistItemsFromFormData();
    }

    /**
     * Update checklist items from the form data.
     */
    protected function updateChecklistItemsFromFormData(): void
    {
        if (empty($this->checklistItemsData)) {
            return;
        }

        foreach ($this->checklistItemsData as $itemKey => $itemData) {
            if (!empty($itemData['item_key'])) {
                // Find existing checklist item by ID if available, otherwise by item_key using polymorphic relationship
                $checklistItem = null;

                if (!empty($itemData['id'])) {
                    $checklistItem = TechnicianReportChecklistItem::where('id', $itemData['id'])
                        ->where('reportable_type', \App\Models\TechnicianReport::class)
                        ->where('reportable_id', $this->getRecord()->id)
                        ->first();
                } else {
                    $checklistItem = TechnicianReportChecklistItem::where('reportable_type', \App\Models\TechnicianReport::class)
                        ->where('reportable_id', $this->getRecord()->id)
                        ->where('item_key', $itemData['item_key'])
                        ->first();
                }

                if ($checklistItem) {
                    // Determine status based on checkbox completion
                    $status = !empty($itemData['completed'])
                        ? \App\Models\TechnicianReportChecklistItem::STATUS_PASSED
                        : \App\Models\TechnicianReportChecklistItem::STATUS_NOT_CHECKED;

                    $checklistItem->update([
                        'status' => $status,
                    ]);
                } else {
                    // Create new checklist item if it doesn't exist (shouldn't happen in edit mode)
                    $status = !empty($itemData['completed'])
                        ? \App\Models\TechnicianReportChecklistItem::STATUS_PASSED
                        : \App\Models\TechnicianReportChecklistItem::STATUS_NOT_CHECKED;

                    \App\Models\TechnicianReportChecklistItem::create([
                        'reportable_type' => \App\Models\TechnicianReport::class,
                        'reportable_id' => $this->getRecord()->id,
                        'category' => $itemData['category'],
                        'item_key' => $itemData['item_key'],
                        'description' => json_decode($itemData['description'], true),
                        'status' => $status,
                        'is_required' => $itemData['is_required'] ?? false,
                        'sort_order' => $itemData['sort_order'] ?? 0,
                    ]);
                }
            }
        }
    }

    /**
     * Store checklist items data for processing after save
     */
    protected array $checklistItemsData = [];

    /**
     * Add a drawing file to the attachments with enhanced integration
     */
    public function addDrawingToAttachments($filePath, $filename = null)
    {
        try {
            // Get current form data
            $data = $this->form->getState();

            // Get current attachments
            $currentAttachments = $data['attachments'] ?? [];

            // Ensure it's an array
            if (!is_array($currentAttachments)) {
                if (is_string($currentAttachments)) {
                    $decoded = json_decode($currentAttachments, true);
                    $currentAttachments = is_array($decoded) ? $decoded : [];
                } else {
                    $currentAttachments = [];
                }
            }

            // Check if file already exists to prevent duplicates
            if (!in_array($filePath, $currentAttachments)) {
                // Add the new file path
                $currentAttachments[] = $filePath;

                // Update the form state with enhanced refresh
                $this->form->fill(['attachments' => $currentAttachments]);

                // Force refresh the form component
                $this->form->getComponent('attachments')?->state($currentAttachments);

                // Update the record directly to ensure persistence
                $this->getRecord()->update(['attachments' => $currentAttachments]);

                // Log the update
                \Log::info('Drawing added to attachments via Livewire', [
                    'report_id' => $this->getRecord()->id,
                    'file_path' => $filePath,
                    'filename' => $filename,
                    'total_attachments' => count($currentAttachments),
                    'all_attachments' => $currentAttachments
                ]);

                // Emit enhanced event to update UI
                $this->dispatch('drawing-added-to-attachments', [
                    'filename' => $filename,
                    'path' => $filePath,
                    'total' => count($currentAttachments),
                    'success' => true
                ]);

                // Show success notification
                \Filament\Notifications\Notification::make()
                    ->title(__('technician.resources.report.drawing.messages.saved_to_attachments'))
                    ->body(__('technician.resources.report.drawing.messages.saved_to_attachments_description'))
                    ->success()
                    ->duration(5000)
                    ->send();

                return true;
            } else {
                \Log::info('Drawing file already exists in attachments', [
                    'report_id' => $this->getRecord()->id,
                    'file_path' => $filePath,
                    'filename' => $filename
                ]);

                // Show info notification
                \Filament\Notifications\Notification::make()
                    ->title(__('technician.resources.report.drawing.messages.file_already_exists'))
                    ->body(__('technician.resources.report.drawing.messages.file_already_exists_description'))
                    ->info()
                    ->send();

                return true;
            }

        } catch (\Exception $e) {
            \Log::error('Failed to add drawing to attachments', [
                'error' => $e->getMessage(),
                'file_path' => $filePath,
                'filename' => $filename,
                'trace' => $e->getTraceAsString()
            ]);

            // Show error notification
            \Filament\Notifications\Notification::make()
                ->title(__('technician.resources.report.drawing.messages.save_error'))
                ->body(__('technician.resources.report.drawing.messages.save_error_description'))
                ->danger()
                ->send();

            return false;
        }
    }

    /**
     * Save drawing data as an image file
     */
    protected function saveDrawingAsFile(string $drawingData): ?string
    {
        try {
            // Extract base64 data from data URL
            if (strpos($drawingData, 'data:image/png;base64,') === 0) {
                $base64Data = substr($drawingData, strlen('data:image/png;base64,'));
                $imageData = base64_decode($base64Data);

                if ($imageData === false) {
                    return null;
                }

                // Generate unique filename
                $filename = 'drawing_' . time() . '_' . uniqid() . '.png';
                $directory = 'technician-reports/drawings';
                $fullPath = $directory . '/' . $filename;

                // Store the file
                \Storage::disk('public')->put($fullPath, $imageData);

                return $fullPath;
            }
        } catch (\Exception $e) {
            \Log::error('Failed to save drawing: ' . $e->getMessage());
        }

        return null;
    }
}
