<?php

namespace App\Filament\Technician\Resources\TechnicianReportResource\Pages;

use App\Filament\Technician\Resources\TechnicianReportResource;
use App\Models\TechnicianReport;
use App\Models\TechnicianReportChecklistItem;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateTechnicianReport extends CreateRecord
{
    protected static string $resource = TechnicianReportResource::class;

    public function getTitle(): string
    {
        return __('technician.resources.report.pages.create.title');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the technician_id to the current authenticated user
        $data['technician_id'] = auth()->id();

        // Handle translatable fields - form sends data as ['ar' => 'text'] or null
        // <PERSON><PERSON> Translatable expects this exact format
        $translatableFields = ['work_summary', 'findings', 'recommendations', 'issues_found', 'parts_used'];

        foreach ($translatableFields as $field) {
            if (isset($data[$field])) {
                // The form dehydrateStateUsing already formats data as ['ar' => $text] or null
                // We just need to ensure empty arrays become null
                if (is_array($data[$field])) {
                    // Check if the array has any non-empty values
                    $hasContent = false;
                    foreach ($data[$field] as $locale => $value) {
                        if (!empty($value) && trim($value) !== '') {
                            $hasContent = true;
                            break;
                        }
                    }

                    if (!$hasContent) {
                        $data[$field] = null;
                    }
                    // If it has content, leave the array as-is for Spatie Translatable
                } elseif (empty($data[$field]) || trim($data[$field]) === '') {
                    // Handle empty strings or null values
                    $data[$field] = null;
                }
            } else {
                // Field not set, ensure it's null
                $data[$field] = null;
            }
        }

        // Extract checklist items from the form data
        $checklistItems = $data['checklist_items'] ?? [];
        unset($data['checklist_items']);

        // Store checklist items for processing after creation
        $this->checklistItemsData = $checklistItems;

        // Handle attachments field - ensure it's properly formatted for JSON storage
        if (array_key_exists('attachments', $data)) {
            if (empty($data['attachments']) || $data['attachments'] === [] || $data['attachments'] === '' || $data['attachments'] === null) {
                $data['attachments'] = null;
            } else {
                // Ensure attachments is an array for JSON storage
                if (is_string($data['attachments'])) {
                    // If it's a string, try to decode it as JSON
                    $decoded = json_decode($data['attachments'], true);
                    $data['attachments'] = is_array($decoded) ? $decoded : [$data['attachments']];
                } elseif (!is_array($data['attachments'])) {
                    // If it's not an array, wrap it in an array
                    $data['attachments'] = [$data['attachments']];
                }

                // Filter out any empty values
                $data['attachments'] = array_filter($data['attachments'], function($value) {
                    return !empty($value) && $value !== '';
                });

                // If after filtering we have no items, set to null
                if (empty($data['attachments'])) {
                    $data['attachments'] = null;
                } else {
                    // Re-index the array to ensure sequential keys
                    $data['attachments'] = array_values($data['attachments']);
                }
            }
        } else {
            // If attachments key doesn't exist, set it to null
            $data['attachments'] = null;
        }

        // Handle drawing_data field - ensure it's properly formatted
        if (array_key_exists('drawing_data', $data)) {
            if (empty($data['drawing_data']) || $data['drawing_data'] === '' || $data['drawing_data'] === null) {
                $data['drawing_data'] = null;
            }
            // Leave arrays as-is for proper casting by the model
        }

        // Handle attachment_file_names field - ensure it's properly formatted
        if (array_key_exists('attachment_file_names', $data)) {
            if (empty($data['attachment_file_names']) || $data['attachment_file_names'] === '' || $data['attachment_file_names'] === null) {
                $data['attachment_file_names'] = null;
            }
            // Leave arrays as-is for proper casting by the model
        }

        // Keep drawing_data for potential future use, but drawings are now handled via file uploads
        // The drawing canvas will automatically add files to the attachments array via JavaScript

        return $data;
    }

    protected function afterCreate(): void
    {
        // Create default checklist items first if none exist
        $this->createDefaultChecklistItems();

        // Then update them with form data if provided
        if (!empty($this->checklistItemsData)) {
            $this->updateChecklistItemsFromFormData();
        }

        Notification::make()
            ->title(__('technician.resources.report.messages.created_successfully'))
            ->body(__('technician.resources.report.messages.created_with_checklist'))
            ->success()
            ->send();
    }

    /**
     * Create default checklist items for the newly created report.
     */
    protected function createDefaultChecklistItems(): void
    {
        $defaultItems = TechnicianReportChecklistItem::getDefaultChecklistItems();

        foreach ($defaultItems as $item) {
            TechnicianReportChecklistItem::create([
                'reportable_type' => TechnicianReport::class,
                'reportable_id' => $this->getRecord()->id,
                'category' => $item['category'],
                'item_key' => $item['item_key'],
                'description' => $item['description'],
                'is_required' => $item['is_required'],
                'sort_order' => $item['sort_order'],
                'status' => TechnicianReportChecklistItem::STATUS_NOT_CHECKED,
            ]);
        }
    }

    /**
     * Update checklist items from the form data.
     */
    protected function updateChecklistItemsFromFormData(): void
    {
        if (empty($this->checklistItemsData)) {
            return;
        }

        foreach ($this->checklistItemsData as $itemKey => $itemData) {
            if (!empty($itemData['item_key'])) {
                $checklistItem = $this->getRecord()->checklistItems()
                    ->where('item_key', $itemData['item_key'])
                    ->first();

                if ($checklistItem) {
                    // Determine status based on checkbox completion
                    $status = !empty($itemData['completed'])
                        ? TechnicianReportChecklistItem::STATUS_PASSED
                        : TechnicianReportChecklistItem::STATUS_NOT_CHECKED;

                    $checklistItem->update([
                        'status' => $status,
                    ]);
                }
            }
        }
    }

    /**
     * Store checklist items data for processing after creation
     */
    protected array $checklistItemsData = [];

    /**
     * Add a drawing file to the attachments
     */
    public function addDrawingToAttachments($filePath, $filename = null)
    {
        try {
            // Get current form data
            $data = $this->form->getState();

            // Get current attachments
            $currentAttachments = $data['attachments'] ?? [];

            // Ensure it's an array
            if (!is_array($currentAttachments)) {
                if (is_string($currentAttachments)) {
                    $decoded = json_decode($currentAttachments, true);
                    $currentAttachments = is_array($decoded) ? $decoded : [];
                } else {
                    $currentAttachments = [];
                }
            }

            // Add the new file path
            $currentAttachments[] = $filePath;

            // Update the form state
            $this->form->fill(['attachments' => $currentAttachments]);

            // Log the update
            \Log::info('Drawing added to attachments via Livewire (Create)', [
                'file_path' => $filePath,
                'filename' => $filename,
                'total_attachments' => count($currentAttachments)
            ]);

            // Emit event to update UI
            $this->dispatch('drawing-added-to-attachments', [
                'filename' => $filename,
                'path' => $filePath,
                'total' => count($currentAttachments)
            ]);

            return true;

        } catch (\Exception $e) {
            \Log::error('Failed to add drawing to attachments (Create)', [
                'error' => $e->getMessage(),
                'file_path' => $filePath,
                'filename' => $filename
            ]);

            return false;
        }
    }

    /**
     * Save drawing data as an image file
     */
    protected function saveDrawingAsFile(string $drawingData): ?string
    {
        try {
            // Extract base64 data from data URL
            if (strpos($drawingData, 'data:image/png;base64,') === 0) {
                $base64Data = substr($drawingData, strlen('data:image/png;base64,'));
                $imageData = base64_decode($base64Data);

                if ($imageData === false) {
                    return null;
                }

                // Generate unique filename
                $filename = 'drawing_' . time() . '_' . uniqid() . '.png';
                $directory = 'technician-reports/drawings';
                $fullPath = $directory . '/' . $filename;

                // Store the file
                \Storage::disk('public')->put($fullPath, $imageData);

                return $fullPath;
            }
        } catch (\Exception $e) {
            \Log::error('Failed to save drawing: ' . $e->getMessage());
        }

        return null;
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return null; // We handle notifications in afterCreate()
    }
}
