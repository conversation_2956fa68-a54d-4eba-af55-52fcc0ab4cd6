<?php

namespace App\Filament\Technician\Resources\MaintenanceRequestResource\RelationManagers;

use App\Models\Document;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class DocumentsRelationManager extends RelationManager
{
    protected static string $relationship = 'documents';
    protected static ?string $recordTitleAttribute = 'title';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('technician.resources.maintenance_request.relation_managers.documents.title');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('technician.resources.maintenance_request.relation_managers.documents.sections.document_details'))
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('technician.resources.maintenance_request.relation_managers.documents.fields.title'))
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('technician.resources.maintenance_request.relation_managers.documents.placeholders.title')),


                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('technician.resources.maintenance_request.relation_managers.documents.sections.file_upload'))
                    ->schema([
                        Forms\Components\FileUpload::make('file_path')
                            ->label(__('technician.resources.maintenance_request.relation_managers.documents.fields.file'))
                            ->required()
                            ->disk('public')
                            ->directory('documents/technician-uploads')
                            ->visibility('public')
                            ->acceptedFileTypes([
                                'image/*',
                                'application/pdf',
                            ])
                            ->maxSize(10240) // 10MB
                            ->helperText(__('technician.resources.maintenance_request.relation_managers.documents.helpers.file_upload')),

                        Forms\Components\Textarea::make('description')
                            ->label(__('technician.resources.maintenance_request.relation_managers.documents.fields.description'))
                            ->placeholder(__('technician.resources.maintenance_request.relation_managers.documents.placeholders.description'))
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.columns.title'))
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('file_type')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.columns.file_type'))
                    ->badge()
                    ->color(fn (string $state): string => match (strtolower($state)) {
                        'pdf' => 'danger',
                        'jpg', 'jpeg', 'png', 'gif', 'webp' => 'primary',
                        'doc', 'docx' => 'warning',
                        'xls', 'xlsx' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => strtoupper($state)),

                Tables\Columns\TextColumn::make('file_size')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.columns.file_size')),

                Tables\Columns\TextColumn::make('uploader.name')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.columns.uploaded_by'))
                    ->placeholder(__('technician.resources.maintenance_request.relation_managers.documents.placeholders.system')),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.columns.uploaded_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('file_type')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.filters.file_type'))
                    ->options([
                        'pdf' => 'PDF',
                        'jpg' => 'JPG',
                        'jpeg' => 'JPEG',
                        'png' => 'PNG',
                        'gif' => 'GIF',
                        'webp' => 'WEBP',
                    ]),

                Tables\Filters\Filter::make('my_uploads')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.filters.my_uploads'))
                    ->query(fn ($query) => $query->where('uploaded_by', auth()->id())),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.actions.upload'))
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['uploaded_by'] = auth()->id();

                        // Handle file_path processing
                        if (isset($data['file_path'])) {
                            $filePath = is_array($data['file_path']) ? $data['file_path'][0] : $data['file_path'];
                            $data['file_path'] = $filePath;

                            // Set file_type based on extension
                            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                            $data['file_type'] = strtolower($extension);

                            // Calculate file_size
                            $fullPath = storage_path('app/public/' . $filePath);
                            if (file_exists($fullPath)) {
                                $size = filesize($fullPath);
                                $data['file_size'] = $this->formatFileSize($size);
                            } else {
                                $data['file_size'] = '0 B';
                            }
                        }

                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('download')
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.actions.download'))
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn (Document $record): string => route('document.download', $record))
                    ->openUrlInNewTab(),

                Tables\Actions\ViewAction::make()
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.actions.view')),

                Tables\Actions\EditAction::make()
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.actions.edit'))
                    ->visible(fn (Document $record): bool => $record->uploaded_by === auth()->id()),

                Tables\Actions\DeleteAction::make()
                    ->label(__('technician.resources.maintenance_request.relation_managers.documents.actions.delete'))
                    ->visible(fn (Document $record): bool => $record->uploaded_by === auth()->id()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label(__('technician.resources.maintenance_request.relation_managers.documents.actions.delete_selected'))
                        ->visible(fn () => true), // Allow bulk delete for technician's own uploads
                ]),
            ])
            ->emptyStateHeading(__('technician.resources.maintenance_request.relation_managers.documents.empty.heading'))
            ->emptyStateDescription(__('technician.resources.maintenance_request.relation_managers.documents.empty.description'))
            ->emptyStateIcon('heroicon-o-document-plus');
    }

    public function canViewAny(): bool
    {
        // Only allow technicians to view documents for their assigned requests
        return $this->getOwnerRecord()->assigned_to === auth()->id();
    }

    public function canCreate(): bool
    {
        // Only allow technicians to create documents for their assigned requests
        return $this->getOwnerRecord()->assigned_to === auth()->id();
    }

    public function canEdit(Model $record): bool
    {
        // Only allow technicians to edit their own uploaded documents
        return $this->getOwnerRecord()->assigned_to === auth()->id() &&
               $record->uploaded_by === auth()->id();
    }

    public function canDelete(Model $record): bool
    {
        // Only allow technicians to delete their own uploaded documents
        return $this->getOwnerRecord()->assigned_to === auth()->id() &&
               $record->uploaded_by === auth()->id();
    }

    /**
     * Format file size in human-readable format.
     *
     * @param int $size
     * @return string
     */
    private function formatFileSize(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $power = floor(log($size, 1024));
        $power = min($power, count($units) - 1);

        return round($size / pow(1024, $power), 2) . ' ' . $units[$power];
    }
}
