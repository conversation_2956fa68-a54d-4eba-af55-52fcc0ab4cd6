<?php

namespace App\Filament\Technician\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Technician\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewMaintenanceRequest extends ViewRecord
{
    protected static string $resource = MaintenanceRequestResource::class;

    public function getTitle(): string
    {
        return __('technician.resources.maintenance_request.pages.view.title', [
            'number' => $this->record->request_number,
        ]);
    }

    public function getHeading(): string
    {
        return __('technician.resources.maintenance_request.pages.view.heading', [
            'number' => $this->record->request_number,
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label(__('technician.resources.maintenance_request.actions.edit'))
                ->visible(fn (MaintenanceRequest $record): bool => 
                    in_array($record->status, [MaintenanceRequest::STATUS_ASSIGNED, MaintenanceRequest::STATUS_IN_PROGRESS])
                ),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make(__('technician.resources.maintenance_request.sections.request_details'))
                    ->schema([
                        Infolists\Components\TextEntry::make('request_number')
                            ->label(__('technician.resources.maintenance_request.fields.request_number')),
                        
                        Infolists\Components\TextEntry::make('title')
                            ->label(__('technician.resources.maintenance_request.fields.title')),
                        
                        Infolists\Components\TextEntry::make('description')
                            ->label(__('technician.resources.maintenance_request.fields.description'))
                            ->columnSpanFull(),
                        
                        Infolists\Components\TextEntry::make('status')
                            ->label(__('technician.resources.maintenance_request.fields.status'))
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                MaintenanceRequest::STATUS_ASSIGNED => 'warning',
                                MaintenanceRequest::STATUS_IN_PROGRESS => 'primary',
                                MaintenanceRequest::STATUS_COMPLETED => 'success',
                                MaintenanceRequest::STATUS_CANCELLED => 'danger',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn (string $state): string =>
                                __("technician.resources.maintenance_request.status_options.{$state}")
                            ),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make(__('technician.resources.maintenance_request.sections.client_info'))
                    ->schema([
                        Infolists\Components\TextEntry::make('client.name')
                            ->label(__('technician.resources.maintenance_request.fields.client_name')),
                        
                        Infolists\Components\TextEntry::make('client.phone')
                            ->label(__('technician.resources.maintenance_request.fields.client_phone'))
                            ->url(fn ($state) => "tel:{$state}")
                            ->openUrlInNewTab(false),
                        
                        Infolists\Components\TextEntry::make('client.email')
                            ->label(__('technician.resources.maintenance_request.fields.client_email'))
                            ->url(fn ($state) => "mailto:{$state}")
                            ->openUrlInNewTab(false),
                        
                        Infolists\Components\TextEntry::make('client.address')
                            ->label(__('technician.resources.maintenance_request.fields.client_address'))
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make(__('technician.resources.maintenance_request.sections.contract_info'))
                    ->schema([
                        Infolists\Components\TextEntry::make('contractType.name')
                            ->label(__('technician.resources.maintenance_request.fields.contract_type')),
                        
                        Infolists\Components\TextEntry::make('price')
                            ->label(__('technician.resources.maintenance_request.fields.price'))
                            ->money('SAR'),
                        
                        Infolists\Components\TextEntry::make('visits_included')
                            ->label(__('technician.resources.maintenance_request.fields.visits_included')),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make(__('technician.resources.maintenance_request.sections.technician_notes'))
                    ->schema([
                        Infolists\Components\TextEntry::make('notes')
                            ->label(__('technician.resources.maintenance_request.fields.technician_notes'))
                            ->placeholder(__('technician.resources.maintenance_request.placeholders.no_notes'))
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => !empty($record->notes)),

                Infolists\Components\Section::make(__('technician.resources.maintenance_request.sections.timeline'))
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label(__('technician.resources.maintenance_request.fields.created_at'))
                            ->dateTime(),
                        
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label(__('technician.resources.maintenance_request.fields.updated_at'))
                            ->dateTime(),
                        
                        Infolists\Components\TextEntry::make('completed_at')
                            ->label(__('technician.resources.maintenance_request.fields.completed_at'))
                            ->dateTime()
                            ->visible(fn ($record) => !empty($record->completed_at)),
                    ])
                    ->columns(3),
            ]);
    }
}
