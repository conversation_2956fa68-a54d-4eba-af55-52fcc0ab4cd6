<?php

namespace App\Filament\Technician\Resources;

use App\Models\TechnicianReport;
use App\Models\TechnicianReportChecklistItem as ChecklistItem;
use App\Models\MaintenanceRequest;
use App\Forms\Components\DrawingCanvas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use App\Filament\Technician\Resources\TechnicianReportResource\Pages;

/**
 * TechnicianReportResource for Technician Panel
 *
 * This resource allows technicians to create and manage reports for their assigned maintenance requests.
 * Includes checklist items, file attachments, and translatable content.
 */
class TechnicianReportResource extends Resource
{
    protected static ?string $model = TechnicianReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?int $navigationSort = 2;

    public static function getNavigationGroup(): string
    {
        return __('technician.navigation.groups.reports');
    }

    public static function getModelLabel(): string
    {
        return __('technician.resources.report.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('technician.resources.report.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema(static::getFormSchema());
    }

    /**
     * Get the form schema with conditional components based on context
     */
    protected static function getFormSchema(): array
    {
        $context = request()->route()?->getActionMethod() ?? 'unknown';
        $isViewPage = $context === 'view' || str_contains(request()->url(), '/view');

        return [
                Forms\Components\Section::make(__('technician.resources.report.sections.basic_info'))
                    ->schema([
                        Forms\Components\Select::make('maintenance_request_id')
                            ->label(__('technician.resources.report.fields.maintenance_request'))
                            ->options(function () {
                                return MaintenanceRequest::where('assigned_to', auth()->id())
                                    ->whereIn('status', [MaintenanceRequest::STATUS_ASSIGNED, MaintenanceRequest::STATUS_IN_PROGRESS, MaintenanceRequest::STATUS_COMPLETED])
                                    ->with('client')
                                    ->get()
                                    ->pluck('request_number_with_client', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->disabled(fn ($context) => $context === 'edit')
                            ->default(fn () => request()->get('maintenance_request'))
                            ->helperText(__('technician.resources.report.helpers.multiple_reports_allowed')),

                        Forms\Components\TextInput::make('time_spent_minutes')
                            ->label(__('technician.resources.report.fields.time_spent'))
                            ->numeric()
                            ->suffix(__('technician.resources.report.fields.time_spent_suffix'))
                            ->helperText(__('technician.resources.report.helpers.time_spent')),

                        Forms\Components\Select::make('status')
                            ->label(__('technician.resources.report.fields.status'))
                            ->options(TechnicianReport::getStatusOptions())
                            ->default(TechnicianReport::STATUS_DRAFT)
                            ->required()
                            ->disabled(fn ($record) => $record && !$record->canBeEdited()),
                    ])
                    ->columns(3),

                Forms\Components\Section::make(__('technician.resources.report.sections.work_details'))
                    ->schema([
                        Forms\Components\Textarea::make('work_summary')
                            ->label(__('technician.resources.report.fields.work_summary'))
                            ->placeholder(__('technician.resources.report.placeholders.work_summary'))
                            ->rows(4)
                            ->required()
                            ->columnSpanFull()
                            ->formatStateUsing(fn ($state, $record) => is_array($state) ? ($state['ar'] ?? '') : $state)
                            ->dehydrateStateUsing(fn ($state) => $state ? ['ar' => $state] : null),

                        Forms\Components\Textarea::make('findings')
                            ->label(__('technician.resources.report.fields.findings'))
                            ->placeholder(__('technician.resources.report.placeholders.findings'))
                            ->rows(4)
                            ->columnSpanFull()
                            ->formatStateUsing(fn ($state, $record) => is_array($state) ? ($state['ar'] ?? '') : $state)
                            ->dehydrateStateUsing(fn ($state) => $state ? ['ar' => $state] : null),

                        Forms\Components\Textarea::make('recommendations')
                            ->label(__('technician.resources.report.fields.recommendations'))
                            ->placeholder(__('technician.resources.report.placeholders.recommendations'))
                            ->rows(3)
                            ->columnSpanFull()
                            ->formatStateUsing(fn ($state, $record) => is_array($state) ? ($state['ar'] ?? '') : $state)
                            ->dehydrateStateUsing(fn ($state) => $state ? ['ar' => $state] : null),

                        Forms\Components\Textarea::make('issues_found')
                            ->label(__('technician.resources.report.fields.issues_found'))
                            ->placeholder(__('technician.resources.report.placeholders.issues_found'))
                            ->rows(3)
                            ->columnSpanFull()
                            ->formatStateUsing(fn ($state, $record) => is_array($state) ? ($state['ar'] ?? '') : $state)
                            ->dehydrateStateUsing(fn ($state) => $state ? ['ar' => $state] : null),

                        Forms\Components\Textarea::make('parts_used')
                            ->label(__('technician.resources.report.fields.parts_used'))
                            ->placeholder(__('technician.resources.report.placeholders.parts_used'))
                            ->rows(3)
                            ->columnSpanFull()
                            ->formatStateUsing(fn ($state, $record) => is_array($state) ? ($state['ar'] ?? '') : $state)
                            ->dehydrateStateUsing(fn ($state) => $state ? ['ar' => $state] : null),
                    ]),

                Forms\Components\Section::make(__('technician.resources.report.sections.checklist'))
                    ->schema(function (?TechnicianReport $record, $livewire = null, $get = null) {
                        // Try multiple ways to get the record
                        if (!$record && $livewire) {
                            if (method_exists($livewire, 'getRecord')) {
                                $record = $livewire->getRecord();
                            } elseif (property_exists($livewire, 'record')) {
                                $record = $livewire->record;
                            }
                        }

                        // Debug information (remove in production)
                        \Log::info('Checklist Schema Debug', [
                            'record_id' => $record?->id,
                            'record_exists' => $record?->exists,
                            'livewire_class' => $livewire ? get_class($livewire) : null,
                            'url' => request()->url(),
                        ]);

                        return static::buildChecklistSchema($record);
                    }),

                static::getAttachmentsSection($isViewPage),
            ];
    }

    /**
     * Get the attachments section with conditional drawing canvas
     */
    protected static function getAttachmentsSection(bool $isViewPage = false): Forms\Components\Section
    {
        $tabs = [
            Forms\Components\Tabs\Tab::make('file_uploads')
                ->label(__('technician.resources.report.tabs.file_uploads'))
                ->icon('heroicon-o-document')
                ->extraAttributes([
                    'id' => 'file-uploads-tab',
                ])
                ->schema([
                    Forms\Components\FileUpload::make('attachments')
                        ->label(__('technician.resources.report.fields.attachments'))
                        ->multiple()
                        ->acceptedFileTypes([
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp',
                            'application/pdf',
                            'application/msword',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'text/plain',
                        ])
                        ->maxSize(10240) // 10MB per file
                        ->maxFiles(25) // Increased to accommodate more drawings and files
                        ->helperText(__('technician.resources.report.helpers.attachments_enhanced'))
                        ->disk('public')
                        ->directory('technician-reports')
                        ->visibility('public') // Public for better access
                        ->downloadable()
                        ->openable()
                        ->previewable()
                        ->columnSpanFull()
                        ->nullable()
                        ->default(null)
                        ->reorderable()
                        ->appendFiles() // Keep existing files when adding new ones
                        ->storeFileNamesIn('attachment_file_names') // Store original filenames
                        ->dehydrateStateUsing(function ($state) {
                            // Enhanced state processing for better file handling
                            if (empty($state) || $state === [] || $state === '' || $state === null) {
                                return null;
                            }

                            if (!is_array($state)) {
                                return [$state];
                            }

                            // Filter out empty values and validate file paths
                            $filtered = array_filter($state, function($value) {
                                if (empty($value) || $value === '') {
                                    return false;
                                }

                                // Accept valid file paths
                                if (is_string($value)) {
                                    return str_starts_with($value, 'technician-reports/') ||
                                           str_starts_with($value, 'http') ||
                                           str_starts_with($value, '/storage/') ||
                                           str_starts_with($value, 'storage/');
                                }

                                return true;
                            });

                            return empty($filtered) ? null : array_values($filtered);
                        })
                        ->formatStateUsing(function ($state, $record) {
                            // Enhanced state formatting for better display in edit mode
                            if (empty($state)) {
                                return null;
                            }

                            if (is_string($state)) {
                                $decoded = json_decode($state, true);
                                $state = is_array($decoded) ? $decoded : [$state];
                            }

                            if (!is_array($state)) {
                                return [$state];
                            }

                            // Log for debugging
                            if ($record) {
                                \Log::info('Formatting attachments state for display', [
                                    'report_id' => $record->id,
                                    'attachments_count' => count($state),
                                    'attachments' => $state
                                ]);
                            }

                            return $state;
                        })
                        ->loadStateFromRelationshipsUsing(function ($component, $state, $record) {
                            // Enhanced loading for existing attachments
                            if (!$record || !$record->exists) {
                                return $state;
                            }

                            $attachments = $record->attachments;

                            if (is_string($attachments)) {
                                $decoded = json_decode($attachments, true);
                                $attachments = is_array($decoded) ? $decoded : [];
                            } elseif (!is_array($attachments)) {
                                $attachments = [];
                            }

                            \Log::info('Loading existing attachments', [
                                'report_id' => $record->id,
                                'attachments_count' => count($attachments),
                                'attachments' => $attachments
                            ]);

                            return $attachments;
                        })
                        ->extraAttributes([
                            'id' => 'attachments-upload',
                            'x-ref' => 'attachmentsUpload',
                            'class' => 'file-upload-enhanced enhanced-attachments',
                            'data-file-types' => json_encode([
                                'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                                'documents' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
                                'drawings' => ['رسم-توضيحي', 'drawing-']
                            ]),
                        ])
                        ->panelLayout('grid')
                        ->imagePreviewHeight('180') // Increased for better preview
                        ->imageCropAspectRatio(null)
                        ->imageResizeMode('contain')
                        ->imageResizeTargetWidth('400')
                        ->imageResizeTargetHeight('400'),
                ]),
        ];

        // Only add drawing canvas tab if not on view page
        if (!$isViewPage) {
            $tabs[] = Forms\Components\Tabs\Tab::make('drawing_canvas')
                ->label(__('technician.resources.report.tabs.drawing_canvas'))
                ->icon('heroicon-o-pencil')
                ->schema([
                    Forms\Components\Placeholder::make('drawing_instructions')
                        ->content(__('technician.resources.report.drawing.instructions_unified'))
                        ->columnSpanFull(),

                    DrawingCanvas::make('drawing_data')
                        ->label(__('technician.resources.report.fields.drawing_area'))
                        ->width(800)
                        ->height(400)
                        ->backgroundColor('#ffffff')
                        ->strokeColor('#000000')
                        ->strokeWidth(2)
                        ->enableTouch()
                        ->enableMouse()
                        ->columnSpanFull(),
                ]);
        }

        return Forms\Components\Section::make(__('technician.resources.report.sections.attachments'))
            ->schema([
                Forms\Components\Tabs::make('attachment_tabs')
                    ->extraAttributes([
                        'x-data' => $isViewPage ? '' : 'drawingIntegration()',
                        'class' => 'tab-switching',
                        'id' => 'attachment-tabs-container',
                        'x-init' => $isViewPage ? '' : 'init()',
                    ])
                    ->tabs($tabs)
                    ->columnSpanFull(),
            ]);
    }

    /**
     * Build the complete checklist schema based on the record
     */
    protected static function buildChecklistSchema(?TechnicianReport $record): array
    {
        $categories = [
            ChecklistItem::CATEGORY_SAFETY,
            ChecklistItem::CATEGORY_ELECTRICAL,
            ChecklistItem::CATEGORY_MECHANICAL,
            ChecklistItem::CATEGORY_CLEANING,
            ChecklistItem::CATEGORY_TESTING,
            ChecklistItem::CATEGORY_DOCUMENTATION,
            ChecklistItem::CATEGORY_GENERAL,
        ];

        $schema = [];

        foreach ($categories as $category) {
            $categoryItems = static::getChecklistItemsForCategory($category, $record?->id);

            if ($categoryItems->isNotEmpty()) {
                $schema[] = Forms\Components\Fieldset::make(__("technician.resources.report.checklist.categories.{$category}"))
                    ->schema(static::getChecklistFieldsForCategory($category, $categoryItems, $record));
            }
        }

        // If no schema items and we have a record, try to create default items
        if (empty($schema) && $record && $record->exists) {
            // For existing reports without checklist items, create them from defaults
            static::createDefaultChecklistItemsForReport($record);

            // Reload the schema with the newly created items
            foreach ($categories as $category) {
                $categoryItems = static::getChecklistItemsForCategory($category, $record->id);

                if ($categoryItems->isNotEmpty()) {
                    $schema[] = Forms\Components\Fieldset::make(__("technician.resources.report.checklist.categories.{$category}"))
                        ->schema(static::getChecklistFieldsForCategory($category, $categoryItems, $record));
                }
            }
        }

        // If still no schema items, add a placeholder
        if (empty($schema)) {
            $schema[] = Forms\Components\Placeholder::make('checklist_placeholder')
                ->content($record ? __('technician.resources.report.checklist.no_items') : __('technician.resources.report.checklist.loading'))
                ->columnSpanFull();
        }

        return $schema;
    }

    /**
     * Get checklist fields for a specific category with items
     */
    protected static function getChecklistFieldsForCategory(string $category, \Illuminate\Support\Collection $items, ?TechnicianReport $record = null): array
    {
        $fields = [];

        foreach ($items as $item) {
            $itemKey = $item->item_key;
            $isRequired = $item->is_required;
            $isCompleted = $item->status !== ChecklistItem::STATUS_NOT_CHECKED;
            $description = $item->getTranslation('description', 'ar');

            // Determine if this is a view context
            $isViewContext = request()->route()?->getActionMethod() === 'view' ||
                           str_contains(request()->url(), '/view') ||
                           (isset($record) && !$record->canBeEdited());

            $fields[] = Forms\Components\Grid::make(2)
                ->schema([
                    // Checkbox for completion
                    Forms\Components\Checkbox::make("checklist_items.{$itemKey}.completed")
                        ->label($description)
                        ->live(!$isViewContext)
                        ->default($isCompleted)
                        ->disabled($isViewContext)
                        ->afterStateUpdated($isViewContext ? null : function ($state, Forms\Set $set) use ($itemKey) {
                            if ($state) {
                                $set("checklist_items.{$itemKey}.status", ChecklistItem::STATUS_PASSED);
                            } else {
                                $set("checklist_items.{$itemKey}.status", ChecklistItem::STATUS_NOT_CHECKED);
                            }
                        })
                        ->columnSpan(2),
                ])
                ->columnSpanFull();

            // Notes field removed - keeping only checkbox functionality

            // Hidden fields to store item metadata and status
            $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.status")
                ->default($item->status);

            $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.item_key")
                ->default($itemKey);

            $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.category")
                ->default($category);

            $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.description")
                ->default(json_encode($item->getTranslations('description')));

            $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.is_required")
                ->default($isRequired);

            $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.sort_order")
                ->default($item->sort_order);

            if ($item->exists) {
                $fields[] = Forms\Components\Hidden::make("checklist_items.{$itemKey}.id")
                    ->default($item->id);
            }
        }

        return $fields;
    }

    /**
     * Get checklist items for a specific category from database
     */
    protected static function getChecklistItemsForCategory(string $category, ?int $reportId = null): \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
    {
        if ($reportId) {
            // For existing reports, load actual checklist items using polymorphic relationship
            $items = ChecklistItem::where('reportable_type', TechnicianReport::class)
                ->where('reportable_id', $reportId)
                ->where('category', $category)
                ->ordered()
                ->get();

            // Debug information (remove in production)
            \Log::info('Checklist Items Query', [
                'category' => $category,
                'report_id' => $reportId,
                'items_count' => $items->count(),
                'items' => $items->pluck('item_key')->toArray(),
            ]);

            return $items;
        } else {
            // For new reports, create default items if they don't exist
            return static::getOrCreateDefaultChecklistItems($category);
        }
    }

    /**
     * Get or create default checklist items for a category
     */
    protected static function getOrCreateDefaultChecklistItems(string $category): \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
    {
        $defaultItems = ChecklistItem::getDefaultChecklistItems();
        $categoryItems = array_filter($defaultItems, fn ($item) => $item['category'] === $category);

        $items = collect();

        foreach ($categoryItems as $item) {
            // Create a temporary model instance for form rendering
            $checklistItem = new ChecklistItem([
                'category' => $item['category'],
                'item_key' => $item['item_key'],
                'description' => $item['description'],
                'status' => ChecklistItem::STATUS_NOT_CHECKED,
                'is_required' => $item['is_required'],
                'sort_order' => $item['sort_order'],
            ]);

            $items->push($checklistItem);
        }

        return $items;
    }

    /**
     * Create default checklist items for an existing report
     */
    protected static function createDefaultChecklistItemsForReport(TechnicianReport $record): void
    {
        $defaultItems = ChecklistItem::getDefaultChecklistItems();

        foreach ($defaultItems as $item) {
            ChecklistItem::create([
                'reportable_type' => TechnicianReport::class,
                'reportable_id' => $record->id,
                'category' => $item['category'],
                'item_key' => $item['item_key'],
                'description' => $item['description'],
                'status' => ChecklistItem::STATUS_NOT_CHECKED,
                'is_required' => $item['is_required'],
                'sort_order' => $item['sort_order'],
            ]);
        }
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('maintenanceRequest.request_number')
                    ->label(__('technician.resources.report.columns.request_number'))
                    ->searchable()
                    ->sortable()
                    ->url(fn (TechnicianReport $record): string =>
                        route('filament.technician.resources.maintenance-requests.view', $record->maintenance_request_id)
                    ),

                Tables\Columns\TextColumn::make('maintenanceRequest.client.name')
                    ->label(__('technician.resources.report.columns.client'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('id')
                    ->label(__('technician.resources.report.columns.report_number'))
                    ->formatStateUsing(fn ($state) => "#{$state}")
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('technician.resources.report.columns.status'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        TechnicianReport::STATUS_DRAFT => 'gray',
                        TechnicianReport::STATUS_SUBMITTED => 'warning',
                        TechnicianReport::STATUS_APPROVED => 'success',
                        TechnicianReport::STATUS_REJECTED => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string =>
                        __("technician.resources.report.status_options.{$state}")
                    ),

                Tables\Columns\TextColumn::make('time_spent_minutes')
                    ->label(__('technician.resources.report.columns.time_spent'))
                    ->formatStateUsing(fn ($state) => $state ? "{$state} " . __('technician.resources.report.fields.time_spent_suffix') : '-'),

                Tables\Columns\TextColumn::make('attachments')
                    ->label(__('technician.resources.report.columns.attachments'))
                    ->formatStateUsing(function ($state) {
                        // Handle different data types safely
                        $attachments = null;

                        if (is_string($state)) {
                            // If it's a JSON string, decode it
                            $decoded = json_decode($state, true);
                            $attachments = is_array($decoded) ? $decoded : null;
                        } elseif (is_array($state)) {
                            // If it's already an array, use it directly
                            $attachments = $state;
                        }

                        // Safe count with type checking
                        $count = $attachments && is_array($attachments) ? count($attachments) : 0;

                        if ($count === 0) {
                            return '0';
                        }

                        // Count different file types
                        $imageCount = 0;
                        $documentCount = 0;

                        if ($attachments && is_array($attachments)) {
                            foreach ($attachments as $file) {
                                if (is_string($file)) {
                                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                                        $imageCount++;
                                    } else {
                                        $documentCount++;
                                    }
                                }
                            }
                        }

                        $parts = [];
                        if ($imageCount > 0) {
                            $parts[] = $imageCount . ' ' . __('technician.resources.report.columns.images');
                        }
                        if ($documentCount > 0) {
                            $parts[] = $documentCount . ' ' . __('technician.resources.report.columns.documents');
                        }

                        return $count . ' ' . __('technician.resources.report.columns.files') .
                               ($parts ? ' (' . implode(', ', $parts) . ')' : '');
                    })
                    ->badge()
                    ->color(function ($state) {
                        // Safe type checking for color determination
                        $attachments = null;

                        if (is_string($state)) {
                            $decoded = json_decode($state, true);
                            $attachments = is_array($decoded) ? $decoded : null;
                        } elseif (is_array($state)) {
                            $attachments = $state;
                        }

                        $count = $attachments && is_array($attachments) ? count($attachments) : 0;
                        return $count > 0 ? 'success' : 'gray';
                    }),

                Tables\Columns\TextColumn::make('submitted_at')
                    ->label(__('technician.resources.report.columns.submitted_at'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('technician.resources.report.columns.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('technician.resources.report.filters.status'))
                    ->options(TechnicianReport::getStatusOptions()),

                Tables\Filters\SelectFilter::make('maintenance_request_id')
                    ->label(__('technician.resources.report.filters.maintenance_request'))
                    ->options(function () {
                        return MaintenanceRequest::where('assigned_to', auth()->id())
                            ->with('client')
                            ->get()
                            ->pluck('request_number_with_client', 'id');
                    })
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('submitted_today')
                    ->label(__('technician.resources.report.filters.submitted_today'))
                    ->query(fn (Builder $query): Builder => $query->whereDate('submitted_at', today())),

                Tables\Filters\Filter::make('with_attachments')
                    ->label(__('technician.resources.report.filters.with_attachments'))
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('attachments')->where('attachments', '!=', '[]')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('technician.resources.report.actions.view')),
                Tables\Actions\EditAction::make()
                    ->label(__('technician.resources.report.actions.edit'))
                    ->visible(fn (TechnicianReport $record): bool => $record->canBeEdited()),
                Tables\Actions\Action::make('submit')
                    ->label(__('technician.resources.report.actions.submit'))
                    ->icon('heroicon-o-paper-airplane')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading(__('technician.resources.report.actions.submit_modal_heading'))
                    ->modalDescription(__('technician.resources.report.actions.submit_modal_description'))
                    ->action(function (TechnicianReport $record) {
                        if ($record->submit()) {
                            Notification::make()
                                ->title(__('technician.resources.report.messages.submitted_successfully'))
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title(__('technician.resources.report.messages.submit_failed'))
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (TechnicianReport $record): bool => $record->canBeSubmitted()),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('technician_id', auth()->id())
            ->with([
                'maintenanceRequest.client:id,name',
                'checklistItems',
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTechnicianReports::route('/'),
            'create' => Pages\CreateTechnicianReport::route('/create'),
            'view' => Pages\ViewTechnicianReport::route('/{record}'),
            'edit' => Pages\EditTechnicianReport::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return true;
    }

    public static function canEdit(Model $record): bool
    {
        return $record->canBeEdited();
    }

    public static function canDelete(Model $record): bool
    {
        return $record->status === TechnicianReport::STATUS_DRAFT;
    }
}
