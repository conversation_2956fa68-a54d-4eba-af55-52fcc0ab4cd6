<?php

namespace App\Filament\Client\Pages\Auth;

use Closure;
use Exception;
use App\Models\UserOTP;
use Filament\Forms\Form;
use Illuminate\Support\Str;
use Filament\Actions\Action;
use App\Models\Client as User;
use Filament\Facades\Filament;
use Filament\Pages\SimplePage;
use Illuminate\Validation\Rule;
use Filament\Actions\ActionGroup;
use App\Notifications\OTPNotification;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Notifications\OTPMailNotification;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Validation\ValidationException;
use DanH<PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use <PERSON><PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;

/**
 * @property Form $form
 */
class CustomLogin extends SimplePage
{
    use InteractsWithFormActions;
    use WithRateLimiting;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.auth.custom-login';

    /**
     * @var array<string, mixed> | null
     */
    public ?array $data = [];

    public $showMethods = false;
    public $selectedMethod = null;
    public $allowedMethods = [
        'whatsapp' => true,
        'sms' => true,
        'email' => false,
    ];

    // QR Code related properties
    public $isDev = false;
    public $showQRCode = false;
    public $qrcodeText = null;

    public function mount(): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }

        $this->isDev = app()->environment(['local']) || app()->environment(['staging']);

        if (app()->environment(['local'])) {
            $this->data = array_merge($this->data, [
                'phone' => env('ADMIN_SEEDER_PHONE', '966550000000'),
                'remember' => true,
                'otp' => '1436',
            ]);
        }

        $this->form->fill($this->data);
    }

    public function getTitle(): string|Htmlable
    {
        return __('filament-panels::pages/auth/login.title');
    }

    public function getHeading(): string|Htmlable
    {
        return __('filament-panels::pages/auth/login.heading');
    }

    public function verifyQrToken($token): ?LoginResponse
    {
        try {
            // Find user associated with the token
            $tokenData = decrypt($token);
            if (!$tokenData || $tokenData['uuid'] !== $this->qrcodeText || empty($tokenData['user_id']))
                throw  new Exception('خطأ فى البيانات المقدمة !');

            /** @var User $user */
            $user = User::query()->findOrFail($tokenData['user_id']);

            if ($user) {
                return $this->loginUser($user);
            }

        } catch (Exception $e) {
            Notification::make()
                ->body('خطأ في التحقق من الرمز')
                ->danger()
                ->send();
        }

        return null;
    }

    public function form(Form $form): Form
    {
        return $form;
    }

    public function authenticate(): ?LoginResponse
    {
        if (!$this->showMethods && !$this->showQRCode) {
            if (empty($this->selectedMethod))
                $this->showAvailableMethods();
            elseif (isset($this->data['user_id'])) {
                $user = User::find(decrypt($this->data['user_id']));
                $data = $this->form->getState();
                $otp = @$data['otp'];
                return $this->handleOTPAuthentication($user, $otp);
            }
        }
        return null;
    }

    public function showAvailableMethods(): void
    {
        $data = $this->form->getState();

        $phone = phone_format($data['phone'], ['SA', 'AUTO'], false);
        $user = User::query()
            ->where(compact('phone'))
            ->first();

        if ($user) {
            $this->data['user_id'] = encrypt($user->id);

            $this->allowedMethods = [
                'sms' => !empty($user->phone) && is_sa_phone($user->phone),
                'email' => !empty($user->email),
                'whatsapp' => !empty($user->phone) /*&& is_waha_working()*/,
            ];

            $methods = $this->getActiveMethods();

            if (count($methods) === 1) {
                $this->selectMethod(array_keys($methods)[0]);
            } else {
                $this->showMethods = true;
            }
        }
    }

    public function selectMethod($method): void
    {
        if ($this->selectedMethod !== null) {
            return;
        }

        if (in_array($method, array_keys($this->getActiveMethods()))) {
            $user = User::find(decrypt($this->data['user_id']));

            if (in_array($method, ['email', 'sms', 'whatsapp'])) {
                $otp = !$this->isDev ? rand(1009, 9999) : 1436;

                $userOTP = new UserOTP([
                    'method' => $method,
                    'code' => $otp,
                    'expired_at' => now()->addMinutes(config('otp.timeout') ?? 3),
                ]);

                $user->otp()->save($userOTP);
                $this->data['otp_uuid'] = $userOTP->uuid;

                if (!$this->isDev)
                    $this->sendOTP($user, $method, $otp);
            }

            $this->showMethods = false;
            $this->selectedMethod = $method;
        } else {
            $this->reset();
        }
    }

    public function startQRCode(): void
    {
        $this->showQRCode = true;
        $this->qrcodeText = (string)Str::uuid();
        $this->loginWithQr($this->qrcodeText);
    }

    public function loginWithQr($uuid): void
    {
        // Using Laravel Reverb for broadcasting
        if (class_exists('App\Events\LoginWithQRCodeEvent')) {
            // Set up listener for QR code scan events
            $this->dispatch('reverb:subscribe', [
                'channel' => "Login.{$uuid}",
                'event' => 'LoginWithQRCodeEvent',
            ]);
        }
    }

    public function cancelLoginWithQr(): void
    {
        // Stop listening for events if using broadcast
        if (class_exists('App\Events\LoginWithQRCodeEvent')) {
            // Using Reverb for broadcasting - unsubscribe from channel
            $this->dispatch('reverb:leave', ['channel' => "Login.{$this->qrcodeText}"]);
        }

        $this->showQRCode = false;
        $this->qrcodeText = null;
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getEmailFormComponent(),
                        $this->getOtpFormComponent(),
                        $this->getRememberFormComponent(),
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('phone')
            ->label('رقم الجوال')
            ->placeholder('05xxx')
            ->tel()
            ->required()
            ->autofocus()
            ->autocomplete()
            ->extraInputAttributes(['tabindex' => 1])
            ->default($this->isDev ? env('LOGIN_PHONE', '') : '')
            ->rules([
                Rule::phone()->country(['AUTO'])->mobile(),
                fn(): Closure => function (string $attribute, $value, Closure $fail) {
                    $phone = phone_format($value, ['SA', 'AUTO'], false);
                    $user = User::query()
                        ->where(compact('phone'))
                        ->first();
                    if (is_null($user))
                        $fail('لا يوجد مستخدم مسجل بهذا الرقم !');
                },
            ])
            ->hidden(fn() => $this->showQRCode)
            ->disabled(fn() => $this->showMethods || !empty($this->selectedMethod));
    }

    protected function getOtpFormComponent(): Component
    {
        return TextInput::make('otp')
            ->label(__('auth.otp'))
            ->visible(fn() => !(empty($this->selectedMethod) || $this->showQRCode))
            ->afterStateUpdated(function (string $state) {
                if (strlen($state) === 4)
                    $this->authenticate()?->toResponse($this->form);
            })
            ->rules([
                'required',
                'numeric',
                'regex:/^[0-9]+$/',
                'digits:4',
            ]);
    }

    protected function getActiveMethods(): array
    {
        return array_filter($this->allowedMethods, fn($val) => $val === true);
    }

    protected function loginUser(User $user): LoginResponse
    {
        Filament::auth()->login($user, $this->data['remember'] ?? false);

        session()->regenerate();

        return app(LoginResponse::class);
    }

    protected function handleOTPAuthentication(User $user, $otp): ?LoginResponse
    {
        if (!app()->environment(['production']) && in_array($this->selectedMethod, ['sms', 'whatsapp', 'email'])) {
            if ($otp === '1436') {
                return $this->loginUser($user);
            } else {
                throw ValidationException::withMessages([
                    'data.otp' => 'رمز الدخول غير صحيح !',
                ]);
            }
        }

        $userOTP = $user->otp()
            ->where('expired_at', '>', now())
            ->find($this->data['otp_uuid']);

        if (!is_null($userOTP) && $otp == $userOTP->code) {
            $userOTP->delete();
            return $this->loginUser($user);
        } else {
            throw ValidationException::withMessages([
                'data.otp' => 'رمز الدخول غير صحيح !',
            ]);
        }
    }

    protected function getRememberFormComponent(): Component
    {
        return Checkbox::make('remember')
            ->visible(fn() => !empty($this->selectedMethod) && !$this->showQRCode)
            ->label(__('filament-panels::pages/auth/login.form.remember.label'));
    }

    protected function getRateLimitedNotification(TooManyRequestsException $exception): ?Notification
    {
        return Notification::make()
            ->title(__('filament-panels::pages/auth/login.notifications.throttled.title', [
                'seconds' => $exception->secondsUntilAvailable,
                'minutes' => $exception->minutesUntilAvailable,
            ]))
            ->body(array_key_exists('body', __('filament-panels::pages/auth/login.notifications.throttled') ?: []) ? __('filament-panels::pages/auth/login.notifications.throttled.body', [
                'seconds' => $exception->secondsUntilAvailable,
                'minutes' => $exception->minutesUntilAvailable,
            ]) : null)
            ->danger();
    }

    /**
     * @return array<Action | ActionGroup>
     */
    protected function getFormActions(): array
    {
        return [
            $this->getAuthenticateFormAction(),
            ...$this->getActiveMethodsActions(),
            //$this->getQRCodeLoginAction(),
            $this->getCancelQRCodeAction(),
            $this->getRegisterAction(),
        ];
    }

    protected function getAuthenticateFormAction(): Action
    {
        return Action::make('authenticate')
            ->visible(fn() => !$this->showMethods && !$this->showQRCode)
            ->label(__('filament-panels::pages/auth/login.form.actions.authenticate.label'))
            ->submit('authenticate');
    }

    // QR Code related methods

    protected function getActiveMethodsActions(): array
    {
        return [
            Action::make('sms')
                ->label('رسالة نصية')
                ->visible(fn() => $this->showMethods && $this->allowedMethods['sms'] && !$this->showQRCode)
                ->action(function () {
                    $this->selectMethod('sms');
                }),
            Action::make('whatsapp')
                ->label('واتساب')
                ->visible(fn() => $this->showMethods && $this->allowedMethods['whatsapp'] && !$this->showQRCode)
                ->action(function () {
                    $this->selectMethod('whatsapp');
                }),
            Action::make('email')
                ->label('البريد الإلكتروني')
                ->visible(fn() => $this->showMethods && $this->allowedMethods['email'] && !$this->showQRCode)
                ->action(function () {
                    $this->selectMethod('email');
                }),
        ];
    }

    protected function sendOTP(User $user, string $method, string $otp): void
    {
        try {
            match ($method) {
                'email' => $user->notify(new OTPMailNotification($otp)),
                'sms' => $user->notify(new OTPNotification($otp, domain: get_domain(request()->headers->get('referer')))),
                'whatsapp' => $user->notify(new OTPNotification($otp, domain: get_domain(request()->headers->get('referer')), via: 'waha')),
                default => null
            };
        } catch (Exception $exception) {
            if ($method === 'sms' && Str::lower($exception->getMessage()) === Str::lower('Your balance is 0')) {
                $this->addError('phone', '4203 راجع الإدارة .');
            } else {
                $this->addError('phone', 'خطأ غير متوقع !');
            }
        }
    }

    protected function getQRCodeLoginAction(): Action
    {
        return Action::make('qrcode')
            ->label('دخول بواسطة رمز QR')
            ->icon('heroicon-o-qr-code')
            ->visible(fn() => !$this->showQRCode && !$this->showMethods && empty($this->selectedMethod))
            ->action(function () {
                $this->startQRCode();
            });
    }

    protected function getCancelQRCodeAction(): Action
    {
        return Action::make('cancelQR')
            ->label('إلغاء')
            ->visible(fn() => $this->showQRCode)
            ->action(function () {
                $this->cancelLoginWithQr();
            });
    }

    protected function getRegisterAction(): Action
    {
        return Action::make('register')
            ->label(__('client/auth.labels.dont_have_account') . ' ' . __('client/auth.labels.create_account'))
            ->url(filament()->getRegistrationUrl())
            ->color('gray')
            ->visible(fn() => !$this->showMethods && !$this->showQRCode && empty($this->selectedMethod));
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }
}
