<?php

namespace App\Filament\Client\Pages\Auth;

use Closure;
use Exception;
use App\Models\UserOTP;
use Filament\Forms\Form;
use Illuminate\Support\Str;
use Filament\Actions\Action;
use App\Models\Client as User;
use Filament\Facades\Filament;
use Filament\Pages\SimplePage;
use Illuminate\Validation\Rule;
use Filament\Actions\ActionGroup;
use App\Notifications\OTPNotification;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Notifications\OTPMailNotification;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Validation\ValidationException;
use DanHarrin\LivewireRateLimiting\WithRateLimiting;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use DanHarrin\LivewireRateLimiting\Exceptions\TooManyRequestsException;

/**
 * @property Form $form
 */
class Register extends SimplePage
{
    use InteractsWithFormActions;
    use WithRateLimiting;

    protected static ?string $navigationIcon = 'heroicon-o-user-plus';

    protected static string $view = 'filament.pages.auth.register';

    /**
     * @var array<string, mixed> | null
     */
    public ?array $data = [];

    public $currentStep = 'registration';
    public $showMethods = false;
    public $selectedMethod = null;
    public $allowedMethods = [
        'whatsapp' => true,
        'sms' => true,
        'email' => false,
    ];

    public $isDev = false;

    public function mount(): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }

        $this->isDev = app()->environment(['local']) || app()->environment(['staging']);

        if (app()->environment(['local'])) {
            $this->data = array_merge($this->data, [
                'phone' => env('ADMIN_SEEDER_PHONE', '966550000000'),
                'otp' => '1436',
            ]);
        }

        $this->form->fill($this->data);
    }

    public function getTitle(): string|Htmlable
    {
        return __('client/auth.labels.register_title');
    }

    public function getHeading(): string|Htmlable
    {
        return __('client/auth.labels.register_title');
    }

    public function authenticate(): ?LoginResponse
    {
        try {
            $this->rateLimit(5);
        } catch (TooManyRequestsException $exception) {
            Notification::make()
                ->title(__('filament-panels::pages/auth/login.notifications.throttled.title', [
                    'seconds' => $exception->secondsUntilAvailable,
                    'minutes' => ceil($exception->secondsUntilAvailable / 60),
                ]))
                ->body(array_key_exists('body', __('filament-panels::pages/auth/login.notifications.throttled') ?: []) ? __('filament-panels::pages/auth/login.notifications.throttled.body', [
                    'seconds' => $exception->secondsUntilAvailable,
                    'minutes' => ceil($exception->secondsUntilAvailable / 60),
                ]) : null)
                ->danger()
                ->send();

            return null;
        }

        $data = $this->form->getState();

        if ($this->currentStep === 'registration') {
            return $this->handleRegistrationStep($data);
        } elseif ($this->currentStep === 'otp_verification') {
            return $this->handleOTPVerification($data);
        }

        return null;
    }

    protected function handleRegistrationStep(array $data): ?LoginResponse
    {
        // Check if user already exists
        $phone = phone_format($data['phone'], ['SA', 'AUTO'], false);
        $existingUser = User::where('phone', $phone)->first();
        
        if ($existingUser) {
            throw ValidationException::withMessages([
                'data.phone' => __('client/auth.errors.user_already_exists'),
            ]);
        }

        // Check for unique email if provided
        if (!empty($data['email'])) {
            $existingEmail = User::where('email', $data['email'])->first();
            if ($existingEmail) {
                throw ValidationException::withMessages([
                    'data.email' => __('client/auth.errors.email_exists'),
                ]);
            }
        }

        // Check for unique national_id if provided
        if (!empty($data['national_id'])) {
            $existingNationalId = User::where('national_id', $data['national_id'])->first();
            if ($existingNationalId) {
                throw ValidationException::withMessages([
                    'data.national_id' => __('client/auth.errors.national_id_exists'),
                ]);
            }
        }

        // Store registration data temporarily
        $this->data = array_merge($this->data, $data);
        
        // Move to OTP verification step
        $this->currentStep = 'otp_verification';
        $this->showMethods = true;
        
        return null;
    }

    public function selectMethod($method): void
    {
        if ($this->selectedMethod !== null) {
            return;
        }

        if (in_array($method, array_keys($this->getActiveMethods()))) {
            // Create temporary user for OTP sending
            $tempUser = new User([
                'phone' => phone_format($this->data['phone'], ['SA', 'AUTO'], false),
                'email' => $this->data['email'] ?? null,
            ]);

            if (in_array($method, ['email', 'sms', 'whatsapp'])) {
                $otp = !$this->isDev ? rand(1009, 9999) : 1436;

                $userOTP = new UserOTP([
                    'method' => $method,
                    'code' => $otp,
                    'expired_at' => now()->addMinutes(config('otp.timeout') ?? 3),
                    'auth_type' => 'App\\Models\\Client',
                    'auth_id' => 'temp_registration_' . Str::random(10),
                ]);

                $userOTP->save();
                $this->data['otp_uuid'] = $userOTP->uuid;

                if (!$this->isDev) {
                    $this->sendOTP($tempUser, $method, $otp);
                }
            }

            $this->showMethods = false;
            $this->selectedMethod = $method;
        } else {
            $this->reset();
        }
    }

    protected function handleOTPVerification(array $data): ?LoginResponse
    {
        $otp = $data['otp'] ?? null;
        
        if (!$otp) {
            throw ValidationException::withMessages([
                'data.otp' => __('client/auth.errors.invalid_otp'),
            ]);
        }

        // Verify OTP
        if (!$this->verifyOTP($otp)) {
            throw ValidationException::withMessages([
                'data.otp' => __('client/auth.errors.invalid_otp'),
            ]);
        }

        // Create the user account
        try {
            $userData = [
                'name' => $this->data['name'],
                'phone' => phone_format($this->data['phone'], ['SA', 'AUTO'], false),
                'email' => $this->data['email'] ?? null,
                'national_id' => $this->data['national_id'] ?? null,
                'address' => $this->data['address'] ?? null,
            ];

            $user = User::create($userData);

            // Clean up OTP
            if (isset($this->data['otp_uuid'])) {
                UserOTP::where('uuid', $this->data['otp_uuid'])->delete();
            }

            // Login the user
            return $this->loginUser($user);

        } catch (Exception $e) {
            throw ValidationException::withMessages([
                'data.otp' => __('client/auth.errors.registration_failed'),
            ]);
        }
    }

    protected function verifyOTP(string $otp): bool
    {
        if (!app()->environment(['production']) && in_array($this->selectedMethod, ['sms', 'whatsapp', 'email'])) {
            return $otp === '1436';
        }

        if (!isset($this->data['otp_uuid'])) {
            return false;
        }

        $userOTP = UserOTP::where('uuid', $this->data['otp_uuid'])
            ->where('expired_at', '>', now())
            ->first();

        return $userOTP && $otp == $userOTP->code;
    }

    protected function loginUser(User $user): LoginResponse
    {
        Filament::auth()->login($user);
        session()->regenerate();

        Notification::make()
            ->title(__('client/auth.messages.registration_success'))
            ->body(__('client/auth.messages.welcome'))
            ->success()
            ->send();

        return app(LoginResponse::class);
    }

    protected function sendOTP(User $user, string $method, string $otp): void
    {
        try {
            match ($method) {
                'email' => $user->notify(new OTPMailNotification($otp)),
                'sms' => $user->notify(new OTPNotification($otp, domain: get_domain(request()->headers->get('referer')))),
                'whatsapp' => $user->notify(new OTPNotification($otp, domain: get_domain(request()->headers->get('referer')), via: 'waha')),
                default => null
            };
        } catch (Exception $exception) {
            if ($method === 'sms' && Str::lower($exception->getMessage()) === Str::lower('Your balance is 0')) {
                $this->addError('phone', __('client/auth.errors.sms_balance_low'));
            } else {
                $this->addError('phone', __('client/auth.errors.unexpected_error'));
            }
        }
    }

    public function backToRegistration(): void
    {
        $this->currentStep = 'registration';
        $this->showMethods = false;
        $this->selectedMethod = null;
        $this->data['otp'] = null;
        $this->data['otp_uuid'] = null;
    }

    public function resendOTP(): void
    {
        if ($this->selectedMethod && isset($this->data['phone'])) {
            $this->selectedMethod = null;
            $this->showMethods = true;
        }
    }

    protected function getActiveMethods(): array
    {
        return array_filter($this->allowedMethods, fn($val) => $val === true);
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema($this->getFormSchema())
                    ->statePath('data'),
            ),
        ];
    }

    protected function getFormSchema(): array
    {
        if ($this->currentStep === 'registration') {
            return $this->getRegistrationFormSchema();
        } elseif ($this->currentStep === 'otp_verification') {
            return $this->getOTPFormSchema();
        }

        return [];
    }

    protected function getRegistrationFormSchema(): array
    {
        return [
            Section::make(__('client/auth.labels.personal_info'))
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextInput::make('name')
                                ->label(__('client/auth.fields.name'))
                                ->placeholder(__('client/auth.placeholders.name'))
                                ->required()
                                ->maxLength(255)
                                ->autofocus()
                                ->columnSpan(['default' => 2, 'md' => 1]),

                            TextInput::make('phone')
                                ->label(__('client/auth.fields.phone'))
                                ->placeholder(__('client/auth.placeholders.phone'))
                                ->tel()
                                ->required()
                                ->unique(User::class, 'phone')
                                ->rules([
                                    Rule::phone()->country(['AUTO'])->mobile(),
                                    fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                        $phone = phone_format($value, ['SA', 'AUTO'], false);
                                        $existingUser = User::where('phone', $phone)->first();
                                        if ($existingUser) {
                                            $fail(__('client/auth.errors.phone_exists'));
                                        }
                                    },
                                ])
                                ->columnSpan(['default' => 2, 'md' => 1]),
                        ]),

                    Grid::make(2)
                        ->schema([
                            TextInput::make('email')
                                ->label(__('client/auth.fields.email'))
                                ->placeholder(__('client/auth.placeholders.email'))
                                ->email()
                                ->unique(User::class, 'email')
                                ->maxLength(255)
                                ->columnSpan(['default' => 2, 'md' => 1]),

                            TextInput::make('national_id')
                                ->label(__('client/auth.fields.national_id'))
                                ->placeholder(__('client/auth.placeholders.national_id'))
                                ->unique(User::class, 'national_id')
                                ->maxLength(255)
                                ->columnSpan(['default' => 2, 'md' => 1]),
                        ]),

                    TextInput::make('address')
                        ->label(__('client/auth.fields.address'))
                        ->placeholder(__('client/auth.placeholders.address'))
                        ->maxLength(500)
                        ->columnSpanFull(),
                ]),
        ];
    }

    protected function getOTPFormSchema(): array
    {
        return [
            Section::make(__('client/auth.labels.verification'))
                ->description(__('client/auth.labels.otp_instructions'))
                ->schema([
                    TextInput::make('otp')
                        ->label(__('client/auth.fields.otp'))
                        ->placeholder(__('client/auth.placeholders.otp'))
                        ->visible(fn() => !empty($this->selectedMethod))
                        ->afterStateUpdated(function (string $state) {
                            if (strlen($state) === 4) {
                                $this->authenticate();
                            }
                        })
                        ->rules([
                            'required',
                            'numeric',
                            'regex:/^[0-9]+$/',
                            'digits:4',
                        ])
                        ->maxLength(4)
                        ->autofocus(),
                ]),
        ];
    }

    protected function getFormActions(): array
    {
        if ($this->currentStep === 'registration') {
            return [
                $this->getRegisterFormAction(),
                $this->getLoginLinkAction(),
            ];
        } elseif ($this->currentStep === 'otp_verification') {
            return [
                $this->getVerifyFormAction(),
                $this->getBackAction(),
                $this->getResendOTPAction(),
            ];
        }

        return [];
    }

    protected function getRegisterFormAction(): Action
    {
        return Action::make('register')
            ->label(__('client/auth.actions.next'))
            ->submit('authenticate')
            ->keyBindings(['mod+s']);
    }

    protected function getLoginLinkAction(): Action
    {
        return Action::make('login')
            ->label(__('client/auth.labels.already_have_account'))
            ->url(filament()->getLoginUrl())
            ->color('gray');
    }

    protected function getVerifyFormAction(): Action
    {
        return Action::make('verify')
            ->label(__('client/auth.actions.verify'))
            ->submit('authenticate')
            ->keyBindings(['mod+s']);
    }

    protected function getBackAction(): Action
    {
        return Action::make('back')
            ->label(__('client/auth.actions.back'))
            ->action('backToRegistration')
            ->color('gray');
    }

    protected function getResendOTPAction(): Action
    {
        return Action::make('resend')
            ->label(__('client/auth.actions.resend'))
            ->action('resendOTP')
            ->color('gray');
    }

    public function hasLogo(): bool
    {
        return true;
    }
}
