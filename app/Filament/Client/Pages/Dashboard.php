<?php

namespace App\Filament\Client\Pages;

use App\Models\MaintenanceRequest;
use App\Models\Visit;
use App\Models\Payment;
use Carbon\Carbon;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Dashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static string $view = 'filament.client.pages.dashboard';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationLabel = null;
    protected static ?string $title = null;

    public static function getNavigationLabel(): string
    {
        return __('client.navigation.dashboard');
    }

    public function getTitle(): string
    {
        return __('client.dashboard.title', ['default' => 'لوحة التحكم']);
    }
    protected static ?string $slug = 'dashboard';

    public $currentTime;
    public $greeting;

    public function mount(): void
    {
        $this->currentTime = now();
        $this->greeting = $this->getGreeting();
    }

    protected function getGreeting(): string
    {
        $hour = $this->currentTime->format('H');
        return $hour < 12 ? __('client.dashboard.good_morning', ['default' => 'صباح الخير']) : __('client.dashboard.good_evening', ['default' => 'مساء الخير']);
    }

    public function getStats(): array
    {
        $clientId = Auth::guard('client')->id();

        $contractStats = MaintenanceRequest::where('client_id', $clientId)
            ->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->pluck('total', 'status')
            ->toArray();

        return [
            'active_contracts' => $contractStats['approved'] ?? 0,
            'pending_contracts' => ($contractStats['new'] ?? 0) + ($contractStats['pending'] ?? 0),
            'completed_contracts' => $contractStats['completed'] ?? 0,
            'total_contracts' => MaintenanceRequest::where('client_id', $clientId)->count(),
            'upcoming_visits' => $this->getUpcomingVisitsCount(),
        ];
    }

    public function getUpcomingVisitsCount(): int
    {
        $clientId = Auth::guard('client')->id();

        return Visit::where(function($query) use ($clientId) {
            // Visits directly associated with contracts
            $query->where(function($subQuery) use ($clientId) {
                $subQuery->where('visitable_type', 'App\Models\Contract')
                         ->whereHas('visitable', function($contractQuery) use ($clientId) {
                             // Contracts can be associated with clients in two ways:
                             // 1. Direct client_id on contract
                             // 2. Through maintenance_request_id -> maintenance_request.client_id
                             $contractQuery->where(function($clientFilter) use ($clientId) {
                                 $clientFilter->where('client_id', $clientId)
                                             ->orWhereHas('maintenanceRequest', function($mrQuery) use ($clientId) {
                                                 $mrQuery->where('client_id', $clientId);
                                             });
                             });
                         });
            })
            // OR visits associated with maintenance requests
            ->orWhere(function($subQuery) use ($clientId) {
                $subQuery->where('visitable_type', 'App\Models\MaintenanceRequest')
                         ->whereHas('visitable', function($mrQuery) use ($clientId) {
                             $mrQuery->where('client_id', $clientId);
                         });
            });
        })
        ->whereIn('status', ['scheduled', 'in_progress'])
        ->where('scheduled_at', '>=', now())
        ->count();
    }

    public function getUpcomingVisits(): array
    {
        $clientId = Auth::guard('client')->id();

        return Visit::with(['visitable', 'technician'])
            ->where(function($query) use ($clientId) {
                // Visits directly associated with contracts
                $query->where(function($subQuery) use ($clientId) {
                    $subQuery->where('visitable_type', 'App\Models\Contract')
                             ->whereHas('visitable', function($contractQuery) use ($clientId) {
                                 /*$contractQuery->whereHas('maintenanceRequest', function($mrQuery) use ($clientId) {
                                     $mrQuery->where('client_id', $clientId);
                                 });*/
                                 $contractQuery->where('client_id', $clientId);
                             });
                })
                // OR visits associated with maintenance requests
                ->orWhere(function($subQuery) use ($clientId) {
                    $subQuery->where('visitable_type', 'App\Models\MaintenanceRequest')
                             ->whereHas('visitable', function($mrQuery) use ($clientId) {
                                 $mrQuery->where('client_id', $clientId);
                             });
                });
            })
            ->whereIn('status', ['scheduled', 'in_progress'])
            ->where('scheduled_at', '>=', now())
            ->orderBy('scheduled_at', 'asc')
            ->take(2)
            ->get()
            ->map(function($visit) {
                // Get the request number based on the visitable type
                $requestNumber = 'N/A';

                try {
                    if ($visit->visitable_type === 'App\Models\Contract' && $visit->visitable) {
                        // For contract visits, get the maintenance request through the contract
                        if ($visit->visitable instanceof \App\Models\Contract) {
                            $requestNumber = $visit->visitable->maintenanceRequest?->request_number ?? 'N/A';
                        }
                    } elseif ($visit->visitable_type === 'App\Models\MaintenanceRequest' && $visit->visitable) {
                        // For maintenance request visits, get the request number directly
                        if ($visit->visitable instanceof \App\Models\MaintenanceRequest) {
                            $requestNumber = $visit->visitable->request_number ?? 'N/A';
                        }
                    }
                } catch (\Exception $e) {
                    // Log the error and continue with default value
                    \Log::warning('Error getting request number for visit', [
                        'visit_id' => $visit->id,
                        'visitable_type' => $visit->visitable_type,
                        'visitable_id' => $visit->visitable_id,
                        'error' => $e->getMessage()
                    ]);
                    $requestNumber = 'N/A';
                }

                return [
                    'contract_number' => $requestNumber,
                    'visit_date' => $visit->scheduled_at->format('d/m/Y'),
                    'visit_time' => $visit->scheduled_at->format('h:i A'),
                    'technician' => $visit->technician?->name ?? 'لم يتم التحديد',
                    'status_label' => $visit->status === 'scheduled' ? 'مجدولة' : 'قيد التنفيذ',
                ];
            })->toArray();
    }

    public function getRecentContracts()
    {
        $clientId = Auth::guard('client')->id();
        return MaintenanceRequest::with(['contract_type', 'contract'])
            ->where('client_id', $clientId)
            ->latest()
            ->take(3)
            ->get();
    }

    public function getNotifications(): array
    {
        return [
            [
                'type' => 'info',
                'title' => __('client.dashboard.notifications.upcoming_visit_title', ['default' => 'زيارة صيانة قريبة']),
                'message' => __('client.dashboard.notifications.upcoming_visit_message', ['default' => 'لديك زيارة صيانة مجدولة خلال 3 أيام']),
                'time' => __('client.dashboard.notifications.time_hours', ['hours' => 2, 'default' => '2 ساعات']),
            ],
            [
                'type' => 'warning',
                'title' => __('client.dashboard.notifications.certificate_expiring_title', ['default' => 'شهادة قاربت على الانتهاء']),
                'message' => __('client.dashboard.notifications.certificate_expiring_message', ['default' => 'شهادة فحص المصاعد تنتهي خلال 30 يوم']),
                'time' => __('client.dashboard.notifications.time_day', ['default' => '1 يوم']),
            ],
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            /*Action::make('emergency_request')
                ->label('صيانة طارئة')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('danger')
                ->form([
                    Forms\Components\Select::make('urgency')
                        ->label('مستوى الأولوية')
                        ->options([
                            'high' => 'عالية',
                            'critical' => 'حرجة',
                        ])
                        ->required(),

                    Forms\Components\Textarea::make('description')
                        ->label('وصف المشكلة')
                        ->required(),
                ])
                ->action(function (array $data): void {
                    Notification::make()
                        ->title('تم إرسال طلب الصيانة الطارئة')
                        ->body('سيتم التواصل معك خلال 15 دقيقة')
                        ->success()
                        ->send();
                }),*/

            Action::make('new_contract')
                ->label('طلب عقد جديد')
                ->icon('heroicon-o-plus-circle')
                ->color('primary')
                ->url(route('filament.client.resources.maintenance-contracts.wizard.step1')),
        ];
    }

    public static function canAccess(): bool
    {
        return true;
    }
}
