<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages\MaintenanceWizardSteps;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class Step2 extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.step2';

    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.wizard.step2.title');
    }

    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.wizard.step2.heading');
    }

    public ?array $data = [];

    public function mount(): void
    {
        // Get wizard data from session
        $wizardData = Session::get('maintenance_wizard_data', []);

        // Verify that step 1 has been completed
        if (empty($wizardData['contractType'])) {
            redirect()->route('filament.client.resources.maintenance-contracts.wizard.step1');
            return;
        }

        // Update current step
        $wizardData['currentStep'] = 2;
        Session::put('maintenance_wizard_data', $wizardData);

        // Get current client data if authenticated
        $client = Auth::guard('client')->user();

        // Initialize form data
        $this->data = [
            'companyName' => $wizardData['companyName'] ?? $client?->company ?? '',
            'contactName' => $wizardData['contactName'] ?? $client?->name ?? '',
            'phone' => $wizardData['phone'] ?? $client?->phone ?? '',
            'email' => $wizardData['email'] ?? $client?->email ?? '',
            'address' => $wizardData['address'] ?? $client?->address ?? '',
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('filament-resources/maintenance-request.wizard.step2.section_title'))
                    ->description(__('filament-resources/maintenance-request.wizard.step2.section_description'))
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('companyName')
                                    ->label(__('filament-resources/maintenance-request.wizard.step2.fields.company_name'))
                                    ->required()
                                    ->placeholder(__('filament-resources/maintenance-request.wizard.step2.fields.company_name_placeholder'))
                                    ->maxLength(255),

                                TextInput::make('contactName')
                                    ->label(__('filament-resources/maintenance-request.wizard.step2.fields.contact_name'))
                                    ->required()
                                    ->placeholder(__('filament-resources/maintenance-request.wizard.step2.fields.contact_name_placeholder'))
                                    ->maxLength(255),

                                TextInput::make('phone')
                                    ->label(__('filament-resources/maintenance-request.wizard.step2.fields.phone'))
                                    ->tel()
                                    ->required()
                                    ->placeholder(__('filament-resources/maintenance-request.wizard.step2.fields.phone_placeholder'))
                                    ->maxLength(20),

                                TextInput::make('email')
                                    ->label(__('filament-resources/maintenance-request.wizard.step2.fields.email'))
                                    ->email()
                                    ->required()
                                    ->placeholder(__('filament-resources/maintenance-request.wizard.step2.fields.email_placeholder'))
                                    ->maxLength(255),

                                Textarea::make('address')
                                    ->label(__('filament-resources/maintenance-request.wizard.step2.fields.address'))
                                    ->required()
                                    ->placeholder(__('filament-resources/maintenance-request.wizard.step2.fields.address_placeholder'))
                                    ->rows(3)
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    public function submitStep2(): void
    {
        $this->form->validate();

        // Store data in session
        $wizardData = Session::get('maintenance_wizard_data', []);
        $wizardData['companyName'] = $this->data['companyName'];
        $wizardData['contactName'] = $this->data['contactName'];
        $wizardData['phone'] = $this->data['phone'];
        $wizardData['email'] = $this->data['email'];
        $wizardData['address'] = $this->data['address'];

        Session::put('maintenance_wizard_data', $wizardData);

        // Redirect to next step
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.step3');
    }

    public function previousStep(): void
    {
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.step1');
    }
}
