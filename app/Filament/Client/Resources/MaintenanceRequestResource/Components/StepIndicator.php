<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Components;

use Filament\Forms\Components\Component;
use Illuminate\Contracts\View\View;

class StepIndicator extends Component
{
    protected int $currentStep = 1;

    public function currentStep(int $step): static
    {
        $this->currentStep = $step;

        return $this;
    }

    public function render(): View
    {
        return view('filament.client.resources.maintenance-contract-resource.components.step-indicator', [
            'currentStep' => $this->currentStep,
        ]);
    }
}
