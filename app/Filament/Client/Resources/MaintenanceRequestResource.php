<?php

namespace App\Filament\Client\Resources;

use App\Models\ContractType;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Client\Resources\MaintenanceRequestResource\Pages;
use App\Models\MaintenanceRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MaintenanceRequestResource extends Resource
{
    protected static ?string $model = MaintenanceRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    protected static ?string $navigationLabel = 'عقود الصيانة';

    protected static ?string $pluralModelLabel = 'عقود الصيانة';

    protected static ?string $modelLabel = 'عقد صيانة';

    protected static ?string $slug = 'maintenance-contracts';

    public static function getNavigationGroup(): ?string
    {
        return __('الخدمات');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('request_number')
                    ->label(__('filament-resources/maintenance-request.fields.request_number'))
                    ->disabled()
                    ->dehydrated(false),

                Forms\Components\Select::make('contract_type_id')
                    ->label(__('filament-resources/maintenance-request.fields.contract_type_id'))
                    ->options(
                        ContractType::where('is_active', true)
                            ->orderBy('display_order')
                            ->pluck('name', 'id')
                    )
                    ->required(),

                Forms\Components\TextInput::make('visits_included')
                    ->label(__('filament-resources/maintenance-request.fields.visits_included'))
                    ->numeric()
                    ->required(),

                Forms\Components\Select::make('status')
                    ->label(__('filament-resources/maintenance-request.fields.status'))
                    ->options([
                        'new' => __('filament-resources/maintenance-request.status_options.new'),
                        'pending' => __('filament-resources/maintenance-request.status_options.pending'),
                        'approved' => __('filament-resources/maintenance-request.status_options.approved'),
                        'rejected' => __('filament-resources/maintenance-request.status_options.rejected'),
                        'completed' => __('filament-resources/maintenance-request.status_options.completed'),
                    ])
                    ->required(),

                Forms\Components\Textarea::make('notes')
                    ->label(__('filament-resources/maintenance-request.fields.notes'))
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('request_number')
                    ->label('رقم الطلب')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('تم نسخ رقم الطلب')
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('client.company')
                    ->label('اسم العميل')
                    ->searchable()
                    ->placeholder('غير محدد'),

                Tables\Columns\TextColumn::make('contract_type.name')
                    ->label('نوع العقد')
                    ->sortable()
                    ->placeholder('غير محدد'),

                Tables\Columns\TextColumn::make('visits_included')
                    ->label('عدد الزيارات')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('الحالة')
                    ->sortable()
                    ->colors([
                        'warning' => 'new',
                        'info' => 'pending',
                        'primary' => 'assigned',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'gray' => 'canceled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'new' => 'طلب جديد',
                        'pending' => 'قيد المراجعة',
                        'assigned' => 'تم التعيين',
                        'in_progress' => 'قيد التنفيذ',
                        'completed' => 'مكتمل',
                        'canceled' => 'ملغي',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الطلب')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('الحالة')
                    ->options([
                        'new' => 'طلب جديد',
                        'pending' => 'قيد المراجعة',
                        'assigned' => 'تم التعيين',
                        'in_progress' => 'قيد التنفيذ',
                        'completed' => 'مكتمل',
                        'canceled' => 'ملغي',
                    ]),

                Tables\Filters\SelectFilter::make('contract_type_id')
                    ->label('نوع العقد')
                    ->relationship('contract_type', 'name'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض')
                    ->icon('heroicon-o-eye'),
                /*Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),*/
            ])
            ->bulkActions([
                /*Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),*/
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaintenanceRequests::route('/'),
            'view' => Pages\ViewMaintenanceRequest::route('/{record}'),
            //'create' => Pages\CreateMaintenanceRequest::route('/create'),
            'edit' => Pages\EditMaintenanceRequest::route('/{record}/edit'),
            // Custom wizard pages
            'wizard' => Pages\MaintenanceWizard::route('/wizard'),
            'wizard.step1' => Pages\MaintenanceWizardSteps\Step1::route('/wizard/step1'),
            'wizard.step2' => Pages\MaintenanceWizardSteps\Step2::route('/wizard/step2'),
            'wizard.step3' => Pages\MaintenanceWizardSteps\Step3::route('/wizard/step3'),
            'wizard.success' => Pages\MaintenanceWizardSteps\Success::route('/wizard/success/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    // Customize the navigation badge to show pending requests count
    public static function getNavigationBadge(): ?string
    {
        $pendingCount = static::getModel()::where('status', 'pending')->count();
        return $pendingCount > 0 ? (string) $pendingCount : null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    // Add global search
    public static function getGloballySearchableAttributes(): array
    {
        return ['request_number', 'client.name', 'client.company'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'العميل' => $record->client?->company ?? $record->client?->name,
            'النوع' => $record->contract_type?->name,
            'التاريخ' => $record->created_at->format('d/m/Y'),
        ];
    }
}
