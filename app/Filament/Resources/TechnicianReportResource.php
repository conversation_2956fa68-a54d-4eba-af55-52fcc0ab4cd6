<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TechnicianReportResource\Pages;
use App\Models\TechnicianReport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class TechnicianReportResource extends Resource
{
    protected static ?string $model = TechnicianReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?int $navigationSort = 6;

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.maintenance_management');
    }

    public static function getModelLabel(): string
    {
        return __('filament-resources/technician-report.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-resources/technician-report.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Read-only form for admin viewing
                Forms\Components\Section::make(__('filament-resources/technician-report.sections.basic_info'))
                    ->schema([
                        Forms\Components\TextInput::make('id')
                            ->label(__('filament-resources/technician-report.fields.report_id'))
                            ->formatStateUsing(fn ($state) => "#{$state}")
                            ->disabled(),

                        Forms\Components\Select::make('maintenance_request_id')
                            ->label(__('filament-resources/technician-report.fields.maintenance_request'))
                            ->relationship('maintenanceRequest', 'request_number')
                            ->disabled(),

                        Forms\Components\Select::make('technician_id')
                            ->label(__('filament-resources/technician-report.fields.technician'))
                            ->relationship('technician', 'name')
                            ->disabled(),

                        Forms\Components\Select::make('status')
                            ->label(__('filament-resources/technician-report.fields.status'))
                            ->options(TechnicianReport::getStatusOptions())
                            ->disabled(),

                        Forms\Components\TextInput::make('time_spent_minutes')
                            ->label(__('filament-resources/technician-report.fields.time_spent'))
                            ->suffix(__('filament-resources/technician-report.fields.time_spent_suffix'))
                            ->disabled(),

                        Forms\Components\DateTimePicker::make('submitted_at')
                            ->label(__('filament-resources/technician-report.fields.submitted_at'))
                            ->disabled(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('filament-resources/technician-report.sections.work_details'))
                    ->schema([
                        Forms\Components\Textarea::make('work_summary')
                            ->label(__('filament-resources/technician-report.fields.work_summary'))
                            ->rows(3)
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('findings')
                            ->label(__('filament-resources/technician-report.fields.findings'))
                            ->rows(3)
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('recommendations')
                            ->label(__('filament-resources/technician-report.fields.recommendations'))
                            ->rows(3)
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('issues_found')
                            ->label(__('filament-resources/technician-report.fields.issues_found'))
                            ->rows(3)
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('parts_used')
                            ->label(__('filament-resources/technician-report.fields.parts_used'))
                            ->rows(3)
                            ->disabled()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('filament-resources/technician-report.columns.report_id'))
                    ->formatStateUsing(fn ($state) => "#{$state}")
                    ->sortable(),

                Tables\Columns\TextColumn::make('maintenanceRequest.request_number')
                    ->label(__('filament-resources/technician-report.columns.maintenance_request'))
                    ->searchable()
                    ->sortable()
                    ->url(fn (TechnicianReport $record): string =>
                        route('filament.admin.resources.maintenance-requests.view', $record->maintenance_request_id)
                    ),

                Tables\Columns\TextColumn::make('maintenanceRequest.client.name')
                    ->label(__('filament-resources/technician-report.columns.client'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('technician.name')
                    ->label(__('filament-resources/technician-report.columns.technician'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('filament-resources/technician-report.columns.status'))
                    ->colors([
                        'secondary' => TechnicianReport::STATUS_DRAFT,
                        'warning' => TechnicianReport::STATUS_SUBMITTED,
                        'success' => TechnicianReport::STATUS_APPROVED,
                        'danger' => TechnicianReport::STATUS_REJECTED,
                    ])
                    ->formatStateUsing(fn (string $state): string =>
                        __("technician.resources.report.status_options.{$state}")
                    ),

                Tables\Columns\TextColumn::make('completion_percentage')
                    ->label(__('filament-resources/technician-report.columns.completion'))
                    ->formatStateUsing(fn ($state) => "{$state}%")
                    ->badge()
                    ->color(fn ($state) => $state >= 80 ? 'success' : ($state >= 50 ? 'warning' : 'danger'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('time_spent_minutes')
                    ->label(__('filament-resources/technician-report.columns.time_spent'))
                    ->formatStateUsing(fn ($state) => $state ? "{$state} " . __('technician.resources.report.fields.time_spent_suffix') : '-')
                    ->sortable(),

                Tables\Columns\TextColumn::make('attachments')
                    ->label(__('filament-resources/technician-report.columns.attachments'))
                    ->formatStateUsing(function ($state) {
                        if (empty($state) || !is_array($state)) {
                            return '0';
                        }
                        
                        $count = count($state);
                        return $count . ' ' . __('technician.resources.report.columns.files');
                    })
                    ->badge()
                    ->color(fn ($state) => empty($state) || !is_array($state) ? 'gray' : 'success'),

                Tables\Columns\TextColumn::make('submitted_at')
                    ->label(__('filament-resources/technician-report.columns.submitted_at'))
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->tooltip(fn ($state) => $state?->format('Y-m-d H:i:s')),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources/technician-report.columns.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources/technician-report.filters.status'))
                    ->options(TechnicianReport::getStatusOptions()),

                Tables\Filters\SelectFilter::make('technician_id')
                    ->label(__('filament-resources/technician-report.filters.technician'))
                    ->relationship('technician', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('maintenance_request_id')
                    ->label(__('filament-resources/technician-report.filters.maintenance_request'))
                    ->relationship('maintenanceRequest', 'request_number')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('has_attachments')
                    ->label(__('filament-resources/technician-report.filters.has_attachments'))
                    ->query(fn (Builder $query): Builder => 
                        $query->whereNotNull('attachments')->where('attachments', '!=', '[]')
                    ),

                Tables\Filters\Filter::make('submitted_today')
                    ->label(__('filament-resources/technician-report.filters.submitted_today'))
                    ->query(fn (Builder $query): Builder => 
                        $query->whereDate('submitted_at', today())
                    ),

                Tables\Filters\Filter::make('pending_approval')
                    ->label(__('filament-resources/technician-report.filters.pending_approval'))
                    ->query(fn (Builder $query): Builder => 
                        $query->where('status', TechnicianReport::STATUS_SUBMITTED)
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('filament-resources/technician-report.actions.view')),

                Tables\Actions\Action::make('approve')
                    ->label(__('filament-resources/technician-report.actions.approve'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (TechnicianReport $record): bool => 
                        $record->status === TechnicianReport::STATUS_SUBMITTED
                    )
                    ->requiresConfirmation()
                    ->action(function (TechnicianReport $record) {
                        $record->update(['status' => TechnicianReport::STATUS_APPROVED]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/technician-report.notifications.approved'))
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('reject')
                    ->label(__('filament-resources/technician-report.actions.reject'))
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn (TechnicianReport $record): bool => 
                        $record->status === TechnicianReport::STATUS_SUBMITTED
                    )
                    ->requiresConfirmation()
                    ->action(function (TechnicianReport $record) {
                        $record->update(['status' => TechnicianReport::STATUS_REJECTED]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/technician-report.notifications.rejected'))
                            ->warning()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('approve_selected')
                        ->label(__('filament-resources/technician-report.bulk_actions.approve_selected'))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            $count = 0;
                            foreach ($records as $record) {
                                if ($record->status === TechnicianReport::STATUS_SUBMITTED) {
                                    $record->update(['status' => TechnicianReport::STATUS_APPROVED]);
                                    $count++;
                                }
                            }
                            
                            \Filament\Notifications\Notification::make()
                                ->title(__('filament-resources/technician-report.notifications.bulk_approved', ['count' => $count]))
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('filament-resources/technician-report.empty_state.heading'))
            ->emptyStateDescription(__('filament-resources/technician-report.empty_state.description'))
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'technician:id,name',
                'maintenanceRequest:id,request_number,client_id',
                'maintenanceRequest.client:id,name',
                'checklistItems'
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTechnicianReports::route('/'),
            'view' => Pages\ViewTechnicianReport::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Reports are created by technicians only
    }

    public static function canEdit(Model $record): bool
    {
        return false; // Reports are edited by technicians only
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('delete', $record);
    }
}
