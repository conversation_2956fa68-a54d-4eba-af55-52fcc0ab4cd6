<?php

namespace App\Filament\Resources\BroadcastChannelSenderResource\Pages;

use App\Filament\Resources\BroadcastChannelSenderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBroadcastChannelSenders extends ListRecords
{
    protected static string $resource = BroadcastChannelSenderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
