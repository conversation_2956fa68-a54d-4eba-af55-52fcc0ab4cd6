<?php

namespace App\Filament\Resources\DocumentTemplateResource\Pages;

use App\Filament\Resources\DocumentTemplateResource;
use Filament\Resources\Pages\Page;
use App\Services\DocumentTemplateService;
use App\Models\DocumentTemplate;
use Filament\Notifications\Notification;

class PreviewDocumentTemplate extends Page
{
    protected static string $resource = DocumentTemplateResource::class;

    protected static string $view = 'filament.resources.document-template-resource.pages.preview-document-template';

    public ?DocumentTemplate $record = null;

    public ?array $previewData = null;

    public ?string $previewHtml = null;

    public function mount(DocumentTemplate $record): void
    {
        $this->record = $record;
        $this->loadPreview();
    }

    protected function loadPreview(): void
    {
        // If the template isn't synced, sync it first
        if (!$this->record->is_synced) {
            $this->record = DocumentTemplateService::syncTemplateWithDocking($this->record->type);

            if (!$this->record->is_synced) {
                Notification::make()
                    ->title(__('filament-resources/document-template.notifications.template_not_synced', ['default' => 'لم تتم مزامنة القالب']))
                    ->body($this->record->sync_error ?? __('filament-resources/document-template.notifications.sync_required', ['default' => 'يجب مزامنة القالب أولاً مع DocKing']))
                    ->danger()
                    ->send();

                return;
            }
        }

        // Get sample data for this template type
        $this->previewData = DocumentTemplateService::getSampleDataForTemplate($this->record->type);

        // Get the preview HTML
        $preview = DocumentTemplateService::previewTemplate($this->record->type);

        if ($preview && isset($preview['html'])) {
            $this->previewHtml = $preview['html'];
        } else {
            Notification::make()
                ->title(__('filament-resources/document-template.notifications.preview_failed', ['default' => 'فشل تحميل المعاينة']))
                ->body(__('filament-resources/document-template.notifications.check_settings', ['default' => 'تأكد من صحة إعدادات DocKing ومحتوى القالب']))
                ->danger()
                ->send();
        }
    }

    public function refreshPreview(): void
    {
        $this->loadPreview();
    }

    public function getTitle(): string
    {
        return __('filament-resources/document-template.pages.preview_title', ['name' => $this->record->name, 'default' => 'معاينة قالب: ' . $this->record->name]);
    }
}
