<?php

namespace App\Filament\Resources\DocumentTemplateResource\Pages;

use App\Filament\Resources\DocumentTemplateResource;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;
use App\Services\DocumentTemplateService;
use Filament\Notifications\Notification;

class EditDocumentTemplate extends EditRecord
{
    protected static string $resource = DocumentTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('sync')
                ->label('مزامنة')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->action(function () {
                    $template = DocumentTemplateService::syncTemplateWithDocking($this->record->type);

                    // Refresh record from database
                    $this->record->refresh();

                    // Show notification
                    Notification::make()
                        ->title($template->is_synced ? 'تمت المزامنة بنجاح' : 'فشلت المزامنة')
                        ->body($template->is_synced ? null : $template->sync_error)
                        ->success($template->is_synced)
                        ->danger(!$template->is_synced)
                        ->send();
                }),

            Actions\Action::make('preview')
                ->label('معاينة')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn () => route('filament.admin.resources.document-templates.preview', ['record' => $this->record]))
                ->openUrlInNewTab(),

            Actions\Action::make('generatePdf')
                ->label('إنشاء PDF')
                ->icon('heroicon-o-document')
                ->color('warning')
                ->url(fn () => route('filament.admin.resources.document-templates.generate-pdf', ['record' => $this->record]))
                ->openUrlInNewTab(),

            Actions\DeleteAction::make(),

            Actions\Action::make('exportToFile')
                ->label('تصدير إلى ملف')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->visible(false)
                ->action(function () {
                    $result = DocumentTemplateService::exportTemplateToFile($this->record->type);

                    Notification::make()
                        ->title($result ? 'تم التصدير بنجاح' : 'فشل التصدير')
                        ->success($result)
                        ->danger(!$result)
                        ->send();
                }),

            Actions\Action::make('importFromFile')
                ->label('استيراد من ملف')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('gray')
                ->requiresConfirmation()
                ->modalHeading('استيراد القالب من ملف')
                ->modalDescription('هل أنت متأكد من رغبتك في استيراد القالب من ملف؟ سيتم استبدال المحتوى الحالي.')
                ->action(function () {
                    $result = DocumentTemplateService::importTemplateFromFile($this->record->type);

                    if ($result) {
                        // Refresh the form
                        $this->fillForm();
                    }

                    Notification::make()
                        ->title($result ? 'تم الاستيراد بنجاح' : 'فشل الاستيراد')
                        ->body($result ? null : 'الملف غير موجود أو غير صالح')
                        ->success($result)
                        ->danger(!$result)
                        ->send();
                }),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Update version in metadata
        $metadata = $this->record->metadata ?? [];
        $metadata['version'] = ($metadata['version'] ?? 0) + 1;
        $metadata['updated_at'] = now()->toIso8601String();

        $data['metadata'] = $metadata;

        // Set sync status to false
        $data['is_synced'] = false;
        $data['sync_error'] = null;

        return $data;
    }

    protected function afterSave(): void
    {
        // Clear the template cache
        DocumentTemplateService::clearTemplateCache($this->record->type);

        // Ask if the user wants to sync the template with DocKing
        Notification::make()
            ->title('هل ترغب في مزامنة القالب مع DocKing؟')
            ->actions([
                \Filament\Notifications\Actions\Action::make('sync')
                    ->label('مزامنة الآن')
                    ->color('success')
                    ->button()
                    ->close()
                    ->action(function () {
                        $template = DocumentTemplateService::syncTemplateWithDocking($this->record->type);

                        // Refresh record from database
                        $this->record->refresh();

                        // Show notification
                        Notification::make()
                            ->title($template->is_synced ? 'تمت المزامنة بنجاح' : 'فشلت المزامنة')
                            ->body($template->is_synced ? null : $template->sync_error)
                            ->success($template->is_synced)
                            ->danger(!$template->is_synced)
                            ->send();
                    }),

                \Filament\Notifications\Actions\Action::make('skip')
                    ->label('تخطي')
                    ->color('gray')
                    ->button()
                    ->close(),
            ])
            ->persistent()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
