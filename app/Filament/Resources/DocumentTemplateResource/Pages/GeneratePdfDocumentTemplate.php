<?php

namespace App\Filament\Resources\DocumentTemplateResource\Pages;

use Illuminate\Http\RedirectResponse;
use App\Filament\Resources\DocumentTemplateResource;
use Filament\Resources\Pages\Page;
use App\Services\DocumentTemplateService;
use App\Models\DocumentTemplate;
use Filament\Notifications\Notification;
use Illuminate\Http\Response;

class GeneratePdfDocumentTemplate extends Page
{
    protected static string $resource = DocumentTemplateResource::class;

    protected static string $view = 'filament.resources.document-template-resource.pages.generate-pdf-document-template';

    public ?DocumentTemplate $record = null;

    public ?array $pdfData = null;

    public ?string $pdfUrl = null;

    public function mount(DocumentTemplate $record): void
    {
        $this->record = $record;
        $this->pdfData = DocumentTemplateService::getSampleDataForTemplate($record->type);
        $this->generatePdf();
    }

    public function generatePdf(): void
    {
        // If the template isn't synced, sync it first
        if (!$this->record->is_synced) {
            $this->record = DocumentTemplateService::syncTemplateWithDocking($this->record->type);

            if (!$this->record->is_synced) {
                Notification::make()
                    ->title('لم تتم مزامنة القالب')
                    ->body($this->record->sync_error ?? 'يجب مزامنة القالب أولاً مع DocKing')
                    ->danger()
                    ->send();

                return;
            }
        }

        // Generate the PDF
        $result = DocumentTemplateService::generatePdf($this->record->type, $this->pdfData);

        if ($result && isset($result['url']) && $result['outcome'] === 'SUCCESS') {
            $this->pdfUrl = $result['url'];
        } else {
            Notification::make()
                ->title('فشل إنشاء ملف PDF')
                ->body('تأكد من صحة إعدادات DocKing ومحتوى القالب')
                ->danger()
                ->send();
        }
    }

    public function downloadPdf(): Response|RedirectResponse
    {
        if (!$this->pdfUrl) {
            $this->generatePdf();

            if (!$this->pdfUrl) {
                Notification::make()
                    ->title('لا يمكن تنزيل الملف')
                    ->body('فشل إنشاء ملف PDF')
                    ->danger()
                    ->send();

                return response('', 404);
            }
        }

        return response()->redirectTo($this->pdfUrl);
    }

    public function getTitle(): string
    {
        return 'إنشاء PDF: ' . $this->record->name;
    }
}
