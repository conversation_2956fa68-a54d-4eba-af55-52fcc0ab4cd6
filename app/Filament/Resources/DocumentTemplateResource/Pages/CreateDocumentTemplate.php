<?php

namespace App\Filament\Resources\DocumentTemplateResource\Pages;

use App\Filament\Resources\DocumentTemplateResource;
use Filament\Resources\Pages\CreateRecord;
use App\Services\DocumentTemplateService;
use Filament\Notifications\Notification;

class CreateDocumentTemplate extends CreateRecord
{
    protected static string $resource = DocumentTemplateResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Add metadata
        $data['metadata'] = [
            'driver' => 'gotenberg',
            'templating' => 'blade',
            'version' => 1,
            'created_at' => now()->toIso8601String(),
        ];

        // Set sync status
        $data['is_synced'] = false;

        return $data;
    }

    protected function afterCreate(): void
    {
        // Ask if the user wants to sync the template with DocKing
        Notification::make()
            ->title('هل ترغب في مزامنة القالب مع DocKing؟')
            ->actions([
                \Filament\Notifications\Actions\Action::make('sync')
                    ->label('مزامنة الآن')
                    ->color('success')
                    ->button()
                    ->close()
                    ->action(function () {
                        // Get the newly created record
                        $record = $this->record;

                        // Sync the template with DocKing
                        $template = DocumentTemplateService::syncTemplateWithDocking($record->type);

                        // Show notification
                        Notification::make()
                            ->title($template->is_synced ? 'تمت المزامنة بنجاح' : 'فشلت المزامنة')
                            ->body($template->is_synced ? null : $template->sync_error)
                            ->success($template->is_synced)
                            ->danger(!$template->is_synced)
                            ->send();
                    }),

                \Filament\Notifications\Actions\Action::make('skip')
                    ->label('تخطي')
                    ->color('gray')
                    ->button()
                    ->close(),
            ])
            ->persistent()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
