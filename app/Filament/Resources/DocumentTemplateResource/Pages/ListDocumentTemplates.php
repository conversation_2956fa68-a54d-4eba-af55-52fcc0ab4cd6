<?php

namespace App\Filament\Resources\DocumentTemplateResource\Pages;

use App\Filament\Resources\DocumentTemplateResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions;
use App\Services\DocumentTemplateService;

class ListDocumentTemplates extends ListRecords
{
    protected static string $resource = DocumentTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('syncAll')
                ->label('مزامنة الكل')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('مزامنة جميع القوالب')
                ->modalDescription('هل أنت متأكد من رغبتك في مزامنة جميع القوالب مع DocKing؟')
                ->action(function () {
                    $templateTypes = array_keys(DocumentTemplateService::getTemplateTypes());
                    $successCount = 0;
                    $failCount = 0;

                    foreach ($templateTypes as $templateType) {
                        $template = DocumentTemplateService::getTemplate($templateType);
                        $template = DocumentTemplateService::syncTemplateWithDocking($templateType);

                        if ($template->is_synced) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    }

                    $this->dispatch('filament.notification', [
                        'status' => $failCount === 0 ? 'success' : ($successCount > 0 ? 'warning' : 'danger'),
                        'title' => 'اكتملت عملية المزامنة',
                        'body' => "نجاح: {$successCount}, فشل: {$failCount}",
                    ]);
                }),

            Actions\Action::make('exportAllToFiles')
                ->label('تصدير الكل إلى ملفات')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->visible(false)
                ->requiresConfirmation()
                ->modalHeading('تصدير جميع القوالب')
                ->modalDescription('هل أنت متأكد من رغبتك في تصدير جميع القوالب إلى ملفات؟')
                ->action(function () {
                    $templateTypes = array_keys(DocumentTemplateService::getTemplateTypes());
                    $successCount = 0;
                    $failCount = 0;

                    foreach ($templateTypes as $templateType) {
                        $result = DocumentTemplateService::exportTemplateToFile($templateType);

                        if ($result) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    }

                    $this->dispatch('filament.notification', [
                        'status' => $failCount === 0 ? 'success' : ($successCount > 0 ? 'warning' : 'danger'),
                        'title' => 'اكتملت عملية التصدير',
                        'body' => "نجاح: {$successCount}, فشل: {$failCount}",
                    ]);
                }),

            Actions\Action::make('importAllFromFiles')
                ->label('استيراد الكل من ملفات')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('gray')
                ->requiresConfirmation()
                ->modalHeading('استيراد جميع القوالب')
                ->modalDescription('هل أنت متأكد من رغبتك في استيراد جميع القوالب من ملفات؟ سيتم استبدال المحتوى الحالي.')
                ->action(function () {
                    $templateTypes = array_keys(DocumentTemplateService::getTemplateTypes());
                    $successCount = 0;
                    $failCount = 0;

                    foreach ($templateTypes as $templateType) {
                        $result = DocumentTemplateService::importTemplateFromFile($templateType);

                        if ($result) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    }

                    $this->dispatch('filament.notification', [
                        'status' => $failCount === 0 ? 'success' : ($successCount > 0 ? 'warning' : 'danger'),
                        'title' => 'اكتملت عملية الاستيراد',
                        'body' => "نجاح: {$successCount}, فشل: {$failCount}",
                    ]);
                }),

            //Actions\CreateAction::make(),
        ];
    }
}
