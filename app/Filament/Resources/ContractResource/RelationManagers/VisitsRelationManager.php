<?php

namespace App\Filament\Resources\ContractResource\RelationManagers;

use App\Models\User;
use App\Models\Contract;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Carbon\Carbon;
use Filament\Notifications\Notification;

class VisitsRelationManager extends RelationManager
{
    protected static string $relationship = 'visits';

    protected static ?string $title = null;

    public static function getTitle(\Illuminate\Database\Eloquent\Model $ownerRecord, string $pageClass): string
    {
        return __('filament-resources.contract.relation_managers.visits.title');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('technician_id')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.technician_id'))
                            ->placeholder(__('filament-resources.contract.relation_managers.visits.form.technician_id_placeholder'))
                            ->options(function () {
                                return User::where('role', 'technician')
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->searchable()
                            ->preload(),
                        Forms\Components\DateTimePicker::make('scheduled_at')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.scheduled_at'))
                            ->required()
                            ->default(now()->addDay()->setHour(9)->setMinute(0)->setSecond(0))
                            ->helperText(__('filament-resources.contract.relation_managers.visits.form.scheduled_at_helper')),
                        Forms\Components\Select::make('status')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.status'))
                            ->required()
                            ->options([
                                'scheduled' => __('filament-resources.contract.relation_managers.visits.status_options.scheduled'),
                                'in_progress' => __('filament-resources.contract.relation_managers.visits.status_options.in_progress'),
                                'completed' => __('filament-resources.contract.relation_managers.visits.status_options.completed'),
                                'canceled' => __('filament-resources.contract.relation_managers.visits.status_options.canceled'),
                            ])
                            ->default('scheduled')
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state == 'completed') {
                                    $set('completed_at', now());
                                } elseif ($state == 'in_progress') {
                                    $set('started_at', now());
                                }
                            }),
                        Forms\Components\DateTimePicker::make('started_at')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.started_at'))
                            ->visible(fn (callable $get) => in_array($get('status'), ['in_progress', 'completed']))
                            ->helperText(__('filament-resources.contract.relation_managers.visits.form.started_at_helper')),
                        Forms\Components\DateTimePicker::make('completed_at')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.completed_at'))
                            ->visible(fn (callable $get) => $get('status') == 'completed')
                            ->helperText(__('filament-resources.contract.relation_managers.visits.form.completed_at_helper')),
                        Forms\Components\Textarea::make('findings')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.findings'))
                            ->placeholder(__('filament-resources.contract.relation_managers.visits.form.findings_placeholder'))
                            ->columnSpan('full'),
                        Forms\Components\Textarea::make('actions_taken')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.actions_taken'))
                            ->placeholder(__('filament-resources.contract.relation_managers.visits.form.actions_taken_placeholder'))
                            ->columnSpan('full')
                            ->visible(fn (callable $get) => in_array($get('status'), ['in_progress', 'completed'])),
                        Forms\Components\Textarea::make('recommendations')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.recommendations'))
                            ->placeholder(__('filament-resources.contract.relation_managers.visits.form.recommendations_placeholder'))
                            ->columnSpan('full')
                            ->visible(fn (callable $get) => $get('status') == 'completed'),
                        Forms\Components\Textarea::make('notes')
                            ->label(__('filament-resources.contract.relation_managers.visits.form.notes'))
                            ->placeholder(__('filament-resources.contract.relation_managers.visits.form.notes_placeholder'))
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('technician.name')
                    ->label(__('filament-resources.contract.relation_managers.visits.table.technician'))
                    ->searchable()
                    ->sortable()
                    ->placeholder(__('filament-resources.contract.relation_managers.visits.table.technician_placeholder')),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->label(__('filament-resources.contract.relation_managers.visits.table.scheduled_at'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->tooltip(__('filament-resources.contract.relation_managers.visits.table.scheduled_at_tooltip')),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('filament-resources.contract.relation_managers.visits.table.status'))
                    ->badge()
                    ->colors([
                        'primary' => 'scheduled',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'danger' => 'canceled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'scheduled' => __('filament-resources.contract.relation_managers.visits.status_options.scheduled'),
                        'in_progress' => __('filament-resources.contract.relation_managers.visits.status_options.in_progress'),
                        'completed' => __('filament-resources.contract.relation_managers.visits.status_options.completed'),
                        'canceled' => __('filament-resources.contract.relation_managers.visits.status_options.canceled'),
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('started_at')
                    ->label(__('filament-resources.contract.relation_managers.visits.table.started_at'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(__('filament-resources.contract.relation_managers.visits.table.started_at_tooltip')),
                Tables\Columns\TextColumn::make('completed_at')
                    ->label(__('filament-resources.contract.relation_managers.visits.table.completed_at'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(__('filament-resources.contract.relation_managers.visits.table.completed_at_tooltip')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources.contract.relation_managers.visits.table.created_at'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(__('filament-resources.contract.relation_managers.visits.table.created_at_tooltip')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('technician_id')
                    ->label(__('filament-resources.contract.relation_managers.visits.filters.technician_id'))
                    ->placeholder(__('filament-resources.contract.relation_managers.visits.filters.technician_id_placeholder'))
                    ->options(function () {
                        return User::where('role', 'technician')
                            ->pluck('name', 'id')
                            ->toArray();
                    }),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources.contract.relation_managers.visits.filters.status'))
                    ->placeholder(__('filament-resources.contract.relation_managers.visits.filters.status_placeholder'))
                    ->options([
                        'scheduled' => __('filament-resources.contract.relation_managers.visits.status_options.scheduled'),
                        'in_progress' => __('filament-resources.contract.relation_managers.visits.status_options.in_progress'),
                        'completed' => __('filament-resources.contract.relation_managers.visits.status_options.completed'),
                        'canceled' => __('filament-resources.contract.relation_managers.visits.status_options.canceled'),
                    ]),
                Tables\Filters\Filter::make('scheduled_today')
                    ->label(__('filament-resources.contract.relation_managers.visits.filters.scheduled_today'))
                    ->query(fn (Builder $query): Builder => $query->whereDate('scheduled_at', Carbon::today())),
                Tables\Filters\Filter::make('scheduled_this_week')
                    ->label(__('filament-resources.contract.relation_managers.visits.filters.scheduled_this_week'))
                    ->query(fn (Builder $query): Builder => $query->whereBetween('scheduled_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])),
                Tables\Filters\Filter::make('completed_this_month')
                    ->label(__('filament-resources.contract.relation_managers.visits.filters.completed_this_month'))
                    ->query(fn (Builder $query): Builder => $query->whereMonth('completed_at', Carbon::now()->month)->whereYear('completed_at', Carbon::now()->year)),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.create'))
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.create_modal_heading'))
                    ->mutateFormDataUsing(function (array $data, $livewire) {
                        // Set the contract_id
                        $data['contract_id'] = $livewire->ownerRecord->id;

                        return $data;
                    })
                    ->before(function (array $data, $livewire) {
                        /** @var Contract $contract */
                        $contract = $livewire->ownerRecord;

                        // Check if contract has remaining visits
                        if ($contract->remaining_visits_count <= 0) {
                            Notification::make()
                                ->title(__('filament-resources.contract.relation_managers.visits.messages.no_visits_remaining'))
                                ->body(__('filament-resources.contract.relation_managers.visits.messages.no_visits_remaining_body', ['visits_included' => $contract->visits_included]))
                                ->danger()
                                ->send();

                            // Stop the action from proceeding
                            return false;
                        }

                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.view'))
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.view_modal_heading')),
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.edit'))
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.edit_modal_heading')),
                Tables\Actions\Action::make('complete')
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.complete'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.complete_modal_heading'))
                    ->modalDescription(__('filament-resources.contract.relation_managers.visits.actions.complete_modal_description'))
                    ->modalSubmitActionLabel(__('filament-resources.contract.relation_managers.visits.actions.complete_confirm'))
                    ->visible(fn ($record) => $record->status === 'in_progress')
                    ->action(function ($record) {
                        $record->status = 'completed';
                        $record->completed_at = now();
                        $record->save();
                    }),
                Tables\Actions\Action::make('start')
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.start'))
                    ->icon('heroicon-o-play')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.start_modal_heading'))
                    ->modalDescription(__('filament-resources.contract.relation_managers.visits.actions.start_modal_description'))
                    ->modalSubmitActionLabel(__('filament-resources.contract.relation_managers.visits.actions.start_confirm'))
                    ->visible(fn ($record) => $record->status === 'scheduled')
                    ->action(function ($record) {
                        $record->status = 'in_progress';
                        $record->started_at = now();
                        $record->save();
                    }),
                Tables\Actions\Action::make('cancel')
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.cancel'))
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.cancel_modal_heading'))
                    ->modalDescription(__('filament-resources.contract.relation_managers.visits.actions.cancel_modal_description'))
                    ->modalSubmitActionLabel(__('filament-resources.contract.relation_managers.visits.actions.cancel_confirm'))
                    ->visible(fn ($record) => in_array($record->status, ['scheduled', 'in_progress']))
                    ->action(function ($record) {
                        $record->status = 'canceled';
                        $record->save();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources.contract.relation_managers.visits.actions.delete'))
                    ->modalHeading(__('filament-resources.contract.relation_managers.visits.actions.delete_modal_heading'))
                    ->modalDescription(__('filament-resources.contract.relation_managers.visits.actions.delete_modal_description'))
                    ->modalSubmitActionLabel(__('filament-resources.contract.relation_managers.visits.actions.delete_confirm')),
            ])
            /*->bulkActions([
                Tables\Actions\BulkAction::make('bulk_complete')
                    ->label(__('filament-resources.visit.actions.bulk_complete'))
                    ->color('success')
                    ->icon('heroicon-o-check-circle')
                    ->requiresConfirmation()
                    ->deselectRecordsAfterCompletion()
                    ->action(function ($records) {
                        foreach ($records as $record) {
                            if ($record->status === 'in_progress') {
                                $record->status = 'completed';
                                $record->completed_at = now();
                                $record->save();
                            }
                        }
                    }),
                Tables\Actions\DeleteBulkAction::make(),
            ])*/
            ->defaultSort('scheduled_at');
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
