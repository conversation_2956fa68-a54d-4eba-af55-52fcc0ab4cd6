<?php

namespace App\Filament\Resources\ContractResource\RelationManagers;

use App\Models\TechnicianReport;
use App\Models\MaintenanceRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class TechnicianReportsRelationManager extends RelationManager
{
    protected static string $relationship = 'technicianReports';

    protected static ?string $recordTitleAttribute = 'id';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('filament-resources.contract.relation_managers.technician_reports.title');
    }

    public function getRelationship(): Relation
    {
        // Get technician reports through maintenance requests
        return $this->getOwnerRecord()
            ->maintenanceRequests()
            ->with(['technicianReports.technician', 'technicianReports.checklistItems']);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.report_id'))
                    ->formatStateUsing(fn ($state) => "#{$state}")
                    ->sortable(),

                Tables\Columns\TextColumn::make('maintenanceRequest.request_number')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.maintenance_request'))
                    ->searchable()
                    ->sortable()
                    ->url(fn (TechnicianReport $record): string =>
                        route('filament.admin.resources.maintenance-requests.view', $record->maintenance_request_id)
                    ),

                Tables\Columns\TextColumn::make('technician.name')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.technician'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.status'))
                    ->colors([
                        'secondary' => TechnicianReport::STATUS_DRAFT,
                        'warning' => TechnicianReport::STATUS_SUBMITTED,
                        'success' => TechnicianReport::STATUS_APPROVED,
                        'danger' => TechnicianReport::STATUS_REJECTED,
                    ])
                    ->formatStateUsing(fn (string $state): string =>
                        __("technician.resources.report.status_options.{$state}")
                    ),

                Tables\Columns\TextColumn::make('completion_percentage')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.completion'))
                    ->formatStateUsing(fn ($state) => "{$state}%")
                    ->color(fn ($state) => $state >= 80 ? 'success' : ($state >= 50 ? 'warning' : 'danger'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('time_spent_minutes')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.time_spent'))
                    ->formatStateUsing(fn ($state) => $state ? "{$state} " . __('technician.resources.report.fields.time_spent_suffix') : '-')
                    ->sortable(),

                Tables\Columns\TextColumn::make('attachments')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.attachments'))
                    ->formatStateUsing(function ($state) {
                        if (empty($state) || !is_array($state)) {
                            return '0';
                        }

                        $count = count($state);
                        $imageCount = 0;
                        $documentCount = 0;

                        foreach ($state as $file) {
                            if (is_string($file)) {
                                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                                    $imageCount++;
                                } else {
                                    $documentCount++;
                                }
                            }
                        }

                        $parts = [];
                        if ($imageCount > 0) {
                            $parts[] = $imageCount . ' ' . __('technician.resources.report.columns.images');
                        }
                        if ($documentCount > 0) {
                            $parts[] = $documentCount . ' ' . __('technician.resources.report.columns.documents');
                        }

                        return $count . ' ' . __('technician.resources.report.columns.files') .
                               ($parts ? ' (' . implode(', ', $parts) . ')' : '');
                    })
                    ->badge()
                    ->color(fn ($state) => empty($state) || !is_array($state) ? 'gray' : 'success'),

                Tables\Columns\TextColumn::make('submitted_at')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.submitted_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.columns.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.filters.status'))
                    ->options(TechnicianReport::getStatusOptions()),

                Tables\Filters\SelectFilter::make('technician_id')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.filters.technician'))
                    ->relationship('technician', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('maintenance_request_id')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.filters.maintenance_request'))
                    ->options(function () {
                        return $this->getOwnerRecord()
                            ->maintenanceRequests()
                            ->pluck('request_number', 'id');
                    })
                    ->searchable(),

                Tables\Filters\Filter::make('has_attachments')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.filters.has_attachments'))
                    ->query(fn (Builder $query): Builder =>
                        $query->whereNotNull('attachments')->where('attachments', '!=', '[]')
                    ),

                Tables\Filters\Filter::make('submitted_today')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.filters.submitted_today'))
                    ->query(fn (Builder $query): Builder =>
                        $query->whereDate('submitted_at', today())
                    ),
            ])
            ->headerActions([
                // No create action - reports are created by technicians
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.actions.view'))
                    ->modalHeading(fn (TechnicianReport $record): string =>
                        __('filament-resources.contract.relation_managers.technician_reports.actions.view_modal_heading', ['id' => $record->id])
                    ),

                Tables\Actions\Action::make('view_full')
                    ->label(__('filament-resources.contract.relation_managers.technician_reports.actions.view_full'))
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->url(fn (TechnicianReport $record): string =>
                        route('filament.admin.resources.technician-reports.view', $record)
                    )
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                /*Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn (): bool => auth()->user()->can('delete', TechnicianReport::class)),
                ]),*/
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('filament-resources.contract.relation_managers.technician_reports.empty_state.heading'))
            ->emptyStateDescription(__('filament-resources.contract.relation_managers.technician_reports.empty_state.description'))
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make(__('filament-resources.contract.relation_managers.technician_reports.infolist.basic_info'))
                    ->schema([
                        Infolists\Components\TextEntry::make('id')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.report_id'))
                            ->formatStateUsing(fn ($state) => "#{$state}"),

                        Infolists\Components\TextEntry::make('maintenanceRequest.request_number')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.maintenance_request')),

                        Infolists\Components\TextEntry::make('technician.name')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.technician')),

                        Infolists\Components\TextEntry::make('status')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.status'))
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                TechnicianReport::STATUS_DRAFT => 'secondary',
                                TechnicianReport::STATUS_SUBMITTED => 'warning',
                                TechnicianReport::STATUS_APPROVED => 'success',
                                TechnicianReport::STATUS_REJECTED => 'danger',
                                default => 'secondary',
                            })
                            ->formatStateUsing(fn (string $state): string =>
                                __("technician.resources.report.status_options.{$state}")
                            ),

                        Infolists\Components\TextEntry::make('time_spent_minutes')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.time_spent'))
                            ->formatStateUsing(fn ($state) => $state ? "{$state} " . __('technician.resources.report.fields.time_spent_suffix') : '-'),

                        Infolists\Components\TextEntry::make('submitted_at')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.submitted_at'))
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make(__('filament-resources.contract.relation_managers.technician_reports.infolist.work_details'))
                    ->schema([
                        Infolists\Components\TextEntry::make('work_summary')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.work_summary'))
                            ->markdown()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('findings')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.findings'))
                            ->markdown()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('recommendations')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.recommendations'))
                            ->markdown()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make(__('filament-resources.contract.relation_managers.technician_reports.infolist.attachments'))
                    ->schema([
                        Infolists\Components\TextEntry::make('attachments')
                            ->label(__('filament-resources.contract.relation_managers.technician_reports.infolist.files'))
                            ->formatStateUsing(function ($state) {
                                if (empty($state) || !is_array($state)) {
                                    return __('filament-resources.contract.relation_managers.technician_reports.infolist.no_attachments');
                                }

                                return collect($state)->map(function ($file) {
                                    $filename = basename($file);
                                    return "• {$filename}";
                                })->join("\n");
                            })
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (TechnicianReport $record): bool => !empty($record->attachments)),
            ]);
    }

    protected function getTableQuery(): Builder
    {
        // Get all technician reports for maintenance requests associated with this contract
        return TechnicianReport::query()
            ->whereHas('maintenanceRequest', function (Builder $query) {
                $query->where('contract_id', $this->getOwnerRecord()->id);
            })
            ->with([
                'technician:id,name',
                'maintenanceRequest:id,request_number,contract_id',
                'checklistItems'
            ]);
    }
}
