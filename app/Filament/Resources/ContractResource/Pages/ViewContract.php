<?php

namespace App\Filament\Resources\ContractResource\Pages;

use Filament\Forms;
use App\Models\User;
use App\Models\Visit;
use App\Models\Contract;
use Filament\Actions\Action;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\FontWeight;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Tabs;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Filament\Infolists\Components\Section;
use App\Filament\Resources\ContractResource;
use Filament\Infolists\Components\TextEntry;

class ViewContract extends ViewRecord
{
    protected static string $resource = ContractResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        // Contract Details column
                        Section::make(__('filament-resources/contract.view.contract_details'))
                            ->schema([
                                TextEntry::make('contract_number')
                                    ->label(__('filament-resources/contract.fields.contract_number'))
                                    ->weight(FontWeight::Bold)
                                    ->size(TextEntry\TextEntrySize::Large),
                                TextEntry::make('start_date')
                                    ->label(__('filament-resources/contract.fields.start_date'))
                                    ->date(),
                                TextEntry::make('end_date')
                                    ->label(__('filament-resources/contract.fields.end_date'))
                                    ->date(),
                                TextEntry::make('status')
                                    ->label(__('filament-resources/contract.fields.status'))
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'active' => 'success',
                                        'pending' => 'warning',
                                        'expired' => 'danger',
                                        'terminated' => 'secondary',
                                        default => 'gray',
                                    }),
                                TextEntry::make('is_active')
                                    ->label(__('filament-resources/contract.view.active_contract'))
                                    ->getStateUsing(fn(Contract $record): string => $record->is_active ? __('Yes') : __('No')
                                    ),
                            ])
                            ->columnSpan(1),

                        // Client Information column
                        Section::make(__('filament-resources/contract.view.client_information'))
                            ->schema([
                                TextEntry::make('client.name')
                                    ->label(__('filament-resources/contract.columns.client.name'))
                                    ->weight(FontWeight::Bold)
                                    ->url(fn($record) => $record->client_id
                                        ? url('/admin/clients/' . $record->client_id)
                                        : null)
                                    ->openUrlInNewTab(),
                                TextEntry::make('client.phone')
                                    ->label(__('Phone'))
                                    ->placeholder(__('Not available')),
                                TextEntry::make('client.email')
                                    ->label(__('Email'))
                                    ->placeholder(__('Not available')),
                                TextEntry::make('contract_type.name')
                                    ->label(__('filament-resources/contract.columns.contractType.name')),
                            ])
                            ->columnSpan(1),

                        // Visits & Financials column
                        Section::make(__('filament-resources/contract.view.visits_financials'))
                            ->schema([
                                TextEntry::make('contract_value')
                                    ->label(__('filament-resources/contract.fields.contract_value'))
                                    ->money('SAR')
                                    ->size(TextEntry\TextEntrySize::Large)
                                    ->color('success')
                                    ->weight(FontWeight::Bold),
                                Grid::make(3)
                                    ->schema([
                                        TextEntry::make('visits_included')
                                            ->label(__('filament-resources/contract.view.included_visits'))
                                            ->badge()
                                            ->color('info'),
                                        TextEntry::make('completed_visits_count')
                                            ->label(__('filament-resources/contract.view.completed'))
                                            ->badge()
                                            ->color('success'),
                                        TextEntry::make('remaining_visits_count')
                                            ->label(__('filament-resources/contract.view.remaining'))
                                            ->badge()
                                            ->color(fn($record) => $record->remaining_visits_count > 0 ? 'success' : 'danger'),
                                    ]),
                                TextEntry::make('can_schedule_more_visits')
                                    ->label(__('filament-resources/contract.view.can_schedule_more_visits'))
                                    ->getStateUsing(fn(Contract $record): string => $record->can_schedule_more_visits ? __('Yes') : __('No')
                                    ),
                            ])
                            ->columnSpan(1),
                    ]),

                // Upcoming Visits section
                Section::make(__('filament-resources/contract.view.upcoming_visits'))
                    ->collapsible()
                    ->schema([
                        TextEntry::make('upcoming_visits')
                            ->label('')
                            ->getStateUsing(function (Contract $record) {
                                $upcomingVisits = $record->visits()
                                    ->whereIn('status', ['scheduled', 'in_progress'])
                                    ->orderBy('scheduled_at', 'asc')
                                    ->limit(5)
                                    ->get();

                                if ($upcomingVisits->isEmpty()) {
                                    return __('filament-resources/contract.view.no_upcoming_visits');
                                }

                                return null;
                            })
                            ->visible(fn(Contract $record) => $record->visits()
                                    ->whereIn('status', ['scheduled', 'in_progress'])
                                    ->count() === 0
                            ),

                        // We'll use multiple TextEntry components for each visit
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('upcoming_visit_1_date')
                                    ->label(__('filament-resources/contract.view.scheduled_for'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visit = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->first();

                                        return $visit ? $visit->scheduled_at : null;
                                    })
                                    ->dateTime()
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 0
                                    ),

                                TextEntry::make('upcoming_visit_1_technician')
                                    ->label(__('filament-resources/contract.view.technician'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visit = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->first();

                                        return $visit && $visit->technician ? $visit->technician->name : __('filament-resources/contract.view.not_assigned');
                                    })
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 0
                                    ),

                                TextEntry::make('upcoming_visit_1_status')
                                    ->label(__('filament-resources/contract.fields.status'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visit = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->first();

                                        return $visit ? $visit->status : null;
                                    })
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'scheduled' => 'primary',
                                        'in_progress' => 'warning',
                                        'completed' => 'success',
                                        'canceled' => 'danger',
                                        default => 'gray',
                                    })
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 0
                                    ),

                                TextEntry::make('upcoming_visit_1_completed')
                                    ->label(__('filament-resources/contract.view.completed'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visit = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->first();

                                        return $visit ? $visit->completed_at : null;
                                    })
                                    ->dateTime()
                                    ->placeholder(__('filament-resources/contract.view.not_completed_yet'))
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 0
                                    ),
                            ]),

                        // Second visit (if it exists)
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('upcoming_visit_2_date')
                                    ->label(__('filament-resources/contract.view.scheduled_for'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visits = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->limit(2)
                                            ->get();

                                        return $visits->count() > 1 ? $visits[1]->scheduled_at : null;
                                    })
                                    ->dateTime()
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 1
                                    ),

                                TextEntry::make('upcoming_visit_2_technician')
                                    ->label(__('filament-resources/contract.view.technician'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visits = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->limit(2)
                                            ->get();

                                        return $visits->count() > 1 && $visits[1]->technician
                                            ? $visits[1]->technician->name
                                            : __('filament-resources/contract.view.not_assigned');
                                    })
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 1
                                    ),

                                TextEntry::make('upcoming_visit_2_status')
                                    ->label(__('filament-resources/contract.fields.status'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visits = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->limit(2)
                                            ->get();

                                        return $visits->count() > 1 ? $visits[1]->status : null;
                                    })
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'scheduled' => 'primary',
                                        'in_progress' => 'warning',
                                        'completed' => 'success',
                                        'canceled' => 'danger',
                                        default => 'gray',
                                    })
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 1
                                    ),

                                TextEntry::make('upcoming_visit_2_completed')
                                    ->label(__('filament-resources/contract.view.completed'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visits = $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->orderBy('scheduled_at', 'asc')
                                            ->limit(2)
                                            ->get();

                                        return $visits->count() > 1 ? $visits[1]->completed_at : null;
                                    })
                                    ->dateTime()
                                    ->placeholder(__('filament-resources/contract.view.not_completed_yet'))
                                    ->visible(fn(Contract $record) => $record->visits()
                                            ->whereIn('status', ['scheduled', 'in_progress'])
                                            ->count() > 1
                                    ),
                            ]),
                    ]),

                // Contract Details tab
                Tabs::make(__('filament-resources/contract.view.additional_information'))
                    ->tabs([
                        Tabs\Tab::make(__('filament-resources/contract.view.visit_history'))
                            ->schema([
                                // For visit history, we'll provide a summary text instead of using RepeatableEntry
                                TextEntry::make('visit_history_summary')
                                    ->label(__('filament-resources/contract.view.visit_history'))
                                    ->getStateUsing(function (Contract $record) {
                                        $visits = $record->visits()->count();
                                        $completed = $record->visits()->where('status', 'completed')->count();
                                        $scheduled = $record->visits()->where('status', 'scheduled')->count();
                                        $inProgress = $record->visits()->where('status', 'in_progress')->count();
                                        $canceled = $record->visits()->where('status', 'canceled')->count();

                                        if ($visits === 0) {
                                            return __('filament-resources/contract.view.no_visits_scheduled');
                                        }

                                        return __('filament-resources/contract.view.visit_history_summary', [
                                            'visits' => $visits,
                                            'completed' => $completed,
                                            'scheduled' => $scheduled,
                                            'in_progress' => $inProgress,
                                            'canceled' => $canceled
                                        ]);
                                    }),
                            ]),

                        Tabs\Tab::make(__('filament-resources/contract.view.contract_details_tab'))
                            ->schema([
                                TextEntry::make('terms')
                                    ->label(__('filament-resources/contract.fields.terms'))
                                    ->markdown()
                                    ->placeholder(__('No terms specified'))
                                    ->columnSpanFull(),
                                TextEntry::make('notes')
                                    ->label(__('filament-resources/contract.fields.notes'))
                                    ->markdown()
                                    ->placeholder(__('No notes available'))
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('schedule_visit')
                ->label(__('filament-resources/contract.actions.schedule_visit'))
                ->color('primary')
                ->icon('heroicon-o-calendar')
                ->visible(fn(Contract $record): bool => $record->is_active && $record->remaining_visits_count > 0)
                ->form([
                    Forms\Components\Select::make('technician_id')
                        ->label(__('filament-resources/visit.fields.technician_id'))
                        ->options(function () {
                            return User::where('role', 'technician')
                                ->pluck('name', 'id')
                                ->toArray();
                        })
                        ->searchable()
                        ->preload(),
                    Forms\Components\DateTimePicker::make('scheduled_at')
                        ->label(__('filament-resources/visit.fields.scheduled_at'))
                        ->required()
                        ->default(now()->addDay()->setHour(9)->setMinute(0)->setSecond(0)),
                    Forms\Components\Textarea::make('notes')
                        ->label(__('filament-resources/visit.fields.notes')),
                ])
                ->action(function (array $data, Contract $record): void {
                    // Check if contract has remaining visits
                    if ($record->remaining_visits_count <= 0) {
                        Notification::make()
                            ->title(__('filament-resources/visit.notifications.no_visits_remaining'))
                            ->body(__('filament-resources/visit.notifications.no_visits_remaining_body', [
                                'visits_included' => $record->visits_included,
                                'total_visits' => $record->total_visits_count,
                            ]))
                            ->danger()
                            ->send();

                        return;
                    }

                    // Create a new visit
                    $visit = new Visit();
                    $visit->contract_id = $record->id;
                    $visit->technician_id = $data['technician_id'];
                    $visit->scheduled_at = $data['scheduled_at'];
                    $visit->status = 'scheduled';
                    $visit->notes = $data['notes'] ?? null;
                    $visit->save();

                    // Show notification
                    Notification::make()
                        ->title(__('filament-resources/visit.notifications.visit_scheduled'))
                        ->body(__('filament-resources/visit.notifications.visit_scheduled_body', [
                            'date' => $visit->scheduled_at->format('Y-m-d h:i A'),
                            'remaining' => $record->remaining_visits_count - 1,
                        ]))
                        ->success()
                        ->send();

                    $this->refreshFormData([
                        'remaining_visits_count',
                        'can_schedule_more_visits',
                    ]);
                }),

            Action::make('print')
                ->label(__('filament-resources/contract.view.print_contract'))
                ->color('gray')
                ->icon('heroicon-o-printer')
                ->url(fn(Contract $record): string => route('contract.print', $record))
                ->openUrlInNewTab()
                ->visible(fn(Contract $record): bool => auth()->user()->can('view', $record)),
        ];
    }
}
