<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Payment;
use App\Models\MaintenanceRequest;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use App\Filament\Resources\PaymentResource\Pages;
use App\Filament\Resources\PaymentResource\RelationManagers;

class PaymentResource extends Resource
{
    protected static ?string $model = Payment::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.financial_management');
    }

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.payment.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.payment.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('payment_number')
                            ->label(__('filament-resources/payment.fields.payment_number'))
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->default(function () {
                                $latestPayment = Payment::latest()->first();
                                $nextId = $latestPayment ? $latestPayment->id + 1 : 1;
                                return 'PAY-' . str_pad("$nextId", 6, '0', STR_PAD_LEFT);
                            }),
                        Forms\Components\Select::make('contract_id')
                            ->label(__('filament-resources/payment.fields.contract_id'))
                            ->relationship('contract', 'contract_number')
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                $set('maintenance_request_id', null);
                            }),
                        Forms\Components\Select::make('maintenance_request_id')
                            ->label(__('filament-resources/payment.fields.maintenance_request_id'))
                            ->relationship('maintenanceRequest', 'request_number', function (callable $get, $query) {
                                $contractId = $get('contract_id');
                                if ($contractId) {
                                    return $query->where('contract_id', $contractId);
                                }
                                return $query;
                            }),
                        Forms\Components\TextInput::make('amount')
                            ->label(__('filament-resources/payment.fields.amount'))
                            ->required()
                            ->numeric()
                            ->prefix('SAR'),
                        Forms\Components\DatePicker::make('payment_date')
                            ->label(__('filament-resources/payment.fields.payment_date'))
                            ->required()
                            ->default(now()),
                        Forms\Components\Select::make('payment_method')
                            ->label(__('filament-resources/payment.fields.payment_method'))
                            ->required()
                            ->options([
                                'cash' => __('filament-resources/payment.payment_methods.cash'),
                                'bank_transfer' => __('filament-resources/payment.payment_methods.bank_transfer'),
                                'check' => __('filament-resources/payment.payment_methods.check'),
                                'credit_card' => __('filament-resources/payment.payment_methods.credit_card'),
                                'online' => __('filament-resources/payment.payment_methods.online'),
                            ]),
                        Forms\Components\TextInput::make('reference_number')
                            ->label(__('filament-resources/payment.fields.reference_number'))
                            ->maxLength(255),
                        Forms\Components\Select::make('status')
                            ->label(__('filament-resources/payment.fields.status'))
                            ->required()
                            ->options([
                                'pending' => __('filament-resources/payment.status_options.pending'),
                                'completed' => __('filament-resources/payment.status_options.completed'),
                                'failed' => __('filament-resources/payment.status_options.failed'),
                                'refunded' => __('filament-resources/payment.status_options.refunded'),
                            ])
                            ->default('pending'),
                        Forms\Components\Textarea::make('notes')
                            ->label(__('filament-resources/payment.fields.notes'))
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('payment_number')
                    ->label(__('filament-resources/payment.columns.payment_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('contract.contract_number')
                    ->label(__('filament-resources/payment.columns.contract.contract_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('contract.client.name')
                    ->label(__('filament-resources/payment.columns.contract.client.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('maintenanceRequest.request_number')
                    ->label(__('filament-resources/payment.columns.maintenanceRequest.request_number'))
                    ->toggleable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('filament-resources/payment.columns.amount'))
                    ->riyal()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_date')
                    ->label(__('filament-resources/payment.columns.payment_date'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label(__('filament-resources/payment.columns.payment_method')),
                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('filament-resources/payment.columns.status'))
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'completed',
                        'danger' => 'failed',
                        'secondary' => 'refunded',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('contract_id')
                    ->label(__('filament-resources/payment.filters.contract_id'))
                    ->relationship('contract', 'contract_number'),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label(__('filament-resources/payment.filters.payment_method'))
                    ->options([
                        'cash' => __('filament-resources/payment.payment_methods.cash'),
                        'bank_transfer' => __('filament-resources/payment.payment_methods.bank_transfer'),
                        'check' => __('filament-resources/payment.payment_methods.check'),
                        'credit_card' => __('filament-resources/payment.payment_methods.credit_card'),
                        'online' => __('filament-resources/payment.payment_methods.online'),
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources/payment.filters.status'))
                    ->options([
                        'pending' => __('filament-resources/payment.status_options.pending'),
                        'completed' => __('filament-resources/payment.status_options.completed'),
                        'failed' => __('filament-resources/payment.status_options.failed'),
                        'refunded' => __('filament-resources/payment.status_options.refunded'),
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('filament-resources/payment.actions.view')),
                /*Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/payment.actions.edit')),*/
                /*Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/payment.actions.delete')),*/

                // Add these new actions
                // In PaymentResource.php

                Tables\Actions\Action::make('complete_payment')
                    ->label(__('filament-resources/payment.actions.complete'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (Payment $record) => $record->status === 'pending')
                    ->form([
                        Forms\Components\DatePicker::make('payment_date')
                            ->label(__('filament-resources/payment.fields.payment_date'))
                            ->required()
                            ->default(now()),
                        Forms\Components\Select::make('payment_method')
                            ->label(__('filament-resources/payment.fields.payment_method'))
                            ->required()
                            ->options([
                                'cash' => __('filament-resources/payment.payment_methods.cash'),
                                'bank_transfer' => __('filament-resources/payment.payment_methods.bank_transfer'),
                                'check' => __('filament-resources/payment.payment_methods.check'),
                                'credit_card' => __('filament-resources/payment.payment_methods.credit_card'),
                                'online' => __('filament-resources/payment.payment_methods.online'),
                            ])
                            ->default(function (Payment $record) {
                                return $record->payment_method ?: null;
                            }),
                        Forms\Components\TextInput::make('reference_number')
                            ->label(__('filament-resources/payment.fields.reference_number'))
                            ->required(),
                        Forms\Components\Textarea::make('completion_notes')
                            ->label(__('filament-resources/payment.fields.completion_notes'))
                    ])
                    ->action(function (Payment $record, array $data) {
                        $record->status = 'completed';
                        $record->payment_date = $data['payment_date'];
                        $record->payment_method = $data['payment_method'];
                        $record->reference_number = $data['reference_number'];

                        // Append completion notes to existing notes if any
                        if (!empty($data['completion_notes'])) {
                            $record->notes = $record->notes
                                ? $record->notes . "\n\n" . __('filament-resources/payment.fields.completion_notes') . ": " . $data['completion_notes']
                                : __('filament-resources/payment.fields.completion_notes') . ": " . $data['completion_notes'];
                        }

                        $record->save();

                        $record->maintenanceRequest->update([
                            'status' => 'paid',
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/payment.notifications.payment_completed'))
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('mark_failed')
                    ->label(__('filament-resources/payment.actions.mark_failed'))
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn (Payment $record) => $record->status === 'pending')
                    ->requiresConfirmation()
                    ->action(function (Payment $record) {
                        $record->status = 'failed';
                        $record->save();

                        // Also update the related maintenance request if it exists
                        if ($record->maintenanceRequest) {
                            $record->maintenanceRequest->status = MaintenanceRequest::STATUS_CANCELLED;
                            $record->maintenanceRequest->save();
                        }
                    }),

                Tables\Actions\Action::make('mark_refunded')
                    ->label(__('filament-resources/payment.actions.mark_refunded'))
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (Payment $record) => $record->status === 'completed')
                    ->form([
                        Forms\Components\DatePicker::make('refund_date')
                            ->label(__('filament-resources/payment.fields.refund_date'))
                            ->required()
                            ->default(now()),
                        Forms\Components\TextInput::make('refund_reference')
                            ->label(__('filament-resources/payment.fields.refund_reference'))
                            ->required(),
                        Forms\Components\Textarea::make('refund_notes')
                            ->label(__('filament-resources/payment.fields.refund_notes'))
                            ->required()
                    ])
                    ->action(function (Payment $record, array $data) {
                        $record->status = 'refunded';
                        $record->notes = $record->notes . "\n\n" . __('filament-resources/payment.fields.refund_notes') . ": " . $data['refund_notes'];
                        $record->refund_reference_number = $data['refund_reference'];
                        $record->save();

                        // Also update the related maintenance request if it exists
                        if ($record->maintenanceRequest) {
                            $record->maintenanceRequest->status = MaintenanceRequest::STATUS_CANCELLED;
                            $record->maintenanceRequest->save();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DocumentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayments::route('/'),
            //'create' => Pages\CreatePayment::route('/create'),
            //'view' => Pages\ViewPayment::route('/{record}'),
            //'edit' => Pages\EditPayment::route('/{record}/edit'),
        ];
    }
}
