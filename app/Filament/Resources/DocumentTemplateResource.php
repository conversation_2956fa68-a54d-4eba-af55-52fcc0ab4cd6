<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\DocumentTemplate;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Creagia\FilamentCodeField\CodeField;
use Illuminate\Database\Eloquent\Builder;
use App\Services\DocumentTemplateService;
use Dotswan\FilamentCodeEditor\Fields\CodeEditor;
use App\Filament\Resources\DocumentTemplateResource\Pages;
use AbdelhamidErrahmouni\FilamentMonacoEditor\MonacoEditor;

class DocumentTemplateResource extends Resource
{
    protected static ?string $model = DocumentTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.document_management');
    }

    public static function getModelLabel(): string
    {
        return __('navigation.resources.document_template.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.document_template.plural');
    }

    protected static ?int $navigationSort = 30;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('filament-resources/document-template.sections.template_info'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('filament-resources/document-template.fields.name'))
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Select::make('type')
                            ->label(__('filament-resources/document-template.fields.type'))
                            ->options(DocumentTemplateService::getTemplateTypes())
                            ->required()
                            ->disabled(fn($record) => $record !== null)
                            ->disablePlaceholderSelection(),

                        Forms\Components\Textarea::make('content')
                            ->label(__('filament-resources/document-template.fields.content'))
                            ->required()
                            ->rows(15)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make(__('filament-resources/document-template.sections.sync_status'))
                    ->schema([
                        Forms\Components\Toggle::make('is_synced')
                            ->label(__('filament-resources/document-template.fields.is_synced'))
                            ->disabled()
                            ->helperText(function ($record) {
                                if (!$record) return null;

                                if ($record->sync_error) {
                                    return new HtmlString('<span class="text-danger">' . $record->sync_error . '</span>');
                                }

                                if ($record->is_synced && $record->last_synced_at) {
                                    return __('filament-resources/document-template.helper_texts.last_synced', ['date' => $record->last_synced_at->format('Y-m-d H:i:s')]);
                                }

                                return null;
                            }),

                        Forms\Components\TextInput::make('docking_template_id')
                            ->label(__('filament-resources/document-template.fields.docking_template_id'))
                            ->disabled()
                            ->visible(fn($record) => $record && $record->is_synced),

                        Forms\Components\TextInput::make('docking_instance_url')
                            ->label(__('filament-resources/document-template.fields.docking_instance_url'))
                            ->disabled()
                            ->visible(fn($record) => $record && $record->is_synced),
                    ])
                    ->visible(fn($record) => $record !== null)
                    ->collapsible(),

                Forms\Components\Section::make(__('filament-resources/document-template.sections.metadata'))
                    ->schema([
                        Forms\Components\ViewField::make('metadata')
                            ->label(__('filament-resources/document-template.fields.metadata'))
                            ->view('filament.resources.document-template-resource.components.metadata-viewer'),
                    ])
                    ->visible(fn($record) => $record !== null)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-resources/document-template.columns.name'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label(__('filament-resources/document-template.columns.type'))
                    ->formatStateUsing(fn($state) => DocumentTemplateService::getTemplateTypes()[$state] ?? $state)
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_synced')
                    ->label(__('filament-resources/document-template.columns.is_synced'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('last_synced_at')
                    ->label(__('filament-resources/document-template.columns.last_synced_at'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('filament-resources/document-template.columns.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label(__('filament-resources/document-template.filters.type'))
                    ->options(DocumentTemplateService::getTemplateTypes()),

                Tables\Filters\TernaryFilter::make('is_synced')
                    ->label(__('filament-resources/document-template.filters.is_synced'))
                    ->trueLabel(__('filament-resources/document-template.filters.synced'))
                    ->falseLabel(__('filament-resources/document-template.filters.not_synced'))
                    ->placeholder(__('filament-resources/document-template.filters.all')),
            ])
            ->actions([
                Tables\Actions\Action::make('sync')
                    ->label(__('filament-resources/document-template.actions.sync'))
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading(__('filament-resources/document-template.modals.sync_heading'))
                    ->modalDescription(__('filament-resources/document-template.modals.sync_description'))
                    ->action(function (DocumentTemplate $record) {
                        DocumentTemplateService::syncTemplateWithDocking($record->type);

                        // Refresh record from database
                        $record->refresh();

                        // Show notification
                        \Filament\Notifications\Notification::make()
                            ->title($record->is_synced ? __('filament-resources/document-template.notifications.sync_success') : __('filament-resources/document-template.notifications.sync_failed'))
                            ->body($record->is_synced ? null : $record->sync_error)
                            ->success($record->is_synced)
                            ->danger(!$record->is_synced)
                            ->send();
                    }),

                Tables\Actions\Action::make('preview')
                    ->label(__('filament-resources/document-template.actions.preview'))
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(function (DocumentTemplate $record) {
                        return route('filament.admin.resources.document-templates.preview', ['record' => $record]);
                    })
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('generatePdf')
                    ->label(__('filament-resources/document-template.actions.generate_pdf'))
                    ->icon('heroicon-o-document')
                    ->color('warning')
                    ->url(function (DocumentTemplate $record) {
                        return route('filament.admin.resources.document-templates.generate-pdf', ['record' => $record]);
                    })
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make(),

                /*Tables\Actions\ReplicateAction::make()
                    ->excludeAttributes(['docking_template_id', 'docking_instance_url', 'is_synced', 'last_synced_at', 'sync_error'])
                    ->afterReplicaSaved(function (DocumentTemplate $replica) {
                        $replica->update([
                            'is_synced' => false,
                            'metadata' => array_merge($replica->metadata ?? [], [
                                'version' => 1,
                                'created_at' => now()->toIso8601String(),
                                'duplicated_from' => $replica->id,
                            ]),
                        ]);
                    }),*/

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('syncSelected')
                    ->label(__('filament-resources/document-template.actions.sync_selected'))
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading(__('filament-resources/document-template.modals.bulk_sync_heading'))
                    ->modalDescription(__('filament-resources/document-template.modals.bulk_sync_description'))
                    ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                        $successCount = 0;
                        $failCount = 0;

                        foreach ($records as $record) {
                            DocumentTemplateService::syncTemplateWithDocking($record->type);
                            $record->refresh();

                            if ($record->is_synced) {
                                $successCount++;
                            } else {
                                $failCount++;
                            }
                        }

                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/document-template.notifications.bulk_sync_completed'))
                            ->body(__('filament-resources/document-template.notifications.bulk_sync_results', ['success' => $successCount, 'failed' => $failCount]))
                            ->success($failCount === 0)
                            ->warning($failCount > 0 && $successCount > 0)
                            ->danger($successCount === 0 && $failCount > 0)
                            ->send();
                    }),

                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocumentTemplates::route('/'),
            'create' => Pages\CreateDocumentTemplate::route('/create'),
            'edit' => Pages\EditDocumentTemplate::route('/{record}/edit'),
            'preview' => Pages\PreviewDocumentTemplate::route('/{record}/preview'),
            'generate-pdf' => Pages\GeneratePdfDocumentTemplate::route('/{record}/generate-pdf'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery();
    }
}
