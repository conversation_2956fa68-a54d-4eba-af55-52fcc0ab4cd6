<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Client;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use App\Filament\Resources\ClientResource\Pages;
use App\Filament\Resources\ClientResource\RelationManagers;

class ClientResource extends Resource
{
    protected static ?string $model = Client::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.client_management');
    }

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.client.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.client.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('filament-resources/client.fields.name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label(__('filament-resources/client.fields.email'))
                            ->email()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('filament-resources/client.fields.phone'))
                            ->tel()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('national_id')
                            ->label(__('filament-resources/client.fields.national_id'))
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('address')
                            ->label(__('filament-resources/client.fields.address'))
                            ->maxLength(255),
                        Forms\Components\Textarea::make('notes')
                            ->label(__('filament-resources/client.fields.notes'))
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-resources/client.columns.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('filament-resources/client.columns.email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label(__('filament-resources/client.columns.phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('national_id')
                    ->label(__('filament-resources/client.columns.national_id'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('contracts_count')
                    ->label(__('filament-resources/client.columns.contracts_count'))
                    ->counts('contracts'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources/client.columns.created_at'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('filament-resources/client.actions.view')),
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/client.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/client.actions.delete')),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ContractsRelationManager::class,
            RelationManagers\MaintenanceRequestsRelationManager::class,
            RelationManagers\DocumentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClients::route('/'),
            'create' => Pages\CreateClient::route('/create'),
            'view' => Pages\ViewClient::route('/{record}'),
            'edit' => Pages\EditClient::route('/{record}/edit'),
        ];
    }
}
