<?php

namespace App\Filament\Resources\MaintenanceRequestResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class DocumentsRelationManager extends RelationManager
{
    protected static string $relationship = 'documents';
    protected static ?string $recordTitleAttribute = 'title';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\FileUpload::make('file_path')
                    ->required()
                    ->disk('public')
                    ->directory('documents/maintenance-requests')
                    ->visibility('public')
                    ->acceptedFileTypes([
                        'application/pdf',
                        'image/*',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.ms-excel',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    ])
                    ->maxSize(10240),
                Forms\Components\Hidden::make('file_type')
                    ->dehydrateStateUsing(function ($state, callable $get) {
                        if ($get('file_path')) {
                            $path = $get('file_path');
                            $extension = pathinfo($path, PATHINFO_EXTENSION);
                            return $extension;
                        }
                        return $state;
                    }),
                Forms\Components\Hidden::make('file_size')
                    ->dehydrateStateUsing(function ($state, callable $get) {
                        if ($get('file_path')) {
                            $path = storage_path('app/public/' . $get('file_path'));
                            if (file_exists($path)) {
                                $size = filesize($path);
                                return human_filesize($size);
                            }
                        }
                        return $state;
                    }),
                Forms\Components\Hidden::make('uploaded_by')
                    ->default(fn () => auth()->id()),
                Forms\Components\Textarea::make('description')
                    ->columnSpan('full'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title'),
                Tables\Columns\TextColumn::make('file_type')
                    ->formatStateUsing(fn (string $state): string => strtoupper($state)),
                Tables\Columns\TextColumn::make('file_size'),
                Tables\Columns\TextColumn::make('uploader.name')
                    ->label('Uploaded By'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('download')
                    ->url(fn ($record): string => route('document.download', $record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-download'),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
