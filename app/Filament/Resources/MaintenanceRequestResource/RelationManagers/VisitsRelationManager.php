<?php

namespace App\Filament\Resources\MaintenanceRequestResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class VisitsRelationManager extends RelationManager
{
    protected static string $relationship = 'visits';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('technician_id')
                    ->label(__('filament-resources/visit.fields.technician_id'))
                    ->options(User::where('role', 'technician')->pluck('name', 'id'))
                    ->required()
                    ->searchable(),
                Forms\Components\DateTimePicker::make('scheduled_at')
                    ->required(),
                Forms\Components\DateTimePicker::make('started_at'),
                Forms\Components\DateTimePicker::make('completed_at'),
                Forms\Components\Select::make('status')
                    ->required()
                    ->options([
                        'scheduled' => __('filament-resources/visit.status_options.scheduled'),
                        'in_progress' => __('filament-resources/visit.status_options.in_progress'),
                        'completed' => __('filament-resources/visit.status_options.completed'),
                        'canceled' => __('filament-resources/visit.status_options.canceled'),
                    ])
                    ->default('scheduled'),
                Forms\Components\Textarea::make('findings')
                    ->columnSpan('full'),
                Forms\Components\Textarea::make('actions_taken')
                    ->columnSpan('full'),
                Forms\Components\Textarea::make('recommendations')
                    ->columnSpan('full'),
                Forms\Components\Textarea::make('notes')
                    ->columnSpan('full'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('technician.name')
                    ->label('Technician')
                    ->searchable(),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'primary' => 'scheduled',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'danger' => 'canceled',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('technician_id')
                    ->relationship('technician', 'name')
                    ->label('Technician'),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'canceled' => 'Canceled',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
