<?php

namespace App\Filament\Resources\MaintenanceRequestResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use App\Models\Payment;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Database\Eloquent\Model;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';
    protected static ?string $recordTitleAttribute = 'payment_number';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('filament-resources/payment.relation_manager.title');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('payment_number')
                    ->label(__('filament-resources/payment.fields.payment_number'))
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),
                /*Forms\Components\Select::make('contract_id')
                    ->relationship('contract', 'contract_number')
                    ->required()
                    ->disabled()
                    ->dehydrated()
                    ->default(function (RelationManager $livewire) {
                        return $livewire->ownerRecord->contract_id;
                    }),*/
                Forms\Components\TextInput::make('amount')
                    ->label(__('filament-resources/payment.fields.amount'))
                    ->required()
                    ->numeric()
                    ->prefix('SAR'),
                Forms\Components\DatePicker::make('payment_date')
                    ->label(__('filament-resources/payment.fields.payment_date'))
                    ->required()
                    ->default(now()),
                Forms\Components\Select::make('payment_method')
                    ->label(__('filament-resources/payment.fields.payment_method'))
                    ->required()
                    ->options([
                        'cash' => __('filament-resources/payment.payment_methods.cash'),
                        'bank_transfer' => __('filament-resources/payment.payment_methods.bank_transfer'),
                        'check' => __('filament-resources/payment.payment_methods.check'),
                        'credit_card' => __('filament-resources/payment.payment_methods.credit_card'),
                        'online' => __('filament-resources/payment.payment_methods.online'),
                    ]),
                Forms\Components\TextInput::make('reference_number')
                    ->label(__('filament-resources/payment.fields.reference_number'))
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->label(__('filament-resources/payment.fields.status'))
                    ->required()
                    ->options([
                        'pending' => __('filament-resources/payment.status_options.pending'),
                        'completed' => __('filament-resources/payment.status_options.completed'),
                        'failed' => __('filament-resources/payment.status_options.failed'),
                        'refunded' => __('filament-resources/payment.status_options.refunded'),
                    ])
                    ->default('pending'),
                Forms\Components\Textarea::make('notes')
                    ->label(__('filament-resources/payment.fields.notes'))
                    ->columnSpan('full'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('payment_number')
                    ->label(__('filament-resources/payment.columns.payment_number'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('filament-resources/payment.columns.amount'))
                    ->riyal()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_date')
                    ->label(__('filament-resources/payment.columns.payment_date'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label(__('filament-resources/payment.columns.payment_method'))
                    ->formatStateUsing(fn (string $state): string => __('filament-resources/payment.payment_methods.' . $state)),
                Tables\Columns\TextColumn::make('reference_number')
                    ->label(__('filament-resources/payment.columns.reference_number'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('filament-resources/payment.columns.status'))
                    ->formatStateUsing(fn (string $state): string => __('filament-resources/payment.status_options.' . $state))
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'completed',
                        'danger' => 'failed',
                        'secondary' => 'refunded',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources/payment.filters.status'))
                    ->options([
                        'pending' => __('filament-resources/payment.status_options.pending'),
                        'completed' => __('filament-resources/payment.status_options.completed'),
                        'failed' => __('filament-resources/payment.status_options.failed'),
                        'refunded' => __('filament-resources/payment.status_options.refunded'),
                    ]),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label(__('filament-resources/payment.filters.payment_method'))
                    ->options([
                        'cash' => __('filament-resources/payment.payment_methods.cash'),
                        'bank_transfer' => __('filament-resources/payment.payment_methods.bank_transfer'),
                        'check' => __('filament-resources/payment.payment_methods.check'),
                        'credit_card' => __('filament-resources/payment.payment_methods.credit_card'),
                        'online' => __('filament-resources/payment.payment_methods.online'),
                    ]),
            ])
            ->headerActions([
                //Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('filament-resources/payment.actions.view')),
                //Tables\Actions\EditAction::make(),

                // In PaymentsRelationManager.php

                Tables\Actions\Action::make('complete_payment')
                    ->label(__('filament-resources/payment.actions.complete'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn(Payment $record) => $record->status === 'pending')
                    ->form([
                        Forms\Components\DatePicker::make('payment_date')
                            ->label(__('filament-resources/payment.fields.payment_date'))
                            ->required()
                            ->default(now()),
                        Forms\Components\Select::make('payment_method')
                            ->label(__('filament-resources/payment.fields.payment_method'))
                            ->required()
                            ->options([
                                'cash' => __('filament-resources/payment.payment_methods.cash'),
                                'bank_transfer' => __('filament-resources/payment.payment_methods.bank_transfer'),
                                'check' => __('filament-resources/payment.payment_methods.check'),
                                'credit_card' => __('filament-resources/payment.payment_methods.credit_card'),
                                'online' => __('filament-resources/payment.payment_methods.online'),
                            ])
                            ->default(function (Payment $record) {
                                return $record->payment_method ?: null;
                            }),
                        Forms\Components\TextInput::make('reference_number')
                            ->label(__('filament-resources/payment.fields.reference_number'))
                            ->required(),
                        Forms\Components\Textarea::make('completion_notes')
                            ->label(__('filament-resources/payment.fields.completion_notes')),
                    ])
                    ->action(function (Payment $record, array $data) {
                        $record->status = 'completed';
                        $record->payment_date = $data['payment_date'];
                        $record->payment_method = $data['payment_method'];
                        $record->reference_number = $data['reference_number'];

                        // Append completion notes to existing notes if any
                        if (!empty($data['completion_notes'])) {
                            $record->notes = $record->notes
                                ? $record->notes . "\n\n" . __('filament-resources/payment.messages.completion_notes_prefix') . $data['completion_notes']
                                : __('filament-resources/payment.messages.completion_notes_prefix') . $data['completion_notes'];
                        }

                        $record->save();

                        $record->maintenanceRequest->update([
                            'status' => 'paid',
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/payment.notifications.payment_completed'))
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('mark_failed')
                    ->label(__('filament-resources/payment.actions.mark_failed'))
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn(Payment $record) => $record->status === 'pending')
                    ->requiresConfirmation()
                    ->action(function (Payment $record) {
                        $record->status = 'failed';
                        $record->save();

                        // Also update the related maintenance request if it exists
                        if ($record->maintenanceRequest) {
                            $record->maintenanceRequest->status = 'canceled';
                            $record->maintenanceRequest->save();
                        }

                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/payment.notifications.payment_failed'))
                            ->danger()
                            ->send();
                    }),

                Tables\Actions\Action::make('mark_refunded')
                    ->label(__('filament-resources/payment.actions.mark_refunded'))
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn(Payment $record) => $record->status === 'completed')
                    ->form([
                        Forms\Components\DatePicker::make('refund_date')
                            ->label(__('filament-resources/payment.fields.refund_date'))
                            ->required()
                            ->default(now()),
                        Forms\Components\TextInput::make('refund_reference')
                            ->label(__('filament-resources/payment.fields.refund_reference'))
                            ->required(),
                        Forms\Components\Textarea::make('refund_notes')
                            ->label(__('filament-resources/payment.fields.refund_notes'))
                            ->required(),
                    ])
                    ->action(function (Payment $record, array $data) {
                        $record->status = 'refunded';
                        $record->notes = $record->notes . "\n\n" . __('filament-resources/payment.messages.refund_notes_prefix') . $data['refund_notes'];
                        $record->refund_reference_number = $data['refund_reference'];
                        $record->save();

                        // Also update the related maintenance request if it exists
                        if ($record->maintenanceRequest) {
                            $record->maintenanceRequest->status = 'canceled';
                            $record->maintenanceRequest->save();
                        }

                        \Filament\Notifications\Notification::make()
                            ->title(__('filament-resources/payment.notifications.payment_refunded'))
                            ->warning()
                            ->send();
                    }),
                //Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                //Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
