<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Hash;
use App\Filament\Resources\UserResource\Pages;

class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.user_management');
    }

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.user.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.user.plural');
    }

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('filament-resources/user.fields.name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label(__('filament-resources/user.fields.email'))
                            ->email()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('filament-resources/user.fields.phone'))
                            ->tel()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('national_id')
                            ->label(__('filament-resources/user.fields.national_id'))
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label(__('filament-resources/user.fields.email_verified_at')),
                        Forms\Components\TextInput::make('password')
                            ->label(__('filament-resources/user.fields.password'))
                            ->password()
                            ->dehydrateStateUsing(fn($state) => filled($state) ? Hash::make($state) : null)
                            ->dehydrated(fn($state) => filled($state))
                            ->required(fn(string $context): bool => $context === 'create'),
                        Forms\Components\Select::make('role')
                            ->label(__('filament-resources/user.fields.role'))
                            ->required()
                            ->options([
                                'admin' => __('filament-resources/user.role_options.admin'),
                                'manager' => __('filament-resources/user.role_options.manager'),
                                'technician' => __('filament-resources/user.role_options.technician'),
                                'client' => __('filament-resources/user.role_options.client'),
                                'employee' => __('filament-resources/user.role_options.employee'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-resources/user.columns.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('filament-resources/user.columns.email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label(__('filament-resources/user.columns.phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('national_id')
                    ->label(__('filament-resources/user.columns.national_id'))
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('role')
                    ->label(__('filament-resources/user.columns.role'))
                    ->colors([
                        'primary' => 'admin',
                        'success' => 'manager',
                        'warning' => 'technician',
                        'danger' => 'client',
                        'secondary' => 'employee',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources/user.columns.created_at'))
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label(__('filament-resources/user.filters.role'))
                    ->options([
                        'admin' => __('filament-resources/user.role_options.admin'),
                        'manager' => __('filament-resources/user.role_options.manager'),
                        'technician' => __('filament-resources/user.role_options.technician'),
                        'client' => __('filament-resources/user.role_options.client'),
                        'employee' => __('filament-resources/user.role_options.employee'),
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/user.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/user.actions.delete')),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
