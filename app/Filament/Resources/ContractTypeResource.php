<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractTypeResource\Pages;
use App\Filament\Resources\ContractTypeResource\RelationManagers;
use App\Models\ContractType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class ContractTypeResource extends Resource
{
    protected static ?string $model = ContractType::class;
    //protected static ?string $navigationIcon = 'heroicon-o-tag';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.contract_management');
    }

    protected static ?int $navigationSort = 2;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.contract_type.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.contract_type.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('filament-resources/contract-type.fields.name'))
                            ->required()
                            ->maxLength(255)
                            ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                if (!$state) {
                                    return;
                                }
                                $set('slug', Str::slug($state));
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->label(__('filament-resources/contract-type.fields.slug'))
                            ->required()
                            ->maxLength(255)
                            ->unique(ContractType::class, 'slug', ignoreRecord: true),
                        Forms\Components\TextInput::make('icon')
                            ->label(__('filament-resources/contract-type.fields.icon'))
                            ->default('heroicon-o-calendar')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label(__('filament-resources/contract-type.fields.description'))
                            ->required()
                            ->columnSpan('full'),
                        Forms\Components\Repeater::make('benefits')
                            ->label(__('filament-resources/contract-type.fields.benefits'))
                            ->schema([
                                Forms\Components\TextInput::make('value')
                                    ->label(__('filament-resources/contract-type.fields.benefit'))
                                    ->required()
                            ])
                            ->defaultItems(3)
                            ->columnSpan('full')
                            ->createItemButtonLabel(__('filament-resources/contract-type.fields.add_benefit'))
                            ->collapsible()
                            ->afterStateHydrated(function (Forms\Set $set, $state) {
                                // If the state is a JSON string array, convert it to a Repeater-compatible format
                                if (is_array($state) && isset($state[0]) && is_string($state[0])) {
                                    $formattedState = array_map(function ($item) {
                                        return ['value' => $item];
                                    }, $state);

                                    $set('benefits', $formattedState);
                                }
                            })
                            ->dehydrateStateUsing(function ($state) {
                                // Convert the Repeater format back to a simple array of strings
                                if (is_array($state)) {
                                    return array_map(function ($item) {
                                        return $item['value'] ?? '';
                                    }, $state);
                                }

                                return $state;
                            }),
                        Forms\Components\TextInput::make('period')
                            ->label(__('filament-resources/contract-type.fields.period'))
                            ->numeric()
                            ->required()
                            ->helperText(__('filament-resources/contract-type.fields.period_help')),
                        Forms\Components\TextInput::make('visit_limit')
                            ->label(__('filament-resources/contract-type.fields.visit_limit'))
                            ->numeric()
                            ->required()
                            ->integer(),
                        Forms\Components\Toggle::make('is_active')
                            ->label(__('filament-resources/contract-type.fields.is_active'))
                            ->default(true),
                        Forms\Components\TextInput::make('display_order')
                            ->label(__('filament-resources/contract-type.fields.display_order'))
                            ->numeric()
                            ->integer()
                            ->default(0),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-resources/contract-type.columns.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->label(__('filament-resources/contract-type.columns.slug'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('period')
                    ->label(__('filament-resources/contract-type.columns.period'))
                    ->suffix(' ' . __('app.months')),
                Tables\Columns\TextColumn::make('visit_limit')
                    ->label(__('filament-resources/contract-type.columns.visit_limit')),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('filament-resources/contract-type.columns.is_active'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('display_order')
                    ->label(__('filament-resources/contract-type.columns.display_order'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('contracts_count')
                    ->label(__('filament-resources/contract-type.columns.contracts_count'))
                    ->counts('contracts'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources/contract-type.columns.created_at'))
                    ->dateTime()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label(__('filament-resources/contract-type.filters.is_active')),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/contract-type.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/contract-type.actions.delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('display_order');
    }

    public static function getRelations(): array
    {
        return [
            //RelationManagers\ContractsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractTypes::route('/'),
            'create' => Pages\CreateContractType::route('/create'),
            'edit' => Pages\EditContractType::route('/{record}/edit'),
        ];
    }
}
