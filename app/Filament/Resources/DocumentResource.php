<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DocumentResource\Pages;
use App\Filament\Resources\DocumentResource\RelationManagers;
use App\Models\Document;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DocumentResource extends Resource
{
    protected static ?string $model = Document::class;
    protected static bool $isDiscovered = false;
    //protected static ?string $navigationIcon = 'heroicon-o-document';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.document_management');
    }

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.document.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.document.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('filament-resources/document.fields.title'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\FileUpload::make('file_path')
                            ->label(__('filament-resources/document.fields.file_path'))
                            ->required()
                            ->acceptedFileTypes(['application/pdf', 'image/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])
                            ->directory('documents')
                            ->maxSize(10240)
                            ->visibility('private'),
                        Forms\Components\Hidden::make('file_type')
                            ->dehydrateStateUsing(function ($state, callable $get) {
                                if ($get('file_path')) {
                                    $path = $get('file_path');
                                    $extension = pathinfo($path, PATHINFO_EXTENSION);
                                    return $extension;
                                }
                                return $state;
                            }),
                        Forms\Components\Hidden::make('file_size')
                            ->dehydrateStateUsing(function ($state, callable $get) {
                                if ($get('file_path')) {
                                    $path = storage_path('app/public/' . $get('file_path'));
                                    if (file_exists($path)) {
                                        $size = filesize($path);
                                        return human_filesize($size);
                                    }
                                }
                                return $state;
                            }),
                        // Polymorphic relationship fields
                        Forms\Components\Select::make('documentable_type')
                            ->label(__('filament-resources/document.fields.documentable_type'))
                            ->required()
                            ->options([
                                \App\Models\Client::class => __('filament-resources/document.documentable_types.client'),
                                \App\Models\Contract::class => __('filament-resources/document.documentable_types.contract'),
                                \App\Models\MaintenanceRequest::class => __('filament-resources/document.documentable_types.maintenance_request'),
                                \App\Models\Visit::class => __('filament-resources/document.documentable_types.visit'),
                                \App\Models\Payment::class => __('filament-resources/document.documentable_types.payment'),
                            ])
                            ->reactive(),
                        Forms\Components\Select::make('documentable_id')
                            ->label(__('filament-resources/document.fields.documentable_id'))
                            ->required()
                            ->options(function (callable $get) {
                                $type = $get('documentable_type');
                                if (!$type) return [];

                                switch ($type) {
                                    case \App\Models\Client::class:
                                        return \App\Models\Client::pluck('name', 'id');
                                    case \App\Models\Contract::class:
                                        return \App\Models\Contract::pluck('contract_number', 'id');
                                    case \App\Models\MaintenanceRequest::class:
                                        return \App\Models\MaintenanceRequest::pluck('request_number', 'id');
                                    case \App\Models\Visit::class:
                                        return \App\Models\Visit::with('maintenanceRequest')->get()->pluck('maintenanceRequest.request_number', 'id');
                                    case \App\Models\Payment::class:
                                        return \App\Models\Payment::pluck('payment_number', 'id');
                                    default:
                                        return [];
                                }
                            }),
                        Forms\Components\Select::make('uploaded_by')
                            ->label(__('filament-resources/document.fields.uploaded_by'))
                            ->relationship('uploader', 'name')
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label(__('filament-resources/document.fields.description'))
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label(__('filament-resources/document.columns.title'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('file_type')
                    ->label(__('filament-resources/document.columns.file_type'))
                    ->formatStateUsing(fn (string $state): string => strtoupper($state)),
                Tables\Columns\TextColumn::make('file_size')
                    ->label(__('filament-resources/document.columns.file_size')),
                Tables\Columns\TextColumn::make('documentable_type')
                    ->label(__('filament-resources/document.columns.documentable_type'))
                    ->formatStateUsing(function ($state) {
                        $parts = explode('\\', $state);
                        return end($parts);
                    }),
                Tables\Columns\TextColumn::make('uploader.name')
                    ->label(__('filament-resources/document.columns.uploader.name')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources/document.columns.created_at'))
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('documentable_type')
                    ->label(__('filament-resources/document.filters.documentable_type'))
                    ->options([
                        \App\Models\Client::class => __('filament-resources/document.documentable_types.client'),
                        \App\Models\Contract::class => __('filament-resources/document.documentable_types.contract'),
                        \App\Models\MaintenanceRequest::class => __('filament-resources/document.documentable_types.maintenance_request'),
                        \App\Models\Visit::class => __('filament-resources/document.documentable_types.visit'),
                        \App\Models\Payment::class => __('filament-resources/document.documentable_types.payment'),
                    ]),
            ])
            ->actions([
                Tables\Actions\Action::make('download')
                    ->label(__('filament-resources/document.actions.download'))
                    ->url(fn (Document $record): string => route('document.download', $record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-download'),
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/document.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/document.actions.delete')),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocuments::route('/'),
            'create' => Pages\CreateDocument::route('/create'),
            'edit' => Pages\EditDocument::route('/{record}/edit'),
        ];
    }
}
