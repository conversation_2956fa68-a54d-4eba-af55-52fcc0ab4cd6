<?php
// Example: BroadcastChannelResource.php

namespace App\Filament\Resources;

use App\Broadcasting\WahaChannel;
use App\Broadcasting\MsegatChannel;
use App\Broadcasting\TaqnyatChannel;
use App\Filament\Resources\BroadcastChannelResource\Pages;
use App\Models\BroadcastChannel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BroadcastChannelResource extends Resource
{
    protected static ?string $model = BroadcastChannel::class;

    //protected static ?string $navigationIcon = 'heroicon-o-radio';

    // Use translation key for navigation group
    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.system');
    }

    protected static ?int $navigationSort = 1;

    // Use translation keys for model labels
    public static function getModelLabel(): string
    {
        return __('navigation.resources.broadcast_channel.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.broadcast_channel.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('filament-resources/broadcast-channel.sections.channel_information'))
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label(__('filament-resources/broadcast-channel.fields.title'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('url')
                            ->label(__('filament-resources/broadcast-channel.fields.url'))
                            ->required()
                            ->url()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('balance_url')
                            ->label(__('filament-resources/broadcast-channel.fields.balance_url'))
                            ->url()
                            ->maxLength(255),
                        Forms\Components\Select::make('class')
                            ->label(__('filament-resources/broadcast-channel.fields.class'))
                            ->required()
                            ->options([
                                TaqnyatChannel::class => 'SMS Taqnyat',
                                MsegatChannel::class => 'SMS Msegat',
                                WahaChannel::class => 'Waha Channel',
                            ])
                            ->searchable()
                            ->helperText(__('filament-resources/broadcast-channel.helper_text.class')),
                        Forms\Components\Toggle::make('is_active')
                            ->label(__('filament-resources/broadcast-channel.fields.is_active'))
                            ->default(true),
                        Forms\Components\Toggle::make('default')
                            ->label(__('filament-resources/broadcast-channel.fields.default'))
                            ->default(false)
                            ->helperText(__('filament-resources/broadcast-channel.helper_text.default')),
                        Forms\Components\Toggle::make('has_attachments')
                            ->label(__('filament-resources/broadcast-channel.fields.has_attachments'))
                            ->default(false),
                        Forms\Components\Toggle::make('has_poll')
                            ->label(__('filament-resources/broadcast-channel.fields.has_poll'))
                            ->default(false),
                    ]),

                Forms\Components\Section::make(__('filament-resources/broadcast-channel.sections.authentication'))
                    ->schema([
                        Forms\Components\Select::make('auth')
                            ->label(__('filament-resources/broadcast-channel.fields.auth'))
                            ->required()
                            ->options([
                                'HEADER' => 'Header Authentication',
                                'BASIC' => 'Basic Authentication',
                                'QUERY' => 'Query Parameter',
                                'BODY' => 'Request Body',
                            ])
                            ->default('HEADER')
                            ->live(),

                        Forms\Components\TextInput::make('header_name')
                            ->label(__('filament-resources/broadcast-channel.fields.header_name'))
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get) => $get('auth') === 'HEADER'),
                        Forms\Components\Textarea::make('header_value')
                            ->label(__('filament-resources/broadcast-channel.fields.header_value'))
                            ->visible(fn (Forms\Get $get) => $get('auth') === 'HEADER'),

                        Forms\Components\KeyValue::make('auth_body')
                            ->label(__('filament-resources/broadcast-channel.fields.auth_body'))
                            ->visible(fn (Forms\Get $get) => $get('auth') === 'BODY')
                            ->keyLabel(__('filament-resources/broadcast-channel.key_value.key'))
                            ->valueLabel(__('filament-resources/broadcast-channel.key_value.value'))
                            ->addActionLabel(__('filament-resources/broadcast-channel.key_value.add_action'))
                            ->reorderable(),

                        Forms\Components\TextInput::make('basic_user_name')
                            ->label(__('filament-resources/broadcast-channel.fields.basic_user_name'))
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get) => $get('auth') === 'BASIC'),
                        Forms\Components\TextInput::make('basic_password')
                            ->label(__('filament-resources/broadcast-channel.fields.basic_password'))
                            ->password()
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get) => $get('auth') === 'BASIC'),

                        Forms\Components\Textarea::make('query_text')
                            ->label(__('filament-resources/broadcast-channel.fields.query_text'))
                            ->visible(fn (Forms\Get $get) => $get('auth') === 'QUERY'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label(__('filament-resources/broadcast-channel.columns.title'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('url')
                    ->label(__('filament-resources/broadcast-channel.columns.url'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('class')
                    ->label(__('filament-resources/broadcast-channel.columns.class'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('filament-resources/broadcast-channel.columns.is_active'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('default')
                    ->label(__('filament-resources/broadcast-channel.columns.default'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('has_attachments')
                    ->label(__('filament-resources/broadcast-channel.columns.has_attachments'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('has_poll')
                    ->label(__('filament-resources/broadcast-channel.columns.has_poll'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-resources/broadcast-channel.columns.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('filament-resources/broadcast-channel.columns.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/broadcast-channel.actions.edit')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label(__('filament-resources/broadcast-channel.actions.delete_selected')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBroadcastChannels::route('/'),
            'create' => Pages\CreateBroadcastChannel::route('/create'),
            'edit' => Pages\EditBroadcastChannel::route('/{record}/edit'),
        ];
    }
}
