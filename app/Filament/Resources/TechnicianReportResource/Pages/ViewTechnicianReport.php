<?php

namespace App\Filament\Resources\TechnicianReportResource\Pages;

use App\Filament\Resources\TechnicianReportResource;
use App\Models\TechnicianReport;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewTechnicianReport extends ViewRecord
{
    protected static string $resource = TechnicianReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('approve')
                ->label(__('filament-resources/technician-report.actions.approve'))
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn (TechnicianReport $record): bool => 
                    $record->status === TechnicianReport::STATUS_SUBMITTED
                )
                ->requiresConfirmation()
                ->action(function (TechnicianReport $record) {
                    $record->update(['status' => TechnicianReport::STATUS_APPROVED]);
                    
                    \Filament\Notifications\Notification::make()
                        ->title(__('filament-resources/technician-report.notifications.approved'))
                        ->success()
                        ->send();
                }),

            Actions\Action::make('reject')
                ->label(__('filament-resources/technician-report.actions.reject'))
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn (TechnicianReport $record): bool => 
                    $record->status === TechnicianReport::STATUS_SUBMITTED
                )
                ->requiresConfirmation()
                ->action(function (TechnicianReport $record) {
                    $record->update(['status' => TechnicianReport::STATUS_REJECTED]);
                    
                    \Filament\Notifications\Notification::make()
                        ->title(__('filament-resources/technician-report.notifications.rejected'))
                        ->warning()
                        ->send();
                }),

            Actions\Action::make('view_maintenance_request')
                ->label(__('filament-resources/technician-report.actions.view_maintenance_request'))
                ->icon('heroicon-o-wrench-screwdriver')
                ->color('primary')
                ->url(fn (TechnicianReport $record): string =>
                    route('filament.admin.resources.maintenance-requests.view', $record->maintenance_request_id)
                ),
        ];
    }

    public function getTitle(): string
    {
        return __('filament-resources/technician-report.pages.view.title', ['id' => $this->getRecord()->id]);
    }

    public function getHeading(): string
    {
        return __('filament-resources/technician-report.pages.view.heading', ['id' => $this->getRecord()->id]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make(__('filament-resources/technician-report.infolist.basic_info'))
                    ->schema([
                        Infolists\Components\TextEntry::make('id')
                            ->label(__('filament-resources/technician-report.infolist.report_id'))
                            ->formatStateUsing(fn ($state) => "#{$state}"),

                        Infolists\Components\TextEntry::make('maintenanceRequest.request_number')
                            ->label(__('filament-resources/technician-report.infolist.maintenance_request'))
                            ->url(fn (TechnicianReport $record): string =>
                                route('filament.admin.resources.maintenance-requests.view', $record->maintenance_request_id)
                            ),

                        Infolists\Components\TextEntry::make('maintenanceRequest.client.name')
                            ->label(__('filament-resources/technician-report.infolist.client')),

                        Infolists\Components\TextEntry::make('technician.name')
                            ->label(__('filament-resources/technician-report.infolist.technician')),

                        Infolists\Components\TextEntry::make('status')
                            ->label(__('filament-resources/technician-report.infolist.status'))
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                TechnicianReport::STATUS_DRAFT => 'secondary',
                                TechnicianReport::STATUS_SUBMITTED => 'warning',
                                TechnicianReport::STATUS_APPROVED => 'success',
                                TechnicianReport::STATUS_REJECTED => 'danger',
                                default => 'secondary',
                            })
                            ->formatStateUsing(fn (string $state): string =>
                                __("technician.resources.report.status_options.{$state}")
                            ),

                        Infolists\Components\TextEntry::make('time_spent_minutes')
                            ->label(__('filament-resources/technician-report.infolist.time_spent'))
                            ->formatStateUsing(fn ($state) => $state ? "{$state} " . __('technician.resources.report.fields.time_spent_suffix') : '-'),

                        Infolists\Components\TextEntry::make('completion_percentage')
                            ->label(__('filament-resources/technician-report.infolist.completion'))
                            ->formatStateUsing(fn ($state) => "{$state}%")
                            ->badge()
                            ->color(fn ($state) => $state >= 80 ? 'success' : ($state >= 50 ? 'warning' : 'danger')),

                        Infolists\Components\TextEntry::make('submitted_at')
                            ->label(__('filament-resources/technician-report.infolist.submitted_at'))
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make(__('filament-resources/technician-report.infolist.work_details'))
                    ->schema([
                        Infolists\Components\TextEntry::make('work_summary')
                            ->label(__('filament-resources/technician-report.infolist.work_summary'))
                            ->markdown()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('findings')
                            ->label(__('filament-resources/technician-report.infolist.findings'))
                            ->markdown()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('recommendations')
                            ->label(__('filament-resources/technician-report.infolist.recommendations'))
                            ->markdown()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('issues_found')
                            ->label(__('filament-resources/technician-report.infolist.issues_found'))
                            ->markdown()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('parts_used')
                            ->label(__('filament-resources/technician-report.infolist.parts_used'))
                            ->markdown()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make(__('filament-resources/technician-report.infolist.checklist'))
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('checklistItems')
                            ->label(__('filament-resources/technician-report.infolist.checklist_items'))
                            ->schema([
                                Infolists\Components\TextEntry::make('category')
                                    ->label(__('filament-resources/technician-report.infolist.category'))
                                    ->badge()
                                    ->color('primary'),

                                Infolists\Components\TextEntry::make('description')
                                    ->label(__('filament-resources/technician-report.infolist.description')),

                                Infolists\Components\TextEntry::make('status')
                                    ->label(__('filament-resources/technician-report.infolist.checklist_status'))
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'not_checked' => 'gray',
                                        'passed' => 'success',
                                        'failed' => 'danger',
                                        'not_applicable' => 'warning',
                                        default => 'gray',
                                    }),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (TechnicianReport $record): bool => $record->checklistItems()->count() > 0),

                Infolists\Components\Section::make(__('filament-resources/technician-report.infolist.attachments'))
                    ->schema([
                        Infolists\Components\TextEntry::make('attachments')
                            ->label(__('filament-resources/technician-report.infolist.files'))
                            ->formatStateUsing(function ($state) {
                                if (empty($state) || !is_array($state)) {
                                    return __('filament-resources/technician-report.infolist.no_attachments');
                                }
                                
                                return collect($state)->map(function ($file) {
                                    $filename = basename($file);
                                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                                    $icon = match ($extension) {
                                        'pdf' => '📄',
                                        'jpg', 'jpeg', 'png', 'gif', 'webp' => '🖼️',
                                        'doc', 'docx' => '📝',
                                        default => '📎',
                                    };
                                    
                                    return "{$icon} {$filename}";
                                })->join("\n");
                            })
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (TechnicianReport $record): bool => !empty($record->attachments)),
            ]);
    }
}
