<?php

namespace App\Filament\Resources\TechnicianReportResource\Pages;

use App\Filament\Resources\TechnicianReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTechnicianReports extends ListRecords
{
    protected static string $resource = TechnicianReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action - reports are created by technicians
        ];
    }

    public function getTitle(): string
    {
        return __('filament-resources/technician-report.pages.list.title');
    }

    public function getHeading(): string
    {
        return __('filament-resources/technician-report.pages.list.heading');
    }
}
