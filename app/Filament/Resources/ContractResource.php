<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractResource\Pages;
use App\Filament\Resources\ContractResource\RelationManagers;
use App\Models\Contract;
use App\Models\Visit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Notifications\Notification;

class ContractResource extends Resource
{
    protected static ?string $model = Contract::class;
    //protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.contract_management');
    }

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.contract.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.contract.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('contract_number')
                            ->label(__('filament-resources/contract.fields.contract_number'))
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->default(function () {
                                $latestPayment = \App\Models\Contract::latest()->first();
                                $nextId = $latestPayment ? $latestPayment->id + 1 : 1;
                                return 'PAY-' . str_pad("$nextId", 6, '0', STR_PAD_LEFT);
                            }),
                        Forms\Components\Select::make('client_id')
                            ->label(__('filament-resources/contract.fields.client_id'))
                            ->relationship('client', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('contract_type_id')
                            ->label(__('filament-resources/contract.fields.contract_type_id'))
                            ->relationship('contractType', 'name')
                            ->required(),
                        Forms\Components\DatePicker::make('start_date')
                            ->label(__('filament-resources/contract.fields.start_date'))
                            ->required(),
                        Forms\Components\DatePicker::make('end_date')
                            ->label(__('filament-resources/contract.fields.end_date'))
                            ->required()
                            ->after('start_date'),
                        Forms\Components\TextInput::make('contract_value')
                            ->label(__('filament-resources/contract.fields.contract_value'))
                            ->required()
                            ->numeric()
                            ->prefix('SAR'),
                        Forms\Components\TextInput::make('visits_included')
                            ->label(__('filament-resources/contract.fields.visits_included'))
                            ->required()
                            ->numeric()
                            ->default(0),
                        Forms\Components\Select::make('status')
                            ->label(__('filament-resources/contract.fields.status'))
                            ->required()
                            ->options([
                                'active' => __('filament-resources/contract.status_options.active'),
                                'pending' => __('filament-resources/contract.status_options.pending'),
                                'expired' => __('filament-resources/contract.status_options.expired'),
                                'terminated' => __('filament-resources/contract.status_options.terminated'),
                            ]),
                        Forms\Components\Textarea::make('terms')
                            ->label(__('filament-resources/contract.fields.terms'))
                            ->columnSpan('full'),
                        Forms\Components\Textarea::make('notes')
                            ->label(__('filament-resources/contract.fields.notes'))
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('contract_number')
                    ->label(__('filament-resources/contract.columns.contract_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('client.name')
                    ->label(__('filament-resources/contract.columns.client.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('contractType.name')
                    ->label(__('filament-resources/contract.columns.contract_type.name')),
                Tables\Columns\TextColumn::make('start_date')
                    ->label(__('filament-resources/contract.columns.start_date'))
                    ->date(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label(__('filament-resources/contract.columns.end_date'))
                    ->date(),
                Tables\Columns\TextColumn::make('contract_value')
                    ->label(__('filament-resources/contract.columns.contract_value'))
                    ->riyal()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('filament-resources/contract.columns.status'))
                    ->colors([
                        'success' => 'active',
                        'warning' => 'pending',
                        'danger' => 'expired',
                        'secondary' => 'terminated',
                    ]),
                Tables\Columns\TextColumn::make('visits_included')
                    ->label(__('filament-resources/contract.columns.visits_included'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('remaining_visits_count')
                    ->label(__('filament-resources/contract.columns.remaining_visits'))
                    ->getStateUsing(fn (Contract $record): int => $record->remaining_visits_count)
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        // This is a complex sort since it's a computed property
                        return $query
                            ->select('contracts.*')
                            ->selectRaw('
                                (visits_included - (
                                    SELECT COUNT(*)
                                    FROM visits
                                    WHERE visits.contract_id = contracts.id
                                    AND visits.status IN (\'scheduled\', \'in_progress\', \'completed\')
                                )) as remaining_visits
                            ')
                            ->orderBy('remaining_visits', $direction);
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('client_id')
                    ->label(__('filament-resources/contract.filters.client_id'))
                    ->relationship('client', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('contract_type_id')
                    ->label(__('filament-resources/contract.filters.contract_type_id'))
                    ->relationship('contractType', 'name'),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources/contract.filters.status'))
                    ->options([
                        'active' => __('filament-resources/contract.status_options.active'),
                        'pending' => __('filament-resources/contract.status_options.pending'),
                        'expired' => __('filament-resources/contract.status_options.expired'),
                        'terminated' => __('filament-resources/contract.status_options.terminated'),
                    ]),
                Tables\Filters\Filter::make('has_remaining_visits')
                    ->label(__('filament-resources/contract.filters.has_remaining_visits'))
                    ->query(function (Builder $query) {
                        return $query
                            ->whereRaw('
                                visits_included > (
                                    SELECT COUNT(*)
                                    FROM visits
                                    WHERE visits.contract_id = contracts.id
                                    AND visits.status IN (\'scheduled\', \'in_progress\', \'completed\')
                                )
                            ');
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('schedule_visit')
                    ->label(__('filament-resources/contract.actions.schedule_visit'))
                    ->icon('heroicon-o-calendar')
                    ->color('primary')
                    ->visible(fn (Contract $record): bool => $record->is_active && $record->remaining_visits_count > 0)
                    ->form([
                        Forms\Components\Select::make('technician_id')
                            ->label(__('filament-resources/visit.fields.technician_id'))
                            ->options(\App\Models\User::pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\DateTimePicker::make('scheduled_at')
                            ->label(__('filament-resources/visit.fields.scheduled_at'))
                            ->required()
                            ->default(now()->addDay()->setHour(9)->setMinute(0)->setSecond(0)),
                        Forms\Components\Textarea::make('notes')
                            ->label(__('filament-resources/visit.fields.notes')),
                    ])
                    ->action(function (array $data, Contract $record): void {
                        // Check if contract has remaining visits
                        if ($record->remaining_visits_count <= 0) {
                            Notification::make()
                                ->title(__('filament-resources/visit.notifications.no_visits_remaining'))
                                ->body(__('filament-resources/visit.notifications.no_visits_remaining_body', [
                                    'visits_included' => $record->visits_included,
                                    'total_visits' => $record->total_visits_count,
                                ]))
                                ->danger()
                                ->send();

                            return;
                        }

                        // Create a new visit
                        $visit = new Visit();
                        $visit->contract_id = $record->id;
                        $visit->technician_id = $data['technician_id'];
                        $visit->scheduled_at = $data['scheduled_at'];
                        $visit->status = 'scheduled';
                        $visit->notes = $data['notes'] ?? null;
                        $visit->save();

                        // Show notification
                        Notification::make()
                            ->title(__('filament-resources/visit.notifications.visit_scheduled'))
                            ->body(__('filament-resources/visit.notifications.visit_scheduled_body', [
                                'date' => $visit->scheduled_at->format('Y-m-d h:i A'),
                                'remaining' => $record->remaining_visits_count - 1,
                            ]))
                            ->success()
                            ->send();
                    }),

                /*Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/contract.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/contract.actions.delete')),*/
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\VisitsRelationManager::class,
            RelationManagers\TechnicianReportsRelationManager::class,
            //RelationManagers\DocumentsRelationManager::class,
            //RelationManagers\MaintenanceRequestsRelationManager::class,
            //RelationManagers\PaymentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContracts::route('/'),
            'view' => Pages\ViewContract::route('/{record}'),
            //'create' => Pages\CreateContract::route('/create'),
            //'edit' => Pages\EditContract::route('/{record}/edit'),
        ];
    }
}
