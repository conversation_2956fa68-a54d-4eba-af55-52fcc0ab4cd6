<?php

namespace App\Services;

use App\Facades\DocKing;
use App\Models\DocumentTemplate;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\File;

class DocumentTemplateService
{
    /**
     * Redis cache TTL in seconds (1 hour)
     */
    const CACHE_TTL = 3600;

    /**
     * Base path for template files
     */
    const TEMPLATES_PATH = 'resources/templates/documents';

    /**
     * Get all registered template types.
     *
     * @return array
     */
    public static function getTemplateTypes(): array
    {
        return [
            'MaintenanceRequestPrint' => 'طلب الصيانة',
            'InvoicePrint' => 'الفاتورة',
            'ContractPrint' => 'العقد',
            'QuotationPrint' => 'عرض السعر',
            'ReportPrint' => 'التقرير',
        ];
    }

    /**
     * Get default content for a template type from filesystem.
     *
     * @param string $templateType
     * @return string
     * @throws FileNotFoundException
     */
    public static function getDefaultContent(string $templateType): string
    {
        $templatePath = base_path(self::TEMPLATES_PATH . "/{$templateType}.html");

        // Check if template file exists
        if (File::exists($templatePath)) {
            return File::get($templatePath);
        }

        // If file doesn't exist, return empty template with placeholders
        return self::getEmptyTemplateContent($templateType);
    }

    /**
     * Get an empty template with placeholders when no file exists.
     *
     * @param string $templateType
     * @return string
     */
    protected static function getEmptyTemplateContent(string $templateType): string
    {
        $templateName = self::getTemplateTypes()[$templateType] ?? $templateType;

        return <<<HTML
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$templateName}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .placeholder {
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px dashed #ccc;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>{$templateName}</h1>
    <div class="placeholder">
        <p>هذا قالب فارغ لـ {$templateName}. يرجى إضافة محتوى القالب الخاص بك.</p>
    </div>
</body>
</html>
HTML;
    }

    /**
     * Get template by type.
     *
     * @param string $templateType
     * @return DocumentTemplate|null
     */
    public static function getTemplate(string $templateType): ?DocumentTemplate
    {
        $cacheKey = self::getTemplateCacheKey($templateType);

        // Try to get from Redis cache first
        if (Redis::exists($cacheKey)) {
            $templateData = json_decode(Redis::get($cacheKey), true);

            if ($templateData) {
                $template = new DocumentTemplate();
                $template->fill($templateData);
                $template->exists = true;
                return $template;
            }
        }

        // Get from database
        $template = DocumentTemplate::ofType($templateType)->first();

        // Create template if it doesn't exist
        if (!$template) {
            $template = self::createDefaultTemplate($templateType);
        }

        // Store in Redis cache
        if ($template) {
            Redis::setex($cacheKey, self::CACHE_TTL, json_encode($template->toArray()));
        }

        return $template;
    }

    /**
     * Create a default template for a template type.
     *
     * @param string $templateType
     * @return DocumentTemplate
     * @throws FileNotFoundException
     */
    public static function createDefaultTemplate(string $templateType): DocumentTemplate
    {
        $typeLabels = self::getTemplateTypes();

        return DocumentTemplate::create([
            'type' => $templateType,
            'name' => $typeLabels[$templateType] ?? $templateType,
            'content' => self::getDefaultContent($templateType),
            'metadata' => [
                'driver' => 'gotenberg',
                'templating' => 'blade',
                'version' => 1,
                'created_at' => now()->toIso8601String(),
            ],
            'is_synced' => false,
        ]);
    }

    /**
     * Update a template's content.
     *
     * @param string $templateType
     * @param string $content
     * @return DocumentTemplate
     */
    public static function updateTemplate(string $templateType, string $content): DocumentTemplate
    {
        $template = self::getTemplate($templateType);

        // Update content and metadata
        $template->update([
            'content' => $content,
            'metadata' => array_merge($template->metadata ?? [], [
                'version' => ($template->metadata['version'] ?? 0) + 1,
                'updated_at' => now()->toIso8601String(),
            ]),
            'is_synced' => false,
        ]);

        // Clear the cache
        self::clearTemplateCache($templateType);

        return $template;
    }

    /**
     * Export a template to the filesystem.
     *
     * @param string $templateType
     * @return bool
     */
    public static function exportTemplateToFile(string $templateType): bool
    {
        $template = self::getTemplate($templateType);

        if (!$template) {
            return false;
        }

        $directory = base_path(self::TEMPLATES_PATH);

        // Create directory if it doesn't exist
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $templatePath = "{$directory}/{$templateType}.html";

        try {
            File::put($templatePath, $template->content);
            return true;
        } catch (\Exception $e) {
            Log::error("Error exporting template {$templateType} to file: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Import a template from the filesystem.
     *
     * @param string $templateType
     * @return bool
     */
    public static function importTemplateFromFile(string $templateType): bool
    {
        $templatePath = base_path(self::TEMPLATES_PATH . "/{$templateType}.html");

        if (!File::exists($templatePath)) {
            return false;
        }

        try {
            $content = File::get($templatePath);
            self::updateTemplate($templateType, $content);
            return true;
        } catch (\Exception $e) {
            Log::error("Error importing template {$templateType} from file: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync a template with DocKing.
     *
     * @param string $templateType
     * @return DocumentTemplate
     */
    public static function syncTemplateWithDocking(string $templateType): DocumentTemplate
    {
        $template = self::getTemplate($templateType);
        $tenant = tenant();

        // Check if DocKing is configured
        if (empty($tenant->docking_base_url) || empty($tenant->docking_api_key)) {
            $template->markSyncError('DocKing not configured - missing base URL or API key');
            return $template;
        }

        try {
            // If we have an existing template ID and the DocKing URL matches
            if ($template->docking_template_id && $template->docking_instance_url == $tenant->docking_base_url) {
                // Update existing template
                $response = DocKing::updateDocumentTemplate($template->docking_template_id, [
                    'title' => $template->name,
                    'template' => $template->content,
                    'category' => "Tenant_{$tenant->id}",
                    'metadata' => $template->metadata,
                    'default_variables' => self::getSampleDataForTemplate($templateType),
                ]);
            } else {
                // Create new template
                $response = DocKing::storeDocumentTemplate([
                    'key' => "Tenant_{$tenant->id}_{$template->type}_{$template->id}",
                    'title' => $template->name,
                    'template' => $template->content,
                    'category' => "Tenant_{$tenant->id}",
                    'metadata' => $template->metadata,
                    'default_variables' => self::getSampleDataForTemplate($templateType),
                ]);
            }

            // Check if the response has a UUID
            if (isset($response['uuid'])) {
                // Mark template as synced
                $template->markAsSynced($response['uuid'], $tenant->docking_base_url);

                // Clear the cache
                self::clearTemplateCache($templateType);

                return $template;
            } else {
                // Handle error
                $template->markSyncError('DocKing API response missing UUID');
            }
        } catch (\Exception $e) {
            Log::error("Error syncing template {$templateType} with DocKing: " . $e->getMessage());
            $template->markSyncError($e->getMessage());
        }

        return $template;
    }

    /**
     * Preview a document template with sample data.
     *
     * @param string $templateType
     * @return array|null
     */
    public static function previewTemplate(string $templateType): ?array
    {
        $template = self::getTemplate($templateType);

        // If the template isn't synced, sync it first
        if (!$template->is_synced) {
            $template = self::syncTemplateWithDocking($templateType);
        }

        // If we still don't have a DocKing template ID, return null
        if (!$template->docking_template_id) {
            return null;
        }

        try {
            // Get sample data for this template type
            $sampleData = self::getSampleDataForTemplate($templateType);

            // Use DocKing service to preview the template
            return DocKing::previewHtmlDocumentTemplate($template->docking_template_id, $sampleData);
        } catch (\Exception $e) {
            Log::error("Error previewing template {$templateType}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate a PDF document for a specific template type with provided data.
     *
     * @param string $templateType
     * @param array $data
     * @return array|null
     */
    public static function generatePdf(string $templateType, array $data): ?array
    {
        $template = self::getTemplate($templateType);

        // If the template isn't synced, sync it first
        if (!$template->is_synced) {
            $template = self::syncTemplateWithDocking($templateType);
        }

        // If we still don't have a DocKing template ID, return null
        if (!$template->docking_template_id) {
            return null;
        }

        try {
            // Use DocKing service to generate PDF
            return DocKing::renderPdf($template->docking_template_id, $data);
        } catch (\Exception $e) {
            Log::error("Error generating PDF for template {$templateType}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate a PDF document asynchronously for a specific template type with provided data.
     *
     * @param string $templateType
     * @param array $data
     * @return array|null
     */
    public static function generatePdfAsync(string $templateType, array $data): ?array
    {
        $template = self::getTemplate($templateType);

        // If the template isn't synced, sync it first
        if (!$template->is_synced) {
            $template = self::syncTemplateWithDocking($templateType);
        }

        // If we still don't have a DocKing template ID, return null
        if (!$template->docking_template_id) {
            return null;
        }

        try {
            // Use DocKing service to generate PDF asynchronously
            return DocKing::renderPdfAsync($template->docking_template_id, $data);
        } catch (\Exception $e) {
            Log::error("Error generating async PDF for template {$templateType}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get cache key for a template type.
     *
     * @param string $templateType
     * @return string
     */
    public static function getTemplateCacheKey(string $templateType): string
    {
        return "document_template:" . tenant()->id . ":{$templateType}";
    }

    /**
     * Clear the cache for a template type.
     *
     * @param string $templateType
     * @return void
     */
    public static function clearTemplateCache(string $templateType): void
    {
        $cacheKey = self::getTemplateCacheKey($templateType);
        Redis::del($cacheKey);
    }

    /**
     * Clear all template caches for the current tenant.
     *
     * @return void
     */
    public static function clearAllTemplateCaches(): void
    {
        $templateTypes = array_keys(self::getTemplateTypes());

        foreach ($templateTypes as $templateType) {
            self::clearTemplateCache($templateType);
        }
    }

    /**
     * Get sample data for a template preview.
     *
     * @param string $templateType
     * @return array
     */
    public static function getSampleDataForTemplate(string $templateType): array
    {
        switch ($templateType) {
            case 'MaintenanceRequestPrint':
                return [
                    'maintenanceRequest' => [
                        'id' => 'MR-123456',
                        'title' => 'صيانة مكيف الهواء',
                        'description' => 'لا يبرد المكيف بشكل صحيح ويصدر ضوضاء غير طبيعية',
                        'status' => 'قيد المعالجة',
                        'priority' => 'عالية',
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'scheduled_at' => now()->addDays(2)->format('Y-m-d H:i:s'),
                    ],
                    'customer' => [
                        'name' => 'أحمد محمد علي',
                        'phone' => '0512345678',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، حي النخيل، شارع العليا',
                    ],
                    'technician' => [
                        'name' => 'عبدالله محمد',
                        'phone' => '0598765432',
                        'email' => '<EMAIL>',
                    ],
                    'company' => [
                        'name' => tenant()->name ?? 'شركة الصيانة',
                        'phone' => '*********',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، المملكة العربية السعودية',
                        'logo' => 'https://placehold.co/150x50',
                    ],
                ];

            case 'InvoicePrint':
                return [
                    'invoice' => [
                        'id' => 'INV-123456',
                        'number' => '2024/0001',
                        'date' => now()->format('Y-m-d'),
                        'due_date' => now()->addDays(30)->format('Y-m-d'),
                        'status' => 'مدفوعة',
                        'subtotal' => 1000.00,
                        'tax' => 150.00,
                        'discount' => 50.00,
                        'total' => 1100.00,
                        'notes' => 'شكراً لتعاملكم معنا',
                    ],
                    'customer' => [
                        'name' => 'محمد عبدالله',
                        'phone' => '0512345678',
                        'email' => '<EMAIL>',
                        'address' => 'جدة، حي الروضة، شارع الملك فهد',
                        'vat_number' => '300012345600003',
                    ],
                    'items' => [
                        [
                            'description' => 'صيانة وإصلاح مكيف الهواء',
                            'quantity' => 1,
                            'unit_price' => 500.00,
                            'tax' => 75.00,
                            'total' => 575.00,
                        ],
                        [
                            'description' => 'استبدال قطع غيار',
                            'quantity' => 2,
                            'unit_price' => 250.00,
                            'tax' => 75.00,
                            'total' => 575.00,
                        ],
                    ],
                    'company' => [
                        'name' => tenant()->name ?? 'شركة الصيانة',
                        'phone' => '*********',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، المملكة العربية السعودية',
                        'vat_number' => '300000000000003',
                        'logo' => 'https://placehold.co/150x50',
                    ],
                ];

            case 'ContractPrint':
                return [
                    'contract' => [
                        'id' => 'CONT-123456',
                        'title' => 'عقد صيانة دورية',
                        'start_date' => now()->format('Y-m-d'),
                        'end_date' => now()->addYear()->format('Y-m-d'),
                        'status' => 'ساري',
                        'total_amount' => 12000.00,
                        'payment_terms' => 'دفعة شهرية قدرها 1000 ريال',
                        'terms_conditions' => 'تشمل الصيانة الدورية زيارة شهرية للفحص وتنظيف المكيفات. لا تشمل قطع الغيار.',
                    ],
                    'customer' => [
                        'name' => 'شركة الأفق للاستثمار',
                        'representative' => 'فهد العتيبي',
                        'phone' => '0512345678',
                        'email' => '<EMAIL>',
                        'address' => 'الدمام، حي الخليج، شارع الملك عبدالعزيز',
                        'cr_number' => '1234567890',
                    ],
                    'services' => [
                        [
                            'description' => 'صيانة شهرية للمكيفات',
                            'quantity' => 12,
                            'unit_price' => 1000.00,
                            'total' => 12000.00,
                        ],
                    ],
                    'company' => [
                        'name' => tenant()->name ?? 'شركة الصيانة',
                        'representative' => 'خالد المالكي',
                        'phone' => '*********',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، المملكة العربية السعودية',
                        'cr_number' => '1111222333',
                        'logo' => 'https://placehold.co/150x50',
                    ],
                ];

            case 'QuotationPrint':
                return [
                    'quotation' => [
                        'id' => 'QT-123456',
                        'number' => '2024/0001',
                        'date' => now()->format('Y-m-d'),
                        'expiry_date' => now()->addDays(30)->format('Y-m-d'),
                        'status' => 'نشط',
                        'subtotal' => 8500.00,
                        'tax' => 1275.00,
                        'discount' => 500.00,
                        'total' => 9275.00,
                        'notes' => 'هذا العرض صالح لمدة 30 يوماً من تاريخ إصداره',
                    ],
                    'customer' => [
                        'name' => 'سعيد الدوسري',
                        'phone' => '0512345678',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، حي الياسمين، شارع الأمير سلطان',
                    ],
                    'items' => [
                        [
                            'description' => 'توريد وتركيب مكيف مركزي',
                            'quantity' => 1,
                            'unit_price' => 5000.00,
                            'tax' => 750.00,
                            'total' => 5750.00,
                        ],
                        [
                            'description' => 'توريد وتركيب مكيف سبليت',
                            'quantity' => 3,
                            'unit_price' => 1000.00,
                            'tax' => 450.00,
                            'total' => 3450.00,
                        ],
                        [
                            'description' => 'أعمال تمديدات',
                            'quantity' => 1,
                            'unit_price' => 500.00,
                            'tax' => 75.00,
                            'total' => 575.00,
                        ],
                    ],
                    'company' => [
                        'name' => tenant()->name ?? 'شركة الصيانة',
                        'phone' => '*********',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، المملكة العربية السعودية',
                        'vat_number' => '300000000000003',
                        'logo' => 'https://placehold.co/150x50',
                    ],
                ];

            case 'ReportPrint':
                return [
                    'report' => [
                        'id' => 'RPT-123456',
                        'title' => 'تقرير صيانة',
                        'date' => now()->format('Y-m-d'),
                        'type' => 'تقرير فني',
                        'summary' => 'تم إجراء الصيانة الدورية للمكيفات وفقاً لجدول الصيانة المتفق عليه',
                        'details' => 'تم فحص وتنظيف المكيفات والتأكد من سلامة التشغيل. تم استبدال بعض الفلاتر وإصلاح تسرب بسيط في أحد الوحدات.',
                        'recommendations' => 'ينصح بتغيير الضاغط في الوحدة رقم 3 خلال الشهرين القادمين نظراً لضعف أدائه.',
                    ],
                    'customer' => [
                        'name' => 'مؤسسة الرياض التعليمية',
                        'contact_person' => 'نورة السعيد',
                        'phone' => '0512345678',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، حي العليا، شارع التحلية',
                    ],
                    'maintenance_items' => [
                        [
                            'description' => 'فحص وتنظيف المكيفات المركزية',
                            'status' => 'تم',
                            'notes' => 'جميع الوحدات تعمل بكفاءة',
                        ],
                        [
                            'description' => 'فحص وتنظيف مكيفات السبليت',
                            'status' => 'تم',
                            'notes' => 'تم استبدال فلاتر الوحدات',
                        ],
                        [
                            'description' => 'فحص تسربات التبريد',
                            'status' => 'تم',
                            'notes' => 'تم إصلاح تسرب بسيط في الوحدة رقم 2',
                        ],
                        [
                            'description' => 'فحص كفاءة الضواغط',
                            'status' => 'تم',
                            'notes' => 'ضاغط الوحدة رقم 3 يعمل بكفاءة منخفضة ويحتاج للاستبدال قريباً',
                        ],
                    ],
                    'technician' => [
                        'name' => 'فيصل العنزي',
                        'position' => 'فني صيانة أول',
                        'phone' => '0598765432',
                        'email' => '<EMAIL>',
                    ],
                    'company' => [
                        'name' => tenant()->name ?? 'شركة الصيانة',
                        'phone' => '*********',
                        'email' => '<EMAIL>',
                        'address' => 'الرياض، المملكة العربية السعودية',
                        'logo' => 'https://placehold.co/150x50',
                    ],
                ];

            default:
                return [];
        }
    }
}
