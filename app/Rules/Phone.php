<?php

namespace App\Rules;

use Propaganistas\LaravelPhone\Exceptions\IncompatibleTypesException;

class Phone extends \Propaganistas\LaravelPhone\Rules\Phone
{
    /**
     * Run the validation rule.
     *
     * @throws IncompatibleTypesException
     */
    public function passes($attribute, $value)
    {
        if (!empty($value))
            $value = phone_format($value, allowed_countries(), true) ?: $value;
        return parent::passes($attribute, $value);
    }
}
