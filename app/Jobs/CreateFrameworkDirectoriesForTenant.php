<?php

namespace App\Jobs;

use Stancl\Tenancy\Contracts\Tenant;

class CreateFrameworkDirectoriesForTenant
{
    protected $tenant;

    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
    }

    public function handle()
    {
        $this->tenant->run(function ($tenant) {
            $storage_path = storage_path();

            $paths = [
                "framework/cache",
                "framework/sessions",
                "app",
                "app/excel-files",
                "app/wallet/assets/memberships",
                "app/wallet/passes",

                "app/webhook/myfatoorah",
                "app/webhook/strava",
                "app/webhook/tally",
                "app/webhook/waha",
                "app/webhook/taqnyat",

                "app/public/activities/thumbs",
                "app/public/companies/thumbs",
                "app/public/debt-discharges/thumbs",
                "app/public/documents/thumbs",
                "app/public/family-case/thumbs",
                "app/public/help-forms/images/thumbnails",
                "app/public/help-forms/pdf/thumbnails",
                "app/public/images/news",
                "app/public/kin-charities/thumbs",
                "app/public/literatures/cover_images",
                "app/public/literatures/files",
                "app/public/passes/json",
                "app/public/passes/images",
                "app/public/support-services/thumbs",
                "app/public/transactions/pdf/thumbnails",
                "app/public/uploads/thumbs",
                "app/public/uploads/fake",
                "app/public/user-pic/originals",
                "tmp",
            ];
            foreach ($paths as $path) {
                if (!file_exists("$storage_path/$path"))
                    mkdir("$storage_path/$path", 0777, true);
            }
        });
    }
}

