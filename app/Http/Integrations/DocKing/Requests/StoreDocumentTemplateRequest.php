<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Traits\Body\HasJsonBody;

class StoreDocumentTemplateRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(
        protected array $data
    )
    {
    }

    public function resolveEndpoint(): string
    {
        return '/v1/document-templates';
    }

    protected function defaultBody(): array
    {
        return $this->data;
    }
}
