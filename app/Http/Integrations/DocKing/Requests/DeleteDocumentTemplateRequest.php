<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class DeleteDocumentTemplateRequest extends Request
{
    protected Method $method = Method::DELETE;

    public function __construct(
        protected string $documentTemplateUuid
    )
    {
    }

    public function resolveEndpoint(): string
    {
        return "/v1/document-templates/{$this->documentTemplateUuid}";
    }
}
