<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class GetDocumentTemplateRequest extends Request
{
    protected Method $method = Method::GET;

    public function __construct(
        protected string $documentTemplateUuid
    )
    {
    }

    public function resolveEndpoint(): string
    {
        return "/v1/document-templates/{$this->documentTemplateUuid}";
    }
}

