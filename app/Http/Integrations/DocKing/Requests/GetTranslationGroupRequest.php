<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class GetTranslationGroupRequest extends Request
{
    protected Method $method = Method::GET;

    public function __construct(
        protected string $translationGroupId
    )
    {
    }

    public function resolveEndpoint(): string
    {
        return "/v1/translation-groups/{$this->translationGroupId}";
    }
}
