<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class StoreTranslationRequest extends Request
{
    protected Method $method = Method::POST;

    public function __construct(
        protected array $data
    ) {
    }

    public function resolveEndpoint(): string
    {
        return '/v1/translations';
    }

    protected function defaultBody(): array
    {
        return $this->data;
    }
}
