<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class StoreTranslationGroupRequest extends Request
{
    protected Method $method = Method::POST;

    public function __construct(
        protected array $data
    ) {
    }

    public function resolveEndpoint(): string
    {
        return '/v1/translation-groups';
    }

    protected function defaultBody(): array
    {
        return $this->data;
    }
}
