<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class UpdateTranslationRequest extends Request
{
    protected Method $method = Method::PUT;

    public function __construct(
        protected string $translationId,
        protected array $data
    ) {
    }

    public function resolveEndpoint(): string
    {
        return "/v1/translations/{$this->translationId}";
    }

    protected function defaultBody(): array
    {
        return $this->data;
    }
}
