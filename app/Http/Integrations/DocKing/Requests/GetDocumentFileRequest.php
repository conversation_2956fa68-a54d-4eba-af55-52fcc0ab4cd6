<?php

namespace App\Http\Integrations\DocKing\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class GetDocumentFileRequest extends Request
{
    protected Method $method = Method::GET;

    public function __construct(
        protected string $documentFileId
    ) {
    }

    public function resolveEndpoint(): string
    {
        return "/v1/document-files/{$this->documentFileId}";
    }
}
