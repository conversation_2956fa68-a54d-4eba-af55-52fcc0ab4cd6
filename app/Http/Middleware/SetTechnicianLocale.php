<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware to set the locale for the Technician panel
 *
 * This middleware supports multi-language functionality for the Technician panel
 * including Arabic (RTL), English, Urdu (RTL), and Filipino/Tagalog (LTR).
 * It provides session-based locale persistence and URL parameter switching.
 */
class SetTechnicianLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Determine the appropriate locale for the request
        $locale = $this->determineLocale($request);

        // Set the application locale
        App::setLocale($locale);

        // Store locale in session for persistence
        Session::put('locale', $locale);

        // Set direction for RTL languages
        //$this->setDirectionForLocale($locale);

        return $next($request);
    }

    /**
     * Determine the appropriate locale for the request
     *
     * @param Request $request
     * @return string
     */
    protected function determineLocale(Request $request): string
    {
        $supportedLocales = $this->getSupportedLocales();
        $supportedLocalesConfig = config('app.supported_locales', []);

        // Priority order:
        // 1. URL parameter (for language switching)
        // 2. Session stored locale
        // 3. User preference (if authenticated)
        // 4. Default to Arabic for Technician panel

        // Check for URL parameter (for language switching)
        if ($request->has('locale')) {
            $requestedLocale = $request->get('locale');

            // Handle case where display name is passed instead of locale code
            $localeCode = $this->resolveLocaleCode($requestedLocale, $supportedLocalesConfig);

            if (in_array($localeCode, $supportedLocales)) {
                return $localeCode;
            }
        }

        // Check session for stored locale
        if (Session::has('locale')) {
            $sessionLocale = Session::get('locale');
            $localeCode = $this->resolveLocaleCode($sessionLocale, $supportedLocalesConfig);

            if (in_array($localeCode, $supportedLocales)) {
                return $localeCode;
            }
        }

        // Check authenticated user preference
        if (auth()->check() && auth()->user()->locale) {
            $userLocale = auth()->user()->locale;
            $localeCode = $this->resolveLocaleCode($userLocale, $supportedLocalesConfig);

            if (in_array($localeCode, $supportedLocales)) {
                return $localeCode;
            }
        }

        // Default to Arabic for Technician panel (Arabic-first design)
        return 'ar';
    }

    /**
     * Get supported locales from configuration
     *
     * @return array
     */
    protected function getSupportedLocales(): array
    {
        return array_keys(config('app.supported_locales', ['ar', 'en', 'ur', 'fil']));
    }

    /**
     * Resolve locale code from potential display name or locale code
     *
     * @param string $input
     * @param array $supportedLocalesConfig
     * @return string
     */
    protected function resolveLocaleCode(string $input, array $supportedLocalesConfig): string
    {
        // If input is already a valid locale code, return it
        if (array_key_exists($input, $supportedLocalesConfig)) {
            return $input;
        }

        // Check if input is a display name and find corresponding locale code
        foreach ($supportedLocalesConfig as $localeCode => $config) {
            if (isset($config['name']) && $config['name'] === $input) {
                return $localeCode;
            }
        }

        // If no match found, check against common display names
        $displayNameMap = [
            'العربية' => 'ar',
            'English' => 'en',
            'اردو' => 'ur',
            'Filipino' => 'fil',
        ];

        if (isset($displayNameMap[$input])) {
            return $displayNameMap[$input];
        }

        // Return input as-is if no conversion needed
        return $input;
    }

    /**
     * Set direction attribute for RTL languages
     *
     * @param string $locale
     * @return void
     */
    protected function setDirectionForLocale(string $locale): void
    {
        $supportedLocales = config('app.supported_locales', []);

        if (isset($supportedLocales[$locale])) {
            $direction = $supportedLocales[$locale]['direction'] ?? 'ltr';

            // Store direction in session for use in views
            Session::put('direction', $direction);

            // Set global view variable for direction
            view()->share('direction', $direction);
        }
    }
}
