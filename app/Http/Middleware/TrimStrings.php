<?php

namespace App\Http\Middleware;

class TrimStrings extends \Illuminate\Foundation\Http\Middleware\TrimStrings
{
    /**
     * Transform the given value.
     *
     * @param string $key
     * @param mixed $value
     * @return mixed
     */
    protected function transform($key, $value)
    {
        $except = array_merge($this->except, static::$neverTrim);

        if ($this->shouldSkip($key, $except) || !is_string($value)) {
            return $value;
        }

        return clean_unicode($value);
    }
}
