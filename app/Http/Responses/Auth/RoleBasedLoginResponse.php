<?php

namespace App\Http\Responses\Auth;

use Filament\Facades\Filament;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;

class RoleBasedLoginResponse implements LoginResponse
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        $user = Filament::auth()->user();

        if (!$user || !isset($user->role)) {
            // Fallback to default panel if no role is set
            return redirect()->intended(Filament::getUrl());
        }

        $currentPanel = Filament::getCurrentPanel();

        // Determine the appropriate panel based on user role
        $panelUrl = $this->getPanelUrlForRole($user->role);

        // If user should be redirected to their role-specific panel, do it
        if ($panelUrl && $this->shouldRedirectToRolePanel($user->role, $currentPanel)) {
            return redirect()->to($panelUrl);
        }

        // If user has access to current panel, stay here
        if ($user->canAccessPanel($currentPanel)) {
            return redirect()->intended(Filament::getUrl());
        }

        // If user doesn't have access to current panel but has a valid role panel, redirect there
        if ($panelUrl) {
            return redirect()->to($panelUrl);
        }

        // Fallback: logout and redirect to admin login with error
        Filament::auth()->logout();
        return redirect()->to('/admin/login')->with('error', __('auth.no_panel_access'));
    }

    /**
     * Get the appropriate panel URL based on user role
     */
    protected function getPanelUrlForRole(string $role): ?string
    {
        $currentHost = request()->getHost();
        $scheme = request()->getScheme();
        
        return match ($role) {
            'admin', 'manager' => $this->buildPanelUrl($scheme, $currentHost, '/admin'),
            'technician' => $this->buildPanelUrl($scheme, $currentHost, '/technician'),
            default => null,
        };
    }

    /**
     * Build the complete panel URL
     */
    protected function buildPanelUrl(string $scheme, string $host, string $path): string
    {
        return "{$scheme}://{$host}{$path}";
    }

    /**
     * Determine if user should be redirected to their role-specific panel
     */
    protected function shouldRedirectToRolePanel(string $role, $currentPanel): bool
    {
        $currentPanelId = $currentPanel->getId();
        
        return match ($role) {
            'admin', 'manager' => $currentPanelId !== 'admin',
            'technician' => $currentPanelId !== 'technician',
            default => false,
        };
    }
}
