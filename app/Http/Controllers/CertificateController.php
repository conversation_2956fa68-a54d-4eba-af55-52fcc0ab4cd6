<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\CertificateRequest;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class CertificateController extends Controller
{
    public function __construct()
    {
        // Initialize default values if the session is empty
        if (!Session::has('certificate_data')) {
            Session::put('certificate_data', [
                'currentStep' => 1,
                'fullName' => 'Test User',
                'phone' => '966500000000',
                'email' => '<EMAIL>',
                'company' => 'Test Company Name',
                'address' => 'حي النقع طريق الملك عبدالله بريدة 52361 القصيم',
                'title' => 'طلب تجريبي',
                'description' => 'وصف طلب تجريبي',
                'priority' => 'medium',
                'preferredDate' => Carbon::now()->addDays(3)->format('Y-m-d'),
                'attachments' => [],
                'notes' => 'Testing Notes',
                'termsAgreement' => false,
                'inspectionFee' => 500.00,
                'certificateFee' => 200.00,
                'totalFee' => 700.00,
            ]);
        }
    }

    public function index()
    {
        $data = Session::get('certificate_data');
        return view('certificates.step1', compact('data'));
    }

    public function step1()
    {
        $data = Session::get('certificate_data');
        $data['currentStep'] = 1;
        Session::put('certificate_data', $data);
        return view('certificates.step1', compact('data'));
    }

    public function step2()
    {
        $data = Session::get('certificate_data');
        $data['currentStep'] = 2;
        Session::put('certificate_data', $data);
        return view('certificates.step2', compact('data'));
    }

    public function step3()
    {
        $data = Session::get('certificate_data');
        $data['currentStep'] = 3;
        Session::put('certificate_data', $data);
        return view('certificates.step3', compact('data'));
    }

    public function step4()
    {
        $data = Session::get('certificate_data');
        $data['currentStep'] = 4;

        // Prepare summary data for review
        $requestSummary = $this->prepareRequestSummary($data);

        Session::put('certificate_data', $data);
        return view('certificates.step4', compact('data', 'requestSummary'));
    }

    public function success()
    {
        $successMessage = Session::get('success_message', 'تم تقديم طلب شهادة الفحص بنجاح!');
        Session::forget('certificate_data');
        Session::forget('success_message');

        return view('certificates.success', compact('successMessage'));
    }

    public function processStep1(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fullName' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'company' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
        ], [
            'fullName.required' => 'الرجاء إدخال الاسم الكامل',
            'phone.required' => 'الرجاء إدخال رقم الهاتف',
            'email.required' => 'الرجاء إدخال البريد الإلكتروني',
            'email.email' => 'الرجاء إدخال بريد إلكتروني صحيح',
        ]);

        if ($validator->fails()) {
            return redirect()->route('certificate.step1')
                ->withErrors($validator)
                ->withInput();
        }

        $data = Session::get('certificate_data');
        $data['fullName'] = $request->fullName;
        $data['phone'] = $request->phone;
        $data['email'] = $request->email;
        $data['company'] = $request->company;
        $data['address'] = $request->address;

        Session::put('certificate_data', $data);

        return redirect()->route('certificate.step2');
    }

    public function processStep2(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,critical',
            'preferredDate' => 'required|date|after_or_equal:today',
        ], [
            'title.required' => 'الرجاء إدخال عنوان الطلب',
            'description.required' => 'الرجاء إدخال وصف الطلب',
            'preferredDate.required' => 'الرجاء اختيار تاريخ مفضل',
            'preferredDate.after_or_equal' => 'يجب أن يكون التاريخ اليوم أو بعده',
        ]);

        if ($validator->fails()) {
            return redirect()->route('certificate.step2')
                ->withErrors($validator)
                ->withInput();
        }

        $data = Session::get('certificate_data');
        $data['title'] = $request->title;
        $data['description'] = $request->description;
        $data['priority'] = $request->priority;
        $data['preferredDate'] = $request->preferredDate;

        Session::put('certificate_data', $data);

        return redirect()->route('certificate.step3');
    }

    public function processStep3(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'attachments.*' => 'nullable|file|max:10240', // 10MB Max
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('certificate.step3')
                ->withErrors($validator)
                ->withInput();
        }

        $data = Session::get('certificate_data');
        $data['notes'] = $request->notes;

        // Handle file uploads
        if ($request->hasFile('attachments')) {
            $attachmentPaths = [];

            foreach ($request->file('attachments') as $file) {
                $path = $file->store('certificate-requests', 'public');
                $attachmentPaths[] = [
                    'path' => $path,
                    'name' => $file->getClientOriginalName(),
                    'type' => $file->getClientOriginalExtension(),
                    'size' => $file->getSize(),
                ];
            }

            $data['attachments'] = $attachmentPaths;
        }

        Session::put('certificate_data', $data);

        return redirect()->route('certificate.step4');
    }

    public function submitRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'termsAgreement' => 'accepted',
        ], [
            'termsAgreement.accepted' => 'يجب الموافقة على الشروط والأحكام',
        ]);

        if ($validator->fails()) {
            return redirect()->route('certificate.step4')
                ->withErrors($validator)
                ->withInput();
        }

        $data = Session::get('certificate_data');

        $guard = Auth::guard('client');

        // Find or create a client
        $client = $guard->check() ? $guard->user() : Client::firstOrCreate(
            ['phone' => $data['phone']],
            [
                'name' => $data['fullName'],
                'email' => $data['email'],
                'company' => $data['company'],
                'address' => $data['address'],
            ]
        );

        // Create certificate request
        $certificateRequest = CertificateRequest::create([
            'request_number' => 'CERT-' . date('Ymd') . '-' . Str::random(4),
            'client_id' => $client->id,
            'title' => $data['title'],
            'description' => $data['description'],
            'priority' => $data['priority'],
            'status' => 'pending',
            'request_date' => now(),
            'preferred_inspection_date' => $data['preferredDate'],
            'inspection_fee' => $data['inspectionFee'],
            'certificate_fee' => $data['certificateFee'],
            'total_fee' => $data['totalFee'],
            'notes' => $data['notes'],
        ]);

        // Handle file uploads
        if (!empty($data['attachments'])) {
            foreach ($data['attachments'] as $attachment) {
                $certificateRequest->documents()->create([
                    'title' => 'مرفق - ' . $attachment['name'],
                    'file_path' => $attachment['path'],
                    'file_type' => $attachment['type'],
                    'file_size' => $attachment['size'],
                    'uploaded_by' => 1, // default user ID
                    'description' => 'مرفق لطلب شهادة الفحص',
                ]);
            }
        }

        // Set success message
        Session::put('success_message', 'تم تقديم طلب شهادة الفحص بنجاح! رقم الطلب: ' . $certificateRequest->request_number);

        return redirect()->route('certificate.success');
    }

    private function prepareRequestSummary($data)
    {
        return [
            'clientInfo' => [
                'fullName' => $data['fullName'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'company' => $data['company'],
                'address' => $data['address'],
            ],
            'requestDetails' => [
                'title' => $data['title'],
                'description' => $data['description'],
                'priority' => $this->getPriorityLabel($data['priority']),
                'preferredDate' => Carbon::parse($data['preferredDate'])->format('d/m/Y'),
            ],
            'attachments' => count($data['attachments'] ?? []) . ' مرفقات',
            'fees' => [
                'inspectionFee' => $data['inspectionFee'],
                'certificateFee' => $data['certificateFee'],
                'totalFee' => $data['totalFee'],
            ],
        ];
    }


}
