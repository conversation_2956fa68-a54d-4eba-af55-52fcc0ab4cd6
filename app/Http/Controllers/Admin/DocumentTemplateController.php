<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DocumentTemplate;
use App\Services\DocumentTemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class DocumentTemplateController extends Controller
{
    /**
     * Display the template editor page
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\View\View
     */
    public function edit(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            abort(404, 'نوع القالب غير صالح');
        }

        // Get template from database or create it
        $templateModel = DocumentTemplateService::getTemplate($template);

        // Check for sync status
        $syncStatus = [
            'is_synced' => $templateModel->is_synced,
            'last_synced_at' => $templateModel->last_synced_at,
            'docking_template_id' => $templateModel->docking_template_id,
            'sync_error' => $templateModel->sync_error,
        ];

        // Get sample data for preview
        $sampleData = DocumentTemplateService::getSampleDataForTemplate($template);

        return view('admin.document-templates.edit', [
            'templateType' => $template,
            'templateName' => $templateTypes[$template],
            'templateContent' => $templateModel->content,
            'templateModel' => $templateModel,
            'syncStatus' => $syncStatus,
            'sampleData' => $sampleData,
        ]);
    }

    /**
     * Update template content
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            return response()->json([
                'success' => false,
                'message' => 'نوع القالب غير صالح',
            ], 404);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صالحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Update template
            $template = DocumentTemplateService::updateTemplate($template, $request->input('content'));

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث القالب بنجاح',
                'template' => [
                    'type' => $template->type,
                    'name' => $template->name,
                    'is_synced' => $template->is_synced,
                    'last_synced_at' => $template->last_synced_at,
                    'docking_template_id' => $template->docking_template_id,
                    'sync_error' => $template->sync_error,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث القالب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Sync template with DocKing
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\JsonResponse
     */
    public function sync(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            return response()->json([
                'success' => false,
                'message' => 'نوع القالب غير صالح',
            ], 404);
        }

        try {
            // Sync template with DocKing
            $template = DocumentTemplateService::syncTemplateWithDocking($template);

            if ($template->is_synced) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم مزامنة القالب بنجاح',
                    'template' => [
                        'type' => $template->type,
                        'name' => $template->name,
                        'is_synced' => $template->is_synced,
                        'last_synced_at' => $template->last_synced_at,
                        'docking_template_id' => $template->docking_template_id,
                        'sync_error' => null,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشلت مزامنة القالب',
                    'error' => $template->sync_error,
                    'template' => [
                        'type' => $template->type,
                        'name' => $template->name,
                        'is_synced' => $template->is_synced,
                        'last_synced_at' => $template->last_synced_at,
                        'docking_template_id' => $template->docking_template_id,
                        'sync_error' => $template->sync_error,
                    ]
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في مزامنة القالب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Preview a document template HTML with sample data
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\Response
     */
    public function preview(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            abort(404, 'نوع القالب غير صالح');
        }

        // Get preview HTML
        $preview = DocumentTemplateService::previewTemplate($template);

        if (!$preview || empty($preview['html'])) {
            return response()->view('admin.document-templates.error', [
                'message' => 'فشل تحميل معاينة القالب. تأكد من صحة إعدادات DocKing وتمت مزامنة القالب.',
            ]);
        }

        // Return preview HTML
        return response($preview['html'], 200, [
            'Content-Type' => 'text/html',
        ]);
    }

    /**
     * Preview a document template using custom content and data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function previewContent(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صالحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Create a temporary template in DocKing for previewing
            $response = app('docking')->storeDocumentTemplate([
                'name' => 'Preview Template ' . now()->format('Y-m-d H:i:s'),
                'content' => $request->input('content'),
                'metadata' => [
                    'driver' => 'gotenberg',
                    'templating' => 'blade',
                    'is_temp' => true,
                    'created_at' => now()->toIso8601String(),
                ],
            ]);

            if (!isset($response['uuid'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل إنشاء قالب مؤقت للمعاينة',
                ], 500);
            }

            $tempTemplateId = $response['uuid'];

            // Get preview HTML using the temporary template
            $preview = app('docking')->previewHtmlDocumentTemplate($tempTemplateId, $request->input('data'));

            if (!$preview || empty($preview['html'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل تحميل معاينة القالب',
                ], 500);
            }

            // Try to delete the temporary template (but don't fail if it doesn't work)
            try {
                app('docking')->deleteDocumentTemplate($tempTemplateId);
            } catch (\Exception $e) {
                // Just log the error, don't fail the preview
                \Illuminate\Support\Facades\Log::warning("Failed to delete temporary template: {$e->getMessage()}");
            }

            return response()->json([
                'success' => true,
                'html' => $preview['html'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في معاينة القالب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export template to filesystem
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportToFile(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            return response()->json([
                'success' => false,
                'message' => 'نوع القالب غير صالح',
            ], 404);
        }

        try {
            // Export template to file
            $result = DocumentTemplateService::exportTemplateToFile($template);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تصدير القالب إلى ملف بنجاح',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل تصدير القالب إلى ملف',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تصدير القالب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import template from filesystem
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\JsonResponse
     */
    public function importFromFile(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            return response()->json([
                'success' => false,
                'message' => 'نوع القالب غير صالح',
            ], 404);
        }

        try {
            // Import template from file
            $result = DocumentTemplateService::importTemplateFromFile($template);

            if ($result) {
                $templateModel = DocumentTemplateService::getTemplate($template);

                return response()->json([
                    'success' => true,
                    'message' => 'تم استيراد القالب من ملف بنجاح',
                    'template' => [
                        'type' => $templateModel->type,
                        'name' => $templateModel->name,
                        'content' => $templateModel->content,
                        'is_synced' => $templateModel->is_synced,
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل استيراد القالب من ملف (الملف غير موجود)',
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في استيراد القالب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset template to default
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetToDefault(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            return response()->json([
                'success' => false,
                'message' => 'نوع القالب غير صالح',
            ], 404);
        }

        try {
            // Get default content for this template type
            $defaultContent = DocumentTemplateService::getDefaultContent($template);

            // Update template with default content
            $templateModel = DocumentTemplateService::updateTemplate($template, $defaultContent);

            return response()->json([
                'success' => true,
                'message' => 'تم إعادة ضبط القالب إلى القيم الافتراضية',
                'template' => [
                    'type' => $templateModel->type,
                    'name' => $templateModel->name,
                    'content' => $templateModel->content,
                    'is_synced' => $templateModel->is_synced,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إعادة ضبط القالب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate PDF for a template
     *
     * @param Request $request
     * @param string $template
     * @return \Illuminate\Http\Response
     */
    public function generatePdf(Request $request, string $template)
    {
        // Validate template type
        $templateTypes = DocumentTemplateService::getTemplateTypes();
        if (!array_key_exists($template, $templateTypes)) {
            abort(404, 'نوع القالب غير صالح');
        }

        // Get data from request or use sample data if empty
        $data = $request->input('data');
        if (empty($data)) {
            $data = DocumentTemplateService::getSampleDataForTemplate($template);
        }

        // Generate PDF
        $result = DocumentTemplateService::generatePdf($template, $data);

        if (!$result || empty($result['base64_content'])) {
            return response()->json([
                'success' => false,
                'message' => 'فشل إنشاء ملف PDF. تأكد من صحة إعدادات DocKing وتمت مزامنة القالب.',
            ], 400);
        }

        // Decode base64 content
        $pdfContent = base64_decode($result['base64_content']);

        // Generate filename
        $filename = strtolower($template) . '_' . date('Ymd_His') . '.pdf';

        // Return PDF file
        return Response::make($pdfContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }
}
