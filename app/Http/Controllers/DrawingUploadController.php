<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class DrawingUploadController extends Controller
{
    /**
     * Upload a drawing file from canvas
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'drawing_data' => 'required|string',
                'filename' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $drawingData = $request->input('drawing_data');
            $originalFilename = $request->input('filename');

            // Validate base64 image data
            if (!preg_match('/^data:image\/png;base64,/', $drawingData)) {
                return response()->json([
                    'success' => false,
                    'message' => 'تنسيق الصورة غير صحيح'
                ], 422);
            }

            // Extract base64 data
            $base64Data = preg_replace('/^data:image\/png;base64,/', '', $drawingData);
            $imageData = base64_decode($base64Data);

            if ($imageData === false) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في فك تشفير الصورة'
                ], 422);
            }

            // Generate unique filename
            $timestamp = now()->format('Y-m-d-H-i-s');
            $uniqueId = Str::random(8);
            $filename = "رسم-توضيحي-{$timestamp}-{$uniqueId}.png";
            
            // Define storage path
            $directory = 'technician-reports';
            $fullPath = $directory . '/' . $filename;

            // Store the file
            $stored = Storage::disk('public')->put($fullPath, $imageData);

            if (!$stored) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في حفظ الملف'
                ], 500);
            }

            // Get the full URL for the file
            $fileUrl = Storage::disk('public')->url($fullPath);

            return response()->json([
                'success' => true,
                'message' => 'تم رفع الرسم بنجاح',
                'data' => [
                    'filename' => $filename,
                    'path' => $fullPath,
                    'url' => $fileUrl,
                    'size' => strlen($imageData),
                    'original_filename' => $originalFilename,
                    'livewire_event' => 'drawing-uploaded'
                ],
                'javascript' => [
                    'event' => 'drawing-file-uploaded',
                    'payload' => [
                        'filename' => $filename,
                        'path' => $fullPath,
                        'url' => $fileUrl
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Drawing upload failed: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'request_data' => $request->except(['drawing_data']) // Don't log the large base64 data
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء رفع الرسم'
            ], 500);
        }
    }
}
