<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\MaintenanceRequest;
use App\Models\Payment;
use App\Models\Visit;
use App\Models\CertificateRequest;
use App\Services\PaymentService;
use App\Services\VisitSchedulerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\URL;

class ClientPortalController extends Controller
{
    protected $paymentService;
    protected $visitSchedulerService;

    public function __construct(
        PaymentService $paymentService,
        VisitSchedulerService $visitSchedulerService
    ) {
        $this->paymentService = $paymentService;
        $this->visitSchedulerService = $visitSchedulerService;

        // Apply auth middleware except for login/logout
        //$this->middleware('auth:client')->except(['login', 'showLogin', 'processLogin']);
    }

    /**
     * Show login page
     */
    public function showLogin()
    {
        if (Auth::guard('client')->check()) {
            return redirect()->intended(route('client.dashboard'));
        }

        return view('client.auth.login');
    }

    /**
     * Process login
     */
    public function processLogin(Request $request)
    {
        Auth::guard('client')->login(Client::first());
        if (Auth::guard('client')->check()) {
            return redirect()->intended(route('client.dashboard'));
        }

        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('username', 'password');
        $credentials['is_active'] = true;

        if (Auth::guard('client')->attempt($credentials)) {
            $clientCredential = Auth::guard('client')->user();

            // Update last login time
            $clientCredential->last_login_at = now();
            $clientCredential->save();

            return redirect()->intended(route('client.dashboard'));
        }

        return back()->withErrors([
            'username' => 'The provided credentials do not match our records.',
        ]);
    }

    /**
     * Show dashboard
     */
    public function dashboard()
    {
        $client = Auth::guard('client')->user();

        // Get active contracts
        $activeContracts = Contract::where('client_id', $client->id)
            ->whereIn('status', ['active', 'pending'])
            ->get();

        // Get active maintenance requests
        $activeRequests = MaintenanceRequest::where('client_id', $client->id)
            ->whereIn('status', ['new', 'assigned', 'in_progress'])
            ->get();

        // Get pending certificate requests
        $pendingCertificates = CertificateRequest::where('client_id', $client->id)
            ->whereIn('status', ['pending', 'processing', 'inspection_scheduled'])
            ->get();

        // Get upcoming visits
        $upcomingVisits = Visit::whereHas('maintenanceRequest', function ($query) use ($client) {
            $query->where('client_id', $client->id);
        })
            ->where('status', 'scheduled')
            ->where('scheduled_at', '>', now())
            ->orderBy('scheduled_at')
            ->get();

        // Get recent payments
        $recentPayments = Payment::whereHas('contract', function ($query) use ($client) {
            $query->where('client_id', $client->id);
        })
            ->orderBy('payment_date', 'desc')
            ->take(5)
            ->get();

        // Get upcoming payments
        $upcomingPayments = $this->paymentService->getUpcomingPayments($client->id, 30);

        return view('client.dashboard', compact(
            'client',
            'activeContracts',
            'activeRequests',
            'pendingCertificates',
            'upcomingVisits',
            'recentPayments',
            'upcomingPayments'
        ));
    }

    /**
     * Show contracts list
     */
    public function contracts()
    {
        $client = Auth::guard('client')->user();
        $contracts = Contract::with('contractType')
            ->where('client_id', $client->id)
            ->orderBy('start_date', 'desc')
            ->get();

        return view('client.contracts.index', compact('contracts'));
    }

    /**
     * Show contract details
     */
    public function showContract($id)
    {
        $client = Auth::guard('client')->user();

        $contract = Contract::with(['contractType', 'maintenanceRequests', 'payments', 'visitSchedules'])
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        // Get upcoming visits for this contract
        $upcomingVisits = Visit::whereHas('maintenanceRequest', function ($query) use ($contract) {
            $query->where('contract_id', $contract->id);
        })
            ->where('status', 'scheduled')
            ->where('scheduled_at', '>', now())
            ->orderBy('scheduled_at')
            ->get();

        return view('client.contracts.show', compact('contract', 'upcomingVisits'));
    }

    /**
     * Show the payment form for a contract
     */
    public function showPaymentForm($contractId)
    {
        $client = Auth::guard('client')->user();

        $contract = Contract::where('client_id', $client->id)
            ->where('id', $contractId)
            ->whereIn('status', ['active', 'pending'])
            ->firstOrFail();

        $paymentAmount = $this->paymentService->calculateContractPaymentAmount($contract);

        return view('client.payments.create', compact('contract', 'paymentAmount'));
    }

    /**
     * Process payment for a contract
     */
    public function processPayment(Request $request, $contractId)
    {
        $request->validate([
            'payment_method' => 'required|in:cash,bank_transfer,check,credit_card,online',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $client = Auth::guard('client')->user();

        // Verify the contract belongs to the client
        $contract = Contract::where('id', $contractId)
            ->where('client_id', $client->id)
            ->whereIn('status', ['active', 'pending'])
            ->firstOrFail();

        // Calculate payment amount based on contract terms
        $amount = $this->paymentService->calculateContractPaymentAmount($contract);

        // Process payment
        $payment = $this->paymentService->createContractPayment(
            $contract,
            $amount,
            $request->payment_method,
            $request->reference_number,
            $request->input('notes')
        );

        return redirect()->route('client.contracts.show', $contract->id)
            ->with('success', 'Payment processed successfully. Payment #' . $payment->payment_number);
    }

    /**
     * Show the form to schedule a visit
     */
    public function showScheduleVisitForm($contractId)
    {
        $client = Auth::guard('client')->user();

        $contract = Contract::where('client_id', $client->id)
            ->where('id', $contractId)
            ->whereIn('status', ['active', 'pending'])
            ->firstOrFail();

        // Check if the client has remaining visits
        if ($contract->remainingVisits <= 0) {
            return redirect()->route('client.contracts.show', $contract->id)
                ->with('error', 'No remaining visits available in this contract.');
        }

        return view('client.visits.schedule', compact('contract'));
    }

    /**
     * Process the visit scheduling
     */
    public function scheduleVisit(Request $request, $contractId)
    {
        $request->validate([
            'preferred_date' => 'required|date|after:today',
            'preferred_time' => 'required|date_format:H:i',
            'description' => 'nullable|string',
        ]);

        $client = Auth::guard('client')->user();

        // Verify the contract belongs to the client
        $contract = Contract::where('id', $contractId)
            ->where('client_id', $client->id)
            ->whereIn('status', ['active', 'pending'])
            ->firstOrFail();

        // Check if the client has remaining visits
        if ($contract->remainingVisits <= 0) {
            return redirect()->route('client.contracts.show', $contract->id)
                ->with('error', 'No remaining visits available in this contract.');
        }

        // Create maintenance request
        $maintenanceRequest = MaintenanceRequest::create([
            'request_number' => 'MR-' . date('Ymd') . '-' . rand(1000, 9999),
            'contract_id' => $contract->id,
            'client_id' => $client->id,
            'title' => 'Scheduled Visit',
            'description' => $request->description ?? 'Client requested maintenance visit',
            'priority' => 'medium',
            'status' => 'new',
            'request_date' => now(),
        ]);

        // Create the visit
        $scheduledDateTime = Carbon::parse($request->preferred_date . ' ' . $request->preferred_time);

        $visit = Visit::create([
            'maintenance_request_id' => $maintenanceRequest->id,
            'technician_id' => null, // To be assigned by admin
            'scheduled_at' => $scheduledDateTime,
            'status' => 'scheduled',
        ]);

        return redirect()->route('client.contracts.show', $contract->id)
            ->with('success', 'Visit scheduled successfully for ' . $scheduledDateTime->format('F j, Y \a\t g:i A'));
    }

    /**
     * Show the form to create a maintenance request
     */
    public function showCreateRequestForm($contractId)
    {
        $client = Auth::guard('client')->user();

        $contract = Contract::where('client_id', $client->id)
            ->where('id', $contractId)
            ->whereIn('status', ['active', 'pending'])
            ->firstOrFail();

        return view('client.maintenance.create', compact('contract'));
    }

    /**
     * Process the maintenance request creation
     */
    public function createMaintenanceRequest(Request $request, $contractId)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,critical',
        ]);

        $client = Auth::guard('client')->user();

        // Verify the contract belongs to the client
        $contract = Contract::where('id', $contractId)
            ->where('client_id', $client->id)
            ->whereIn('status', ['active', 'pending'])
            ->firstOrFail();

        $maintenanceRequest = MaintenanceRequest::create([
            'request_number' => 'MR-' . date('Ymd') . '-' . rand(1000, 9999),
            'contract_id' => $contract->id,
            'client_id' => $client->id,
            'title' => $request->title,
            'description' => $request->description,
            'priority' => $request->priority,
            'status' => 'new',
            'request_date' => now(),
        ]);

        return redirect()->route('client.maintenance.index')
            ->with('success', 'Maintenance request created successfully.');
    }

    /**
     * Show all maintenance requests
     */
    public function maintenanceRequests()
    {
        $client = Auth::guard('client')->user();

        $requests = MaintenanceRequest::with(['contract', 'assignedTo'])
            ->where('client_id', $client->id)
            ->orderBy('request_date', 'desc')
            ->get();

        return view('client.maintenance.index', compact('requests'));
    }

    /**
     * Show maintenance request details
     */
    public function showMaintenanceRequest($id)
    {
        $client = Auth::guard('client')->user();

        $request = MaintenanceRequest::with(['contract', 'assignedTo', 'visits'])
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        return view('client.maintenance.show', compact('request'));
    }

    /**
     * Show all visits
     */
    public function visits()
    {
        $client = Auth::guard('client')->user();

        $visits = Visit::with(['maintenanceRequest', 'technician'])
            ->whereHas('maintenanceRequest', function ($query) use ($client) {
                $query->where('client_id', $client->id);
            })
            ->orderBy('scheduled_at', 'desc')
            ->get();

        return view('client.visits.index', compact('visits'));
    }

    /**
     * Show visit details
     */
    public function showVisit($id)
    {
        $client = Auth::guard('client')->user();

        $visit = Visit::with(['maintenanceRequest', 'technician'])
            ->whereHas('maintenanceRequest', function ($query) use ($client) {
                $query->where('client_id', $client->id);
            })
            ->where('id', $id)
            ->firstOrFail();

        return view('client.visits.show', compact('visit'));
    }

    /**
     * Show all certificate requests
     */
    public function certificateRequests()
    {
        $client = Auth::guard('client')->user();

        $requests = CertificateRequest::where('client_id', $client->id)
            ->orderBy('request_date', 'desc')
            ->get();

        return view('client.certificates.index', compact('requests'));
    }

    /**
     * Show certificate request details
     */
    public function showCertificateRequest($id)
    {
        $client = Auth::guard('client')->user();

        $request = CertificateRequest::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        return view('client.certificates.show', compact('request'));
    }

    /**
     * Cancel a certificate request
     */
    public function cancelCertificateRequest($id)
    {
        $client = Auth::guard('client')->user();

        $request = CertificateRequest::where('client_id', $client->id)
            ->where('id', $id)
            ->whereIn('status', ['pending', 'processing'])
            ->firstOrFail();

        $request->status = 'canceled';
        $request->save();

        return redirect()->route('client.certificates.show', $request->id)
            ->with('success', 'Certificate request canceled successfully.');
    }

    /**
     * Show client profile
     */
    public function showProfile()
    {
        $client = Auth::guard('client')->user();
        return view('client.profile.index', compact('client'));
    }

    /**
     * Update client profile
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|max:255',
            'phone' => 'sometimes|required|string|max:20',
            'address' => 'sometimes|required|string|max:255',
        ]);

        $client = Auth::guard('client')->user();

        // Update only provided fields
        $client->fill($request->only(['name', 'email', 'phone', 'address']));
        $client->save();

        return redirect()->route('client.profile')
            ->with('success', 'Profile updated successfully.');
    }

    /**
     * Process logout
     */
    public function logout(Request $request)
    {
        Auth::guard('client')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }
}
