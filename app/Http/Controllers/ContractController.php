<?php

namespace App\Http\Controllers;

use App\Models\Contract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ContractController extends Controller
{
    /**
     * Print a contract.
     *
     * @param  \App\Models\Contract  $record
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|object
     */
    public function print(Contract $record)
    {
        // Check permissions
        if (!Auth::user()->can('view', $record)) {
            abort(403);
        }

        // Load relationships
        $record->load([
            'client',
            'contract_type',
            'visits' => function($query) {
                $query->with('technician')
                    ->orderBy('scheduled_at', 'desc');
            }
        ]);

        // Calculate visit statistics
        $totalVisits = $record->visits->count();
        $completedVisits = $record->visits->where('status', 'completed')->count();
        $scheduledVisits = $record->visits->where('status', 'scheduled')->count();
        $inProgressVisits = $record->visits->where('status', 'in_progress')->count();
        $canceledVisits = $record->visits->where('status', 'canceled')->count();

        return view('contracts.print', [
            'contract' => $record,
            'totalVisits' => $totalVisits,
            'completedVisits' => $completedVisits,
            'scheduledVisits' => $scheduledVisits,
            'inProgressVisits' => $inProgressVisits,
            'canceledVisits' => $canceledVisits,
            'remainingVisits' => max(0, $record->visits_included - ($completedVisits + $scheduledVisits + $inProgressVisits)),
            'company' => [
                'name' => config('app.company_name', 'Your Company Name'),
                'address' => config('app.company_address', 'Company Address'),
                'phone' => config('app.company_phone', 'Company Phone'),
                'email' => config('app.company_email', '<EMAIL>'),
                'logo' => config('app.company_logo', '/images/logo.png'),
            ]
        ]);
    }
}
