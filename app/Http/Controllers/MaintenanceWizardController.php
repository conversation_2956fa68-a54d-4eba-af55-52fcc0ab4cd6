<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\ContractType;
use App\Models\MaintenanceRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class MaintenanceWizardController extends Controller
{
    public function __construct()
    {
        // Initialize default values if the session is empty
        /*if (!Session::has('maintenance_wizard_data'))*/ {
            $contractTypes = ContractType::where('is_active', true)
                ->orderBy('display_order')
                ->get();

            // Use the first contract type as default, if available
            $defaultType = $contractTypes->first();

            // Set default start date to 7 days from now
            $startDate = Carbon::now()->addDays(7)->format('Y-m-d');

            $guard = Auth::guard('client');
            $client = $guard->check() ? $guard->user() : null;

            Session::put('maintenance_wizard_data', [
                'currentStep' => 1,
                'contractType' => $defaultType ? $defaultType->slug : null,
                'contractTypeDetails' => $defaultType ? [
                    'id' => $defaultType->id,
                    'name' => $defaultType->name,
                    'period' => $defaultType->period,
                    'visit_limit' => $defaultType->visit_limit,
                ] : null,
                'companyName' => $client?->company ?? 'Test Company Name',
                'contactName' => $client?->name ?? '',
                'phone' => $client?->phone ?? '',
                'email' => $client?->email ?? '',
                'address' => $client?->address ?? 'Test Address',
                'startDate' => $startDate,
                'contractDuration' => $defaultType ? $defaultType->period : 12,
            ]);
        }
    }

    /**
     * Step 1: Contract Type Selection
     */
    public function index()
    {
        return $this->step1();
    }

    public function step1()
    {
        // Load contract types from the database
        $contractTypes = ContractType::where('is_active', true)
            ->orderBy('display_order')
            ->get()
            ->mapWithKeys(function ($type) {
                return [$type->slug => [
                    'id' => $type->id,
                    'name' => $type->name,
                    'icon' => $type->icon,
                    'description' => $type->description,
                    'benefits' => $type->benefits ?? [],
                    'period' => $type->period,
                    'visit_limit' => $type->visit_limit,
                ]];
            });

        $data = Session::get('maintenance_wizard_data');
        $data['currentStep'] = 1;
        Session::put('maintenance_wizard_data', $data);

        return view('maintenance.step1', [
            'contractTypes' => $contractTypes,
            'selectedType' => $data['contractType'],
            'currentStep' => $data['currentStep'],
        ]);
    }

    /**
     * Process Step 1
     */
    public function processStep1(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contractType' => 'required|string|exists:contract_types,slug',
        ], [
            'contractType.required' => 'الرجاء اختيار نوع العقد',
            'contractType.exists' => 'نوع العقد المختار غير صالح',
        ]);

        if ($validator->fails()) {
            return redirect()->route('maintenance.step1')
                ->withErrors($validator)
                ->withInput();
        }

        // Get contract type details from database
        $contractType = ContractType::where('slug', $request->contractType)->first();

        // Update session data
        $data = Session::get('maintenance_wizard_data');
        $data['contractType'] = $request->contractType;
        $data['contractTypeDetails'] = [
            'id' => $contractType->id,
            'name' => $contractType->name,
            'period' => $contractType->period,
            'visit_limit' => $contractType->visit_limit,
        ];
        $data['contractDuration'] = $contractType->period;

        Session::put('maintenance_wizard_data', $data);

        return redirect()->route('maintenance.step2');
    }

    /**
     * Step 2: Client Information
     */
    public function step2()
    {
        $data = Session::get('maintenance_wizard_data');
        $data['currentStep'] = 2;
        Session::put('maintenance_wizard_data', $data);

        // Check for required previous data
        if (empty($data['contractType'])) {
            return redirect()->route('maintenance.step1');
        }

        return view('maintenance.step2', [
            'companyName' => $data['companyName'],
            'contactName' => $data['contactName'],
            'phone' => $data['phone'],
            'email' => $data['email'],
            'address' => $data['address'],
            'currentStep' => $data['currentStep'],
        ]);
    }

    /**
     * Process Step 2
     */
    public function processStep2(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'companyName' => 'required|string|max:255',
            'contactName' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'address' => 'required|string|max:500',
        ], [
            'companyName.required' => 'الرجاء إدخال اسم الشركة / المؤسسة',
            'contactName.required' => 'الرجاء إدخال اسم المسؤول',
            'phone.required' => 'الرجاء إدخال رقم الهاتف',
            'email.required' => 'الرجاء إدخال البريد الإلكتروني',
            'email.email' => 'الرجاء إدخال بريد إلكتروني صحيح',
            'address.required' => 'الرجاء إدخال العنوان',
        ]);

        if ($validator->fails()) {
            return redirect()->route('maintenance.step2')
                ->withErrors($validator)
                ->withInput();
        }

        // Update session data
        $data = Session::get('maintenance_wizard_data');
        $data['companyName'] = $request->companyName;
        $data['contactName'] = $request->contactName;
        $data['phone'] = $request->phone;
        $data['email'] = $request->email;
        $data['address'] = $request->address;

        Session::put('maintenance_wizard_data', $data);

        return redirect()->route('maintenance.step3');
    }

    /**
     * Step 3: Review and Confirm
     */
    public function step3()
    {
        $data = Session::get('maintenance_wizard_data');
        $data['currentStep'] = 3;
        Session::put('maintenance_wizard_data', $data);

        // Check for required previous data
        if (empty($data['contractType']) ||
            empty($data['email']) ||
            empty($data['startDate'])) {
            return redirect()->route('maintenance.step1');
        }

        // Prepare contract summary
        $contractSummary = $this->prepareContractSummary($data);

        return view('maintenance.step3', [
            'contractSummary' => $contractSummary,
            'currentStep' => $data['currentStep'],
        ]);
    }

    /**
     * Process Step 3 and submit request
     */
    public function processStep3(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'termsAgreement' => 'accepted',
        ], [
            'termsAgreement.accepted' => __('maintenance.validation.terms_agreement'),
        ]);

        if ($validator->fails()) {
            return redirect()->route('maintenance.step3')
                ->withErrors($validator);
        }

        // Get all the data from the session
        $data = Session::get('maintenance_wizard_data');

        $guard = Auth::guard('client');

        // Find or create a client
        $client = $guard->check() ? $guard->user() : Client::firstOrCreate(
            ['phone' => $data['phone']],
            [
                'name' => $data['contactName'],
                'email' => $data['email'],
                'company' => $data['companyName'],
                'address' => $data['address'],
            ]
        );

        $latestRequest = MaintenanceRequest::latest()->first();
        $nextId = $latestRequest ? $latestRequest->id + 1 : 1;
        $requestNumber = 'REQ-' . date('Ym') . '-' . str_pad("$nextId", 4, '0', STR_PAD_LEFT);

        // Create maintenance request instead of contract
        $maintenanceRequest = MaintenanceRequest::create([
            'request_number' => $requestNumber,
            'client_id' => $client->id,
            'contract_type_id' => $data['contractTypeDetails']['id'],
            'visits_included' => $data['contractTypeDetails']['visit_limit'],
            'status' => 'new',
            'notes' => '',
        ]);

        // Set a success message in session
        Session::flash('success', 'تم تقديم طلب عقد الصيانة بنجاح! رقم الطلب: ' . $requestNumber);

        // Clear wizard session data
        Session::forget('maintenance_wizard_data');

        return redirect()->route('maintenance.success', ['request' => $maintenanceRequest->id]);
    }

    /**
     * Show success page
     */
    public function success($requestId)
    {
        $request = MaintenanceRequest::findOrFail($requestId);

        return view('maintenance.success', [
            'request' => $request,
            'currentStep' => 5,
        ]);
    }

    /**
     * Helper method to prepare contract summary
     */
    private function prepareContractSummary($data)
    {
        $startDate = $data['startDate'];
        $endDate = Carbon::parse($startDate)->addMonths($data['contractTypeDetails']['period']);

        return [
            'contractInfo' => [
                'type' => $data['contractTypeDetails']['name'],
                'startDate' => Carbon::parse($startDate)->format('d/m/Y'),
                'endDate' => $endDate->format('d/m/Y'),
                'duration' => $data['contractTypeDetails']['period'] . ' شهر',
                'value' => null,
                'visitLimit' => $data['contractTypeDetails']['visit_limit'],
            ],
            'clientInfo' => [
                'companyName' => $data['companyName'],
                'contactName' => $data['contactName'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'address' => $data['address'],
            ],
        ];
    }
}
