<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BroadcastChannel extends Model
{
    use SoftDeletes;

    protected $guarded = [];
    protected $casts = [
        'has_attachments' => 'boolean',
        'has_poll' => 'boolean',
        'is_active' => 'boolean',
        'default' => 'boolean',
        'auth_body' => 'json',
    ];

    public function senders()
    {
        return $this->hasMany(BroadcastChannelSender::class);
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    public function getHasBalanceAttribute(): bool
    {
        return in_array($this->class, [
            \App\Broadcasting\TaqnyatChannel::class,
            \App\Broadcasting\MsegatChannel::class,
        ]);
    }

    public function getBalance(): array
    {
        return match ($this->class) {
            \App\Broadcasting\TaqnyatChannel::class => [
                'balance' => 100,
                'points' => 100,
            ],
            \App\Broadcasting\MsegatChannel::class => [
                'balance' => 100,
                'points' => -1,
            ],
            default => [
                'balance' => -1,
                'points' => -1,
            ],
        };
    }
}
