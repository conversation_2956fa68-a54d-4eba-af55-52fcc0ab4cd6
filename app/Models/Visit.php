<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Visit extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'maintenance_request_id', // Keep for backward compatibility
        'technician_id',
        'visitable_type',
        'visitable_id',
        'scheduled_at',
        'started_at',
        'completed_at',
        'status',
        'findings',
        'actions_taken',
        'recommendations',
        'notes',
        'data',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'data' => 'array',
    ];

    /**
     * Get the polymorphic visitable model (Contract or MaintenanceRequest).
     */
    public function visitable()
    {
        return $this->morphTo();
    }

    /**
     * Get the contract that this visit belongs to (through polymorphic relationship).
     * This method provides backward compatibility for existing code.
     */
    public function contract()
    {
        // For polymorphic visits associated with contracts
        if ($this->visitable_type === 'App\Models\Contract') {
            return $this->belongsTo(Contract::class, 'visitable_id');
        }

        // For visits associated with maintenance requests, get contract through maintenance request
        if ($this->visitable_type === 'App\Models\MaintenanceRequest' && $this->maintenance_request_id) {
            return $this->belongsTo(Contract::class, 'maintenance_request_id', 'maintenance_request_id');
        }

        // Return empty relationship for other cases - use a valid column but with impossible condition
        return $this->belongsTo(Contract::class, 'id')->whereRaw('0 = 1');
    }

    /**
     * Get the maintenance request that this visit belongs to (backward compatibility).
     */
    public function maintenanceRequest()
    {
        return $this->belongsTo(MaintenanceRequest::class);
    }

    /**
     * Get the technician assigned to this visit.
     */
    public function technician()
    {
        return $this->belongsTo(User::class, 'technician_id');
    }

    /**
     * Get the client for this visit through the visitable relationship.
     */
    public function getClientAttribute()
    {
        if ($this->visitable_type === 'App\Models\Contract') {
            return $this->visitable?->client;
        } elseif ($this->visitable_type === 'App\Models\MaintenanceRequest') {
            return $this->visitable?->client;
        }

        // Fallback to maintenance request relationship for backward compatibility
        return $this->maintenanceRequest?->client;
    }

    /**
     * Helper method to get the contract if this visit is contract-based.
     */
    public function getContractAttribute()
    {
        if ($this->visitable_type === 'App\Models\Contract') {
            return $this->visitable;
        }

        return null;
    }

    /**
     * Helper method to get the maintenance request if this visit is maintenance request-based.
     */
    public function getMaintenanceRequestAttribute()
    {
        if ($this->visitable_type === 'App\Models\MaintenanceRequest') {
            return $this->visitable;
        }

        // Fallback to direct relationship for backward compatibility
        // Access the relationship directly from the attributes to avoid recursion
        if ($this->maintenance_request_id) {
            return $this->getRelationValue('maintenanceRequest');
        }

        return null;
    }

    public function documents()
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Scope a query to only include visits with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include scheduled visits.
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope a query to only include visits in progress.
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope a query to only include completed visits.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include canceled visits.
     */
    public function scopeCanceled($query)
    {
        return $query->where('status', 'canceled');
    }

    /**
     * Scope a query to only include visits scheduled for today.
     */
    public function scopeScheduledToday($query)
    {
        return $query->whereDate('scheduled_at', now()->toDateString());
    }

    /**
     * Scope a query to only include visits scheduled for this week.
     */
    public function scopeScheduledThisWeek($query)
    {
        return $query->whereBetween('scheduled_at', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    /**
     * Scope a query to only include visits completed this month.
     */
    public function scopeCompletedThisMonth($query)
    {
        return $query->completed()->whereMonth('completed_at', now()->month)->whereYear('completed_at', now()->year);
    }

    /**
     * Get the duration of the visit in minutes.
     *
     * @return int|null
     */
    public function getDurationInMinutesAttribute(): ?int
    {
        if ($this->started_at && $this->completed_at) {
            return $this->started_at->diffInMinutes($this->completed_at);
        }

        return null;
    }
}
