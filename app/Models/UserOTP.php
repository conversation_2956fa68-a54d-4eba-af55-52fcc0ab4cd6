<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class UserOTP extends Model
{
    use HasUuids;

    protected $primaryKey = 'uuid';

    protected $table = 'user_otp';

    protected $casts = [
        'expired_at' => 'datetime',
        'success_at' => 'datetime',
        'failed_at' => 'datetime',
        'metadata' => 'json',
    ];

    public function auth()
    {
        return $this->morphTo('auth')->withTrashed();
    }
}
