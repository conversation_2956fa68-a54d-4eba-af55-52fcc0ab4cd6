<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DocumentTemplate extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'is_synced' => 'boolean',
        'last_synced_at' => 'datetime',
    ];

    /**
     * Scope a query to get template by type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to get synced templates.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSynced($query)
    {
        return $query->where('is_synced', true);
    }

    /**
     * Scope a query to get templates that need syncing.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNeedsSyncing($query)
    {
        return $query->where(function($q) {
            $q->where('is_synced', false)
                ->orWhereNull('docking_template_id')
                ->orWhere('docking_instance_url', '!=', tenant()->docking_base_url);
        });
    }

    /**
     * Mark the template as synced with DocKing.
     *
     * @param string $dockingTemplateId
     * @param string $dockingInstanceUrl
     * @return $this
     */
    public function markAsSynced(string $dockingTemplateId, string $dockingInstanceUrl)
    {
        $this->update([
            'docking_template_id' => $dockingTemplateId,
            'docking_instance_url' => $dockingInstanceUrl,
            'is_synced' => true,
            'last_synced_at' => now(),
            'sync_error' => null,
        ]);

        return $this;
    }

    /**
     * Mark the template as having a sync error.
     *
     * @param string $errorMessage
     * @return $this
     */
    public function markSyncError(string $errorMessage)
    {
        $this->update([
            'is_synced' => false,
            'sync_error' => $errorMessage,
        ]);

        return $this;
    }

    /**
     * Get template cache key.
     *
     * @return string
     */
    public function getCacheKey(): string
    {
        return "document_template:{$this->id}:{$this->updated_at->timestamp}";
    }
}
