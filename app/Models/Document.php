<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Document extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'file_path',
        'file_type',
        'file_size',
        'documentable_type',
        'documentable_id',
        'uploaded_by',
        'description',
    ];

    /**
     * Get the parent documentable model (MaintenanceRequest, Contract, etc.).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function documentable(): \Illuminate\Database\Eloquent\Relations\MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who uploaded this document.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function uploadedBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Legacy alias for uploadedBy relationship.
     *
     * @deprecated Use uploadedBy() instead
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function uploader(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->uploadedBy();
    }
}
