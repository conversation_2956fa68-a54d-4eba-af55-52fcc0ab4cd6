<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class VisitSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'contract_id',
        'frequency',
        'day_of_week',
        'day_of_month',
        'month',
        'custom_pattern',
        'preferred_time',
        'is_active',
    ];

    protected $casts = [
        'day_of_week' => 'integer',
        'day_of_month' => 'integer',
        'month' => 'array',
        'preferred_time' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    // Generate next visit date based on schedule pattern
    public function getNextVisitDate(?Carbon $fromDate = null): Carbon
    {
        $startDate = $fromDate ?? Carbon::now();

        switch ($this->frequency) {
            case 'weekly':
                $nextDate = $startDate->copy()->next($this->day_of_week);
                break;

            case 'monthly':
                $nextDate = $startDate->copy()->startOfMonth()->addDays($this->day_of_month - 1);
                if ($nextDate->lt($startDate)) {
                    $nextDate->addMonth();
                }
                break;

            case 'quarterly':
                $nextDate = $this->getNextDateForMonths($startDate, 3);
                break;

            case 'biannually':
                $nextDate = $this->getNextDateForMonths($startDate, 6);
                break;

            case 'annually':
                $nextDate = $this->getNextDateForMonths($startDate, 12);
                break;

            case 'custom':
                // Custom pattern handling would need specific implementation
                // For now return a date 30 days in the future
                $nextDate = $startDate->copy()->addDays(30);
                break;

            default:
                $nextDate = $startDate->copy()->addMonth();
        }

        // Apply preferred time if available
        if ($this->preferred_time) {
            $nextDate->setHour($this->preferred_time->format('H'))
                ->setMinute($this->preferred_time->format('i'))
                ->setSecond(0);
        }

        return $nextDate;
    }

    // Helper method for quarterly, biannual, annual calculations
    private function getNextDateForMonths(Carbon $startDate, int $monthInterval): Carbon
    {
        $months = $this->month ?? [1];
        $dayOfMonth = $this->day_of_month ?? 1;

        // Find the next applicable month
        $nextDate = $startDate->copy();
        $found = false;

        for ($i = 0; $i < 12; $i += $monthInterval) {
            $checkDate = $startDate->copy()->addMonths($i);
            $checkMonth = (int) $checkDate->format('n');

            if (in_array($checkMonth, $months) && $checkDate->gt($startDate)) {
                $nextDate = $checkDate->startOfMonth()->addDays($dayOfMonth - 1);
                $found = true;
                break;
            }
        }

        // If no suitable month found, go to next year's first applicable month
        if (!$found) {
            $nextDate = $startDate->copy()->addYear()->startOfYear();
            $monthToUse = min($months);
            $nextDate->setMonth($monthToUse)->startOfMonth()->addDays($dayOfMonth - 1);
        }

        return $nextDate;
    }

    // Get human-readable schedule description
    public function getScheduleDescriptionAttribute(): string
    {
        $time = $this->preferred_time ? ' at ' . $this->preferred_time->format('g:i A') : '';

        switch ($this->frequency) {
            case 'weekly':
                $dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                $day = $dayNames[$this->day_of_week - 1] ?? 'Unknown day';
                return "Every {$day}{$time}";

            case 'monthly':
                $day = $this->day_of_month;
                $dayStr = $day . $this->getDaySuffix($day);
                return "Monthly on the {$dayStr}{$time}";

            case 'quarterly':
                $monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'];
                $months = collect($this->month)->map(fn($m) => $monthNames[$m - 1])->join(', ');
                $day = $this->day_of_month;
                $dayStr = $day . $this->getDaySuffix($day);
                return "Quarterly on the {$dayStr} of {$months}{$time}";

            case 'biannually':
                $monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'];
                $months = collect($this->month)->map(fn($m) => $monthNames[$m - 1])->join(', ');
                $day = $this->day_of_month;
                $dayStr = $day . $this->getDaySuffix($day);
                return "Bi-annually on the {$dayStr} of {$months}{$time}";

            case 'annually':
                $monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'];
                $month = $monthNames[($this->month[0] ?? 1) - 1];
                $day = $this->day_of_month;
                $dayStr = $day . $this->getDaySuffix($day);
                return "Annually on the {$dayStr} of {$month}{$time}";

            case 'custom':
                return "Custom schedule: {$this->custom_pattern}{$time}";

            default:
                return "Unknown schedule type";
        }
    }

    private function getDaySuffix(int $day): string
    {
        if ($day >= 11 && $day <= 13) {
            return 'th';
        }

        switch ($day % 10) {
            case 1:  return 'st';
            case 2:  return 'nd';
            case 3:  return 'rd';
            default: return 'th';
        }
    }
}
