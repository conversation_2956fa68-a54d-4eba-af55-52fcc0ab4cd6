<?php /** @noinspection LaravelFunctionsInspection */

namespace App\Broadcasting;

use App\Settings\ServicesSettings;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class TaqnyatChannel
{
    /* @var Dispatcher */
    protected Dispatcher $events;

    /**
     * Create a new channel instance.
     *
     * @param Dispatcher $events
     */
    public function __construct(Dispatcher $events)
    {
        $this->events = $events;
    }

    /**
     * Authenticate the user's access to the channel.
     *
     * @param mixed $notifiable
     * @param Notification $notification
     *
     * @return void
     * @throws Exception
     * @throws GuzzleException
     */
    public function send(mixed $notifiable, Notification $notification)
    {
        if (!method_exists($notification, 'toTaqnyat'))
            throw new Exception('Notification must have toTaqnyat method');

        $phone = $notifiable->routeNotificationFor('Taqnyat', $notification);
        $message = $notification->toTaqnyat($notifiable);

        $sender = app(ServicesSettings::class)->taqnyatSender;
        if (method_exists($notification, 'taqnyatSender') && !empty($notification->taqnyatSender()))
            $sender = $notification->taqnyatSender();

        if (app()->environment('production')) {
            $key = app(ServicesSettings::class)->taqnyatKey;
            if (is_null($key))
                throw new Exception('taqnyat key not declared');

            if (empty($message))
                throw new Exception('trying send empty message');
            if (is_sa_phone($phone)) {
                $client = new Client();
                $errorMessage = null;
                try {
                    $res = $client->post('https://api.taqnyat.sa/v1/messages', [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Accept' => 'application/json',
                            'Authorization' => "Bearer $key",
                        ],
                        'json' => [
                            'sender' => $sender,
                            'recipients' => $phone,
                            'body' => $message,
                        ],
                    ]);
                    if (method_exists($notification, 'successResponse')) {
                        $json = json_decode((string)$res->getBody(), true);
                        $notification->successResponse($json);
                    }
                } catch (ClientException $exception) {
                    try {
                        $errorMessage = json_decode((string)$exception->getResponse()->getBody(), true)['message'];
                    } catch (Exception) {
                        $errorMessage = (string)$exception->getResponse()->getBody();
                    }
                } catch (Exception $exception) {
                    $errorMessage = $exception->getMessage();
                }
                if (!is_null($errorMessage))
                    Log::error($errorMessage);
            }/* else {
                Log::error("@$sender $phone: $message");
            }*/
        } else {
            if (!app()->environment('testing'))
                Log::info("@$sender $phone: $message");
            if (method_exists($notification, 'successResponse')) {
                $json = json_decode(json_encode([
                    'messageId' => '123',
                ]), true);
                $notification->successResponse($json);
            }
        }
    }
}
