<?php

namespace App\Broadcasting;

use App\Http\Integrations\Waha\APIConnector;
use App\Http\Integrations\Waha\Requests\EditTextRequest;
use App\Http\Integrations\Waha\Requests\SendFileRequest;
use App\Http\Integrations\Waha\Requests\SendImageRequest;
use App\Http\Integrations\Waha\Requests\SendPollRequest;
use App\Http\Integrations\Waha\Requests\SendTextRequest;
use App\Http\Integrations\Waha\Requests\SendVideoRequest;
use App\Http\Integrations\Waha\Requests\SendVoiceRequest;
use App\Http\Integrations\Waha\Requests\StartTypingRequest;
use App\Http\Integrations\Waha\Requests\StopTypingRequest;
use App\Notifications\EditSMSNotification;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class WahaChannel
{
    protected Dispatcher $events;
    private const MAX_RETRIES = 3;
    private const RETRY_DELAY_MS = 1000;

    public function __construct(Dispatcher $events)
    {
        $this->events = $events;
    }

    /**
     * Send a notification via Waha channel
     *
     * @throws Exception|GuzzleException
     */
    public function send(mixed $notifiable, Notification $notification): void
    {
        if (!method_exists($notification, 'toWaha')) {
            throw new Exception('Notification must have toWaha method');
        }

        if (!method_exists($notification, 'wahaChannel')) {
            throw new Exception('Notification must have wahaChannel method');
        }

        $phone = $notifiable->routeNotificationFor('Waha', $notification);
        $data = $notification->toWaha($notifiable);

        $apiConnector = $this->getApiConnector($notification);

        if ($notification instanceof EditSMSNotification) {
            $this->handleEditMessage($notification, $phone, $data, $apiConnector);
            return;
        }

        match ($data['type']) {
            'poll' => $this->sendPollMessage($phone, $data, $notification, $apiConnector),
            'text' => $this->sendTextMessage($phone, $data, $notification, $apiConnector),
            'image' => $this->sendImageMessage($phone, $data, $notification, $apiConnector),
            'video' => $this->sendVideoMessage($phone, $data, $notification, $apiConnector),
            'voice' => $this->sendVoiceMessage($phone, $data, $notification, $apiConnector),
            default => throw new Exception('Invalid notification type')
        };
    }

    private function getApiConnector(Notification $notification): APIConnector
    {
        return new APIConnector($notification->wahaChannel());
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function handleEditMessage(EditSMSNotification $notification, string $phone, array $data, APIConnector $apiConnector): void
    {
        if (!method_exists($notification, 'oldWahaMessageId')) {
            throw new Exception('Notification must have oldWahaMessageId method');
        }

        $request = new EditTextRequest(
            $phone,
            $data['text'],
            messageId: $notification->oldWahaMessageId(),
            channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
        );

        $response = $this->sendWithRetry($apiConnector, $request);
        $this->handleResponse($response, 'Failed to edit message', $notification);
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function sendPollMessage(string $phone, array $data, Notification $notification, APIConnector $apiConnector): void
    {
        $this->simulateTyping($apiConnector, $phone, shorter: true);

        $request = new SendPollRequest(
            $phone,
            $data['text'],
            $data['options'],
            $data['multiple'],
            channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
        );

        $response = $this->sendWithRetry($apiConnector, $request);
        $this->handleResponse($response, 'Failed to send poll message', $notification);
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function sendTextMessage(string $phone, array $data, Notification $notification, APIConnector $apiConnector): void
    {
        if (!empty($data['attachments'])) {
            $this->sendAttachments($phone, $data, $notification, $apiConnector);
            return;
        }

        $this->simulateTyping($apiConnector, $phone, shorter: true);

        $request = new SendTextRequest(
            $phone,
            $data['text'],
            channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
        );

        $response = $this->sendWithRetry($apiConnector, $request);
        $this->handleResponse($response, 'Failed to send text message', $notification);
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function sendImageMessage(string $phone, array $data, Notification $notification, APIConnector $apiConnector): void
    {
        $this->simulateTyping($apiConnector, $phone, shorter: true);

        $request = new SendImageRequest(
            $phone,
            $data['file'],
            $data['caption'],
            channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
        );

        $response = $this->sendWithRetry($apiConnector, $request);
        $this->handleResponse($response, 'Failed to send image message', $notification);
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function sendVideoMessage(string $phone, array $data, Notification $notification, APIConnector $apiConnector): void
    {
        $this->simulateTyping($apiConnector, $phone, shorter: true);

        $request = new SendVideoRequest(
            $phone,
            $data['file'],
            $data['caption'] ?? null,
            channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
        );

        $response = $this->sendWithRetry($apiConnector, $request);
        $this->handleResponse($response, 'Failed to send video message', $notification);
    }

    /**
     * @throws Exception|GuzzleException
     */
    private function sendVoiceMessage(string $phone, array $data, Notification $notification, APIConnector $apiConnector): void
    {
        $this->simulateTyping($apiConnector, $phone, shorter: true);

        $request = new SendVoiceRequest(
            $phone,
            $data['file'],
            channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
        );

        $response = $this->sendWithRetry($apiConnector, $request);
        $this->handleResponse($response, 'Failed to send voice message', $notification);
    }

    private function sendAttachments(string $phone, array $data, Notification $notification, APIConnector $apiConnector): void
    {
        foreach ($data['attachments'] as $index => $attachment) {
            $this->simulateTyping($apiConnector, $phone, shorter: true);

            $fileType = strtoupper($attachment['type'] ?? '');
            unset($attachment['type']);

            $request = match ($fileType) {
                'IMAGE' => new SendImageRequest(
                    $phone,
                    $attachment,
                    $index === 0 && !empty($data['text']) ? $data['text'] : null,
                    channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
                ),
                'FILE', 'PDF' => new SendFileRequest(
                    $phone,
                    $attachment,
                    $index === 0 && !empty($data['text']) ? $data['text'] : null,
                    channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
                ),
                'VIDEO' => new SendVideoRequest(
                    $phone,
                    $attachment,
                    $index === 0 && !empty($data['text']) ? $data['text'] : null,
                    channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
                ),
                'VOICE', 'AUDIO' => new SendVoiceRequest(
                    $phone,
                    $attachment,
                    $index === 0 && !empty($data['text']) ? $data['text'] : null,
                    channelSender: method_exists($notification, 'wahaChannel') ? $notification->wahaChannel() : null
                ),
                default => null
            };

            if ($request) {
                $response = $this->sendWithRetry($apiConnector, $request);
                $this->handleResponse($response, 'Failed to send attachment message', $notification);
            }
        }
    }

    private function simulateTyping(APIConnector $apiConnector, string $phone, bool $shorter = false): void
    {
        $apiConnector->send(new StartTypingRequest($phone));
        usleep($shorter ? rand(75000, 100000) : rand(150000, 200000));
        $apiConnector->send(new StopTypingRequest($phone));
        usleep($shorter ? 250000 : rand(50000, 100000));
    }

    private function sendWithRetry($apiConnector, $request, int $attempt = 0)
    {
        $response = $apiConnector->send($request);

        if ($response->status() === 500 && $attempt < self::MAX_RETRIES) {
            usleep(self::RETRY_DELAY_MS * pow(2, $attempt)); // Exponential backoff
            return $this->sendWithRetry($apiConnector, $request, $attempt + 1);
        }

        return $response;
    }

    /**
     * @throws Exception
     */
    private function handleResponse($response, string $errorMessage, Notification $notification): void
    {
        $json = $response->json();

        if ((!isset($json['key']) && !isset($json['id']))) {
            Log::info($json);
            throw new Exception($errorMessage);
        }

        if (method_exists($notification, 'wahaSuccessResponse')) {
            $notification->wahaSuccessResponse($json);
        }
    }
}
